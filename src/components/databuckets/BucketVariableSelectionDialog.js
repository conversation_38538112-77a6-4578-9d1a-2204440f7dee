import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Checkbox,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  alpha,
  Divider,
  FormControlLabel,
  Collapse,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Badge,
  Tooltip,
  Switch
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import StorageIcon from '@mui/icons-material/Storage';
import DataObjectIcon from '@mui/icons-material/DataObject';
import TableViewIcon from '@mui/icons-material/TableView';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import LinkIcon from '@mui/icons-material/Link';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import TagIcon from '@mui/icons-material/Tag';
import { styled, useTheme } from '@mui/material/styles';
import { ruleVariableRegistryService } from '../../services/api';
import { dataBucketService } from '../../services/api';
import dataSourceService from '../../services/dataSourceService';

// Styled components for Progize branding
const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  maxHeight: 600,
  '&::-webkit-scrollbar': {
    width: '8px',
    height: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: alpha(theme.palette.primary.main, 0.05),
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: alpha(theme.palette.primary.main, 0.2),
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: alpha(theme.palette.primary.main, 0.3),
  },
  border: 'none',
  boxShadow: 'none',
  '& .MuiPaper-root': {
    boxShadow: 'none',
    border: 'none',
    backgroundColor: 'transparent'
  }
}));

// Table card component
const TableCard = styled(Paper)(({ theme }) => ({
  marginBottom: theme.spacing(1.5),
  borderRadius: theme.shape.borderRadius,
  overflow: 'hidden',
  boxShadow: 'none',
  border: `1px solid ${alpha(theme.palette.divider, theme.palette.mode === 'dark' ? 0.3 : 0.15)}`,
  backgroundColor: theme.palette.background.paper,
}));

// Variable count indicator
const CountIndicator = styled(Box)(({ theme }) => ({
  width: 32,
  height: 32,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: alpha(theme.palette.success.main, theme.palette.mode === 'dark' ? 0.2 : 0.1),
  color: theme.palette.mode === 'dark' ? theme.palette.success.light : theme.palette.success.main,
  fontWeight: 'bold',
  fontSize: '14px',
  flexShrink: 0,
  border: `1px solid ${alpha(theme.palette.success.main, theme.palette.mode === 'dark' ? 0.4 : 0.3)}`
}));

// Small label for the count indicator
const CountLabel = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: -10,
  left: '50%',
  transform: 'translateX(-50%)',
  fontSize: '8px',
  fontWeight: 'bold',
  textTransform: 'uppercase',
  padding: '1px 4px',
  borderRadius: '4px',
  whiteSpace: 'nowrap',
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.secondary,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: theme.shadows[1]
}));

// Variable list item style
const VariableItem = styled(Box)(({ theme, selected, disabled }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0.75, 1.5),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: selected
    ? alpha(theme.palette.primary.main, 0.1)
    : 'transparent',
  transition: 'all 0.2s ease',
  cursor: disabled ? 'not-allowed' : 'pointer',
  opacity: disabled ? 0.6 : 1,
  '&:hover': {
    backgroundColor: disabled
      ? 'transparent'
      : selected
        ? alpha(theme.palette.primary.main, 0.15)
        : alpha(theme.palette.action.hover, 0.1),
  }
}));

// Type tag for variable data types
const TypeTag = styled(Typography)(({ theme, datatype }) => {
  // Choose color based on data type
  let color = theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.main;
  if (datatype?.toLowerCase().includes('int') ||
      datatype?.toLowerCase().includes('number') ||
      datatype?.toLowerCase().includes('float') ||
      datatype?.toLowerCase().includes('double')) {
    color = theme.palette.mode === 'dark' ? theme.palette.success.light : theme.palette.success.main;
  } else if (datatype?.toLowerCase().includes('date') ||
             datatype?.toLowerCase().includes('time')) {
    color = theme.palette.mode === 'dark' ? theme.palette.warning.light : theme.palette.warning.main;
  } else if (datatype?.toLowerCase().includes('bool')) {
    color = theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main;
  }

  return {
    backgroundColor: alpha(color, 0.1),
    color: color,
    padding: theme.spacing(0.25, 0.75),
    borderRadius: theme.shape.borderRadius,
    fontSize: '0.7rem',
    marginLeft: theme.spacing(1),
    fontWeight: 500,
    display: 'inline-block',
    border: `1px solid ${alpha(color, 0.3)}`,
  };
});

// Selected variable chip
const SelectedVariableChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  height: 24,
  fontSize: '0.75rem',
  fontWeight: 500,
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.main,
  border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
}));

// Column grid container
const ColumnGrid = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(1),
}));

// Variable card for grid layout
const VariableCard = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${alpha(theme.palette.divider, theme.palette.mode === 'dark' ? 0.2 : 0.1)}`,
  marginBottom: theme.spacing(2),
  width: '100%',
  transition: 'all 0.2s ease-in-out',
  boxShadow: theme.palette.mode === 'dark'
    ? `0 1px 3px ${alpha(theme.palette.common.black, 0.2)}`
    : `0 1px 3px ${alpha(theme.palette.common.black, 0.05)}`,
  '&:hover': {
    boxShadow: theme.palette.mode === 'dark'
      ? `0 3px 6px ${alpha(theme.palette.common.black, 0.3)}`
      : `0 3px 6px ${alpha(theme.palette.common.black, 0.08)}`,
    transform: 'translateY(-2px)',
    borderColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.3 : 0.2),
  },
}));

// Type badge for data types
const TypeBadge = styled(Box)(({ theme, datatype }) => {
  const typeColor = getDataTypeColor(datatype, theme);

  return {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(0.5, 1.5),
    borderRadius: 50,
    backgroundColor: alpha(typeColor, theme.palette.mode === 'dark' ? 0.2 : 0.1),
    color: typeColor,
    fontSize: '0.75rem',
    fontWeight: 600,
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    boxShadow: theme.palette.mode === 'dark' ? 'none' : `0 1px 2px ${alpha(typeColor, 0.1)}`,
  };
});

// Sticky search bar container
const StickySearchContainer = styled(Box)(({ theme }) => ({
  position: 'sticky',
  top: 0,
  zIndex: 10,
  backgroundColor: alpha(theme.palette.background.paper, theme.palette.mode === 'dark' ? 0.9 : 1),
  padding: theme.spacing(2, 0),
  marginBottom: theme.spacing(2),
  borderBottom: `1px solid ${alpha(theme.palette.divider, theme.palette.mode === 'dark' ? 0.2 : 0.1)}`,
  backdropFilter: 'blur(8px)',
  boxShadow: theme.palette.mode === 'dark'
    ? `0 2px 4px ${alpha(theme.palette.common.black, 0.2)}`
    : `0 2px 4px ${alpha(theme.palette.common.black, 0.03)}`,
}));

// Get color based on data type
const getDataTypeColor = (dataType, theme) => {
  if (!dataType) return theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.main;

  const type = dataType.toLowerCase();

  // Numeric types
  if (type.includes('int') || type.includes('number') || type.includes('float') || type.includes('double') || type.includes('decimal')) {
    return theme.palette.mode === 'dark' ? theme.palette.success.light : theme.palette.success.main;
  }
  // Date/time types
  else if (type.includes('date') || type.includes('time') || type.includes('timestamp')) {
    return theme.palette.mode === 'dark' ? theme.palette.warning.light : theme.palette.warning.main;
  }
  // Boolean types
  else if (type.includes('bool')) {
    return theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main;
  }
  // String types
  else if (type.includes('char') || type.includes('text') || type.includes('string') || type.includes('varchar')) {
    return theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.main;
  }
  // JSON or object types
  else if (type.includes('json') || type.includes('object')) {
    return theme.palette.mode === 'dark' ? theme.palette.secondary.light : theme.palette.secondary.main;
  }
  // Default for other types
  else {
    return theme.palette.mode === 'dark' ? theme.palette.grey[400] : theme.palette.grey[500];
  }
};

// Status badge for already added variables
const StatusBadge = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 8,
  right: 8,
  padding: '2px 6px',
  borderRadius: 12,
  fontSize: '0.65rem',
  fontWeight: 600,
  backgroundColor: alpha(theme.palette.success.main, 0.1),
  color: theme.palette.success.main,
  border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`,
  zIndex: 1,
}));

const BucketVariableSelectionDialog = ({
  open,
  onClose,
  bucket,
  onVariablesAdded,
  bucketExistingVariables,
  onRefreshVariables,
  useDrawerMode = false
}) => {
  const theme = useTheme();
  const [tables, setTables] = useState([]);
  const [loadingTables, setLoadingTables] = useState(false);
  const [tableColumns, setTableColumns] = useState({});
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [expandedTables, setExpandedTables] = useState({});
  const [tableFilter, setTableFilter] = useState('');
  const [columnFilter, setColumnFilter] = useState('');
  const [existingBucketVariables, setExistingBucketVariables] = useState([]);
  // We don't need the map anymore as we're doing direct comparison
  const [addingVariables, setAddingVariables] = useState(false);
  const [loadingBucketVariables, setLoadingBucketVariables] = useState(false);
  const [dataSource, setDataSource] = useState(null);
  const [loadingDataSource, setLoadingDataSource] = useState(false);

  // When dialog opens, load tables and reset state
  useEffect(() => {
    if (open) {
      // Load tables when dialog opens
      loadTables();

      // Reset selections when dialog opens
      setSelectedColumns([]);

      // Load data source if bucket has one
      if (bucket && bucket.dataSourceId) {
        loadDataSource(bucket.dataSourceId);
      } else {
        setDataSource(null);
      }
    } else {
      // Reset state when dialog closes
      setSelectedColumns([]);
      setExpandedTables({});
      setDataSource(null);
      setExistingBucketVariables([]);
    }
  }, [open, bucket?.dataSourceId]);

  // Handle bucket variables loading
  useEffect(() => {
    // Only load bucket variables from props or API when dialog is open
    if (open) {
      if (bucketExistingVariables && bucketExistingVariables.length > 0) {
        // Use variables from props if available
        processExistingVariables(bucketExistingVariables);
      } else if (bucket && bucket.id) {
        // Otherwise load from API
        loadExistingBucketVariables();
      }
    }
  }, [open, bucket?.id]); // Only depend on open state and bucket ID, not the entire bucket object

  // Process existing variables
  const processExistingVariables = (variables) => {
    // Just save the array of variables
    setExistingBucketVariables(Array.isArray(variables) ? variables : []);

    // Log the variables for debugging
    console.log('Existing bucket variables:', variables);
  };

  // Load data source information
  const loadDataSource = async (dataSourceId) => {
    if (!dataSourceId) {
      setDataSource(null);
      return;
    }

    try {
      setLoadingDataSource(true);
      const source = await dataSourceService.getDataSourceById(dataSourceId);
      setDataSource(source);
    } catch (err) {
      console.error('Failed to load data source:', err);
      setDataSource(null);
    } finally {
      setLoadingDataSource(false);
    }
  };

  // Load variables that are already in the bucket
  const loadExistingBucketVariables = async () => {
    if (!bucket || !bucket.id) return;

    try {
      // Add a flag to prevent multiple calls
      if (loadingBucketVariables) return;
      setLoadingBucketVariables(true);

      const variables = await dataBucketService.getVariablesByBucketId(bucket.id);
      processExistingVariables(variables);

      // If refresh callback is provided, call it to update parent component
      if (onRefreshVariables) {
        onRefreshVariables();
      }
    } catch (err) {
      console.error('Failed to load existing bucket variables:', err);
      setExistingBucketVariables([]);
    } finally {
      setLoadingBucketVariables(false);
    }
  };

  // Check if a column is already added to the bucket using direct comparison with existing variables
  const isColumnAlreadyInBucket = (tableName, columnName) => {
    if (!existingBucketVariables || existingBucketVariables.length === 0) {
      return false;
    }

    // Check if any existing variable has the same column name (case insensitive)
    // For bucket variables, the columnName property contains the actual column name
    return existingBucketVariables.some(variable => {
      // Direct match on column name
      if (variable.columnName && variable.columnName.toLowerCase() === columnName.toLowerCase()) {
        return true;
      }

      // Fallback to name if columnName is not available
      if (variable.name && variable.name.toLowerCase() === columnName.toLowerCase()) {
        return true;
      }

      return false;
    });
  };

  // Load tables from API - if bucket has a data source, only load tables from that data source
  const loadTables = async () => {
    try {
      setLoadingTables(true);

      let tables = [];

      // If bucket has a data source, use that to get tables
      if (bucket && bucket.dataSourceId) {
        const schema = await dataSourceService.discoverSchema(bucket.dataSourceId);
        console.log('Discovered schema from data source:', schema);

        // Extract tables from schema and filter to only include the bucket's table
        if (Array.isArray(schema)) {
          tables = schema
            .filter(entity => entity.entityType === 'TABLE')
            .filter(table => !bucket.tableName || table.entityName === bucket.tableName) // Only include the bucket's table if specified
            .map(table => ({
              schemaName: table.schemaName || 'default',
              tableName: table.entityName,
              // Add other properties as needed
            }));
        }
      } else {
        // Fallback to the old method if no data source
        const response = await ruleVariableRegistryService.discoverTables();
        tables = Array.isArray(response) ? response : [];
      }

      // Initialize tables without columns - we'll load them all at once
      const processedTables = tables.map(table => ({
        ...table,
        columns: [], // Initialize with empty columns array
        columnsLoaded: false, // Flag to track if columns have been loaded
        loadingColumns: false
      }));

      setTables(processedTables);

      // Preload all columns for all tables
      if (processedTables.length > 0) {
        await loadAllTableColumns(processedTables);
      }
    } catch (err) {
      console.error('Failed to load tables:', err);
      setTables([]);
    } finally {
      setLoadingTables(false);
    }
  };

  // Load columns for all tables
  const loadAllTableColumns = async (tablesList) => {
    try {
      // Create an array of promises to load columns for all tables
      const columnPromises = tablesList.map(table =>
        loadTableColumnsForTable(table)
      );

      // Wait for all column loading promises to complete
      await Promise.all(columnPromises);
    } catch (err) {
      console.error('Error preloading table columns:', err);
    }
  };

  // Load columns for a specific table and return the columns
  const loadTableColumnsForTable = async (table) => {
    try {
      // Update the loading state for this table
      setTables(prev => {
        const updatedTables = [...prev];
        const tableIndex = updatedTables.findIndex(t =>
          t.schemaName === table.schemaName && t.tableName === table.tableName
        );

        if (tableIndex !== -1) {
          updatedTables[tableIndex] = {
            ...updatedTables[tableIndex],
            loadingColumns: true
          };
        }

        return updatedTables;
      });

      let columns = [];

      // If bucket has a data source, use that to get columns
      if (bucket && bucket.dataSourceId) {
        const schema = await dataSourceService.discoverSchema(bucket.dataSourceId);

        // Find the table in the schema
        const tableSchema = schema.find(entity =>
          entity.entityType === 'TABLE' && entity.entityName === table.tableName
        );

        if (tableSchema && tableSchema.fields) {
          columns = tableSchema.fields.map(field => ({
            name: field.name,
            type: field.type,
            // Add other properties as needed
          }));
        }
      } else {
        // Fallback to the old method if no data source
        columns = await ruleVariableRegistryService.getTableColumns(
          table.schemaName,
          table.tableName
        );
      }

      // Update the table with columns
      setTables(prev => {
        const updatedTables = [...prev];
        const tableIndex = updatedTables.findIndex(t =>
          t.schemaName === table.schemaName && t.tableName === table.tableName
        );

        if (tableIndex !== -1) {
          updatedTables[tableIndex] = {
            ...updatedTables[tableIndex],
            columns: Array.isArray(columns) ? columns : [],
            columnsLoaded: true,
            loadingColumns: false
          };
        }

        return updatedTables;
      });

      // Also update the tableColumns state for easier access
      setTableColumns(prev => ({
        ...prev,
        [`${table.schemaName}.${table.tableName}`]: columns || []
      }));

      return columns || [];
    } catch (err) {
      console.error(`Failed to load columns for table ${table.schemaName}.${table.tableName}:`, err);

      // Update the table to show error state
      setTables(prev => {
        const updatedTables = [...prev];
        const tableIndex = updatedTables.findIndex(t =>
          t.schemaName === table.schemaName && t.tableName === table.tableName
        );

        if (tableIndex !== -1) {
          updatedTables[tableIndex] = {
            ...updatedTables[tableIndex],
            columnsError: true,
            loadingColumns: false
          };
        }

        return updatedTables;
      });

      return [];
    }
  };

  const handleToggleTable = (table) => {
    const tableKey = `${table.schemaName}.${table.tableName}`;

    // Since we're preloading all columns, we don't need to load them on expand
    setExpandedTables(prev => ({
      ...prev,
      [tableKey]: !prev[tableKey]
    }));
  };

  const handleColumnToggle = (table, columnName) => {
    const columnKey = `${table.schemaName}.${table.tableName}.${columnName}`;

    // Prevent toggling if the column is already in the bucket
    if (isColumnAlreadyInBucket(table.tableName, columnName)) {
      return;
    }

    setSelectedColumns(prev => {
      if (prev.includes(columnKey)) {
        return prev.filter(key => key !== columnKey);
      } else {
        return [...prev, columnKey];
      }
    });
  };

  const handleAddVariables = async () => {
    if (!bucket || !bucket.id) return;

    // Get only the newly selected variables (not ones already in the bucket)
    const selectedVars = getSelectedVariables();

    // Don't proceed if no new variables are selected
    if (selectedVars.length === 0) {
      return;
    }

    setAddingVariables(true);

    try {
      console.log('Adding variables to bucket:', selectedVars);
      await dataBucketService.addVariablesToBucket(bucket.id, selectedVars);

      // Reset selections after successful addition
      setSelectedColumns([]);

      // Refresh bucket variables
      loadExistingBucketVariables();

      // Call the callback if provided
      if (onVariablesAdded) {
        onVariablesAdded(selectedVars);
      }

      // Close dialog if not in drawer mode
      if (!useDrawerMode && onClose) {
        onClose();
      }
    } catch (err) {
      console.error('Failed to add variables to bucket:', err);
    } finally {
      setAddingVariables(false);
    }
  };

  // Map ClickHouse data types to our system data types
  const mapClickhouseTypeToDataType = (clickhouseType) => {
    if (!clickhouseType) return 'string';

    const type = clickhouseType.toLowerCase();

    if (type.startsWith('string')) {
      return 'string';
    } else if (type.startsWith('int') || type.startsWith('uint')) {
      return 'integer';
    } else if (type.startsWith('float')) {
      return 'float';
    } else if (type.startsWith('decimal')) {
      return 'bigdecimal';
    } else if (type.startsWith('date') || type.startsWith('datetime')) {
      return 'date';
    } else if (type === 'bool') {
      return 'boolean';
    }

    // Default fallback for unknown types
    return 'string';
  };

  // Get the selected variables for display and submission
  const getSelectedVariables = () => {
    // Only include newly selected columns, not ones already in the bucket
    return selectedColumns
      .filter(columnKey => {
        const [schema, table, column] = columnKey.split('.');
        // Only include if it's not already in the bucket
        return !isColumnAlreadyInBucket(table, column);
      })
      .map(columnKey => {
        const [schema, table, column] = columnKey.split('.');

        // Find the table and column to get data type information
        const tableData = tables.find(t =>
          t.schemaName === schema && t.tableName === table
        );

        if (!tableData || !tableData.columns) {
          return {
            code: `${table.toUpperCase()}_${column.toUpperCase()}`,
            name: column,
            description: '',
            tableName: table,
            columnName: column,
            dataTypeCode: 'string',
            aggregatable: false,
            metadata: {}
          };
        }

        // Find the column in the table
        const columnData = tableData.columns.find(c => c.name === column);

        if (!columnData) {
          return {
            code: `${table.toUpperCase()}_${column.toUpperCase()}`,
            name: column,
            description: '',
            tableName: table,
            columnName: column,
            dataTypeCode: 'string',
            aggregatable: false,
            metadata: {}
          };
        }

        // Map the ClickHouse data type to our system data type
        const mappedDataType = mapClickhouseTypeToDataType(columnData.type);

        // Determine if the type is aggregatable (numeric types are typically aggregatable)
        const isAggregatable = ['integer', 'float', 'bigdecimal'].includes(mappedDataType);

        // Include the data type information and all fields expected by the backend
        return {
          code: `${table.toUpperCase()}_${column.toUpperCase()}`,
          name: column,
          description: '',
          tableName: table,
          columnName: column,
          dataTypeCode: mappedDataType,
          aggregatable: isAggregatable,
          metadata: {}
        };
      });
  };

  // Determine if a type is aggregatable
  const isAggregatableType = (type) => {
    if (!type) return false;

    const lowerType = type.toLowerCase();
    return lowerType.includes('int') ||
           lowerType.includes('float') ||
           lowerType.includes('double') ||
           lowerType.includes('decimal') ||
           lowerType.includes('numeric');
  };

  // Get the total number of columns and how many are selected for a table
  const getSelectedColumnCountForTable = (table) => {
    if (!table || !table.columns) return { selected: 0, total: 0 };

    const tableKey = `${table.schemaName}.${table.tableName}`;
    const total = table.columns.length;
    const selected = selectedColumns.filter(key => key.startsWith(tableKey)).length;

    return { selected, total };
  };

  // Get the selection status for a table (none, some, all)
  const getTableSelectionStatus = (table) => {
    const { selected, total } = getSelectedColumnCountForTable(table);

    return {
      selected,
      total,
      status: selected === 0 ? 'none' : (selected === total ? 'all' : 'some')
    };
  };

  // Filter columns based on search text
  const filteredColumns = (columns = [], filterText = '') => {
    if (!filterText.trim()) return columns;
    return columns.filter(col =>
      col.name.toLowerCase().includes(filterText.toLowerCase())
    );
  };

  // Handle removing a variable from selection
  const handleRemoveVariable = (variable) => {
    const columnKey = `${variable.schemaName}.${variable.tableName}.${variable.columnName}`;
    setSelectedColumns(prev => prev.filter(key => key !== columnKey));
  };

  // Render the selection count and chip
  const renderSelectionCount = () => {
    const selectedVars = getSelectedVariables();
    const selectedCount = selectedVars.length;

    // Don't show anything if no variables are selected
    if (selectedCount === 0) {
      return null;
    }

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
          {selectedCount === 1
            ? '1 variable selected'
            : `${selectedCount} variables selected`}
        </Typography>

        {selectedCount > 0 && selectedVars.length > 0 && (
          <Chip
            label={selectedCount === 1 && selectedVars[0] ? selectedVars[0].columnName : 'multiple'}
            size="small"
            onDelete={() => setSelectedColumns([])}
            deleteIcon={<CloseIcon fontSize="small" />}
          />
        )}
      </Box>
    );
  };

  // Render the dialog footer with action buttons
  const renderFooter = () => {
    // Get the count of newly selected variables (not already in bucket)
    const newSelectionCount = getSelectedVariables().length;

    return (
      <DialogActions sx={{
        justifyContent: 'space-between',
        px: 3,
        py: 2,
        borderTop: `1px solid ${alpha(theme.palette.divider, 0.4)}`,
        backgroundColor: theme.palette.mode === 'dark'
          ? alpha(theme.palette.background.paper, 0.2)
          : alpha(theme.palette.background.default, 0.7),
      }}>
        <Box>
          {/* Show chips for selected variables */}
          {newSelectionCount > 0 && (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', maxWidth: '400px' }}>
              {getSelectedVariables().map(variable => (
                <Box
                  key={`${variable.schemaName}.${variable.tableName}.${variable.columnName}`}
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    height: '24px',
                    px: 1,
                    py: 0.5,
                    m: 0.5,
                    borderRadius: '8px',
                    bgcolor: bucket?.bucketType === 'LOOKUP' ? '#E3F2FD' : '#FFF3E0', // Blue tint for Lookup, Orange tint for Rule
                    color: bucket?.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00', // Blue text for Lookup, Orange text for Rule
                    fontSize: '13px',
                    fontWeight: 500,
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      bgcolor: bucket?.bucketType === 'LOOKUP' ? '#BBDEFB' : '#FFE0B2',
                      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                    }
                  }}
                >
                  {variable.columnName}
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveVariable(variable)}
                    sx={{
                      ml: 0.5,
                      p: 0.2,
                      color: bucket?.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00',
                      '&:hover': {
                        bgcolor: bucket?.bucketType === 'LOOKUP' ? '#BBDEFB' : '#FFE0B2',
                      }
                    }}
                  >
                    <CloseIcon sx={{ fontSize: '14px' }} />
                  </IconButton>
                </Box>
              ))}
            </Box>
          )}
        </Box>
        <Box>
          <Button onClick={onClose} color="inherit" sx={{ mr: 1 }}>
            Cancel
          </Button>
          <Button
            onClick={handleAddVariables}
            variant="contained"
            disabled={newSelectionCount === 0 || addingVariables}
            startIcon={addingVariables ? <CircularProgress size={20} /> : null}
          >
            Add Variables
          </Button>
        </Box>
      </DialogActions>
    );
  };

  // Component content
const renderContent = () => {
  const hasTableData = bucket && bucket.tableName && tables.length > 0;
  const table = tables.find(t => t.tableName === bucket?.tableName);

  return (
    <Box height="100%" sx={{ backgroundColor: theme.palette.background.paper, overflow: 'hidden' }}>

      {/* Main Content */}
      <Box sx={{ p: 1.5, height: 'calc(100% - 200px)', display: 'flex', flexDirection: 'column', overflow: 'auto' }}>
        {loadingTables ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : hasTableData && table ? (
          <Box>
            {/* Search */}
            <StickySearchContainer>
              <TextField
                fullWidth
                placeholder="Search variables..."
                variant="outlined"
                size="small"
                value={columnFilter}
                onChange={(e) => setColumnFilter(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.background.default, 0.5),
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.background.default, 0.8),
                    },
                    '&.Mui-focused': {
                      backgroundColor: theme.palette.background.paper,
                      boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`,
                    }
                  }
                }}
              />
            </StickySearchContainer>

            {/* Selected Variables */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, fontSize: '1rem' }}>
                Selected variables
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {existingBucketVariables.map(variable => (
                  <Box
                    key={`existing-${variable.id}`}
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      height: '24px',
                      px: 1,
                      py: 0.5,
                      mr: 0.5,
                      mb: 0.5,
                      borderRadius: '8px',
                      bgcolor: bucket?.bucketType === 'LOOKUP' ? '#E3F2FD' : '#FFF3E0', // Blue tint for Lookup, Orange tint for Rule
                      color: bucket?.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00', // Blue text for Lookup, Orange text for Rule
                      fontSize: '13px',
                      fontWeight: 500,
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        bgcolor: bucket?.bucketType === 'LOOKUP' ? '#BBDEFB' : '#FFE0B2',
                        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                      }
                    }}
                  >
                    <CheckCircleIcon sx={{ fontSize: '14px', mr: 0.5, color: bucket?.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00' }} />
                    {variable.columnName}
                  </Box>
                ))}
                {selectedColumns
                  .filter(columnKey => {
                    const [_, tableName, columnName] = columnKey.split('.');
                    return !existingBucketVariables.some(v =>
                      v.tableName === tableName && v.columnName === columnName
                    );
                  })
                  .map(columnKey => {
                    const [schemaName, tableName, columnName] = columnKey.split('.');
                    return (
                      <Box
                        key={`new-${columnKey}`}
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: '24px',
                          px: 1,
                          py: 0.5,
                          mr: 0.5,
                          mb: 0.5,
                          borderRadius: '8px',
                          bgcolor: bucket?.bucketType === 'LOOKUP' ? '#E3F2FD' : '#FFF3E0', // Blue tint for Lookup, Orange tint for Rule
                          color: bucket?.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00', // Blue text for Lookup, Orange text for Rule
                          fontSize: '13px',
                          fontWeight: 500,
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            bgcolor: bucket?.bucketType === 'LOOKUP' ? '#BBDEFB' : '#FFE0B2',
                            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                          }
                        }}
                      >
                        {columnName}
                        <IconButton
                          size="small"
                          onClick={() => handleColumnToggle({ schemaName, tableName }, columnName)}
                          sx={{
                            ml: 0.5,
                            p: 0.2,
                            color: bucket?.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00',
                            '&:hover': {
                              bgcolor: bucket?.bucketType === 'LOOKUP' ? '#BBDEFB' : '#FFE0B2',
                            }
                          }}
                        >
                          <CloseIcon sx={{ fontSize: '14px' }} />
                        </IconButton>
                      </Box>
                    );
                  })}
                {existingBucketVariables.length === 0 && selectedColumns.length === 0 && (
                  <Typography variant="body2" color="text.secondary">
                    No variables selected
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Table Columns */}
            {table.loadingColumns ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                <CircularProgress size={24} />
                <Typography variant="body2" sx={{ ml: 1 }}>
                  Loading columns...
                </Typography>
              </Box>
            ) : table.columnsError ? (
              <Box sx={{ py: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="error">
                  Failed to load columns. Please try again.
                </Typography>
              </Box>
            ) : table.columns?.length > 0 ? (
              <Grid container spacing={2}>
                {filteredColumns(table.columns, columnFilter)
                  .filter(column => !isColumnAlreadyInBucket(table.tableName, column.name))
                  .map(column => {
                    const columnKey = `${table.schemaName}.${table.tableName}.${column.name}`;
                    const isSelected = selectedColumns.includes(columnKey);
                    return (
                      <Grid item key={column.name} xs={12} sm={6} md={4}>
                        <VariableCard>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.5 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
                              {column.name}
                            </Typography>
                            <Switch
                              checked={isSelected}
                              onChange={() => handleColumnToggle(table, column.name)}
                              color="primary"
                              size="small"
                            />
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <TypeBadge datatype={column.type}>{column.type}</TypeBadge>
                          </Box>
                        </VariableCard>
                      </Grid>
                    );
                  })}
              </Grid>
            ) : (
              <Box sx={{ py: 3 }}>
                <Typography variant="body2">No columns available for this table</Typography>
              </Box>
            )}
          </Box>
        ) : (
          <Box sx={{ py: 4, textAlign: 'center' }}>
            <StorageIcon sx={{ fontSize: 60, opacity: 0.3, mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>No Tables Found</Typography>
            <Typography variant="body2" sx={{ textAlign: 'center' }}>
              {tableFilter
                ? `No tables matching "${tableFilter}" were found.`
                : 'No discoverable tables are available.'}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Footer */}
      {renderFooter()}
    </Box>
  );
};



  // Filter tables based on the search filter
  const filteredTables = tables.filter(
    table =>
      !tableFilter ||
      table.tableName.toLowerCase().includes(tableFilter.toLowerCase()) ||
      table.schemaName.toLowerCase().includes(tableFilter.toLowerCase())
  );

  // Return just the content if using drawer mode, otherwise wrap in Dialog
  if (useDrawerMode) {
    // In drawer mode, we still need to respect the open prop
    if (!open) {
      return null;
    }
    return renderContent();
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
    >
      {renderContent()}
    </Dialog>
  );
};

export default BucketVariableSelectionDialog;
