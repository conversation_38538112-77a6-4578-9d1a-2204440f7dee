package com.progize.tms.service;

import com.progize.tms.repository.entity.CaseComment;
import com.progize.tms.repository.entity.WorkflowCase;
import java.util.List;

/** Service for managing case comments. */
public interface CaseCommentService {

  /**
   * Add a comment to a workflow case.
   *
   * @param workflowCase the workflow case
   * @param processInstanceId the process instance ID
   * @param taskId the task ID
   * @param commentText the comment text
   * @param commentType the comment type (SYSTEM, USER, DECISION)
   * @param createdBy the user ID who created the comment
   * @return the created comment
   */
  CaseComment addComment(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      String commentText,
      String commentType,
      Long createdBy);

  /**
   * Get all comments for a workflow case.
   *
   * @param workflowCase the workflow case
   * @return list of comments
   */
  List<CaseComment> getCommentsByCase(WorkflowCase workflowCase);

  /**
   * Get all comments for a task.
   *
   * @param taskId the task ID
   * @return list of comments
   */
  List<CaseComment> getCommentsByTask(String taskId);

  /**
   * Get all comments for a process instance.
   *
   * @param processInstanceId the process instance ID
   * @return list of comments
   */
  List<CaseComment> getCommentsByProcessInstance(String processInstanceId);
}
