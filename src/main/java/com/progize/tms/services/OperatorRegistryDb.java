package com.progize.tms.services;

import com.progize.tms.repository.DataTypeRepository;
import com.progize.tms.repository.DataTypeSupportedOperatorRepository;
import com.progize.tms.repository.OperatorExpressionRepository;
import com.progize.tms.repository.OperatorRepository;
import com.progize.tms.repository.entity.DataType;
import com.progize.tms.repository.entity.Operator;
import com.progize.tms.repository.entity.OperatorExpression;
import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Database-backed implementation of operator registry. This replaces the hard-coded implementation
 * with a dynamic one that loads operators, expressions, and supported operators from the database.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperatorRegistryDb {
  // Constants to maintain API compatibility with the original OperatorRegistry
  public static final String EQUALS = "equals";
  public static final String NOT_EQUALS = "notEquals";
  public static final String GREATER_THAN = "greaterThan";
  public static final String LESS_THAN = "lessThan";
  public static final String GREATER_THAN_EQUALS = "greaterThanEquals";
  public static final String LESS_THAN_EQUALS = "lessThanEquals";
  public static final String BETWEEN = "between";
  public static final String NOT_BETWEEN = "notBetween";
  public static final String CONTAINS = "contains";
  public static final String STARTS_WITH = "startsWith";
  public static final String ENDS_WITH = "endsWith";
  public static final String MATCHES = "matches";
  public static final String IS_EMPTY = "isEmpty";
  public static final String IS_NOT_EMPTY = "isNotEmpty";
  public static final String IS_NULL = "isNull";
  public static final String IS_NOT_NULL = "isNotNull";

  private final OperatorRepository operatorRepository;
  private final DataTypeRepository dataTypeRepository;
  private final OperatorExpressionRepository operatorExpressionRepository;
  private final DataTypeSupportedOperatorRepository dataTypeSupportedOperatorRepository;

  // Cache for performance
  private final Map<String, Map<String, String>> typeSpecificExpressionsCache =
      new ConcurrentHashMap<>();
  private final Map<String, String> operatorExpressionsCache = new ConcurrentHashMap<>();
  private final Map<String, String> reverseOperatorMapCache = new ConcurrentHashMap<>();
  private final Map<String, List<String>> supportedOperatorsCache = new ConcurrentHashMap<>();

  // Cache for operator and datatype lookup
  private final Map<String, Long> operatorCodeToIdMap = new ConcurrentHashMap<>();
  private final Map<String, Long> dataTypeCodeToIdMap = new ConcurrentHashMap<>();

  // Track last cache refresh time
  private LocalDateTime lastCacheRefresh = LocalDateTime.now().minusDays(1);

  @PostConstruct
  public void initCache() {
    log.info("Initializing OperatorRegistryDb cache");
    refreshCaches();
  }

  @Scheduled(fixedRate = 300000) // Refresh every 5 minutes
  public void refreshCaches() {
    log.debug("Refreshing OperatorRegistryDb caches");
    try {
      // Only attempt to check for updates if we've done a full refresh before
      doFullCacheRefresh();
    } catch (Exception e) {
      log.error("Error refreshing operator registry caches", e);
    }
  }

  private void doFullCacheRefresh() {
    log.info("Performing full cache refresh for OperatorRegistryDb");

    // Clear caches
    typeSpecificExpressionsCache.clear();
    operatorExpressionsCache.clear();
    reverseOperatorMapCache.clear();
    supportedOperatorsCache.clear();
    operatorCodeToIdMap.clear();
    dataTypeCodeToIdMap.clear();

    // Load and cache operator codes -> ids
    List<Operator> operators = operatorRepository.findAll();
    for (Operator operator : operators) {
      operatorCodeToIdMap.put(operator.getCode(), operator.getId());
    }

    // Load and cache data type codes -> ids
    List<DataType> dataTypes = dataTypeRepository.findAll();
    for (DataType dataType : dataTypes) {
      dataTypeCodeToIdMap.put(dataType.getCode(), dataType.getId());
    }

    // Load standard expressions (no data type specific)
    List<OperatorExpression> standardExpressions =
        operatorExpressionRepository.findGenericExpressions();
    for (OperatorExpression expr : standardExpressions) {
      String operatorCode = expr.getOperator().getCode();
      operatorExpressionsCache.put(operatorCode, expr.getExpressionTemplate());
      reverseOperatorMapCache.put(expr.getExpressionTemplate(), operatorCode);
    }

    // Load type-specific expressions
    for (DataType dataType : dataTypes) {
      String dataTypeCode = dataType.getCode();
      List<OperatorExpression> typeExpressions =
          operatorExpressionRepository.findByDataTypeCode(dataTypeCode);

      if (!typeExpressions.isEmpty()) {
        Map<String, String> expressionsMap = new HashMap<>();
        for (OperatorExpression expr : typeExpressions) {
          expressionsMap.put(expr.getOperator().getCode(), expr.getExpressionTemplate());
        }
        typeSpecificExpressionsCache.put(dataTypeCode, expressionsMap);
      }
    }

    // Load supported operators by data type
    for (DataType dataType : dataTypes) {
      String dataTypeCode = dataType.getCode();
      List<String> operatorCodes =
          dataTypeSupportedOperatorRepository.findOperatorCodesByDataTypeCode(dataTypeCode);
      if (!operatorCodes.isEmpty()) {
        supportedOperatorsCache.put(dataTypeCode, operatorCodes);
      }
    }
  }

  /** Get the MVEL expression for a given operator and field type */
  public String getOperatorExpression(String operatorId, String fieldType) {
    // First check type-specific expressions
    if (fieldType != null && typeSpecificExpressionsCache.containsKey(fieldType)) {
      Map<String, String> typeExprs = typeSpecificExpressionsCache.get(fieldType);
      if (typeExprs.containsKey(operatorId)) {
        return typeExprs.get(operatorId);
      }
    }

    // Fall back to standard expressions
    return operatorExpressionsCache.getOrDefault(operatorId, operatorId);
  }

  /** Get the operator ID for a given MVEL expression (reverse lookup) */
  public String getOperatorIdForExpression(String expression) {
    return reverseOperatorMapCache.getOrDefault(expression, expression);
  }

  /** Get the default set of supported operators for a given field type */
  public List<String> getDefaultSupportedOperators(String fieldType) {
    // Check cache first
    if (supportedOperatorsCache.containsKey(fieldType)) {
      return supportedOperatorsCache.get(fieldType);
    }

    // If not found in cache, query from DB and cache the result
    try {
      List<String> operatorCodes =
          dataTypeSupportedOperatorRepository.findOperatorCodesByDataTypeCode(fieldType);
      if (!operatorCodes.isEmpty()) {
        supportedOperatorsCache.put(fieldType, operatorCodes);
        return operatorCodes;
      }
    } catch (Exception e) {
      log.error("Error retrieving supported operators for data type: " + fieldType, e);
    }

    // Fallback to default behavior (empty list if no match)
    return Collections.emptyList();
  }

  /** Get all operator codes */
  public Set<String> getAllOperatorCodes() {
    return operatorCodeToIdMap.keySet();
  }

  /** Get all data type codes */
  public Set<String> getAllDataTypeCodes() {
    return dataTypeCodeToIdMap.keySet();
  }

  /** Get the operator ID by code */
  public Long getOperatorIdByCode(String code) {
    return operatorCodeToIdMap.get(code);
  }

  /** Get the data type ID by code */
  public Long getDataTypeIdByCode(String code) {
    return dataTypeCodeToIdMap.get(code);
  }
}
