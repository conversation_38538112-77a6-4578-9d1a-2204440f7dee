<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://www.flowable.org/processdef">

    <process id="complianceReviewProcess" name="Compliance Case Review Process">

        <!-- Start Event -->
        <startEvent id="startEvent" name="Compliance Case Started"/>

        <!-- Compliance Officer Review -->
        <userTask id="complianceOfficerReview" name="Compliance Officer Review"
                  flowable:candidateGroups="compliance_officer">
            <extensionElements>
                <flowable:formProperty id="officerDecision" name="Decision" type="enum" required="true">
                    <flowable:value id="false_positive" name="Mark as False Positive"/>
                    <flowable:value id="positive" name="Mark as Positive"/>
                </flowable:formProperty>
                <flowable:formProperty id="officerComments" name="Comments" type="string" required="true"/>
            </extensionElements>
        </userTask>

        <!-- Gateway after Compliance Officer Review -->
        <exclusiveGateway id="officerDecisionGateway" name="Officer Decision"/>

        <!-- Compliance Manager Review -->
        <userTask id="complianceManagerReview" name="Compliance Manager Review"
                 flowable:candidateGroups="compliance_manager">
            <extensionElements>
                <flowable:formProperty id="managerDecision" name="Decision" type="enum" required="true">
                    <flowable:value id="close_as_false_positive" name="Close Case (False Positive)"/>
                    <flowable:value id="escalate_to_ir" name="Escalate to Investor Relations"/>
                    <flowable:value id="return_to_officer" name="Return to Officer"/>
                </flowable:formProperty>
                <flowable:formProperty id="managerComments" name="Comments" type="string" required="true"/>
            </extensionElements>
        </userTask>

        <!-- Gateway after Manager Review -->
        <exclusiveGateway id="managerDecisionGateway" name="Manager Decision"/>

        <!-- Investor Relations Review -->
        <userTask id="investorRelationsReview" name="Investor Relations Review"
                  flowable:candidateGroups="investor_relation">
            <extensionElements>
                <flowable:formProperty id="irDecision" name="Decision" type="enum" required="true">
                    <flowable:value id="return_to_manager" name="Return to Manager"/>
                </flowable:formProperty>
                <flowable:formProperty id="irComments" name="Comments" type="string" required="true"/>
            </extensionElements>
        </userTask>

        <!-- Gateway after Investor Relations Review -->
        <exclusiveGateway id="irDecisionGateway" name="Investor Relations Decision"/>

        <!-- End Event -->
        <endEvent id="caseClosedEnd" name="Case Closed"/>

        <!-- Sequence Flows -->
        <sequenceFlow id="flowStartToOfficer" sourceRef="startEvent" targetRef="complianceOfficerReview"/>
        <sequenceFlow id="flowOfficerToGateway" sourceRef="complianceOfficerReview" targetRef="officerDecisionGateway"/>

        <sequenceFlow id="flowOfficerFalsePositiveToManager" sourceRef="officerDecisionGateway" targetRef="complianceManagerReview">
            <conditionExpression xsi:type="tFormalExpression">${officerDecision == 'false_positive'}</conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flowOfficerPositiveToManager" sourceRef="officerDecisionGateway" targetRef="complianceManagerReview">
            <conditionExpression xsi:type="tFormalExpression">${officerDecision == 'positive'}</conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flowManagerToGateway" sourceRef="complianceManagerReview" targetRef="managerDecisionGateway"/>

        <sequenceFlow id="flowManagerClose" sourceRef="managerDecisionGateway" targetRef="caseClosedEnd">
            <conditionExpression xsi:type="tFormalExpression">${managerDecision == 'close_as_false_positive'}</conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flowManagerReturnToOfficer" sourceRef="managerDecisionGateway" targetRef="complianceOfficerReview">
            <conditionExpression xsi:type="tFormalExpression">${managerDecision == 'return_to_officer'}</conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flowManagerEscalateToIR" sourceRef="managerDecisionGateway" targetRef="investorRelationsReview">
            <conditionExpression xsi:type="tFormalExpression">${managerDecision == 'escalate_to_ir'}</conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="flowIRToGateway" sourceRef="investorRelationsReview" targetRef="irDecisionGateway"/>
        <sequenceFlow id="flowIRReturnToManager" sourceRef="irDecisionGateway" targetRef="complianceManagerReview"/>

    </process>
</definitions>
