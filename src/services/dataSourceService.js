import api from './api';

/**
 * Service for interacting with the Data Source API
 */
const dataSourceService = {
  /**
   * Get all data sources
   * @returns {Promise<Array>} List of data sources
   */
  getAllDataSources: async () => {
    try {
      const response = await api.get('/api/data-sources');
      return response.data;
    } catch (error) {
      console.error('Error fetching data sources:', error);
      throw error;
    }
  },

  /**
   * Get a data source by ID
   * @param {number} id - Data source ID
   * @returns {Promise<Object>} Data source details
   */
  getDataSourceById: async (id) => {
    try {
      const response = await api.get(`/api/data-sources/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching data source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get data source configurations for UI form
   * @returns {Promise<Array>} List of data source configurations
   */
  getDataSourceConfigurations: async () => {
    try {
      const response = await api.get('/api/data-sources/configurations');
      return response.data;
    } catch (error) {
      console.error('Error fetching data source configurations:', error);
      throw error;
    }
  },

  /**
   * Create a database data source
   * @param {Object} data - Database source data
   * @returns {Promise<Object>} Created data source
   */
  createDatabaseSource: async (data) => {
    try {
      const response = await api.post('/api/data-sources/database', data);
      return response.data;
    } catch (error) {
      console.error('Error creating database source:', error);
      throw error;
    }
  },

  /**
   * Create a REST API data source
   * @param {Object} data - REST API source data
   * @returns {Promise<Object>} Created data source
   */
  createRestApiSource: async (data) => {
    try {
      const response = await api.post('/api/data-sources/rest-api', data);
      return response.data;
    } catch (error) {
      console.error('Error creating REST API source:', error);
      throw error;
    }
  },

  /**
   * Update a database data source
   * @param {number} id - Data source ID
   * @param {Object} data - Updated database source data
   * @returns {Promise<Object>} Updated data source
   */
  updateDatabaseSource: async (id, data) => {
    try {
      const response = await api.put(`/api/data-sources/database/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating database source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update a REST API data source
   * @param {number} id - Data source ID
   * @param {Object} data - Updated REST API source data
   * @returns {Promise<Object>} Updated data source
   */
  updateRestApiSource: async (id, data) => {
    try {
      const response = await api.put(`/api/data-sources/rest-api/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating REST API source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a data source
   * @param {number} id - Data source ID
   * @returns {Promise<void>}
   */
  deleteDataSource: async (id) => {
    try {
      await api.delete(`/api/data-sources/${id}`);
    } catch (error) {
      console.error(`Error deleting data source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Test connection to a data source
   * @param {number} id - Data source ID
   * @returns {Promise<Object>} Connection test result
   */
  testConnection: async (id) => {
    try {
      const response = await api.get(`/api/data-sources/${id}/test-connection`);
      return response.data;
    } catch (error) {
      console.error(`Error testing connection to data source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Discover schema from a data source
   * @param {number} id - Data source ID
   * @returns {Promise<Array>} Discovered schema
   */
  discoverSchema: async (id) => {
    try {
      const response = await api.get(`/api/data-sources/${id}/schema`);
      return response.data;
    } catch (error) {
      console.error(`Error discovering schema from data source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Execute a query against a data source
   * @param {number} id - Data source ID
   * @param {string} entityName - Name of the entity to query
   * @param {Object} parameters - Query parameters
   * @returns {Promise<Array>} Query results
   */
  executeQuery: async (id, entityName, parameters = {}) => {
    try {
      const response = await api.get(`/api/data-sources/${id}/query`, {
        params: {
          entityName,
          ...parameters
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error executing query against data source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get data source types
   * @returns {Promise<Array>} List of data source types
   */
  getDataSourceTypes: async () => {
    try {
      const response = await api.get('/api/data-sources/types');
      return response.data;
    } catch (error) {
      console.error('Error fetching data source types:', error);
      throw error;
    }
  }
};

export default dataSourceService;
