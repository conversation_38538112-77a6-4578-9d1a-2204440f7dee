package com.progize.tms.security;

import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtRequestFilter extends OncePerRequestFilter {

  private final JwtTokenUtil jwtTokenUtil;

  @Override
  protected void doFilterInternal(
      @NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response,
      @NonNull FilterChain chain)
      throws ServletException, IOException {

    final String requestTokenHeader = request.getHeader("Authorization");

    String username = null;
    String jwtToken = null;

    if (requestTokenHeader != null && requestTokenHeader.startsWith("Bearer ")) {
      jwtToken = requestTokenHeader.substring(7);
      try {
        username = jwtTokenUtil.getUsernameFromToken(jwtToken);
      } catch (IllegalArgumentException e) {
        log.error("Unable to get JWT Token");
      } catch (Exception e) {
        log.error("JWT Token has expired or is invalid");
      }
    } else {
      log.debug("JWT Token does not begin with Bearer String or is missing");
    }

    if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
      // Get claims from token
      Claims claims = jwtTokenUtil.getAllClaimsFromToken(jwtToken);

      // Extract roles from token claims
      @SuppressWarnings("unchecked")
      List<String> roles = claims.get("roles", List.class);

      // Convert roles to authorities
      List<SimpleGrantedAuthority> authorities =
          roles.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toList());

      log.debug("Token roles for user {}: {}", username, roles);

      // Create UserDetails with roles from token
      UserDetails userDetails =
          User.withUsername(username)
              .password("") // Password not needed for token auth
              .authorities(authorities)
              .build();

      if (jwtTokenUtil.validateToken(jwtToken, userDetails)) {
        log.debug("Valid token for user {} with authorities: {}", username, authorities);

        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userDetails, null, authorities);

        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

        SecurityContextHolder.getContext().setAuthentication(authentication);
      } else {
        log.warn("Token validation failed for user {}", username);
      }
    }
    chain.doFilter(request, response);
  }
}
