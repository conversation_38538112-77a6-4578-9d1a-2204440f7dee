package com.progize.tms.controllers.rulevariables.res;

import java.util.List;
import java.util.Map;
import lombok.Data;

/** Response DTO for rule variables. */
@Data
public class RuleVariableResponse {
  private String id; // Variable code (used in rules)
  private Long entityId; // Database entity ID
  private String name; // Display name
  private String description; // Description
  private String type; // Data type code (string, integer, date, etc.)
  private String category; // Category (source table name)
  private boolean aggregatable; // Whether variable can be used in aggregations
  private List<String> supportedOperators; // Supported operators for this variable
  private String columnName; // Source column name
  private String tableName; // Source table name
  private Map<String, Object> metadata; // Additional metadata
}
