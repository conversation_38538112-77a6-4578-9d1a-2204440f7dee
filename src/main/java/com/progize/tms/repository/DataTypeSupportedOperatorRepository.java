package com.progize.tms.repository;

import com.progize.tms.repository.entity.DataTypeSupportedOperator;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DataTypeSupportedOperatorRepository
    extends JpaRepository<DataTypeSupportedOperator, Long> {

  @Query(
      "SELECT dto FROM DataTypeSupportedOperator dto WHERE dto.dataType.id = :dataTypeId ORDER BY dto.displayOrder")
  List<DataTypeSupportedOperator> findByDataTypeIdOrderByDisplayOrder(
      @Param("dataTypeId") Long dataTypeId);

  @Query(
      "SELECT dto FROM DataTypeSupportedOperator dto WHERE dto.dataType.code = :dataTypeCode ORDER BY dto.displayOrder")
  List<DataTypeSupportedOperator> findByDataTypeCodeOrderByDisplayOrder(
      @Param("dataTypeCode") String dataTypeCode);

  @Query(
      "SELECT dto.operator.code FROM DataTypeSupportedOperator dto WHERE dto.dataType.code = :dataTypeCode ORDER BY dto.displayOrder")
  List<String> findOperatorCodesByDataTypeCode(@Param("dataTypeCode") String dataTypeCode);
}
