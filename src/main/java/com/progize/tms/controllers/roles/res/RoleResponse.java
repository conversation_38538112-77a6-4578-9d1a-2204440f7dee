package com.progize.tms.controllers.roles.res;

import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** DTO for role response. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleResponse {

  private Long id;
  private String name;
  private String description;

  @Builder.Default private Set<PermissionDto> permissions = new HashSet<>();

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class PermissionDto {
    private Long id;
    private String name;
    private String description;
  }
}
