package com.progize.tms.services;

import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.services.models.AggregateCondition;
import com.progize.tms.services.models.AggregationRequirement;
import com.progize.tms.services.models.ComparisonCondition;
import com.progize.tms.services.models.CompositeCondition;
import com.progize.tms.services.models.Condition;
import com.progize.tms.services.models.RuleVariable;
import com.progize.tms.services.models.SimpleCondition;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RuleExpressionBuilder {

  private static final Logger log = LoggerFactory.getLogger(RuleExpressionBuilder.class);

  private final RuleVariableService ruleVariableService;
  private final OperatorRegistry operatorRegistry;
  private final DataBucketService dataBucketService;

  /**
   * Builds an MVEL expression from a rule condition. Uses global rule variables from
   * RuleVariableService.
   *
   * @param condition The condition to build an expression for
   * @return The MVEL expression
   */
  public String buildMvelExpression(Condition condition) {
    return buildMvelExpression(condition, null);
  }

  /**
   * Builds an MVEL expression from a rule condition using bucket-specific variables if a bucketId
   * is provided.
   *
   * @param condition The condition to build an expression for
   * @param bucketId The ID of the data bucket, or null to use global variables
   * @return The MVEL expression
   */
  public String buildMvelExpression(Condition condition, Long bucketId) {
    if (condition == null) return "";

    return switch (condition.getType()) {
      case "SIMPLE" -> buildSimpleExpression((SimpleCondition) condition, bucketId);
      case "AND" -> buildLogicalExpression((CompositeCondition) condition, "&&", bucketId);
      case "OR" -> buildLogicalExpression((CompositeCondition) condition, "||", bucketId);
      case "NOT" -> buildNotExpression((CompositeCondition) condition, bucketId);
      case "AGGREGATE" -> buildAggregateConditionExpression(
          (AggregateCondition) condition, bucketId);
      case "COMPARISON" -> buildComparisonExpression((ComparisonCondition) condition, bucketId);
      default -> {
        if (condition.getType() != null && condition.getType().startsWith("AGGREGATE_")) {
          yield buildAggregateExpression((SimpleCondition) condition, bucketId);
        }
        throw new IllegalArgumentException("Unsupported condition type: " + condition.getType());
      }
    };
  }

  private String buildNotExpression(CompositeCondition condition, Long bucketId) {
    return (condition.getConditions() != null && !condition.getConditions().isEmpty())
        ? "!(" + buildMvelExpression(condition.getConditions().get(0), bucketId) + ")"
        : "";
  }

  private String buildSimpleExpression(SimpleCondition condition, Long bucketId) {
    String operatorId = condition.getOperator();
    String field = condition.getField();
    Object value = condition.getValue();
    boolean isExpression =
        Boolean.TRUE.equals(condition.getIsExpression())
            && value instanceof String
            && ((String) value).contains("${");

    // Get variable information based on whether a bucket ID is provided
    String fieldType;

    if (bucketId != null) {
      // Use bucket-specific variables - field is already the name
      DataBucketVariableEntity var = getBucketVariableByName(field, bucketId);
      fieldType = (var != null && var.getDataType() != null) ? var.getDataType().getCode() : null;
    } else {
      // Fallback to global variables
      RuleVariable var = getRuleVariableForField(field);
      fieldType = (var != null) ? var.getType() : null;
    }

    if (OperatorRegistry.IS_NULL.equals(operatorId)) return field + " == null";
    if (OperatorRegistry.IS_NOT_NULL.equals(operatorId)) return field + " != null";

    String opPattern = mapOperator(operatorId, fieldType, bucketId);

    if (opPattern.contains("%s")) {
      return handleFormattedOperator(opPattern, isExpression, field, value, fieldType);
    }

    return handleUnformattedOperator(operatorId, field, value, fieldType, opPattern, isExpression);
  }

  private String handleFormattedOperator(
      String pattern, boolean isExpression, String fieldName, Object value, String fieldType) {
    StringBuilder expression = new StringBuilder(fieldName);

    if (pattern.contains("&&")
        && pattern.indexOf("%s", pattern.indexOf("%s") + 2) > 0
        && value instanceof Collection<?> col
        && col.size() >= 2) {
      Iterator<?> it = col.iterator();
      expression.append(
          String.format(
              pattern, formatValue(it.next(), fieldType), formatValue(it.next(), fieldType)));
    } else {
      String formattedValue =
          isExpression ? processExpressionValue((String) value) : formatValue(value, fieldType);
      expression
          .append(operatorRegistry.isPrimitiveNumeric(fieldType) ? " " : "")
          .append(String.format(pattern, formattedValue));
    }
    return expression.toString();
  }

  private String handleUnformattedOperator(
      String operatorId,
      String fieldName,
      Object value,
      String fieldType,
      String opPattern,
      boolean isExpression) {
    StringBuilder expression = new StringBuilder(fieldName);

    if (Set.of(OperatorRegistry.CONTAINS, OperatorRegistry.STARTS_WITH, OperatorRegistry.ENDS_WITH)
        .contains(operatorId)) {
      return expression
          .append(opPattern)
          .append("(")
          .append(
              isExpression ? processExpressionValue((String) value) : formatValue(value, fieldType))
          .append(")")
          .toString();
    }

    if (Set.of(OperatorRegistry.IN, OperatorRegistry.NOT_IN).contains(operatorId)) {
      return expression
          .append(opPattern)
          .append(
              value instanceof Collection
                  ? formatValue(value, fieldType)
                  : "["
                      + (isExpression
                          ? processExpressionValue((String) value)
                          : formatValue(value, fieldType))
                      + "]")
          .toString();
    }

    return expression
        .append(" ")
        .append(opPattern)
        .append(" ")
        .append(
            isExpression ? processExpressionValue((String) value) : formatValue(value, fieldType))
        .toString();
  }

  private String buildLogicalExpression(
      CompositeCondition condition, String operator, Long bucketId) {
    if (condition.getConditions() == null || condition.getConditions().isEmpty()) return "";
    return condition.getConditions().stream()
        .map(c -> buildMvelExpression(c, bucketId))
        .filter(s -> !s.isEmpty())
        .collect(Collectors.joining(" " + operator + " ", "(", ")"));
  }

  private String buildAggregateConditionExpression(AggregateCondition condition, Long bucketId) {
    String field = condition.getField();

    // No need to look up the field name since it's already the name from the frontend

    Integer startOffset = condition.getStartOffset() != null ? condition.getStartOffset() : 0;
    AggregationRequirement requirement =
        new AggregationRequirement(
            condition.getFunction(),
            field,
            condition.getPeriod(),
            startOffset,
            condition.getEntityField(),
            condition.getFilterCondition());

    String aggregateKey = requirement.getAggregationKey();
    String operator = mapOperator(condition.getOperator(), "double", bucketId);
    String threshold =
        Boolean.TRUE.equals(condition.getIsExpression())
                && condition.getThreshold() instanceof String
            ? processExpressionValue((String) condition.getThreshold())
            : formatValue(condition.getThreshold(), "number");

    return operator.contains("%s")
        ? "aggregates['" + aggregateKey + "'] " + String.format(operator, threshold)
        : "aggregates['" + aggregateKey + "'] " + operator + " " + threshold;
  }

  private String buildAggregateExpression(SimpleCondition condition, Long bucketId) {
    String field = condition.getField();

    // No need to look up the field name since it's already the name from the frontend

    String operator = condition.getOperator().replaceFirst("AGGREGATE_", "");
    String expression =
        switch (operator) {
          case "COUNT" -> field + ".size()";
          case "SUM" -> field + ".sum()";
          case "AVG" -> field + ".average()";
          case "MIN" -> field + ".min()";
          case "MAX" -> field + ".max()";
          default -> throw new IllegalArgumentException(
              "Unsupported aggregate operator: " + operator);
        };

    if (condition.getValue() != null) {
      expression += " == " + formatValue(condition.getValue(), "number");
    }

    return expression;
  }

  /**
   * Builds an MVEL expression for a comparison condition. This handles comparisons between two
   * conditions, typically used for temporal comparisons like "if the average transaction amount in
   * the last 7 days is 2x higher than the 7 days before that".
   *
   * @param condition The comparison condition
   * @param bucketId The bucket ID for context, or null for global variables
   * @return The MVEL expression for the comparison
   */
  /**
   * Builds an MVEL expression for an aggregate condition that is part of a comparison condition.
   * This is different from regular aggregate expression building because we only need the raw
   * aggregation value, not a comparison against a threshold.
   *
   * @param condition The aggregate condition
   * @param bucketId The bucket ID for context, or null for global variables
   * @return The MVEL expression for the aggregate value
   */
  private String buildAggregateInComparisonExpression(AggregateCondition condition, Long bucketId) {
    log.debug("Building aggregate in comparison expression for condition: {}", condition);
    String field = condition.getField();

    log.debug(
        "Aggregate condition details: function={}, field={}, period={}, startOffset={}, entityField={}",
        condition.getFunction(),
        field,
        condition.getPeriod(),
        condition.getStartOffset(),
        condition.getEntityField());

    // Create an aggregation requirement
    Integer startOffset = condition.getStartOffset() != null ? condition.getStartOffset() : 0;
    AggregationRequirement requirement =
        new AggregationRequirement(
            condition.getFunction(),
            field,
            condition.getPeriod(),
            startOffset,
            condition.getEntityField(),
            condition.getFilterCondition());

    // Get the aggregation key
    String aggregateKey = requirement.getAggregationKey();
    log.debug("Generated aggregation key: {}", aggregateKey);

    // Return just the aggregation value reference, not a comparison
    String expression = "aggregates[\"" + aggregateKey + "\"]";
    log.debug("Final aggregate expression: {}", expression);
    return expression;
  }

  private String buildComparisonExpression(ComparisonCondition condition, Long bucketId) {
    log.debug("Building comparison expression for condition: {}", condition);

    // Build expressions for left and right sides
    String leftExpression;
    String rightExpression;

    // Special handling for AggregateConditions inside ComparisonConditions
    if (condition.getLeft() instanceof AggregateCondition) {
      log.debug("Left side is an AggregateCondition: {}", condition.getLeft());
      leftExpression =
          buildAggregateInComparisonExpression((AggregateCondition) condition.getLeft(), bucketId);
    } else {
      log.debug("Left side is a regular condition: {}", condition.getLeft());
      leftExpression = buildMvelExpression(condition.getLeft(), bucketId);
    }

    if (condition.getRight() instanceof AggregateCondition) {
      log.debug("Right side is an AggregateCondition: {}", condition.getRight());
      rightExpression =
          buildAggregateInComparisonExpression((AggregateCondition) condition.getRight(), bucketId);
    } else {
      log.debug("Right side is a regular condition: {}", condition.getRight());
      rightExpression = buildMvelExpression(condition.getRight(), bucketId);
    }

    // Apply multiplier to right side if present
    if (condition.getMultiplier() != null && condition.getMultiplier() != 1.0) {
      log.debug("Applying multiplier: {} to right side", condition.getMultiplier());
      rightExpression = "(" + rightExpression + " * " + condition.getMultiplier() + ")";
    }

    // Combine with operator
    String finalExpression = leftExpression + " " + condition.getOperator() + " " + rightExpression;
    log.debug("Final comparison expression: {}", finalExpression);
    return finalExpression;
  }

  private String mapOperator(String operatorId, String fieldType, Long bucketId) {
    // First try to get the operator expression from the OperatorRegistry
    String operatorExpression = operatorRegistry.getOperatorExpression(operatorId, fieldType);
    if (operatorExpression != null) {
      return operatorExpression;
    }

    // If not found and we have a bucket ID, try to get supported operators for the bucket
    if (bucketId != null) {
      try {
        Map<DataBucketVariableEntity, List<String>> variablesWithOperators =
            dataBucketService.getVariablesWithSupportedOperators(bucketId);

        // Find the operator in the supported operators
        for (Map.Entry<DataBucketVariableEntity, List<String>> entry :
            variablesWithOperators.entrySet()) {
          if (entry.getValue().contains(operatorId)) {
            return operatorId; // Return the operator ID as a fallback
          }
        }
      } catch (Exception e) {
        log.warn("Error getting supported operators for bucket {}: {}", bucketId, e.getMessage());
      }
    }

    // Fallback to RuleVariableService
    return ruleVariableService.getAllOperators().stream()
        .filter(op -> op.getId().equals(operatorId))
        .map(op -> op.getSymbol())
        .findFirst()
        .orElse(operatorId);
  }

  private RuleVariable getRuleVariableForField(String field) {
    try {
      return ruleVariableService.getVariable(field);
    } catch (Exception e) {
      log.warn("Error getting variable {}: {}", field, e.getMessage());
      return null;
    }
  }

  private DataBucketVariableEntity getBucketVariableByName(String fieldName, Long bucketId) {
    if (bucketId == null) return null;

    try {
      // Use the method that includes enriched variables from related lookup buckets
      List<DataBucketVariableEntity> variables =
          dataBucketService.getVariablesWithEnrichment(bucketId, true);

      // First try to find the variable in the primary bucket
      DataBucketVariableEntity primaryVariable =
          variables.stream()
              .filter(
                  var ->
                      var.getName().equals(fieldName)
                          && var.getDataBucket() != null
                          && var.getDataBucket().getId().equals(bucketId))
              .findFirst()
              .orElse(null);

      // If found in primary bucket, return it
      if (primaryVariable != null) {
        return primaryVariable;
      }

      // Otherwise, return the first matching variable from any bucket
      return variables.stream()
          .filter(var -> var.getName().equals(fieldName))
          .findFirst()
          .orElse(null);
    } catch (Exception e) {
      log.warn(
          "Error getting variable with name {} from bucket {}: {}",
          fieldName,
          bucketId,
          e.getMessage());
      return null;
    }
  }

  private String formatValue(Object value, String fieldType) {
    if (value == null) return "null";
    return switch (fieldType) {
      case "bigdecimal" -> formatBigDecimalValue(value);
      case "number", "double", "integer", "float" -> value.toString();
      case "date" -> formatDateValue(value);
      default -> formatDefaultValue(value, fieldType);
    };
  }

  private String formatBigDecimalValue(Object value) {
    return "new java.math.BigDecimal(\"" + value + "\")";
  }

  private String formatDateValue(Object value) {
    if (value instanceof Date date) return "new java.util.Date(" + date.getTime() + "L)";
    if (value instanceof String str) {
      try {
        Date date = new SimpleDateFormat("yyyy-MM-dd").parse(str);
        return "new java.util.Date(" + date.getTime() + "L)";
      } catch (ParseException e) {
        log.warn("Failed to parse date string: {}", value);
        return "\"" + value + "\"";
      }
    }
    return "\"" + value + "\"";
  }

  private String formatDefaultValue(Object value, String fieldType) {
    if (value instanceof String) return "\"" + value + "\"";
    if (value instanceof Collection<?>)
      return ((Collection<?>) value)
          .stream().map(v -> formatValue(v, fieldType)).collect(Collectors.joining(", ", "[", "]"));
    return value.toString();
  }

  private String processExpressionValue(String value) {
    String result = value;
    int start = result.indexOf("${");

    while (start >= 0) {
      int end = result.indexOf("}", start);
      if (end < 0) break;
      String var = result.substring(start + 2, end);
      result = result.replace(result.substring(start, end + 1), var);
      start = result.indexOf("${", start);
    }

    return result;
  }
}
