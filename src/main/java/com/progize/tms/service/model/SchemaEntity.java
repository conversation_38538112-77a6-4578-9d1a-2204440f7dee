package com.progize.tms.service.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Model class for schema information. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchemaEntity implements Serializable {
  private String name;
  private String entityName;
  private EntityType entityType;
  private String type;
  private List<SchemaField> fields;
  private LocalDateTime discoveredAt;
  private SchemaEntity items; // For array types

  /** Enum for entity types. */
  public enum EntityType {
    TABLE,
    ENDPOINT,
    OBJECT,
    ARRAY,
    VALUE
  }

  /** Model class for schema field information. */
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Schema<PERSON><PERSON> implements Serializable {
    private String name;
    private String type;
    private boolean nullable;
    private String description;
  }
}
