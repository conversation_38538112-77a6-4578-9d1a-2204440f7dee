{"info": {"name": "SoniQue API Collection", "description": "A comprehensive collection of API endpoints for the SoniQue Transaction Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Transactions", "description": "Endpoints for processing and retrieving transactions", "item": [{"name": "Process Single Transaction", "request": {"method": "POST", "url": "{{base_url}}/api/transactions", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": null,\n  \"customerId\": \"CUST123\",\n  \"fundId\": \"FUND001\",\n  \"fundName\": \"Growth Fund\",\n  \"fundShortName\": \"GF\",\n  \"fundBankAccount\": \"********\",\n  \"transDecs\": \"Purchase\",\n  \"transaction\": \"BUY\",\n  \"portfolioId\": \"PORT001\",\n  \"customerName\": \"John Doe\",\n  \"crmBankAccountNo\": \"********\",\n  \"orderType\": \"MARKET\",\n  \"unitTypeCode\": \"UT001\",\n  \"unitTypeName\": \"Class A\",\n  \"dealDate\": \"2023-01-01\",\n  \"month\": 1,\n  \"transType\": \"PURCHASE\",\n  \"fundNetAmount\": 1000.00,\n  \"amountPerUnit\": 10.00,\n  \"nav\": 10.00,\n  \"transactionUnit\": 100.00,\n  \"entryLoad\": 1.00,\n  \"discountAmt\": 0.00,\n  \"grossLoad\": 1.00,\n  \"netLoad\": 1.00,\n  \"exitLoad\": 0.00,\n  \"zakatAmt\": 0.00,\n  \"cgt\": 0.00,\n  \"transCost\": 0.00,\n  \"col22\": null,\n  \"rmCode\": \"RM001\",\n  \"rmName\": \"<PERSON>\",\n  \"branch\": \"Main Branch\",\n  \"newExist\": \"NEW\",\n  \"region\": \"North\",\n  \"fundCategory\": \"Equity\",\n  \"industryDesc\": \"Technology\",\n  \"secpSector\": \"Tech\",\n  \"city\": \"New York\",\n  \"channel\": \"Online\",\n  \"tradeDateTime\": \"2023-01-01T10:00:00\",\n  \"customerRiskLevel\": \"MEDIUM\",\n  \"kycStatus\": \"COMPLETE\",\n  \"profileLastUpdated\": \"2022-12-15\",\n  \"loginAttempts\": 0\n}"}}}, {"name": "Get Recent Transactions", "request": {"method": "GET", "url": "{{base_url}}/api/transactions"}}, {"name": "Get Flagged Transactions", "request": {"method": "GET", "url": "{{base_url}}/api/transactions/flagged"}}, {"name": "Get Transactions by Account", "request": {"method": "GET", "url": "{{base_url}}/api/transactions/account/{{accountId}}"}}, {"name": "Process Transactions with Rules", "request": {"method": "POST", "url": "{{base_url}}/api/transactions/process-rules", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactions\": [\n    {\n      \"customerId\": \"CUST123\",\n      \"fundId\": \"FUND001\",\n      \"fundName\": \"Growth Fund\",\n      \"fundShortName\": \"GF\",\n      \"fundBankAccount\": \"********\",\n      \"transDecs\": \"Purchase\",\n      \"transaction\": \"BUY\",\n      \"portfolioId\": \"PORT001\",\n      \"customerName\": \"John Doe\",\n      \"crmBankAccountNo\": \"********\",\n      \"orderType\": \"MARKET\",\n      \"unitTypeCode\": \"UT001\",\n      \"unitTypeName\": \"Class A\",\n      \"dealDate\": \"2023-01-01\",\n      \"month\": 1,\n      \"transType\": \"PURCHASE\",\n      \"fundNetAmount\": 1000.00,\n      \"amountPerUnit\": 10.00,\n      \"nav\": 10.00,\n      \"transactionUnit\": 100.00,\n      \"entryLoad\": 1.00,\n      \"discountAmt\": 0.00,\n      \"grossLoad\": 1.00,\n      \"netLoad\": 1.00,\n      \"exitLoad\": 0.00,\n      \"zakatAmt\": 0.00,\n      \"cgt\": 0.00,\n      \"transCost\": 0.00,\n      \"rmCode\": \"RM001\",\n      \"rmName\": \"<PERSON>\",\n      \"branch\": \"Main Branch\",\n      \"newExist\": \"NEW\",\n      \"region\": \"North\",\n      \"fundCategory\": \"Equity\",\n      \"industryDesc\": \"Technology\",\n      \"secpSector\": \"Tech\",\n      \"city\": \"New York\",\n      \"channel\": \"Online\",\n      \"tradeDateTime\": \"2023-01-01T10:00:00\",\n      \"customerRiskLevel\": \"MEDIUM\",\n      \"kycStatus\": \"COMPLETE\",\n      \"profileLastUpdated\": \"2022-12-15\",\n      \"loginAttempts\": 0\n    }\n  ]\n}"}}}, {"name": "Process Bucket Transactions", "request": {"method": "POST", "url": "{{base_url}}/api/transactions/process-bucket-transactions", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bucketId\": 1,\n  \"transactions\": [\n    {\n      \"customerId\": \"CUST123\",\n      \"accountId\": \"ACC001\",\n      \"amount\": 1000.00,\n      \"transactionDate\": \"2023-01-01T10:00:00\",\n      \"transactionType\": \"PURCHASE\",\n      \"channel\": \"Online\",\n      \"customerRiskLevel\": \"MEDIUM\"\n    }\n  ]\n}"}}}]}, {"name": "Data Buckets", "description": "Endpoints for managing data buckets and their variables", "item": [{"name": "Get All Buckets", "request": {"method": "GET", "url": "{{base_url}}/api/data-buckets"}}, {"name": "Create Bucket", "request": {"method": "POST", "url": "{{base_url}}/api/data-buckets", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Transaction Bucket\",\n  \"description\": \"Bucket for transaction data\"\n}"}}}, {"name": "Get Bucket by ID", "request": {"method": "GET", "url": "{{base_url}}/api/data-buckets/{{bucketId}}"}}, {"name": "Delete Bucket", "request": {"method": "DELETE", "url": "{{base_url}}/api/data-buckets/{{bucketId}}"}}, {"name": "Get Variables for Buck<PERSON>", "request": {"method": "GET", "url": "{{base_url}}/api/data-buckets/{{bucketId}}/variables"}}, {"name": "Get All Variables for Bucket", "request": {"method": "GET", "url": "{{base_url}}/api/data-buckets/{{bucketId}}/all-variables"}}, {"name": "Register Variables to <PERSON><PERSON>", "request": {"method": "POST", "url": "{{base_url}}/api/data-buckets/{{bucketId}}/variables", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"code\": \"customerId\",\n    \"name\": \"Customer ID\",\n    \"dataTypeId\": 1,\n    \"description\": \"Unique identifier for the customer\"\n  },\n  {\n    \"code\": \"amount\",\n    \"name\": \"Transaction Amount\",\n    \"dataTypeId\": 2,\n    \"description\": \"Amount of the transaction\"\n  }\n]"}}}, {"name": "Delete Variable", "request": {"method": "DELETE", "url": "{{base_url}}/api/data-buckets/variables/{{variableId}}"}}]}, {"name": "Rules", "description": "Endpoints for managing rule configurations", "item": [{"name": "Create Rule", "request": {"method": "POST", "url": "{{base_url}}/api/rules", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"High Value Transaction Rule\",\n  \"description\": \"Flag transactions over $10,000\",\n  \"dataBucketId\": 1,\n  \"priority\": 1,\n  \"active\": true,\n  \"flagReason\": \"High value transaction detected\",\n  \"ruleJson\": {\n    \"operator\": \">\",\n    \"variable\": \"amount\",\n    \"value\": 10000\n  }\n}"}}}, {"name": "Update Rule", "request": {"method": "PUT", "url": "{{base_url}}/api/rules/{{ruleId}}", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"High Value Transaction Rule\",\n  \"description\": \"Flag transactions over $5,000\",\n  \"dataBucketId\": 1,\n  \"priority\": 1,\n  \"active\": true,\n  \"flagReason\": \"High value transaction detected\",\n  \"ruleJson\": {\n    \"operator\": \">\",\n    \"variable\": \"amount\",\n    \"value\": 5000\n  }\n}"}}}, {"name": "Validate Rule", "request": {"method": "POST", "url": "{{base_url}}/api/rules/validate", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"High Value Transaction Rule\",\n  \"description\": \"Flag transactions over $10,000\",\n  \"dataBucketId\": 1,\n  \"priority\": 1,\n  \"active\": true,\n  \"flagReason\": \"High value transaction detected\",\n  \"ruleJson\": {\n    \"operator\": \">\",\n    \"variable\": \"amount\",\n    \"value\": 10000\n  }\n}"}}}, {"name": "Get Rule by ID", "request": {"method": "GET", "url": "{{base_url}}/api/rules/{{ruleId}}"}}, {"name": "Get All Rules", "request": {"method": "GET", "url": "{{base_url}}/api/rules"}}, {"name": "Get Active Rules", "request": {"method": "GET", "url": "{{base_url}}/api/rules/active"}}, {"name": "Get Active Rules by <PERSON><PERSON>", "request": {"method": "GET", "url": "{{base_url}}/api/rules/active/bucket/{{bucketId}}"}}, {"name": "Activate Rule", "request": {"method": "PUT", "url": "{{base_url}}/api/rules/{{ruleId}}/activate"}}, {"name": "Deactivate Rule", "request": {"method": "PUT", "url": "{{base_url}}/api/rules/{{ruleId}}/deactivate"}}]}, {"name": "Rule Testing", "description": "Endpoints for testing rules against sample data", "item": [{"name": "Test Rule", "request": {"method": "POST", "url": "{{base_url}}/api/rule-testing", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bucketId\": 1,\n  \"rule\": {\n    \"name\": \"Test Rule\",\n    \"description\": \"Rule for testing\",\n    \"ruleJson\": {\n      \"operator\": \">\",\n      \"variable\": \"amount\",\n      \"value\": 1000\n    }\n  },\n  \"sampleData\": {\n    \"customerId\": \"CUST123\",\n    \"amount\": 1500,\n    \"transactionDate\": \"2023-01-01T10:00:00\"\n  },\n  \"useRealAggregations\": false\n}"}}}]}, {"name": "Rule Variable Registry", "description": "Endpoints for managing rule variables and discovering database schema", "item": [{"name": "Get All Variables", "request": {"method": "GET", "url": "{{base_url}}/api/rule-registry"}}, {"name": "Get Variables by Table", "request": {"method": "GET", "url": "{{base_url}}/api/rule-registry/table/{{tableName}}"}}, {"name": "Discover Tables", "request": {"method": "GET", "url": "{{base_url}}/api/rule-registry/schema/tables"}}, {"name": "Get All Source Tables", "request": {"method": "GET", "url": "{{base_url}}/api/rule-registry/source-tables"}}, {"name": "Get Table Columns", "request": {"method": "GET", "url": "{{base_url}}/api/rule-registry/schema/tables/{{schema}}/{{table}}/columns"}}, {"name": "Register Table", "request": {"method": "POST", "url": "{{base_url}}/api/rule-registry/schema/tables/{{schema}}/{{table}}/register", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"Transactions\",\n  \"description\": \"Table containing transaction data\"\n}"}}}, {"name": "Register Columns", "request": {"method": "POST", "url": "{{base_url}}/api/rule-registry/schema/tables/{{tableId}}/columns/register", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"columns\": [\n    {\n      \"name\": \"customer_id\",\n      \"displayName\": \"Customer ID\",\n      \"dataType\": \"STRING\",\n      \"aggregatable\": false\n    },\n    {\n      \"name\": \"amount\",\n      \"displayName\": \"Transaction Amount\",\n      \"dataType\": \"DECIMAL\",\n      \"aggregatable\": true\n    }\n  ]\n}"}}}, {"name": "Update Aggregatable", "request": {"method": "PUT", "url": "{{base_url}}/api/rule-registry/{{variableId}}/aggregatable", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"aggregatable\": true\n}"}}}, {"name": "Delete Variable", "request": {"method": "DELETE", "url": "{{base_url}}/api/rule-registry/{{variableId}}"}}]}, {"name": "Rule Variables", "description": "Endpoints for managing rule variables, operators, and aggregate functions", "item": [{"name": "Get All Variables", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables"}}, {"name": "Get Variables by Category", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables/category/{{category}}"}}, {"name": "Get Variable by ID", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables/{{id}}"}}, {"name": "Get All Operators", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables/operators"}}, {"name": "Get Operators by Type", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables/operators/type/{{type}}"}}, {"name": "Get All Aggregate Functions", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables/aggregate-functions"}}, {"name": "Get Aggregate Functions by Type", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables/aggregate-functions/type/{{type}}"}}, {"name": "<PERSON> <PERSON>", "request": {"method": "GET", "url": "{{base_url}}/api/rule-variables/metadata"}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "bucketId", "value": "1", "type": "string"}, {"key": "ruleId", "value": "1", "type": "string"}, {"key": "accountId", "value": "ACC001", "type": "string"}, {"key": "variableId", "value": "1", "type": "string"}, {"key": "tableId", "value": "1", "type": "string"}, {"key": "tableName", "value": "transactions", "type": "string"}, {"key": "schema", "value": "public", "type": "string"}, {"key": "table", "value": "transactions", "type": "string"}, {"key": "category", "value": "transaction", "type": "string"}, {"key": "type", "value": "number", "type": "string"}]}