package com.progize.tms.service.datasource;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.exceptions.TmsBusinessException;
import com.progize.tms.exceptions.TmsError;
import com.progize.tms.repository.entity.RestApiSourceEntity;
import com.progize.tms.repository.entity.enums.ApiKeyLocation;
import com.progize.tms.repository.entity.enums.AuthenticationType;
import com.progize.tms.service.model.SchemaEntity;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/** Handler for REST API data sources. */
@Slf4j
public class RestApiSourceHandler implements DataSourceHandler {

  private final RestApiSourceEntity restApiSource;
  private final RestTemplate restTemplate;
  private final ObjectMapper objectMapper;

  public RestApiSourceHandler(RestApiSourceEntity restApiSource) {
    this.restApiSource = restApiSource;
    this.restTemplate = new RestTemplate();
    this.objectMapper = new ObjectMapper();
  }

  @Override
  public boolean testConnection() {
    try {
      // Create HTTP headers
      HttpHeaders headers = createHeaders();

      // Create HTTP entity
      HttpEntity<String> entity = new HttpEntity<>(headers);

      // Build URL
      UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(restApiSource.getBaseUrl());

      // Add API key as query parameter if needed
      if (restApiSource.getAuthenticationType() != null
          && AuthenticationType.API_KEY.equals(restApiSource.getAuthenticationType())
          && restApiSource.getApiKeyName() != null
          && restApiSource.getApiKeyValue() != null
          && restApiSource.getApiKeyLocation() != null
          && ApiKeyLocation.QUERY_PARAM.equals(restApiSource.getApiKeyLocation())) {
        builder.queryParam(restApiSource.getApiKeyName(), restApiSource.getApiKeyValue());
      }

      // Execute request
      ResponseEntity<String> response =
          restTemplate.exchange(builder.toUriString(), HttpMethod.GET, entity, String.class);

      // Check if response is successful
      return response.getStatusCode().is2xxSuccessful();
    } catch (Exception e) {
      log.error("Error testing connection: {}", e.getMessage(), e);
      return false;
    }
  }

  @Override
  public List<SchemaEntity> discoverSchema() {
    try {
      // Create HTTP headers
      HttpHeaders headers = createHeaders();

      // Create HTTP entity
      HttpEntity<String> entity = new HttpEntity<>(headers);

      // Build URL
      UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(restApiSource.getBaseUrl());

      // Add API key as query parameter if needed
      if (restApiSource.getAuthenticationType() != null
          && AuthenticationType.API_KEY.equals(restApiSource.getAuthenticationType())
          && restApiSource.getApiKeyName() != null
          && restApiSource.getApiKeyValue() != null
          && restApiSource.getApiKeyLocation() != null
          && ApiKeyLocation.QUERY_PARAM.equals(restApiSource.getApiKeyLocation())) {
        builder.queryParam(restApiSource.getApiKeyName(), restApiSource.getApiKeyValue());
      }

      // Execute request
      ResponseEntity<String> response =
          restTemplate.exchange(builder.toUriString(), HttpMethod.GET, entity, String.class);

      // Check if response is successful
      if (!response.getStatusCode().is2xxSuccessful()) {
        throw new TmsBusinessException(TmsError.CONNECTION_ERROR);
      }

      // Parse response
      JsonNode root = objectMapper.readTree(response.getBody());

      // Analyze response to extract schema
      List<SchemaEntity> schema = new ArrayList<>();
      if (root.isArray()) {
        // If the root is an array, analyze the first item
        if (root.size() > 0) {
          SchemaEntity schemaEntity = analyzeJsonNode("root", root.get(0));
          schema.add(schemaEntity);
        }
      } else {
        // If the root is an object, analyze it directly
        SchemaEntity schemaEntity = analyzeJsonNode("root", root);
        schema.add(schemaEntity);
      }

      return schema;
    } catch (Exception e) {
      log.error("Error discovering schema: {}", e.getMessage(), e);
      throw new TmsBusinessException(TmsError.SCHEMA_DISCOVERY_ERROR);
    }
  }

  @Override
  public List<Map<String, Object>> executeQuery(String entityName, Map<String, Object> parameters) {
    try {
      // Create HTTP headers
      HttpHeaders headers = createHeaders();

      // Create HTTP entity
      HttpEntity<String> entity = new HttpEntity<>(headers);

      // Build URL
      UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(restApiSource.getBaseUrl());
      if (entityName != null && !entityName.isEmpty()) {
        builder.path("/" + entityName);
      }

      // Add parameters as query parameters
      if (parameters != null && !parameters.isEmpty()) {
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
          builder.queryParam(entry.getKey(), entry.getValue());
        }
      }

      // Add API key as query parameter if needed
      if (restApiSource.getAuthenticationType() != null
          && AuthenticationType.API_KEY.equals(restApiSource.getAuthenticationType())
          && restApiSource.getApiKeyName() != null
          && restApiSource.getApiKeyValue() != null
          && restApiSource.getApiKeyLocation() != null
          && ApiKeyLocation.QUERY_PARAM.equals(restApiSource.getApiKeyLocation())) {
        builder.queryParam(restApiSource.getApiKeyName(), restApiSource.getApiKeyValue());
      }

      // Execute request
      ResponseEntity<String> response =
          restTemplate.exchange(builder.toUriString(), HttpMethod.GET, entity, String.class);

      // Check if response is successful
      if (!response.getStatusCode().is2xxSuccessful()) {
        throw new TmsBusinessException(TmsError.CONNECTION_ERROR);
      }

      // Parse response
      JsonNode root = objectMapper.readTree(response.getBody());

      // Convert response to list of maps
      List<Map<String, Object>> results = new ArrayList<>();
      if (root.isArray()) {
        // If the root is an array, convert each item
        for (JsonNode node : root) {
          Map<String, Object> item = convertJsonNodeToMap(node);
          results.add(item);
        }
      } else {
        // If the root is an object, convert it directly
        Map<String, Object> item = convertJsonNodeToMap(root);
        results.add(item);
      }

      return results;
    } catch (Exception e) {
      log.error("Error executing query: {}", e.getMessage(), e);
      throw new TmsBusinessException(TmsError.CONNECTION_ERROR);
    }
  }

  /**
   * Create HTTP headers with authentication.
   *
   * @return HTTP headers
   */
  private HttpHeaders createHeaders() {
    HttpHeaders headers = new HttpHeaders();

    // Add custom headers if provided
    if (restApiSource.getHeaders() != null && !restApiSource.getHeaders().isEmpty()) {
      try {
        @SuppressWarnings("unchecked")
        Map<String, String> customHeaders =
            objectMapper.readValue(restApiSource.getHeaders(), Map.class);
        for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
          headers.add(entry.getKey(), entry.getValue());
        }
      } catch (Exception e) {
        log.warn("Failed to parse custom headers: {}", e.getMessage(), e);
      }
    }

    // Add authentication headers
    if (restApiSource.getAuthenticationType() != null) {
      switch (restApiSource.getAuthenticationType()) {
        case BASIC:
          // Basic authentication
          if (restApiSource.getUsername() != null && restApiSource.getPassword() != null) {
            String auth = restApiSource.getUsername() + ":" + restApiSource.getPassword();
            byte[] encodedAuth = java.util.Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);
            headers.add("Authorization", authHeader);
          }
          break;
        case API_KEY:
          // API key authentication
          if (restApiSource.getApiKeyName() != null && restApiSource.getApiKeyValue() != null) {
            if (restApiSource.getApiKeyLocation() != null
                && ApiKeyLocation.HEADER.equals(restApiSource.getApiKeyLocation())) {
              headers.add(restApiSource.getApiKeyName(), restApiSource.getApiKeyValue());
            }
            // For query parameter API keys, they will be added to the URL in executeQuery
          }
          break;
        case OAUTH2:
          // OAuth 2.0 authentication
          // This would typically involve getting a token first, which is beyond the scope of this
          // example
          break;
        default:
          // No authentication
          break;
      }
    }

    return headers;
  }

  /**
   * Analyze a JSON node to extract schema information.
   *
   * @param name Name of the node
   * @param node JSON node to analyze
   * @return Schema entity
   */
  private SchemaEntity analyzeJsonNode(String name, JsonNode node) {
    SchemaEntity schema = new SchemaEntity();
    schema.setName(name);
    schema.setEntityName(name);

    if (node.isObject()) {
      schema.setEntityType(SchemaEntity.EntityType.OBJECT);
      schema.setType("object");

      List<SchemaEntity.SchemaField> fields = new ArrayList<>();

      Iterator<Map.Entry<String, JsonNode>> fields_it = node.fields();
      while (fields_it.hasNext()) {
        Map.Entry<String, JsonNode> field = fields_it.next();
        String fieldName = field.getKey();
        JsonNode fieldValue = field.getValue();

        String fieldType = getJsonNodeType(fieldValue);

        SchemaEntity.SchemaField schemaField =
            SchemaEntity.SchemaField.builder()
                .name(fieldName)
                .type(fieldType)
                .nullable(true)
                .build();

        fields.add(schemaField);
      }

      schema.setFields(fields);
    } else if (node.isArray()) {
      schema.setEntityType(SchemaEntity.EntityType.ARRAY);
      schema.setType("array");

      // If array has items, analyze the first one to determine item type
      if (node.size() > 0) {
        JsonNode firstItem = node.get(0);
        SchemaEntity itemSchema = analyzeJsonNode(name + "Item", firstItem);
        schema.setItems(itemSchema);
      }
    } else {
      schema.setEntityType(SchemaEntity.EntityType.VALUE);
      schema.setType(getJsonNodeType(node));
    }

    return schema;
  }

  /**
   * Get the type of a JSON node.
   *
   * @param node JSON node
   * @return Type of the node
   */
  private String getJsonNodeType(JsonNode node) {
    if (node.isTextual()) {
      return "string";
    } else if (node.isNumber()) {
      return node.isIntegralNumber() ? "integer" : "number";
    } else if (node.isBoolean()) {
      return "boolean";
    } else if (node.isObject()) {
      return "object";
    } else if (node.isArray()) {
      return "array";
    } else if (node.isNull()) {
      return "null";
    } else {
      return "unknown";
    }
  }

  /**
   * Convert a JSON node to a map.
   *
   * @param node JSON node to convert
   * @return Map representation of the JSON node
   */
  private Map<String, Object> convertJsonNodeToMap(JsonNode node) {
    if (node.isObject()) {
      Map<String, Object> map = new HashMap<>();
      Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
      while (fields.hasNext()) {
        Map.Entry<String, JsonNode> field = fields.next();
        String key = field.getKey();
        JsonNode value = field.getValue();

        if (value.isObject()) {
          map.put(key, convertJsonNodeToMap(value));
        } else if (value.isArray()) {
          List<Object> list = new ArrayList<>();
          for (JsonNode item : value) {
            if (item.isObject()) {
              list.add(convertJsonNodeToMap(item));
            } else {
              list.add(getJsonNodeValue(item));
            }
          }
          map.put(key, list);
        } else {
          map.put(key, getJsonNodeValue(value));
        }
      }
      return map;
    } else {
      throw new IllegalArgumentException("Cannot convert non-object node to map");
    }
  }

  /**
   * Get the value of a JSON node.
   *
   * @param node JSON node
   * @return Value of the node
   */
  private Object getJsonNodeValue(JsonNode node) {
    if (node.isTextual()) {
      return node.textValue();
    } else if (node.isInt()) {
      return node.intValue();
    } else if (node.isLong()) {
      return node.longValue();
    } else if (node.isDouble()) {
      return node.doubleValue();
    } else if (node.isBoolean()) {
      return node.booleanValue();
    } else if (node.isNull()) {
      return null;
    } else {
      return node.toString();
    }
  }
}
