import React, { useState } from 'react';
import { useLoaderData } from 'react-router-dom';
import { Box, Typography, Paper, Container, CircularProgress, Alert, Tabs, Tab } from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
import DataBucketsPanel from './DataBucketsPanel';
import { ruleVariableRegistryService } from '../../services/api';
import ActionButton from '../common/ActionButton';
import AddIcon from '@mui/icons-material/Add';
import StorageIcon from '@mui/icons-material/Storage';

// Flag to switch between mock mode and real API
const MOCK_MODE = false; // Now connecting to real API

const MOCK_REGISTERED_TABLES = [
  {
    id: '1',
    schema: 'public',
    name: 'customers',
    columns: [
      { name: 'id', type: 'int', mappedDataType: 'integer' },
      { name: 'name', type: 'varchar', mappedDataType: 'string' },
      { name: 'email', type: 'varchar', mappedDataType: 'string' },
      { name: 'created_at', type: 'timestamp', mappedDataType: 'datetime' }
    ]
  },
  {
    id: '2',
    schema: 'public',
    name: 'transactions',
    columns: [
      { name: 'id', type: 'int', mappedDataType: 'integer' },
      { name: 'customer_id', type: 'int', mappedDataType: 'integer' },
      { name: 'amount', type: 'decimal', mappedDataType: 'number' },
      { name: 'status', type: 'varchar', mappedDataType: 'string' },
      { name: 'created_at', type: 'timestamp', mappedDataType: 'datetime' }
    ]
  }
];

const MOCK_VARIABLES = [
  {
    id: '1',
    code: 'customers_name',
    name: 'Customer Name',
    description: 'The full name of the customer',
    tableName: 'customers',
    columnName: 'name',
    dataTypeCode: 'string',
    aggregatable: false
  },
  {
    id: '2',
    code: 'transactions_amount',
    name: 'Transaction Amount',
    description: 'The transaction amount',
    tableName: 'transactions',
    columnName: 'amount',
    dataTypeCode: 'number',
    aggregatable: true
  },
  {
    id: '3',
    code: 'transactions_status',
    name: 'Transaction Status',
    description: 'The current status of the transaction',
    tableName: 'transactions',
    columnName: 'status',
    dataTypeCode: 'string',
    aggregatable: false
  }
];

// Styled components
const PageContainer = styled(Container)(({ theme }) => ({
  maxWidth: '100%',
  width: '100%',
  margin: 0,
  padding: 0,
}));

const Header = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
  boxShadow: 'none'
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTabs-indicator': {
    height: 3,
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 500,
  minHeight: 48,
  '&.Mui-selected': {
    fontWeight: 600,
  },
}));

// TabPanel component similar to Rule List page
const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const RuleVariableRegistryPage = () => {
  const loaderData = useLoaderData();
  const { buckets: initialBuckets = [], variables: initialVariables = {} } = loaderData || {};
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0); // Keep tab structure
  const [createDrawerOpen, setCreateDrawerOpen] = useState(false);
  const [buckets, setBuckets] = useState(initialBuckets);
  const [bucketVariables, setBucketVariables] = useState(initialVariables);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleOpenCreateDrawer = () => {
    setCreateDrawerOpen(true);
  };

  return (
    <Box sx={{ width: '100%', height: '100%', backgroundColor: 'transparent' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <DataBucketsPanel createDrawerOpen={createDrawerOpen} setCreateDrawerOpen={setCreateDrawerOpen} />
      )}
    </Box>
  );
};

export default RuleVariableRegistryPage;
