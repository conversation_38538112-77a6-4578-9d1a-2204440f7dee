-- Create data_source table
CREATE TABLE data_source (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGIN<PERSON>,
    updated_by BIGINT,
    last_schema_discovery TIMESTAMP
);

-- Create data_source_database table
CREATE TABLE data_source_database (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_source_id BIGINT NOT NULL,
    connection_url VARCHAR(255) NOT NULL,
    driver_class VARCHAR(255),
    username VARCHAR(255),
    password VARCHAR(255),
    database_type VARCHA<PERSON>(50) NOT NULL,
    schema_name <PERSON><PERSON><PERSON><PERSON>(255),
    additional_properties TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_database_data_source FOREIGN KEY (data_source_id) REFERENCES data_source(id) ON DELETE CASCADE
);

-- Create data_source_rest_api table
CREATE TABLE data_source_rest_api (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_source_id BIGINT NOT NULL,
    base_url VARCHAR(255) NOT NULL,
    authentication_type VARCHAR(50) NOT NULL,
    username VARCHAR(255),
    password VARCHAR(255),
    api_key_name VARCHAR(255),
    api_key_value VARCHAR(255),
    api_key_location VARCHAR(50),
    oauth_token_url VARCHAR(255),
    oauth_client_id VARCHAR(255),
    oauth_client_secret VARCHAR(255),
    oauth_scope VARCHAR(255),
    headers TEXT,
    request_timeout_ms INT DEFAULT 30000,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_rest_api_data_source FOREIGN KEY (data_source_id) REFERENCES data_source(id) ON DELETE CASCADE
);

-- Create schema table to store schema information for data sources
CREATE TABLE data_source_schema (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_source_id BIGINT NOT NULL,
    entity_name VARCHAR(255) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    schema_definition TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_schema_data_source FOREIGN KEY (data_source_id) REFERENCES data_source(id) ON DELETE CASCADE,
    CONSTRAINT uk_data_source_entity UNIQUE (data_source_id, entity_name)
);

-- Add audit tables for data sources
CREATE TABLE data_source_aud (
    id BIGINT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,
    name VARCHAR(255),
    description TEXT,
    type VARCHAR(50),
    active BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    last_schema_discovery TIMESTAMP,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_data_source_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);

CREATE TABLE data_source_database_aud (
    id BIGINT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,
    data_source_id BIGINT,
    connection_url VARCHAR(255),
    driver_class VARCHAR(255),
    username VARCHAR(255),
    password VARCHAR(255),
    database_type VARCHAR(50),
    schema_name VARCHAR(255),
    additional_properties TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_data_source_database_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);

CREATE TABLE data_source_rest_api_aud (
    id BIGINT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,
    data_source_id BIGINT,
    base_url VARCHAR(255),
    authentication_type VARCHAR(50),
    username VARCHAR(255),
    password VARCHAR(255),
    api_key_name VARCHAR(255),
    api_key_value VARCHAR(255),
    api_key_location VARCHAR(50),
    oauth_token_url VARCHAR(255),
    oauth_client_id VARCHAR(255),
    oauth_client_secret VARCHAR(255),
    oauth_scope VARCHAR(255),
    headers TEXT,
    request_timeout_ms INT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_data_source_rest_api_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);
