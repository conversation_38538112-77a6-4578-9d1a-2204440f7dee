package com.progize.tms.repository;

import com.progize.tms.repository.entity.Transaction;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;

@Repository
public class TransactionRepository {
  private final JdbcTemplate jdbcTemplate;

  private static final String INSERT_SQL =
      "INSERT INTO transactions (id, fund_id, fund_name, fund_short_name, fund_bank_account, "
          + "trans_decs, transaction_ref, customer_id, portfolio_id, customer_name, crm_bank_account_no, "
          + "order_type, unit_type_code, unit_type_name, deal_date, month, trans_type, fund_net_amount, "
          + "amount_per_unit, nav, transaction_unit, entry_load, discount_amt, gross_load, net_load, "
          + "exit_load, zakat_amt, cgt, trans_cost, col22, rm_code, rm_name, branch, new_exist, region, "
          + "fund_category, industry_desc, secp_sector, city, channel, trade_date_time, flagged, "
          + "flag_reason, customer_risk_level, kyc_status, profile_last_updated, login_attempts) VALUES ("
          + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
          + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

  public TransactionRepository(JdbcTemplate jdbcTemplate) {
    this.jdbcTemplate = jdbcTemplate;
  }

  public void save(Transaction transaction) {
    jdbcTemplate.update(
        INSERT_SQL,
        new PreparedStatementSetter() {
          @Override
          public void setValues(PreparedStatement ps) throws SQLException {
            setParameters(ps, transaction);
          }
        });
  }

  private void setParameters(PreparedStatement ps, Transaction t) throws SQLException {
    ps.setLong(1, t.getId());
    ps.setString(2, t.getFundId());
    ps.setString(3, t.getFundName());
    ps.setString(4, t.getFundShortName());
    ps.setString(5, t.getFundBankAccount());
    ps.setString(6, t.getTransDecs());
    ps.setString(7, t.getTransaction());
    ps.setString(8, t.getCustomerId());
    ps.setString(9, t.getPortfolioId());
    ps.setString(10, t.getCustomerName());
    ps.setString(11, t.getCrmBankAccountNo());
    ps.setString(12, t.getOrderType());
    ps.setString(13, t.getUnitTypeCode());
    ps.setString(14, t.getUnitTypeName());
    ps.setTimestamp(15, Timestamp.valueOf(t.getDealDate()));
    ps.setString(16, t.getMonth());
    ps.setString(17, t.getTransType());
    ps.setBigDecimal(18, t.getFundNetAmount());
    ps.setBigDecimal(19, t.getAmountPerUnit());
    ps.setBigDecimal(20, t.getNav());
    ps.setBigDecimal(21, t.getTransactionUnit());
    ps.setBigDecimal(22, t.getEntryLoad());
    ps.setBigDecimal(23, t.getDiscountAmt());
    ps.setBigDecimal(24, t.getGrossLoad());
    ps.setBigDecimal(25, t.getNetLoad());
    ps.setBigDecimal(26, t.getExitLoad());
    ps.setBigDecimal(27, t.getZakatAmt());
    ps.setBigDecimal(28, t.getCgt());
    ps.setBigDecimal(29, t.getTransCost());
    ps.setString(30, t.getCol22());
    ps.setString(31, t.getRmCode());
    ps.setString(32, t.getRmName());
    ps.setString(33, t.getBranch());
    ps.setString(34, t.getNewExist());
    ps.setString(35, t.getRegion());
    ps.setString(36, t.getFundCategory());
    ps.setString(37, t.getIndustryDesc());
    ps.setString(38, t.getSecpSector());
    ps.setString(39, t.getCity());
    ps.setString(40, t.getChannel());
    ps.setTimestamp(41, Timestamp.valueOf(t.getTradeDateTime()));
    ps.setInt(42, t.isFlagged() ? 1 : 0);
    ps.setString(43, t.getFlagReason());
    ps.setString(44, t.getCustomerRiskLevel());
    ps.setString(45, t.getKycStatus());
    ps.setTimestamp(
        46,
        t.getProfileLastUpdated() != null ? Timestamp.valueOf(t.getProfileLastUpdated()) : null);
    ps.setInt(47, t.getLoginAttempts());
  }
}
