package com.progize.tms.services;

import com.progize.tms.repository.ClickHouseTransactionRepository;
import com.progize.tms.services.models.AggregationRequirement;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for calculating aggregations from ClickHouse based on requirements from the aggregation
 * registry. This service is responsible for executing the aggregation queries and returning the
 * results.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ClickHouseAggregationService {

  private final ClickHouseTransactionRepository clickHouseRepository;

  // Simple in-memory cache for aggregation results with TTL
  private final Map<String, CacheEntry<Number>> aggregationCache = new ConcurrentHashMap<>();

  // Default cache TTL in milliseconds (5 minutes)
  private static final long CACHE_TTL_MS = TimeUnit.MINUTES.toMillis(5);

  /**
   * Calculate all required aggregations for a set of entity IDs.
   *
   * @param requirements The set of aggregation requirements to calculate
   * @param entityIds The set of entity IDs to calculate aggregations for
   * @return A map of aggregation key to aggregation result
   */
  public Map<String, Number> calculateAggregations(
      Set<AggregationRequirement> requirements, Set<String> entityIds) {

    if (requirements.isEmpty() || entityIds.isEmpty()) {
      return new HashMap<>();
    }

    log.debug(
        "Calculating {} aggregations for {} entity IDs", requirements.size(), entityIds.size());

    Map<String, Number> results = new HashMap<>();

    // For each entity ID
    for (String entityId : entityIds) {
      // For each aggregation requirement
      for (AggregationRequirement req : requirements) {
        // Calculate the aggregation (with caching)
        Number value = calculateAggregation(req, entityId);

        // Store in results map with aggregation key
        if (value != null) {
          results.put(req.getAggregationKey(), value);
        }
      }
    }

    log.debug("Calculated {} aggregation values", results.size());
    return results;
  }

  /**
   * Calculate a single aggregation for an entity ID. Uses caching to avoid redundant calculations.
   *
   * @param requirement The aggregation requirement
   * @param entityId The entity ID
   * @return The calculated aggregation value
   */
  private Number calculateAggregation(AggregationRequirement requirement, String entityId) {
    String cacheKey = generateCacheKey(requirement, entityId);

    // Check cache first
    CacheEntry<Number> cached = aggregationCache.get(cacheKey);
    if (cached != null && !cached.isExpired()) {
      log.debug("Cache hit for aggregation: {}", cacheKey);
      return cached.getValue();
    }

    log.debug("Cache miss for aggregation: {}", cacheKey);

    // Calculate the aggregation
    Number result;
    try {
      switch (requirement.getFunction().toUpperCase()) {
        case "SUM":
          result =
              clickHouseRepository.calculateSum(
                  requirement.getField(),
                  requirement.getPeriod(),
                  requirement.getEntityField(),
                  entityId);
          break;
        case "COUNT":
          result =
              clickHouseRepository.calculateCount(
                  requirement.getField(),
                  requirement.getPeriod(),
                  requirement.getEntityField(),
                  entityId);
          break;
        case "AVG":
          result =
              clickHouseRepository.calculateAverage(
                  requirement.getField(),
                  requirement.getPeriod(),
                  requirement.getEntityField(),
                  entityId);
          break;
        case "MAX":
          result =
              clickHouseRepository.calculateMax(
                  requirement.getField(),
                  requirement.getPeriod(),
                  requirement.getEntityField(),
                  entityId);
          break;
        case "MIN":
          result =
              clickHouseRepository.calculateMin(
                  requirement.getField(),
                  requirement.getPeriod(),
                  requirement.getEntityField(),
                  entityId);
          break;
        default:
          log.warn("Unsupported aggregation function: {}", requirement.getFunction());
          return null;
      }

      // Cache the result
      aggregationCache.put(
          cacheKey, new CacheEntry<>(result, System.currentTimeMillis() + CACHE_TTL_MS));

      return result;
    } catch (Exception e) {
      log.error(
          "Error calculating aggregation: {} for entity ID: {}",
          requirement.getAggregationKey(),
          entityId,
          e);
      return null;
    }
  }

  /**
   * Generate a cache key for an aggregation requirement and entity ID.
   *
   * @param requirement The aggregation requirement
   * @param entityId The entity ID
   * @return A unique cache key
   */
  private String generateCacheKey(AggregationRequirement requirement, String entityId) {
    return requirement.getAggregationKey() + "_" + entityId;
  }

  /** Inner class for cache entries with expiration. */
  private static class CacheEntry<T> {
    private final T value;
    private final long expirationTime;

    public CacheEntry(T value, long expirationTime) {
      this.value = value;
      this.expirationTime = expirationTime;
    }

    public T getValue() {
      return value;
    }

    public boolean isExpired() {
      return System.currentTimeMillis() > expirationTime;
    }
  }
}
