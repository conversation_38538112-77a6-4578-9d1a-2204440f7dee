package com.progize.tms.repository;

import com.progize.tms.repository.entity.CaseAttachment;
import com.progize.tms.repository.entity.WorkflowCase;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Repository for case attachments. */
@Repository
public interface CaseAttachmentRepository extends JpaRepository<CaseAttachment, Long> {

  /**
   * Find all attachments for a specific workflow case.
   *
   * @param workflowCase the workflow case
   * @return list of attachments
   */
  List<CaseAttachment> findByWorkflowCaseOrderByCreatedAtDesc(WorkflowCase workflowCase);

  /**
   * Find all attachments for a specific task.
   *
   * @param taskId the task ID
   * @return list of attachments
   */
  List<CaseAttachment> findByTaskIdOrderByCreatedAtDesc(String taskId);

  /**
   * Find all attachments for a specific process instance.
   *
   * @param processInstanceId the process instance ID
   * @return list of attachments
   */
  List<CaseAttachment> findByProcessInstanceIdOrderByCreatedAtDesc(String processInstanceId);
}
