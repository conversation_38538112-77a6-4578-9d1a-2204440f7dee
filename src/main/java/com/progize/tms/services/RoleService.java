package com.progize.tms.services;

import com.progize.tms.repository.entity.Role;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface RoleService {
  List<Role> findAll();

  Optional<Role> findById(Long id);

  Optional<Role> findByName(String name);

  Role save(Role role);

  void deleteById(Long id);

  boolean existsById(Long id);

  boolean existsByName(String name);

  Role assignPermissions(Long roleId, Set<Long> permissionIds);

  Role removePermissions(Long roleId, Set<Long> permissionIds);

  Role setPermissions(Long roleId, Set<Long> permissionIds);
}
