import React, { useState, useEffect, Fragment } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  Paper,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  Avatar,
  useTheme,
  alpha,
  Collapse,
  Breadcrumbs,
  Link,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import StorageIcon from '@mui/icons-material/Storage';
import ApiIcon from '@mui/icons-material/Api';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import CloseIcon from '@mui/icons-material/Close';
import TableRowsIcon from '@mui/icons-material/TableRows';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import NumbersIcon from '@mui/icons-material/Numbers';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import dataSourceService from '../../services/dataSourceService';
import TestConnectionButton from './TestConnectionButton';

/**
 * Component to display detailed information about a data source
 *
 * @param {Object} props - Component props
 * @param {number} props.dataSourceId - ID of the data source to display
 * @param {Function} props.onDelete - Callback function when data source is deleted
 */
const DataSourceDetail = ({ dataSourceId, onDelete }) => {
  const navigate = useNavigate();

  const theme = useTheme();

  // State
  const [dataSource, setDataSource] = useState(null);
  const [schema, setSchema] = useState([]);
  const [loading, setLoading] = useState(true);
  const [schemaLoading, setSchemaLoading] = useState(false);
  const [error, setError] = useState(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState(null);
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    config: true,
    schema: true
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Function to get icon based on data source type
  const getTypeIcon = (type) => {
    switch(type) {
      case 'DATABASE':
        return <StorageIcon />;
      case 'REST_API':
        return <ApiIcon />;
      default:
        return <StorageIcon />;
    }
  };

  // Load data source details on component mount
  useEffect(() => {
    const loadDataSource = async () => {
      try {
        setLoading(true);
        const data = await dataSourceService.getDataSourceById(dataSourceId);
        setDataSource(data);

        // Try to load schema if available
        if (data.lastSchemaDiscovery) {
          try {
            const schemaData = await dataSourceService.discoverSchema(dataSourceId);
            setSchema(schemaData);
          } catch (schemaErr) {
            console.error('Error loading schema:', schemaErr);
            // Don't show an error for this, just log it
          }
        }

        setError(null);
      } catch (err) {
        console.error('Error loading data source:', err);
        setError('Failed to load data source details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (dataSourceId) {
      loadDataSource();
    }
  }, [dataSourceId]);

  // Handle discover schema button click
  const handleDiscoverSchema = async () => {
    try {
      setSchemaLoading(true);
      const schemaData = await dataSourceService.discoverSchema(dataSourceId);
      setSchema(schemaData);

      // Refresh data source to get updated lastSchemaDiscovery
      const updatedDataSource = await dataSourceService.getDataSourceById(dataSourceId);
      setDataSource(updatedDataSource);

      setSnackbar({
        open: true,
        message: 'Schema discovered successfully!',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error discovering schema:', err);
      setSnackbar({
        open: true,
        message: `Failed to discover schema: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setSchemaLoading(false);
    }
  };

  // Handle delete button click
  const handleDelete = () => {
    if (onDelete) {
      onDelete(dataSource);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/data-sources')}
          sx={{ mt: 2 }}
        >
          Back to Data Sources
        </Button>
      </Box>
    );
  }

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      backgroundColor: 'transparent',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<KeyboardArrowRightIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 1 }}
      >
        <Link
          underline="hover"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
          onClick={() => navigate('/')}
          style={{ cursor: 'pointer' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="small" />
          Home
        </Link>
        <Link
          underline="hover"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
          onClick={() => navigate('/data-sources')}
          style={{ cursor: 'pointer' }}
        >
          Data Sources
        </Link>
        <Typography color="text.secondary" variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
          {dataSource?.name || 'Details'}
        </Typography>
      </Breadcrumbs>

      {/* Header with title and actions */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          pb: 2,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            sx={{
              bgcolor: dataSource?.type === 'DATABASE'
                ? alpha(theme.palette.primary.main, 0.1)
                : alpha(theme.palette.secondary.main, 0.1),
              color: dataSource?.type === 'DATABASE'
                ? theme.palette.primary.main
                : theme.palette.secondary.main,
              width: 56,
              height: 56,
              mr: 2
            }}
          >
            {dataSource && getTypeIcon(dataSource.type)}
          </Avatar>
          <Box>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 600,
                mb: 1,
                background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              {dataSource?.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Chip
                label={dataSource?.type}
                size="small"
                sx={{
                  bgcolor: dataSource?.type === 'DATABASE'
                    ? alpha(theme.palette.primary.main, 0.1)
                    : alpha(theme.palette.secondary.main, 0.1),
                  color: dataSource?.type === 'DATABASE'
                    ? theme.palette.primary.main
                    : theme.palette.secondary.main,
                  fontWeight: 500,
                  mr: 1
                }}
              />
              <Chip
                label={dataSource?.active ? 'Active' : 'Inactive'}
                size="small"
                sx={{
                  bgcolor: dataSource?.active
                    ? alpha(theme.palette.success.main, 0.1)
                    : alpha(theme.palette.grey[500], 0.1),
                  color: dataSource?.active
                    ? theme.palette.success.main
                    : theme.palette.grey[500],
                  fontWeight: 500,
                  mr: 1
                }}
              />
              <Typography variant="body2" color="text.secondary">
                Created: {formatDate(dataSource?.createdAt)}
              </Typography>
            </Box>
          </Box>
        </Box>
        <Box>
          <TestConnectionButton
            dataSourceId={dataSourceId}
            variant="outlined"
            sx={{
              mr: 1,
              borderRadius: '20px'
            }}
          />
          <Button
            variant="outlined"
            color="primary"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/data-sources/${dataSourceId}/edit`)}
            sx={{
              mr: 1,
              borderRadius: '20px'
            }}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDelete}
            sx={{ borderRadius: '20px' }}
          >
            Delete
          </Button>
        </Box>
      </Box>

      {/* Data source details */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
              overflow: 'hidden'
            }}
          >
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Basic Information</Typography>
                  <IconButton size="small" onClick={() => toggleSection('basic')}>
                    {expandedSections.basic ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Box>
              }
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                py: 1.5
              }}
            />
            <Divider />
            <Collapse in={expandedSections.basic} timeout="auto">
              <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    ID
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">{dataSource?.id}</Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Name
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">{dataSource?.name}</Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Description
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {dataSource?.description || 'No description provided'}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Type
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">{dataSource?.type}</Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Status
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {dataSource?.active ? 'Active' : 'Inactive'}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Created At
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {formatDate(dataSource?.createdAt)}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Last Schema Discovery
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {formatDate(dataSource?.lastSchemaDiscovery)}
                  </Typography>
                </Grid>
              </Grid>
              </CardContent>
            </Collapse>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
              overflow: 'hidden'
            }}
          >
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {dataSource?.type === 'DATABASE' ? 'Database' : 'REST API'} Configuration
                  </Typography>
                  <IconButton size="small" onClick={() => toggleSection('config')}>
                    {expandedSections.config ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Box>
              }
              sx={{
                bgcolor: dataSource?.type === 'DATABASE'
                  ? alpha(theme.palette.primary.main, 0.05)
                  : alpha(theme.palette.secondary.main, 0.05),
                py: 1.5
              }}
            />
            <Divider />
            <Collapse in={expandedSections.config} timeout="auto">
              <CardContent>
              <Grid container spacing={2}>
                {dataSource?.type === 'DATABASE' ? (
                  // Database-specific fields
                  <>
                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Database Type
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{dataSource?.databaseType}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Connection URL
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{dataSource?.connectionUrl}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Username
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{dataSource?.username || 'N/A'}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Schema Name
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{dataSource?.schemaName || 'N/A'}</Typography>
                    </Grid>
                  </>
                ) : (
                  // REST API-specific fields
                  <>
                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Base URL
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{dataSource?.baseUrl}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Authentication Type
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{dataSource?.authenticationType}</Typography>
                    </Grid>

                    {dataSource?.authenticationType === 'BASIC' && (
                      <>
                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Username
                          </Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{dataSource?.username || 'N/A'}</Typography>
                        </Grid>
                      </>
                    )}

                    {dataSource?.authenticationType === 'API_KEY' && (
                      <>
                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            API Key Name
                          </Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{dataSource?.apiKeyName || 'N/A'}</Typography>
                        </Grid>

                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            API Key Location
                          </Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{dataSource?.apiKeyLocation || 'N/A'}</Typography>
                        </Grid>
                      </>
                    )}

                    {dataSource?.authenticationType === 'OAUTH2' && (
                      <>
                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            OAuth Token URL
                          </Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{dataSource?.oauthTokenUrl || 'N/A'}</Typography>
                        </Grid>

                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            OAuth Client ID
                          </Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{dataSource?.oauthClientId || 'N/A'}</Typography>
                        </Grid>

                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            OAuth Scope
                          </Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{dataSource?.oauthScope || 'N/A'}</Typography>
                        </Grid>
                      </>
                    )}
                  </>
                )}
              </Grid>
              </CardContent>
            </Collapse>
          </Card>
        </Grid>

        {/* Schema section */}
        <Grid item xs={12}>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
              overflow: 'hidden'
            }}
          >
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Schema</Typography>
                  <IconButton size="small" onClick={() => toggleSection('schema')}>
                    {expandedSections.schema ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Box>
              }
              action={
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={schemaLoading ? <CircularProgress size={20} /> : <RefreshIcon />}
                  onClick={handleDiscoverSchema}
                  disabled={schemaLoading}
                  sx={{
                    borderRadius: '20px',
                    mr: 1
                  }}
                >
                  Discover Schema
                </Button>
              }
              sx={{
                bgcolor: alpha(theme.palette.info.main, 0.05),
                py: 1.5
              }}
            />
            <Divider />
            <Collapse in={expandedSections.schema} timeout="auto">
              <CardContent>
              {schema.length === 0 ? (
                <Typography variant="body2" color="text.secondary" sx={{ py: 2, textAlign: 'center' }}>
                  {dataSource?.lastSchemaDiscovery
                    ? 'No schema information available. Click "Discover Schema" to refresh.'
                    : 'No schema discovered yet. Click "Discover Schema" to discover the schema.'}
                </Typography>
              ) : (
                <TableContainer
                  component={Paper}
                  variant="outlined"
                  sx={{
                    borderRadius: 2,
                    '& .MuiTableRow-root:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.04)
                    }
                  }}
                >
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Entity Name</TableCell>
                        <TableCell>Display Name</TableCell>
                        <TableCell>Entity Type</TableCell>
                        <TableCell>Fields</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {schema.map((entity) => (
                        <TableRow
                          key={entity.entityName || entity.name}
                          hover
                          onClick={() => {
                            setSelectedEntity(entity);
                            setDrawerOpen(true);
                          }}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell>{entity.entityName || entity.name}</TableCell>
                          <TableCell>{entity.displayName || entity.entityName || entity.name}</TableCell>
                          <TableCell>{entity.entityType || entity.type}</TableCell>
                          <TableCell>
                            {entity.fields ? (
                              <Chip
                                label={`${entity.fields.length} fields`}
                                size="small"
                                color="primary"
                                variant="outlined"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedEntity(entity);
                                  setDrawerOpen(true);
                                }}
                              />
                            ) : 'N/A'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
              </CardContent>
            </Collapse>
          </Card>
        </Grid>
      </Grid>

      {/* Fields Drawer */}
      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: 450 },
            boxSizing: 'border-box',
            boxShadow: '-4px 0 20px rgba(0,0,0,0.05)',
            borderLeft: `1px solid ${theme.palette.divider}`
          }
        }}
      >
        {selectedEntity && (
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Drawer Header */}
            <Box
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                borderBottom: `1px solid ${theme.palette.divider}`,
                bgcolor: alpha(theme.palette.primary.main, 0.05)
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main,
                    width: 40,
                    height: 40,
                    mr: 2
                  }}
                >
                  <TableRowsIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {selectedEntity.displayName || selectedEntity.entityName || selectedEntity.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedEntity.fields ? `${selectedEntity.fields.length} fields` : 'No fields'}
                  </Typography>
                </Box>
              </Box>
              <IconButton onClick={() => setDrawerOpen(false)}>
                <CloseIcon />
              </IconButton>
            </Box>

            {/* Fields List */}
            <Box sx={{ flexGrow: 1, overflow: 'auto', p: 0 }}>
              {selectedEntity.fields && selectedEntity.fields.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {selectedEntity.fields.map((field) => {
                    // Determine icon based on field type
                    let FieldIcon = TextFieldsIcon; // Default icon
                    if (field.type) {
                      const type = field.type.toUpperCase();
                      if (type.includes('INT') || type.includes('DECIMAL') || type.includes('FLOAT') || type.includes('DOUBLE') || type.includes('NUMBER')) {
                        FieldIcon = NumbersIcon;
                      } else if (type.includes('DATE') || type.includes('TIME') || type.includes('TIMESTAMP')) {
                        FieldIcon = CalendarMonthIcon;
                      } else if (type.includes('BOOL')) {
                        FieldIcon = field.nullable ? CheckBoxOutlineBlankIcon : CheckBoxIcon;
                      }
                    }

                    return (
                      <ListItem
                        key={field.name}
                        divider
                        sx={{
                          px: 2,
                          py: 1.5,
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.04)
                          }
                        }}
                      >
                        <ListItemIcon sx={{ minWidth: 40 }}>
                          <FieldIcon color="primary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                {field.name}
                              </Typography>
                              <Chip
                                label={field.type}
                                size="small"
                                sx={{
                                  fontSize: '0.75rem',
                                  height: 24,
                                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                                  color: theme.palette.primary.main
                                }}
                              />
                            </Box>
                          }
                          secondary={
                            <Box sx={{ mt: 0.5 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                                  Nullable:
                                </Typography>
                                <Chip
                                  label={field.nullable ? 'Yes' : 'No'}
                                  size="small"
                                  sx={{
                                    fontSize: '0.7rem',
                                    height: 20,
                                    bgcolor: field.nullable
                                      ? alpha(theme.palette.warning.main, 0.1)
                                      : alpha(theme.palette.success.main, 0.1),
                                    color: field.nullable
                                      ? theme.palette.warning.main
                                      : theme.palette.success.main
                                  }}
                                />
                              </Box>
                              {field.description && (
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                  {field.description}
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    );
                  })}
                </List>
              ) : (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    No fields available for this entity.
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        )}
      </Drawer>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DataSourceDetail;
