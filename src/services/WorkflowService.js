import axios from 'axios';

class WorkflowService {
  /**
   * Get all workflow instances
   * @returns {Promise<Array>} List of workflow instances
   */
  async getWorkflowInstances() {
    try {
      const response = await axios.get('/api/workflow-instances');
      console.log('Workflow instances:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching workflow instances:', error);
      throw error;
    }
  }

  /**
   * Get a workflow instance by ID
   * @param {string} id Workflow instance ID
   * @returns {Promise<Object>} Workflow instance details
   */
  async getWorkflowInstanceById(id) {
    try {
      const response = await axios.get(`/api/workflow-instances/${id}`);
      console.log('Workflow instance:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching workflow instance ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get workflow instances by entity
   * @param {string} entityType Entity type
   * @param {string} entityId Entity ID
   * @returns {Promise<Array>} List of workflow instances for the entity
   */
  async getWorkflowInstancesByEntity(entityType, entityId) {
    try {
      const response = await axios.get(`/api/workflow-instances/entity/${entityType}/${entityId}`);
      console.log(`Workflow instances for ${entityType} ${entityId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching workflow instances for ${entityType} ${entityId}:`, error);
      throw error;
    }
  }

  /**
   * Get available actions for a workflow instance
   * @param {string} instanceId Workflow instance ID
   * @returns {Promise<Array>} List of available actions
   */
  async getAvailableActions(instanceId) {
    try {
      const response = await axios.get(`/api/workflow-instances/${instanceId}/actions`);
      console.log(`Available actions for workflow ${instanceId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching available actions for workflow ${instanceId}:`, error);
      throw error;
    }
  }

  /**
   * Get picklist options for a workflow step
   * @param {string} instanceId Workflow instance ID
   * @returns {Promise<Array>} List of picklist options
   */
  async getPicklistOptions(instanceId) {
    try {
      console.log(`Getting picklist options for workflow instance: ${instanceId}`);
      
      // First get the workflow instance to find the current step ID
      const instance = await this.getWorkflowInstanceById(instanceId);
      console.log('Workflow instance for picklist options:', JSON.stringify(instance, null, 2));
      
      if (!instance || !instance.currentStepId) {
        console.warn('No currentStepId found in workflow instance');
        return [];
      }
      
      const stepId = instance.currentStepId;
      console.log(`Using currentStepId: ${stepId}`);
      
      // Get transitions from the current step
      console.log(`Fetching transitions for step ID: ${stepId}`);
      
      try {
        const transitions = await this.getTransitionsFromStep(stepId);
        console.log(`Found ${transitions ? transitions.length : 0} transitions:`, JSON.stringify(transitions, null, 2));
        
        // If we have transitions, map them to picklist options
        if (transitions && transitions.length > 0) {
          console.log('Mapping transitions to picklist options');
          
          // Map transitions to options with toStepId
          const options = transitions.map(transition => ({
            id: transition.id,
            name: transition.name || transition.actionName || 'Take Action',
            description: transition.description || `Move to ${transition.toStep?.name || 'next step'}`,
            toStepId: transition.toStep?.id,
            toStepName: transition.toStep?.name
          }));
          
          console.log('Mapped picklist options:', JSON.stringify(options, null, 2));
          return options;
        }
      } catch (transitionError) {
        console.error(`Error fetching transitions for step ${stepId}:`, transitionError);
      }
      
      // If we still don't have options, create default ones
      console.warn('No transitions found, using default options');
      return [
        {
          id: 'approve',
          name: 'Approve',
          description: 'Approve this case',
          toStepId: stepId // Use current step ID as fallback
        },
        {
          id: 'reject',
          name: 'Reject',
          description: 'Reject this case',
          toStepId: stepId // Use current step ID as fallback
        }
      ];
    } catch (error) {
      console.error(`Error fetching picklist options for workflow ${instanceId}:`, error);
      // Return default options as fallback
      return [
        { id: 'approve', name: 'Approve', description: 'Approve this case', toStepId: null },
        { id: 'reject', name: 'Reject', description: 'Reject this case', toStepId: null }
      ];
    }
  }

  /**
   * Get transitions from a workflow step
   * @param {string} stepId Workflow step ID
   * @returns {Promise<Array>} List of transitions
   */
  async getTransitionsFromStep(stepId) {
    try {
      const response = await axios.get(`/api/workflows/steps/${stepId}/transitions`);
      console.log(`Transitions for step ${stepId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching transitions for step ${stepId}:`, error);
      throw error;
    }
  }

  /**
   * Get all steps for a workflow
   * @param {string} workflowId Workflow ID
   * @returns {Promise<Array>} List of workflow steps
   */
  async getWorkflowSteps(workflowId) {
    try {
      const response = await axios.get(`/api/workflows/${workflowId}/steps`);
      console.log(`Steps for workflow ${workflowId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching steps for workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Get next steps for a workflow instance
   * @param {string} instanceId Workflow instance ID
   * @returns {Promise<Array>} List of next steps
   */
  async getWorkflowNextSteps(instanceId) {
    try {
      // First get the workflow instance to find the current step
      const instance = await this.getWorkflowInstanceById(instanceId);
      
      if (!instance || !instance.currentStepId) {
        console.warn('No currentStepId found in workflow instance');
        return [];
      }
      
      // Get transitions from the current step
      const stepId = instance.currentStepId;
      const transitions = await this.getTransitionsFromStep(stepId);
      
      if (!transitions || transitions.length === 0) {
        console.warn(`No transitions found for step ${stepId}`);
        return [];
      }
      
      console.log(`Found ${transitions.length} transitions for step ${stepId}`);
      
      // Map transitions to next steps
      return transitions.map(transition => ({
        id: transition.id,
        name: transition.name || transition.actionName,
        description: transition.description,
        toStepId: transition.toStep?.id,
        toStepName: transition.toStep?.name
      }));
    } catch (error) {
      console.error(`Error fetching next steps for workflow ${instanceId}:`, error);
      throw error;
    }
  }

  /**
   * Get all open cases with their workflow information
   * @returns {Promise<Array>} List of open cases with workflow data
   */
  async getOpenCasesWithWorkflow() {
    try {
      // Use the cases-workflow endpoint with status/OPEN
      console.log('Fetching open cases with workflow');
      const response = await axios.get('/api/cases-workflow/status/OPEN');
      
      // Process the response to ensure it's in the expected format
      console.log('API Response for open cases:', response.data);
      
      // Return the data array directly
      return response.data || [];
    } catch (error) {
      console.error('Error fetching open cases with workflow:', error);
      throw error;
    }
  }

  /**
   * Execute a workflow action
   * @param {string} instanceId Workflow instance ID
   * @param {Object} transitionData Transition data
   * @returns {Promise<Object>} Updated workflow instance
   */
  async executeAction(instanceId, transitionData) {
    try {
      console.log(`Executing action on workflow ${instanceId}:`, transitionData);
      
      // Ensure we have the required fields
      if (!transitionData.toStepId) {
        throw new Error('toStepId is required for workflow transition');
      }
      
      const payload = {
        toStepId: transitionData.toStepId,
        actionName: transitionData.actionName || 'User Action',
        currentUserId: transitionData.currentUserId || 'admin',
        comments: transitionData.comments || '',
        picklistOptionId: transitionData.picklistOptionId || null
      };
      
      const response = await axios.post(`/api/workflow-instances/${instanceId}/transition`, payload);
      console.log(`Workflow ${instanceId} transitioned:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error executing action on workflow ${instanceId}:`, error);
      throw error;
    }
  }
}

export default new WorkflowService();