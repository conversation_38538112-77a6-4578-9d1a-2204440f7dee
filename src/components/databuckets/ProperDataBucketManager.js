import React, { useState, useEffect } from 'react';
import {
    Box,
    CircularProgress,
    Alert,
    TextField,
    Paper,
    Grid,
    Tabs,
    Tab,
    InputAdornment,
    Typography,
    Button,
    useTheme
} from '@mui/material';
import { Search as SearchIcon, Add as AddIcon } from '@mui/icons-material';
import { dataBucketService } from '../../services/api';
import DataBucketsPanel from '../rulevariables/DataBucketsPanel';
import ProperBucketCreator from './ProperBucketCreator';
import ProperBucketEditor from './ProperBucketEditor';
import ProperVariableManager from './ProperVariableManager';

const ProperDataBucketManager = () => {
    const theme = useTheme();
    const [buckets, setBuckets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [filterTab, setFilterTab] = useState(0); // 0: All, 1: Rule Buckets, 2: Lookup Buckets

    // View states
    const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'variables'
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [createDrawerOpen, setCreateDrawerOpen] = useState(false);

    useEffect(() => {
        fetchBuckets();
    }, []);

    const fetchBuckets = async () => {
        try {
            setLoading(true);
            const data = await dataBucketService.getAllBuckets();
            setBuckets(Array.isArray(data) ? data : []);
            setError(null);
        } catch (err) {
            setError('Failed to load data buckets. Please try again later.');
            console.error('Error fetching buckets:', err);
            setBuckets([]);
        } finally {
            setLoading(false);
        }
    };

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    const handleTabChange = (event, newValue) => {
        setFilterTab(newValue);
    };

    // Filter buckets based on search query and tab selection
    const filteredBuckets = (() => {
        let filtered = [...buckets];

        // Apply tab filter
        switch (filterTab) {
            case 1: // Rule Buckets
                filtered = filtered.filter(bucket => bucket.bucketType === 'RULE_BUCKET' || bucket.type === 'RULE_BUCKET');
                break;
            case 2: // Lookup Buckets
                filtered = filtered.filter(bucket => bucket.bucketType === 'LOOKUP' || bucket.type === 'LOOKUP');
                break;
            default: // All
                break;
        }

        // Apply search filter
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase().trim();
            filtered = filtered.filter(bucket =>
                bucket.name.toLowerCase().includes(query) ||
                bucket.description?.toLowerCase().includes(query) ||
                bucket.tableName?.toLowerCase().includes(query)
            );
        }

        return filtered;
    })();

    const handleCreateBucket = () => {
        setCurrentView('create');
    };

    const handleEditBucket = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('edit');
    };

    const handleManageVariables = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('variables');
    };

    const handleBackToList = () => {
        setCurrentView('list');
        setSelectedBucket(null);
        fetchBuckets(); // Refresh the list
    };

    const handleBucketCreated = (newBucket) => {
        setBuckets(prev => [...prev, newBucket]);
        setCurrentView('list');
    };

    const handleBucketUpdated = (updatedBucket) => {
        setBuckets(prev => prev.map(bucket =>
            bucket.id === updatedBucket.id ? updatedBucket : bucket
        ));
        setCurrentView('list');
    };

    // Custom wrapper that mimics DataBucketsPanel structure but with search bar in correct position
    const CustomDataBucketsPanel = () => {
        return (
            <Box>
                {/* Header section - copied from DataBucketsPanel */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
                    <Box>
                        <Typography
                            variant="h4"
                            component="h1"
                            sx={{
                                fontWeight: 600,
                                color: theme.palette.primary.main,
                                letterSpacing: '-0.5px',
                                mb: 1
                            }}
                        >
                            Data Buckets
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body1" color="text.secondary">
                                Manage your data buckets for rules and lookups. Connect to data sources to use their data in your rules.
                            </Typography>
                            <Box
                                sx={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    height: '24px',
                                    px: 1.5,
                                    py: 0.5,
                                    ml: 2,
                                    borderRadius: '16px',
                                    bgcolor: '#0288D1',
                                    color: '#FFFFFF',
                                    fontSize: '13px',
                                    fontWeight: 600,
                                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                                }}
                            >
                                {buckets.length} buckets
                            </Box>
                        </Box>

                        {/* Bucket Type Legend */}
                        {buckets.length > 0 && (
                            <Box sx={{ display: 'flex', gap: 2, mt: 2, alignItems: 'center' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Box
                                        sx={{
                                            display: 'inline-flex',
                                            alignItems: 'center',
                                            height: '22px',
                                            px: 1,
                                            py: 0.5,
                                            mr: 1,
                                            borderRadius: '16px',
                                            bgcolor: '#E3F2FD',
                                            color: '#1565C0',
                                            fontSize: '0.7rem',
                                            fontWeight: 600
                                        }}
                                    >
                                        Rule
                                    </Box>
                                    <Typography variant="caption">Transaction data</Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Box
                                        sx={{
                                            display: 'inline-flex',
                                            alignItems: 'center',
                                            height: '22px',
                                            px: 1,
                                            py: 0.5,
                                            mr: 1,
                                            borderRadius: '16px',
                                            bgcolor: '#E8F5E9',
                                            color: '#2E7D32',
                                            fontSize: '0.7rem',
                                            fontWeight: 600
                                        }}
                                    >
                                        Lookup
                                    </Box>
                                    <Typography variant="caption">Reference data for enrichment</Typography>
                                </Box>
                            </Box>
                        )}
                    </Box>
                    <Button
                        variant="contained"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={handleCreateBucket}
                        sx={{
                            borderRadius: '28px',
                            px: 3,
                            py: 1,
                            boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                            '&:hover': {
                                transform: 'translateY(-1px)',
                                boxShadow: '0 6px 20px rgba(0,118,255,0.39)',
                            }
                        }}
                    >
                        Create New Bucket
                    </Button>
                </Box>

                {/* Search and Filters - positioned after header */}
                <Paper
                    elevation={0}
                    sx={{
                        mb: 2,
                        p: 2,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 1
                    }}
                >
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} md={6}>
                            <TextField
                                fullWidth
                                size="small"
                                placeholder="Search by bucket name, description, or table..."
                                value={searchQuery}
                                onChange={handleSearchChange}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon sx={{ color: theme.palette.text.secondary, fontSize: '1.2rem' }} />
                                        </InputAdornment>
                                    ),
                                }}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        fontSize: '0.875rem',
                                        '& fieldset': {
                                            borderColor: theme.palette.grey[300]
                                        }
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
                                <Tabs
                                    value={filterTab}
                                    onChange={handleTabChange}
                                    sx={{
                                        minHeight: 36,
                                        '& .MuiTab-root': {
                                            minHeight: 36,
                                            fontSize: '0.8rem',
                                            fontWeight: 500,
                                            textTransform: 'none',
                                            color: theme.palette.text.secondary,
                                            '&.Mui-selected': {
                                                color: theme.palette.primary.main,
                                                fontWeight: 600
                                            }
                                        },
                                        '& .MuiTabs-indicator': {
                                            height: 2
                                        }
                                    }}
                                >
                                    <Tab label="All" />
                                    <Tab label="Rule Buckets" />
                                    <Tab label="Lookup Buckets" />
                                </Tabs>
                            </Box>
                        </Grid>
                    </Grid>
                </Paper>

                {/* DataBucketsPanel with hidden header */}
                <Box sx={{
                    '& > div:first-child': {
                        display: 'none'
                    }
                }}>
                    <DataBucketsPanel
                        initialBuckets={filteredBuckets}
                        createDrawerOpen={createDrawerOpen}
                        setCreateDrawerOpen={(open) => {
                            if (open) {
                                handleCreateBucket(); // Use our wizard instead
                            } else {
                                setCreateDrawerOpen(false);
                            }
                        }}
                        onEditBucket={handleEditBucket}
                        onManageVariables={handleManageVariables}
                    />
                </Box>
            </Box>
        );
    };

    const renderContent = () => {
        switch (currentView) {
            case 'create':
                return (
                    <ProperBucketCreator
                        onCancel={handleBackToList}
                        onBucketCreated={handleBucketCreated}
                    />
                );

            case 'edit':
                return (
                    <ProperBucketEditor
                        bucket={selectedBucket}
                        onCancel={handleBackToList}
                        onBucketUpdated={handleBucketUpdated}
                    />
                );

            case 'variables':
                return (
                    <ProperVariableManager
                        bucket={selectedBucket}
                        onBack={handleBackToList}
                    />
                );

            default:
                return <CustomDataBucketsPanel />;
        }
    };

    // Match the exact structure of RuleVariableRegistryPage
    return (
        <Box sx={{ width: '100%', height: '100%', backgroundColor: 'transparent' }}>
            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
            )}

            {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                    <CircularProgress />
                </Box>
            ) : (
                renderContent()
            )}
        </Box>
    );
};

export default ProperDataBucketManager;
