package com.progize.tms.service;

import com.progize.tms.repository.entity.CaseHistory;
import com.progize.tms.repository.entity.WorkflowCase;
import java.util.List;

/** Service for managing case history. */
public interface CaseHistoryService {

  /**
   * Add a history entry to a workflow case.
   *
   * @param workflowCase the workflow case
   * @param processInstanceId the process instance ID
   * @param taskId the task ID
   * @param taskName the task name
   * @param action the action performed
   * @param oldStatus the old status
   * @param newStatus the new status
   * @param details additional details
   * @param createdBy the user ID who performed the action
   * @return the created history entry
   */
  CaseHistory addHistoryEntry(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      String taskName,
      String action,
      String oldStatus,
      String newStatus,
      String details,
      Long createdBy);

  /**
   * Get all history entries for a workflow case.
   *
   * @param workflowCase the workflow case
   * @return list of history entries
   */
  List<CaseHistory> getHistoryByCase(WorkflowCase workflowCase);

  /**
   * Get all history entries for a task.
   *
   * @param taskId the task ID
   * @return list of history entries
   */
  List<CaseHistory> getHistoryByTask(String taskId);

  /**
   * Get all history entries for a process instance.
   *
   * @param processInstanceId the process instance ID
   * @return list of history entries
   */
  List<CaseHistory> getHistoryByProcessInstance(String processInstanceId);
}
