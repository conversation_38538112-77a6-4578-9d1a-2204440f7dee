import React, { useState } from 'react';
import {
  Box,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Tooltip,
  Stack,
  useTheme,
  Button
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import InfoIcon from '@mui/icons-material/Info';
import AddIcon from '@mui/icons-material/Add';
import ExpressionInput from '../common/ExpressionInput';

const AggregateCondition = ({
  condition,
  onChange,
  onRemove,
  fields,
  operators,
  aggregateFunctions,
  isEditable = true,
  getFieldDisplayName
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Use any field from all fields for aggregate conditions
  const displayFields = fields;

  // Only get aggregatable fields for aggregations
  const aggregatableFields = displayFields.filter(field => field.aggregatable);

  // Only get numeric operators for aggregations
  const numericOperators = operators.filter(op =>
    op.applicableTypes && op.applicableTypes.includes('number')
  );

  // Check if field is numeric type (number, bigdecimal, integer)
  const isNumericType = (type) => {
    if (!type) return false;
    return ['number', 'bigdecimal', 'integer', 'int', 'long', 'double', 'float'].includes(type.toLowerCase());
  };

  // Find the selected field object from all fields
  const selectedField = fields.find(field => field.id === condition.fieldId) ||
                        fields.find(field => field.name === condition.field) || {};

  // Time period options (in days)
  const periodOptions = [
    { value: 1, label: '1 day' },
    { value: 7, label: '7 days' },
    { value: 30, label: '30 days' },
    { value: 90, label: '90 days' },
    { value: 180, label: '180 days' },
    { value: 365, label: '365 days' }
  ];

  // All fields are considered in the current tab since we've removed tabs
  const isFieldInCurrentTab = true;

  // Add a new property for filter condition
  const [showFilter, setShowFilter] = useState(!!condition.filterCondition);

  // Helper to get field display name safely
  const getFieldNameSafely = (fieldId) => {
    if (!fieldId) return 'unknown field';
    if (getFieldDisplayName) {
      return getFieldDisplayName(fieldId);
    }
    return fields.find(f => f.id === fieldId)?.name || 'unknown field';
  };

  // Helper to get operator display name safely
  const getOperatorNameSafely = (operatorId) => {
    if (!operatorId) return '';
    const op = operators.find(op => op.id === operatorId);
    return op ? op.name.toLowerCase() : operatorId;
  };

  // Helper function to get display texts
  const getFunctionDisplayName = () => {
    return aggregateFunctions.find(f => f.id === condition.function)?.name || condition.function;
  };

  const getOperatorDisplayName = () => {
    return getOperatorNameSafely(condition.operator);
  };

  const getPeriodDisplayName = () => {
    return periodOptions.find(p => p.value === condition.period)?.label || `${condition.period} days`;
  };

  // Get a human-readable field name with category info if needed
  const displayFieldName = getFieldNameSafely(condition.fieldId || condition.field);

  // Default field if not present
  React.useEffect(() => {
    // If we have a field name but no fieldId, try to find the matching field and update the fieldId
    if (condition.field && !condition.fieldId && fields.length > 0) {
      const matchingField = fields.find(field => field.name === condition.field);
      if (matchingField) {
        // Update the condition with the fieldId
        onChange({
          ...condition,
          fieldId: matchingField.id
        });
        return; // Exit early since we've updated the condition
      }
    }

    // If no field is selected at all, default to the first aggregatable field
    if (!condition.fieldId && !condition.field && aggregatableFields.length > 0) {
      onChange({
        ...condition,
        id: condition.id, // Explicitly preserve the ID
        field: aggregatableFields[0].name,
        fieldId: aggregatableFields[0].id
      });
    }
  }, [condition, fields, aggregatableFields, onChange]);

  const handleFunctionChange = (e) => {
    console.log('Function change event:', e.target.value);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      function: e.target.value
    };

    console.log('Updated condition with new function:', updatedCondition);
    onChange(updatedCondition);
  };

  const handleFieldChange = (e) => {
    console.log('Field change event:', e.target.value);

    // Get the new field
    const newFieldId = e.target.value;
    const newField = fields.find(field => field.id === newFieldId);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      field: newField?.name, // Use name for rule creation
      fieldId: newFieldId, // Store ID for UI selection
      // Reset any field-dependent values
      threshold: ''
    };

    console.log('Updated condition with new field:', updatedCondition);
    onChange(updatedCondition);
  };

  const handlePeriodChange = (e) => {
    console.log('Period change event:', e.target.value);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      period: e.target.value
    };

    console.log('Updated condition with new period:', updatedCondition);
    onChange(updatedCondition);
  };

  const handleStartOffsetChange = (e) => {
    console.log('Start offset change event:', e.target.value);

    const value = parseInt(e.target.value, 10);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      startOffset: isNaN(value) ? 0 : value
    };

    console.log('Updated condition with new start offset:', updatedCondition);
    onChange(updatedCondition);
  };

  const handleOperatorChange = (e) => {
    console.log('Operator change event:', e.target.value);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      operator: e.target.value
    };

    console.log('Updated condition with new operator:', updatedCondition);
    onChange(updatedCondition);
  };

  const handleThresholdChange = (e) => {
    console.log('Threshold change event:', e.target.value);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      threshold: e.target.value,
      // Add a flag to identify if this is an expression value
      isExpression: e.target.value && e.target.value.includes('${')
    };

    console.log('Updated condition with new threshold:', updatedCondition);
    onChange(updatedCondition);
  };

  // Handler for entity field change
  const handleEntityFieldChange = (e) => {
    console.log('Entity field change event:', e.target.value);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      entityField: e.target.value
    };

    console.log('Updated condition with new entity field:', updatedCondition);
    onChange(updatedCondition);
  };

  // Handler for toggling filter visibility
  const handleToggleFilter = () => {
    setShowFilter(!showFilter);

    // If hiding filter, remove filter condition
    if (showFilter && condition.filterCondition) {
      const updatedCondition = {
        ...condition,
        id: condition.id,
        type: 'AGGREGATE', // Always ensure type is set
        filterCondition: null
      };
      onChange(updatedCondition);
    }
  };

  // Handler for filter field change
  const handleFilterFieldChange = (e) => {
    const fieldId = e.target.value;
    const field = fields.find(f => f.id === fieldId);
    const fieldType = field?.dataTypeCode || field?.type || 'number';

    // Find compatible operators
    const compatibleOperators = operators.filter(op =>
      op.applicableTypes && op.applicableTypes.includes(fieldType)
    );

    const defaultOperator = compatibleOperators.length > 0 ? compatibleOperators[0].id : '';

    const filterCondition = {
      field: fieldId,
      operator: defaultOperator,
      value: ''
    };

    const updatedCondition = {
      ...condition,
      id: condition.id,
      type: 'AGGREGATE', // Always ensure type is set
      filterCondition: filterCondition
    };

    onChange(updatedCondition);
  };

  // Handler for filter operator change
  const handleFilterOperatorChange = (e) => {
    if (!condition.filterCondition) return;

    const updatedCondition = {
      ...condition,
      id: condition.id,
      type: 'AGGREGATE', // Always ensure type is set
      filterCondition: {
        ...condition.filterCondition,
        operator: e.target.value
      }
    };

    onChange(updatedCondition);
  };

  // Handler for filter value change
  const handleFilterValueChange = (e) => {
    console.log('Filter value change event:', e.target.value);

    // Create a completely new filter condition
    const updatedFilterCondition = {
      ...condition.filterCondition,
      value: e.target.value,
      // Track if this is an expression value
      isExpression: e.target.value && e.target.value.includes('${')
    };

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: 'AGGREGATE', // Always ensure type is set
      filterCondition: updatedFilterCondition
    };

    console.log('Updated condition with new filter value:', updatedCondition);
    onChange(updatedCondition);
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        position: 'relative',
        borderLeft: '4px solid #3182CE',
        backgroundColor: theme.palette.background.paper,
        opacity: isEditable ? 1 : 0.8
      }}
    >
      <Box sx={{ position: 'absolute', top: 8, right: 8, display: 'flex' }}>
        <IconButton
          size="small"
          color="error"
          onClick={onRemove}
        >
          <DeleteIcon />
        </IconButton>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          Aggregate Condition
        </Typography>
        <Tooltip title="Define a condition based on an aggregate function over a period of time">
          <IconButton size="small" color="info">
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Analyze historical patterns by aggregating a variable over time
          </Typography>
        </Grid>

        {/* Function and Field row */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Aggregation Function</InputLabel>
            <Select
              value={condition.function || ''}
              label="Aggregation Function"
              onChange={handleFunctionChange}
              disabled={!isEditable}
            >
              {aggregateFunctions.map(func => (
                <MenuItem
                  key={func.id}
                  value={func.id}
                >
                  <Tooltip title={func.description || ''} placement="right">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {func.name}
                      <InfoIcon fontSize="inherit" sx={{ ml: 1, opacity: 0.5, fontSize: '16px' }} />
                    </Box>
                  </Tooltip>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Field to Aggregate</InputLabel>
            <Select
              value={condition.fieldId || ''}
              label="Field to Aggregate"
              onChange={handleFieldChange}
              disabled={!isEditable || !isFieldInCurrentTab}
              error={!isFieldInCurrentTab}
            >
              {aggregatableFields.map(field => (
                <MenuItem
                  key={field.id}
                  value={field.id}
                  sx={{
                    // Add a subtle background color for enriched variables
                    backgroundColor: Boolean(field.isEnriched) ? 'rgba(0, 128, 0, 0.05)' : 'inherit',
                    // Add a left border based on variable type
                    borderLeft: Boolean(field.isEnriched) ? '3px solid #4CAF50' : '3px solid #3182CE'
                  }}
                  // Add onClick to log field details for debugging
                  onClick={(e) => {
                    // Don't interfere with normal selection behavior
                    if (e.ctrlKey || e.metaKey) {
                      e.stopPropagation();
                      console.log('Field details:', field);
                      console.log('isEnriched value:', field.isEnriched);
                      console.log('sourceBucketName:', field.sourceBucketName);
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Box sx={{ flexGrow: 1 }}>
                      {field.name}
                      {field.category && ` (${field.category})`}
                    </Box>
                    {Boolean(field.isEnriched) ? (
                      <Tooltip title={field.sourceBucketName ? `Enriched from ${field.sourceBucketName}` : 'Enriched variable'}>
                        <Box
                          sx={{
                            ml: 1,
                            fontSize: '0.75rem',
                            color: 'success.main',
                            border: '1px solid',
                            borderColor: 'success.main',
                            borderRadius: '4px',
                            px: 0.5,
                            py: 0.1,
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          Enriched
                        </Box>
                      </Tooltip>
                    ) : (
                      <Tooltip title="Variable from main bucket">
                        <Box
                          sx={{
                            ml: 1,
                            fontSize: '0.75rem',
                            color: 'primary.main',
                            border: '1px solid',
                            borderColor: 'primary.main',
                            borderRadius: '4px',
                            px: 0.5,
                            py: 0.1,
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          Main
                        </Box>
                      </Tooltip>
                    )}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Time period row */}
        <Grid item xs={12}>
          <Box sx={{
            p: 1.5,
            borderRadius: 1,
            bgcolor: isDarkMode ? 'rgba(66, 153, 225, 0.08)' : 'rgba(235, 248, 255, 0.8)',
            border: '1px solid',
            borderColor: isDarkMode ? 'rgba(66, 153, 225, 0.2)' : 'rgba(66, 153, 225, 0.3)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Typography variant="body2" fontWeight={500}>Time period for analysis:</Typography>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <Select
                value={condition.period || 30}
                onChange={handlePeriodChange}
                variant="outlined"
                size="small"
                disabled={!isEditable}
                sx={{ height: 36 }}
              >
                {periodOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Grid>

        {/* Start Offset row */}
        <Grid item xs={12}>
          <Box sx={{
            p: 1.5,
            borderRadius: 1,
            bgcolor: isDarkMode ? 'rgba(144, 202, 249, 0.08)' : 'rgba(232, 244, 253, 0.8)',
            border: '1px solid',
            borderColor: isDarkMode ? 'rgba(144, 202, 249, 0.2)' : 'rgba(144, 202, 249, 0.3)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Typography variant="body2" fontWeight={500}>Start offset (days in the past):</Typography>
            <TextField
              value={condition.startOffset || 0}
              onChange={handleStartOffsetChange}
              variant="outlined"
              size="small"
              type="number"
              disabled={!isEditable}
              InputProps={{
                inputProps: { min: 0 }
              }}
              sx={{ width: 150 }}
            />
          </Box>
          <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5, display: 'block' }}>
            0 = start from today, 7 = start from 7 days ago
          </Typography>
        </Grid>

        {/* Comparison row */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Comparison</InputLabel>
            <Select
              value={condition.operator || ''}
              label="Comparison"
              onChange={(e) => {
                console.log('Direct operator change event:', e.target.value);
                handleOperatorChange(e);
              }}
              disabled={!isEditable}
            >
              {numericOperators.map(op => (
                <MenuItem key={op.id} value={op.id}>
                  {op.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <ExpressionInput
            label="Threshold"
            value={condition.threshold || ''}
            onChange={(e) => {
              console.log('Direct threshold change event:', e.target.value);
              handleThresholdChange(e);
            }}
            // Allow all variable types for threshold expressions, not just numbers
            type="text"
            fieldType=""
            disabled={!isEditable}
            placeholder="Enter value or ${variable} expression"
            helperText="You can use expressions like ${incomeThreshold} or numeric values"
          />
        </Grid>

        {/* Entity Field */}
        <Grid item xs={12} md={6}>
          <ExpressionInput
            label="Entity Field"
            value={condition.entityField || 'customerId'}
            onChange={(e) => {
              console.log('Direct entity field change event:', e.target.value);
              handleEntityFieldChange(e);
            }}
            type="text"
            fieldType=""
            disabled={!isEditable}
            placeholder="Enter field name or ${variable} expression"
            helperText="Field to identify the entity (e.g., customerId or ${entityIdField})"
          />
        </Grid>

        {/* Filter condition */}
        {showFilter && (
          <Grid item xs={12}>
            <Box sx={{
              p: 2,
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'primary.light',
              backgroundColor: isDarkMode ? 'rgba(25, 118, 210, 0.08)' : 'rgba(25, 118, 210, 0.05)'
            }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1.5 }}>
                <Typography variant="subtitle2" fontWeight={600} color="primary">
                  Filter Transactions
                </Typography>
                <IconButton
                  size="small"
                  color="error"
                  onClick={handleToggleFilter}
                  title="Remove filter"
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Stack>

              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Field</InputLabel>
                    <Select
                      value={condition.filterCondition?.field || ''}
                      label="Field"
                      onChange={handleFilterFieldChange}
                      disabled={!isEditable}
                    >
                      {fields.map(field => (
                        <MenuItem key={field.id} value={field.id}>
                          {field.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Operator</InputLabel>
                    <Select
                      value={condition.filterCondition?.operator || ''}
                      label="Operator"
                      onChange={(e) => {
                        console.log('Direct filter operator change event:', e.target.value);
                        handleFilterOperatorChange(e);
                      }}
                      disabled={!isEditable}
                    >
                      {operators.map(op => (
                        <MenuItem key={op.id} value={op.id}>
                          {op.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <ExpressionInput
                    label="Value"
                    value={condition.filterCondition?.value || ''}
                    onChange={(e) => {
                      console.log('Direct filter value change event:', e.target.value);
                      handleFilterValueChange(e);
                    }}
                    // Pass the field type for filter value
                    fieldType={fields.find(f => f.id === condition.filterCondition?.field)?.dataTypeCode || fields.find(f => f.id === condition.filterCondition?.field)?.type || 'string'}
                    // Always use text type for expression input
                    type="text"
                    disabled={!isEditable}
                  />
                </Grid>
              </Grid>
            </Box>
          </Grid>
        )}

        {!showFilter && (
          <Grid item xs={12}>
            <Box sx={{
              p: 1.5,
              borderRadius: 1,
              border: '1px dashed',
              borderColor: 'divider',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)'
            }}>
              <Typography variant="body2" color="text.secondary">
                Only count transactions that match a specific condition
              </Typography>
              <Button
                size="small"
                variant="outlined"
                color="primary"
                onClick={handleToggleFilter}
                startIcon={<AddIcon />}
              >
                Add Filter
              </Button>
            </Box>
          </Grid>
        )}

        {/* Preview of what the condition means */}
        <Box sx={{
          mt: 3,
          p: 2,
          borderRadius: 1,
          border: `1px solid ${theme.palette.divider}`,
          backgroundColor: isDarkMode ? 'rgba(79, 209, 197, 0.08)' : 'rgba(79, 209, 197, 0.05)',
        }}>
          <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
            <Box
              component="span"
              sx={{
                p: 0.5,
                px: 1,
                bgcolor: 'primary.main',
                color: 'white',
                borderRadius: 1,
                fontSize: '0.75rem',
                fontWeight: 'bold'
              }}
            >
              RULE SUMMARY
            </Box>
          </Stack>

          <Typography variant="body2" sx={{ fontWeight: 500, lineHeight: 1.5 }}>
            When the <strong>{getFunctionDisplayName().toLowerCase()}</strong> of <strong>{displayFieldName}</strong> over
            the last <strong>{getPeriodDisplayName()}</strong> is <strong>{getOperatorDisplayName().toLowerCase()} {condition.threshold}</strong>
            {' '}for entity <strong>{condition.entityField || 'customerId'}</strong>
            {condition.filterCondition && (
              <>
                {' '}where <strong>{getFieldNameSafely(condition.filterCondition.field)}</strong> is{' '}
                <strong>{getOperatorNameSafely(condition.filterCondition.operator)} {condition.filterCondition.value}</strong>
              </>
            )}
          </Typography>

          {/* Simple example explanation */}
          <Box sx={{ mt: 1.5, fontSize: '0.85rem', color: 'text.secondary' }}>
            {condition.function === 'sum' && (
              <Typography variant="body2">
                Example: Triggers when the total {displayFieldName} over {getPeriodDisplayName()} {getOperatorDisplayName().toLowerCase()} {condition.threshold}
                {condition.filterCondition && (
                  <>, but only including transactions where {getFieldNameSafely(condition.filterCondition.field)} is {getOperatorNameSafely(condition.filterCondition.operator)} {condition.filterCondition.value}</>
                )}
              </Typography>
            )}
            {condition.function === 'avg' && (
              <Typography variant="body2">
                Example: Triggers when the average {displayFieldName} over {getPeriodDisplayName()} {getOperatorDisplayName().toLowerCase()} {condition.threshold}
                {condition.filterCondition && (
                  <>, but only including transactions where {getFieldNameSafely(condition.filterCondition.field)} is {getOperatorNameSafely(condition.filterCondition.operator)} {condition.filterCondition.value}</>
                )}
              </Typography>
            )}
            {condition.function === 'count' && (
              <Typography variant="body2">
                Example: Triggers when the number of transactions over {getPeriodDisplayName()} {getOperatorDisplayName().toLowerCase()} {condition.threshold}
                {condition.filterCondition && (
                  <>, but only counting transactions where {getFieldNameSafely(condition.filterCondition.field)} is {getOperatorNameSafely(condition.filterCondition.operator)} {condition.filterCondition.value}</>
                )}
              </Typography>
            )}
            {condition.function === 'max' && (
              <Typography variant="body2">
                Example: Triggers when the highest {displayFieldName} over {getPeriodDisplayName()} {getOperatorDisplayName().toLowerCase()} {condition.threshold}
                {condition.filterCondition && (
                  <>, but only considering transactions where {getFieldNameSafely(condition.filterCondition.field)} is {getOperatorNameSafely(condition.filterCondition.operator)} {condition.filterCondition.value}</>
                )}
              </Typography>
            )}
            {condition.function === 'min' && (
              <Typography variant="body2">
                Example: Triggers when the lowest {displayFieldName} over {getPeriodDisplayName()} {getOperatorDisplayName().toLowerCase()} {condition.threshold}
                {condition.filterCondition && (
                  <>, but only considering transactions where {getFieldNameSafely(condition.filterCondition.field)} is {getOperatorNameSafely(condition.filterCondition.operator)} {condition.filterCondition.value}</>
                )}
              </Typography>
            )}
          </Box>
        </Box>
      </Grid>

    </Paper>
  );
};

export default AggregateCondition;
