package com.progize.tms.controllers.workflow;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.progize.tms.controllers.workflow.dto.FormPropertyDTO;
import com.progize.tms.controllers.workflow.dto.FormPropertyValueDTO;
import com.progize.tms.controllers.workflow.dto.TaskDTO;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.repository.entity.WorkflowType;
import com.progize.tms.service.CaseAttachmentService;
import com.progize.tms.service.CaseCommentService;
import com.progize.tms.service.CaseHistoryService;
import com.progize.tms.service.WorkflowCaseService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.FormService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.form.FormProperty;
import org.flowable.engine.form.FormType;
import org.flowable.engine.form.TaskFormData;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/** Controller for compliance workflow operations. */
@RestController
@RequestMapping("/api/workflow/compliance")
@RequiredArgsConstructor
@Slf4j
public class ComplianceWorkflowController {

  private final TaskService taskService;
  private final FormService formService;
  private final RuntimeService runtimeService;
  private final WorkflowCaseService workflowCaseService;
  private final CaseCommentService caseCommentService;
  private final CaseAttachmentService caseAttachmentService;
  private final CaseHistoryService caseHistoryService;

  /**
   * Gets the current user ID from the security context.
   *
   * @return the user ID
   */
  private Long getCurrentUserId() {
    try {
      Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
      if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
        String username = ((UserDetails) authentication.getPrincipal()).getUsername();
        // In a real implementation, you would look up the user ID by username
        // For now, we'll return a placeholder value
        return 1L; // Admin user ID
      }
      return 1L; // Default to admin user ID
    } catch (Exception e) {
      log.warn("Could not determine current user ID", e);
      return 1L; // Default to admin user ID if error
    }
  }

  /**
   * Complete a generic task with form properties.
   *
   * @param taskId Task ID
   * @param formData Map of form property values
   * @param attachments Optional attachments
   * @return Response entity
   */
  @PostMapping("/tasks/{taskId}/complete")
  public ResponseEntity<Void> completeTask(
      @PathVariable String taskId,
      @RequestParam Map<String, String> formData,
      @RequestParam(required = false) List<MultipartFile> attachments) {

    log.info("Completing task {} with form data: {}", taskId, formData);

    try {
      // Validate task exists
      Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
      if (task == null) {
        log.error("Task not found: {}", taskId);
        return ResponseEntity.notFound().build();
      }

      // Get the workflow case
      String processInstanceId = task.getProcessInstanceId();
      WorkflowCase workflowCase = workflowCaseService.getCaseByProcessInstanceId(processInstanceId);
      if (workflowCase == null) {
        log.error("Workflow case not found for process instance: {}", processInstanceId);
        return ResponseEntity.notFound().build();
      }

      // Get the current user ID for tracking who performed the action
      Long currentUserId = getCurrentUserId();
      String taskName = task.getName();
      String oldStatus = workflowCase.getStatus();

      // Prepare variables for task completion
      Map<String, Object> variables = new HashMap<>();

      // Add all form data as variables
      for (Map.Entry<String, String> entry : formData.entrySet()) {
        variables.put(entry.getKey(), entry.getValue());
      }

      // Save comments if present in form data
      if (formData.containsKey("comments")) {
        String commentText = formData.get("comments");
        if (commentText != null && !commentText.trim().isEmpty()) {
          caseCommentService.addComment(
              workflowCase, processInstanceId, taskId, commentText, "USER", currentUserId);
          log.info("Added comment to case {}: {}", workflowCase.getId(), commentText);
        }
      }

      // Handle attachments if provided - save to our dedicated table
      if (attachments != null && !attachments.isEmpty()) {
        try {
          // Save attachments to our dedicated table
          caseAttachmentService.addAttachments(
              workflowCase, processInstanceId, taskId, attachments, currentUserId);
          log.info("Added {} attachments to case {}", attachments.size(), workflowCase.getId());

          // Also create attachments in Flowable for backward compatibility
          for (int i = 0; i < attachments.size(); i++) {
            MultipartFile file = attachments.get(i);
            String attachmentName = "attachment_" + i + "_" + file.getOriginalFilename();

            // Create attachment in Flowable
            taskService.createAttachment(
                file.getContentType(),
                taskId,
                processInstanceId,
                attachmentName,
                "Uploaded attachment: " + file.getOriginalFilename(),
                file.getInputStream());

            log.info("Added attachment {} to task {} in Flowable", attachmentName, taskId);
          }
        } catch (IOException e) {
          log.error("Error handling attachments for task {}: {}", taskId, e.getMessage(), e);
          return ResponseEntity.internalServerError().build();
        }
      }

      // Complete the task with variables
      taskService.complete(taskId, variables);
      log.info("Task {} completed successfully", taskId);

      // Update workflow case status based on the current active task
      updateWorkflowCaseStatus(workflowCase, processInstanceId);
      String newStatus = workflowCase.getStatus();

      // Add history entry for task completion
      caseHistoryService.addHistoryEntry(
          workflowCase,
          processInstanceId,
          taskId,
          taskName,
          "TASK_COMPLETE",
          oldStatus,
          newStatus,
          "Task completed: " + taskName,
          currentUserId);

      // Update the workflow case metadata
      try {
        Map<String, Object> metadata = new HashMap<>();
        // Add relevant form data to metadata
        for (Map.Entry<String, String> entry : formData.entrySet()) {
          metadata.put(entry.getKey(), entry.getValue());
        }

        workflowCaseService.updateCaseMetadata(workflowCase.getId(), metadata);
        log.info("Updated workflow case metadata for case {}", workflowCase.getId());
      } catch (Exception e) {
        log.error("Error updating workflow case metadata", e);
        // Continue even if metadata update fails
      }

      return ResponseEntity.ok().build();
    } catch (Exception e) {
      log.error("Unexpected error completing task {}: {}", taskId, e.getMessage(), e);
      return ResponseEntity.internalServerError().build();
    }
  }

  /**
   * Get all compliance tasks.
   *
   * @return List of compliance tasks as DTOs
   */
  @GetMapping("/tasks")
  public ResponseEntity<List<TaskDTO>> getComplianceTasks() {
    try {
      Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
      String username = authentication.getName();
      log.info("Fetching compliance tasks for user {}", username);

      // Get user roles from the authentication token
      List<String> userRoles =
          authentication.getAuthorities().stream()
              .map(GrantedAuthority::getAuthority)
              .collect(Collectors.toList());

      log.info("User {} has roles: {}", username, userRoles);

      // Start building the query
      org.flowable.task.api.TaskQuery query =
          taskService
              .createTaskQuery()
              .processDefinitionKey(WorkflowType.COMPLIANCE.getProcessDefinitionKey())
              .includeProcessVariables();

      // Filter by user roles (candidate groups)
      if (!userRoles.isEmpty()) {
        // For multiple roles, we need to use OR conditions
        if (userRoles.size() == 1) {
          query.taskCandidateGroup(userRoles.get(0).replaceFirst("ROLE_", "").toLowerCase());
        } else {
          query.or();
          for (String role : userRoles) {
            query.taskCandidateGroup(role.replaceFirst("ROLE_", "").toLowerCase());
          }
          query.endOr();
        }
      } else {
        // Default to tasks assigned directly to the user
        query.taskAssignee(username);
      }

      // Execute the query
      List<Task> tasks = query.orderByTaskCreateTime().desc().list();
      log.info("Found {} tasks for user {}", tasks.size(), username);

      // Convert Flowable Task objects to DTOs to avoid lazy loading issues
      List<TaskDTO> taskDTOs = new ArrayList<>();
      for (Task task : tasks) {
        // Get variables safely within the command context
        Map<String, Object> variables = taskService.getVariables(task.getId());

        // Get form properties from the BPMN model
        List<FormPropertyDTO> formProperties = getFormPropertiesForTask(task);

        taskDTOs.add(TaskDTO.fromTask(task, variables, formProperties));
      }

      return ResponseEntity.ok(taskDTOs);
    } catch (Exception e) {
      log.error("Error fetching compliance tasks: {}", e.getMessage(), e);
      return ResponseEntity.internalServerError().build();
    }
  }

  /**
   * Complete an officer review task.
   *
   * @param taskId Task ID
   * @param decision Officer decision
   * @param comments Officer comments
   * @param attachments Optional attachments
   * @return Response entity
   */
  @PostMapping("/tasks/{taskId}/officer-review")
  public ResponseEntity<Void> completeOfficerReview(
      @PathVariable String taskId,
      @RequestParam String decision,
      @RequestParam String comments,
      @RequestParam(required = false) List<MultipartFile> attachments) {

    log.info("Completing officer review for task {}", taskId);

    // Get the task to verify it exists and get the process instance ID
    Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
    if (task == null) {
      return ResponseEntity.notFound().build();
    }

    // Get the workflow case to update its metadata
    WorkflowCase workflowCase =
        workflowCaseService.getCaseByProcessInstanceId(task.getProcessInstanceId());

    // Prepare variables for task completion
    Map<String, Object> variables = new HashMap<>();
    variables.put("officerDecision", decision);
    variables.put("officerComments", comments);

    // Handle attachments if needed
    // This would typically involve storing them and adding references to the variables

    // Complete the task with variables
    taskService.complete(taskId, variables);

    // Update workflow case status based on the current active task
    updateWorkflowCaseStatus(workflowCase, task.getProcessInstanceId());

    // Update the workflow case metadata if needed
    try {
      Map<String, Object> metadata = new HashMap<>();
      metadata.put("officerDecision", decision);
      metadata.put("officerComments", comments);

      workflowCaseService.updateCaseMetadata(workflowCase.getId(), metadata);
    } catch (Exception e) {
      log.error("Error updating workflow case metadata", e);
      // Continue even if metadata update fails
    }

    return ResponseEntity.ok().build();
  }

  /**
   * Complete a manager review task.
   *
   * @param taskId Task ID
   * @param managerDecision Manager decision
   * @param managerComments Manager comments
   * @param attachments Optional attachments
   * @return Response entity
   */
  @PostMapping("/tasks/{taskId}/manager-review")
  public ResponseEntity<Void> completeManagerReview(
      @PathVariable String taskId,
      @RequestParam String managerDecision,
      @RequestParam String managerComments,
      @RequestParam(required = false) List<MultipartFile> attachments) {

    log.info("Completing manager review for task {}", taskId);

    // Get the task to verify it exists and get the process instance ID
    Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
    if (task == null) {
      return ResponseEntity.notFound().build();
    }

    // Get the workflow case to update its metadata
    String processInstanceId = task.getProcessInstanceId();
    WorkflowCase workflowCase = workflowCaseService.getCaseByProcessInstanceId(processInstanceId);
    if (workflowCase == null) {
      log.error("Workflow case not found for process instance: {}", processInstanceId);
      return ResponseEntity.notFound().build();
    }

    // Prepare variables for task completion
    Map<String, Object> variables = new HashMap<>();
    variables.put("managerDecision", managerDecision);
    variables.put("managerComments", managerComments);

    // Handle attachments if needed
    if (attachments != null && !attachments.isEmpty()) {
      try {
        for (int i = 0; i < attachments.size(); i++) {
          MultipartFile file = attachments.get(i);
          String attachmentName = "attachment_" + i + "_" + file.getOriginalFilename();

          // Create attachment in Flowable
          taskService.createAttachment(
              file.getContentType(),
              taskId,
              processInstanceId,
              attachmentName,
              "Uploaded attachment: " + file.getOriginalFilename(),
              file.getInputStream());

          log.info("Added attachment {} to task {}", attachmentName, taskId);
        }
      } catch (Exception e) {
        log.error("Error handling attachments for task {}: {}", taskId, e.getMessage(), e);
        return ResponseEntity.internalServerError().build();
      }
    }

    // Complete the task with variables
    taskService.complete(taskId, variables);

    // Update workflow case status based on the current active task
    updateWorkflowCaseStatus(workflowCase, processInstanceId);

    // Update the workflow case metadata if needed
    try {
      Map<String, Object> metadata = new HashMap<>();
      metadata.put("managerDecision", managerDecision);
      metadata.put("managerComments", managerComments);

      workflowCaseService.updateCaseMetadata(workflowCase.getId(), metadata);
    } catch (Exception e) {
      log.error("Error updating workflow case metadata", e);
      // Continue even if metadata update fails
    }

    return ResponseEntity.ok().build();
  }

  /**
   * Get details of a specific task.
   *
   * @param taskId Task ID
   * @return Task details as DTO
   */
  @GetMapping("/tasks/{taskId}")
  public ResponseEntity<TaskDTO> getTaskDetails(@PathVariable String taskId) {
    Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

    if (task == null) {
      return ResponseEntity.notFound().build();
    }

    // Get variables safely within the command context
    Map<String, Object> variables = taskService.getVariables(taskId);

    // Get form properties from the BPMN model
    List<FormPropertyDTO> formProperties = getFormPropertiesForTask(task);

    // Convert to DTO to avoid lazy loading issues
    TaskDTO taskDTO = TaskDTO.fromTask(task, variables, formProperties);

    return ResponseEntity.ok(taskDTO);
  }

  /**
   * Extracts form properties from a task using FormService.
   *
   * @param task The Flowable task
   * @return List of form properties with their values
   */
  private List<FormPropertyDTO> getFormPropertiesForTask(Task task) {
    List<FormPropertyDTO> formProperties = new ArrayList<>();
    try {
      // Get form data from the task
      TaskFormData formData = formService.getTaskFormData(task.getId());
      if (formData != null && formData.getFormProperties() != null) {
        for (FormProperty formProperty : formData.getFormProperties()) {
          FormPropertyDTO propertyDTO = new FormPropertyDTO();
          propertyDTO.setId(formProperty.getId());
          propertyDTO.setName(formProperty.getName());
          propertyDTO.setType(formProperty.getType().getName());
          propertyDTO.setReadable(formProperty.isReadable());
          propertyDTO.setWritable(formProperty.isWritable());
          propertyDTO.setRequired(formProperty.isRequired());
          // propertyDTO.setDefaultValue(formProperty.getValue());

          // Get possible values for enum types
          if ("enum".equals(formProperty.getType().getName())) {
            FormType formType = formProperty.getType();
            Object valuesObj = formType.getInformation("values");
            if (valuesObj instanceof Map<?, ?>) {
              @SuppressWarnings("unchecked")
              Map<String, String> enumValues = (Map<String, String>) valuesObj;

              // Convert map entries to FormPropertyValueDTO objects
              List<FormPropertyValueDTO> valuesList = new ArrayList<>();
              for (Map.Entry<String, String> entry : enumValues.entrySet()) {
                FormPropertyValueDTO valueDTO = new FormPropertyValueDTO();
                valueDTO.setId(entry.getKey());
                valueDTO.setName(entry.getValue());
                valuesList.add(valueDTO);
              }
              propertyDTO.setValues(valuesList);
            }
          }

          formProperties.add(propertyDTO);
        }
      }
    } catch (Exception e) {
      log.error("Error getting form properties for task {}: {}", task.getId(), e.getMessage(), e);
    }
    return formProperties;
  }

  /**
   * Updates the workflow case status based on the current active task in the process. This method
   * determines the appropriate status value from the active task's definition key and updates the
   * WorkflowCase entity accordingly.
   *
   * @param workflowCase The workflow case to update
   * @param processInstanceId The process instance ID to find active tasks
   */
  private void updateWorkflowCaseStatus(WorkflowCase workflowCase, String processInstanceId) {
    if (workflowCase == null || processInstanceId == null) {
      log.warn("Cannot update workflow status: workflowCase or processInstanceId is null");
      return;
    }

    try {
      // Find all active tasks for this process instance
      List<Task> activeTasks =
          taskService
              .createTaskQuery()
              .processInstanceId(processInstanceId)
              .active()
              .orderByTaskCreateTime()
              .asc()
              .list();

      if (activeTasks.isEmpty()) {
        // No active tasks - process might be completed
        ProcessInstance processInstance =
            runtimeService
                .createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        if (processInstance == null) {
          // Process is completed
          workflowCase.setStatus("completed");
          log.info("Workflow case {} status updated to 'completed'", workflowCase.getId());
        } else {
          // Process is still active but no user tasks - might be in a service task or gateway
          workflowCase.setStatus("in_progress");
          log.info(
              "Workflow case {} status updated to 'in_progress' (no active user tasks)",
              workflowCase.getId());
        }
      } else {
        // Get the current active task's definition key to determine status
        Task currentTask = activeTasks.get(0); // Get the oldest active task
        String taskDefinitionKey = currentTask.getTaskDefinitionKey();

        // Map task definition key to a user-friendly status
        String status;
        switch (taskDefinitionKey) {
          case "officer_review":
            status = "officer_review";
            break;
          case "manager_review":
            status = "manager_review";
            break;
          case "document_upload":
            status = "document_upload";
            break;
          case "compliance_verification":
            status = "verification";
            break;
          case "final_approval":
            status = "final_approval";
            break;
          default:
            status = "in_progress";
            break;
        }

        // Update the workflow case status
        workflowCase.setStatus(status);
        try {
          workflowCaseService.updateCaseMetadata(workflowCase.getId(), Map.of("status", status));
          log.info("Workflow case {} status updated to '{}'", workflowCase.getId(), status);
        } catch (JsonProcessingException e) {
          log.error("Error updating workflow case status: {}", e.getMessage(), e);
        }
      }
    } catch (Exception e) {
      log.error("Error updating workflow case status: {}", e.getMessage(), e);
    }
  }
}
