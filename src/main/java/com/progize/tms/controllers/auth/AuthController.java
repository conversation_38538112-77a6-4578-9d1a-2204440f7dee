package com.progize.tms.controllers.auth;

import com.progize.tms.controllers.auth.req.LoginRequest;
import com.progize.tms.controllers.auth.req.LogoutRequest;
import com.progize.tms.controllers.auth.req.RegisterRequest;
import com.progize.tms.controllers.auth.req.TokenRefreshRequest;
import com.progize.tms.controllers.auth.res.MessageResponse;
import com.progize.tms.controllers.auth.res.TokenRefreshResponse;
import com.progize.tms.exceptions.TokenRefreshException;
import com.progize.tms.repository.UserRepository;
import com.progize.tms.repository.entity.RefreshToken;
import com.progize.tms.repository.entity.User;
import com.progize.tms.security.JwtTokenUtil;
import com.progize.tms.services.RefreshTokenService;
import com.progize.tms.services.UserDetailsServiceImpl;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@CrossOrigin
public class AuthController {

  private final AuthenticationManager authenticationManager;
  private final UserRepository userRepository;
  private final PasswordEncoder passwordEncoder;
  private final JwtTokenUtil jwtTokenUtil;
  private final RefreshTokenService refreshTokenService;
  private final AuthTransformer authTransformer;
  private final UserDetailsServiceImpl userDetailsService;

  /** Authenticates a user and returns JWT token */
  @PostMapping("/login")
  public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
    Authentication authentication =
        authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                loginRequest.getUsername(), loginRequest.getPassword()));

    SecurityContextHolder.getContext().setAuthentication(authentication);

    UserDetails userDetails = (UserDetails) authentication.getPrincipal();
    String jwt = jwtTokenUtil.generateToken(userDetails);

    User user = userRepository.findByUsername(userDetails.getUsername()).orElseThrow();
    RefreshToken refreshToken = refreshTokenService.createRefreshToken(user.getId());

    return ResponseEntity.ok(authTransformer.toJwtResponse(jwt, refreshToken.getToken(), user));
  }

  /** Registers a new user */
  @PostMapping("/register")
  public ResponseEntity<?> registerUser(@Valid @RequestBody RegisterRequest registerRequest) {
    if (userRepository.existsByUsername(registerRequest.getUsername())) {
      return ResponseEntity.badRequest()
          .body(new MessageResponse("Error: Username is already taken!"));
    }

    if (userRepository.existsByEmail(registerRequest.getEmail())) {
      return ResponseEntity.badRequest()
          .body(new MessageResponse("Error: Email is already in use!"));
    }

    // Create new user's account
    User user = authTransformer.toEntity(registerRequest);
    user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));

    userRepository.save(user);

    return ResponseEntity.ok(new MessageResponse("User registered successfully!"));
  }

  /** Refreshes an expired JWT token */
  @PostMapping("/refresh")
  public ResponseEntity<?> refreshToken(@Valid @RequestBody TokenRefreshRequest request) {
    String requestRefreshToken = request.getRefreshToken();

    return refreshTokenService
        .findByToken(requestRefreshToken)
        .map(refreshTokenService::verifyExpiration)
        .map(RefreshToken::getUser)
        .map(
            user -> {
              // Load user details with roles
              UserDetails userDetails = userDetailsService.loadUserByUsername(user.getUsername());
              String token = jwtTokenUtil.generateToken(userDetails);
              return ResponseEntity.ok(new TokenRefreshResponse(token, requestRefreshToken));
            })
        .orElseThrow(
            () ->
                new TokenRefreshException(
                    requestRefreshToken, "Refresh token is not in database!"));
  }

  /** Logs out a user by invalidating their refresh token */
  @PostMapping("/logout")
  public ResponseEntity<?> logoutUser(@Valid @RequestBody LogoutRequest logoutRequest) {
    refreshTokenService.deleteByUserId(logoutRequest.getUserId());
    return ResponseEntity.ok(new MessageResponse("Log out successful!"));
  }
}
