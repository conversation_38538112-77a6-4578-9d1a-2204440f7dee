import { createTheme } from '@mui/material/styles';

// Create default light and dark themes
const lightTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#0c4e7a', // Progize dark blue
      light: '#3a7ca5',
      dark: '#093c5d',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#7bdcb5', // Progize light green
      light: '#a0e7cd',
      dark: '#57c99a',
      contrastText: '#000000',
    },
    background: {
      default: '#f5f5f5', // Light gray background
      paper: '#ffffff',   // White paper/card background
    },
    text: {
      primary: '#333333',
      secondary: '#757575',
    },
    info: {
      main: '#2196f3', // Info blue for info icons
    },
    error: {
      main: '#f44336', // Red for delete icons/buttons
    },
    // Progize custom colors
    progize: {
      darkBlue: '#0c4e7a',
      lightGreen: '#7bdcb5',
      gradientStart: '#0c4e7a',
      gradientEnd: '#7bdcb5',
      gradientHoverStart: '#7bdcb5',
      gradientHoverEnd: '#0c4e7a',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h6: {
      fontWeight: 500,
    },
    subtitle1: {
      fontSize: '0.875rem',
    },
    body1: {
      fontSize: '0.875rem',
    },
    body2: {
      fontSize: '0.875rem',
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 4,
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
        elevation1: {
          boxShadow: '0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12)',
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: '48px',
        },
        indicator: {
          height: '3px',
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          minHeight: '48px',
          fontWeight: 500,
          '&.Mui-selected': {
            color: '#0c4e7a',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
        },
        containedPrimary: {
          boxShadow: '0px 1px 5px 0px rgba(0,0,0,0.2)',
        },
        // Add special styling for primary buttons with Progize gradient
        contained: {
          '&.MuiButton-containedPrimary.progize-gradient': {
            background: 'linear-gradient(135deg, #0c4e7a 0%, #7bdcb5 100%)',
            boxShadow: '0 4px 12px rgba(12, 78, 122, 0.2)',
            '&:hover': {
              background: 'linear-gradient(135deg, #7bdcb5 0%, #0c4e7a 100%)',
              boxShadow: '0 6px 16px rgba(12, 78, 122, 0.3)',
            },
          },
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          padding: '8px',
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: '8px 16px',
          fontSize: '0.875rem',
        },
        head: {
          fontWeight: 500,
          color: '#333333',
        },
      },
    },
  },
});

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#63B3ED', // Progize dark blue for dark mode
      light: '#87CEEB',
      dark: '#4682B4',
      contrastText: '#000000',
    },
    secondary: {
      main: '#4FD1C5', // Progize light green for dark mode
      light: '#6FE7DE',
      dark: '#34C759',
      contrastText: '#000000',
    },
    background: {
      default: '#303030',
      paper: '#424242',
    },
    text: {
      primary: '#ffffff',
      secondary: '#bbbbbb',
    },
    // Progize custom colors for dark mode
    progize: {
      darkBlue: '#63B3ED',
      lightGreen: '#4FD1C5',
      gradientStart: '#63B3ED',
      gradientEnd: '#4FD1C5',
      gradientHoverStart: '#4FD1C5',
      gradientHoverEnd: '#63B3ED',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h6: {
      fontWeight: 500,
    },
    subtitle1: {
      fontSize: '0.875rem',
    },
    body1: {
      fontSize: '0.875rem',
    },
    body2: {
      fontSize: '0.875rem',
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 4,
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: '48px',
        },
        indicator: {
          height: '3px',
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          minHeight: '48px',
          fontWeight: 500,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        // Add special styling for primary buttons with Progize gradient in dark mode
        contained: {
          '&.MuiButton-containedPrimary.progize-gradient': {
            background: 'linear-gradient(135deg, #63B3ED 0%, #4FD1C5 100%)',
            boxShadow: '0 4px 12px rgba(99, 179, 237, 0.2)',
            '&:hover': {
              background: 'linear-gradient(135deg, #4FD1C5 0%, #63B3ED 100%)',
              boxShadow: '0 6px 16px rgba(99, 179, 237, 0.3)',
            },
          },
        },
      },
    },
  },
});

// Function to get theme based on mode
export const getTheme = (mode) => {
  return mode === 'dark' ? darkTheme : lightTheme;
};

// For backward compatibility with existing code
export const createAppTheme = (mode) => {
  return mode === 'dark' ? darkTheme : lightTheme;
};

// Default theme
const theme = lightTheme;
export default theme;
