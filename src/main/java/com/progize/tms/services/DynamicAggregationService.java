package com.progize.tms.services;

import com.progize.tms.repository.DataBucketRepository;
import com.progize.tms.repository.DatabaseSourceRepository;
import com.progize.tms.repository.entity.DataBucketEntity;
import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import com.progize.tms.repository.entity.enums.DatabaseType;
import com.progize.tms.services.models.AggregationRequirement;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import javax.sql.DataSource;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.stereotype.Service;

/**
 * Service for calculating aggregations from dynamic data sources based on requirements from the
 * aggregation registry. This service is responsible for executing the aggregation queries and
 * returning the results.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DynamicAggregationService {

  private final DataBucketRepository bucketRepository;
  private final DatabaseSourceRepository databaseSourceRepository;

  // Simple in-memory cache for aggregation results with TTL
  private final Map<String, CacheEntry<Number>> aggregationCache = new ConcurrentHashMap<>();

  // Default cache TTL in milliseconds (5 minutes)
  private static final long CACHE_TTL_MS = TimeUnit.MINUTES.toMillis(5);

  /**
   * Calculate all required aggregations for a set of entity IDs using the data source configured
   * for the bucket.
   *
   * @param requirements The set of aggregation requirements to calculate
   * @param entityIds The set of entity IDs to calculate aggregations for
   * @param bucketId The ID of the data bucket to use for the aggregation
   * @return A map of aggregation key to aggregation result
   */
  public Map<String, Number> calculateAggregations(
      Set<AggregationRequirement> requirements, Set<String> entityIds, Long bucketId) {

    if (requirements.isEmpty() || entityIds.isEmpty() || bucketId == null) {
      return new HashMap<>();
    }

    log.debug(
        "Calculating {} aggregations for {} entity IDs using bucket {}",
        requirements.size(),
        entityIds.size(),
        bucketId);

    // Get the bucket and its data source
    DataBucketEntity bucket =
        bucketRepository
            .findById(bucketId)
            .orElseThrow(() -> new IllegalArgumentException("Bucket not found: " + bucketId));

    DataSourceEntity dataSource = bucket.getDataSource();
    if (dataSource == null) {
      throw new IllegalArgumentException("Bucket has no data source configured: " + bucketId);
    }

    // Get the database source details
    DatabaseSourceEntity databaseSource =
        databaseSourceRepository
            .findByDataSourceId(dataSource.getId())
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Database source not found for data source: " + dataSource.getId()));

    // Create a JDBC data source
    DataSource jdbcDataSource = createDataSource(databaseSource);
    JdbcTemplate jdbcTemplate = new JdbcTemplate(jdbcDataSource);

    Map<String, Number> results = new HashMap<>();

    // For each entity ID
    for (String entityId : entityIds) {
      // For each aggregation requirement
      for (AggregationRequirement req : requirements) {
        // Calculate the aggregation (with caching)
        Number value =
            calculateAggregation(
                req, entityId, jdbcTemplate, bucket.getTableName(), databaseSource);

        // Store in results map with aggregation key
        if (value != null) {
          results.put(req.getAggregationKey(), value);
        }
      }
    }

    log.debug("Calculated {} aggregation values", results.size());
    return results;
  }

  /**
   * Calculate a single aggregation for an entity ID using the provided JDBC template.
   *
   * @param requirement The aggregation requirement
   * @param entityId The entity ID
   * @param jdbcTemplate The JDBC template to use for the query
   * @param tableName The table name to query
   * @param databaseSource The database source entity for determining database type
   * @return The calculated aggregation value
   */
  private Number calculateAggregation(
      AggregationRequirement requirement,
      String entityId,
      JdbcTemplate jdbcTemplate,
      String tableName,
      DatabaseSourceEntity databaseSource) {

    String cacheKey = generateCacheKey(requirement, entityId, tableName);

    // Check cache first
    //    CacheEntry<Number> cached = aggregationCache.get(cacheKey);
    //    if (cached != null && !cached.isExpired()) {
    //      log.debug("Cache hit for aggregation: {}", cacheKey);
    //      return cached.value();
    //    }

    log.debug("Cache miss for aggregation: {}", cacheKey);

    // Calculate the aggregation
    Number result;
    try {
      // Calculate the start date based on period and startOffset
      Integer startOffset = requirement.getStartOffset() != null ? requirement.getStartOffset() : 0;
      LocalDateTime endDate = LocalDateTime.now().minusDays(startOffset);
      LocalDateTime startDate = endDate.minusDays(requirement.getPeriod());

      log.debug("Calculating aggregation from {} to {}", startDate, endDate);

      // Get the entityField and check if it's a variable expression
      String entityField = requirement.getEntityField();
      String entityFieldValue = entityId; // Default to the provided entityId

      // If entityField is a variable expression like ${customer_id}, use the entityId as is
      // This is because the entityId is already the value of the customer_id variable
      // from the transaction data
      if (entityField != null && entityField.startsWith("${") && entityField.endsWith("}")) {
        log.debug("EntityField is a variable expression: {}", entityField);
        // The entityFieldValue is already set to entityId, which is correct
      }

      String sql =
          buildAggregationQuery(
              requirement.getFunction().toLowerCase(),
              requirement.getField(),
              tableName,
              entityField);

      // Get the database type to handle date formatting correctly
      DatabaseType dbType = databaseSource.getDatabaseType();

      // Format the dates according to the database type
      Object startDateParam;
      Object endDateParam;
      if (dbType == DatabaseType.CLICKHOUSE) {
        // For ClickHouse, use properly formatted date strings
        startDateParam = startDate.toLocalDate().toString(); // Format as YYYY-MM-DD
        endDateParam = endDate.toLocalDate().toString(); // Format as YYYY-MM-DD
        log.debug("Using ClickHouse date formats: start={}, end={}", startDateParam, endDateParam);
      } else {
        // For other databases, use Timestamps
        startDateParam = Timestamp.valueOf(startDate);
        endDateParam = Timestamp.valueOf(endDate);
      }

      // Log the final query with parameter values for debugging
      log.info(
          "Executing aggregation query: {} with parameters: [entityFieldValue={}, startDate={}, endDate={}]",
          sql,
          entityFieldValue,
          startDateParam,
          endDateParam);

      // Execute the query directly for debugging - CAREFULLY replace each parameter
      // First, create a copy of the SQL query
      String directQuery = new String(sql);

      // Replace the first ? with the entityFieldValue
      int firstParamIndex = directQuery.indexOf("?");
      if (firstParamIndex >= 0) {
        directQuery =
            directQuery.substring(0, firstParamIndex)
                + "'"
                + entityFieldValue
                + "'"
                + directQuery.substring(firstParamIndex + 1);
      }

      // Replace the second ? with the startDateParam
      int secondParamIndex = directQuery.indexOf("?");
      if (secondParamIndex >= 0) {
        directQuery =
            directQuery.substring(0, secondParamIndex)
                + "'"
                + startDateParam
                + "'"
                + directQuery.substring(secondParamIndex + 1);
      }

      // Replace the third ? with the endDateParam
      int thirdParamIndex = directQuery.indexOf("?");
      if (thirdParamIndex >= 0) {
        directQuery =
            directQuery.substring(0, thirdParamIndex)
                + "'"
                + endDateParam
                + "'"
                + directQuery.substring(thirdParamIndex + 1);
      }

      log.info("Direct query for debugging: {}", directQuery);

      result =
          jdbcTemplate.queryForObject(
              sql,
              (rs, rowNum) -> {
                Object value = rs.getObject(1);
                if (value == null) {
                  return BigDecimal.ZERO;
                } else if (value instanceof Number) {
                  return (Number) value;
                } else {
                  return BigDecimal.ZERO;
                }
              },
              Long.valueOf(entityFieldValue),
              startDateParam,
              endDateParam);

      // Cache the result
      aggregationCache.put(
          cacheKey, new CacheEntry<>(result, System.currentTimeMillis() + CACHE_TTL_MS));

      return result;
    } catch (Exception e) {
      log.error(
          "Error calculating aggregation: {} for entity ID: {}",
          requirement.getAggregationKey(),
          entityId,
          e);
      return null;
    }
  }

  /**
   * Build an aggregation query for the specified function, field, table, and entity field.
   *
   * @param function The aggregation function (SUM, COUNT, AVG, MAX, MIN)
   * @param field The field to aggregate
   * @param tableName The table name
   * @param entityField The entity field to filter on
   * @return The SQL query
   */
  private String buildAggregationQuery(
      String function, String field, String tableName, String entityField) {
    String fieldToUse = (field == null || field.trim().isEmpty()) ? "*" : field;

    // Handle variable expressions in entityField
    String entityFieldToUse;
    if (entityField == null || entityField.trim().isEmpty()) {
      entityFieldToUse = "customer_id";
    } else if (entityField.startsWith("${") && entityField.endsWith("}")) {
      // Extract the variable name from ${variable_name}
      String variableName = entityField.substring(2, entityField.length() - 1);
      // Use the column name that corresponds to the variable
      // For now, we'll assume the variable name is the column name
      entityFieldToUse = variableName;
      log.debug(
          "Using column name '{}' extracted from variable expression: {}",
          entityFieldToUse,
          entityField);
    } else {
      entityFieldToUse = entityField;
    }

    return String.format(
        "SELECT %s(%s) FROM %s WHERE %s = ? AND deal_date >= ? AND deal_date <= ?",
        function.toLowerCase(), fieldToUse, tableName, entityFieldToUse);
  }

  /**
   * Generate a cache key for an aggregation requirement and entity ID.
   *
   * @param requirement The aggregation requirement
   * @param entityId The entity ID
   * @param tableName The table name
   * @return A unique cache key
   */
  private String generateCacheKey(
      AggregationRequirement requirement, String entityId, String tableName) {
    return requirement.getAggregationKey() + "_" + entityId + "_" + tableName;
  }

  /**
   * Create a JDBC DataSource from a database source entity.
   *
   * @param databaseSource The database source entity
   * @return A configured DataSource
   */
  private DataSource createDataSource(DatabaseSourceEntity databaseSource) {
    DriverManagerDataSource dataSource = new DriverManagerDataSource();

    // Set the driver class if specified
    if (databaseSource.getDriverClass() != null && !databaseSource.getDriverClass().isEmpty()) {
      dataSource.setDriverClassName(databaseSource.getDriverClass());
    } else {
      // Set default driver based on database type
      DatabaseType dbType = databaseSource.getDatabaseType();
      switch (dbType) {
        case MYSQL:
          dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
          break;
        case CLICKHOUSE:
          dataSource.setDriverClassName("com.clickhouse.jdbc.ClickHouseDriver");
          break;
        case MSSQL:
          dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
          break;
        default:
          throw new IllegalArgumentException("Unsupported database type: " + dbType);
      }
    }

    dataSource.setUrl(databaseSource.getConnectionUrl());
    dataSource.setUsername(databaseSource.getUsername());
    dataSource.setPassword(
        null != databaseSource.getPassword() ? databaseSource.getPassword() : "");

    return dataSource;
  }

  /** Inner class for cache entries with expiration. */
  private record CacheEntry<T>(@Getter T value, long expirationTime) {

    public boolean isExpired() {
      return System.currentTimeMillis() > expirationTime;
    }
  }
}
