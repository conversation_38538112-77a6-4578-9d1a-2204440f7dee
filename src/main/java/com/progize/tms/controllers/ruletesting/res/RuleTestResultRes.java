package com.progize.tms.controllers.ruletesting.res;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;

/** Response object containing rule testing results. */
@Data
public class RuleTestResultRes {
  /** Whether the rule passed the test */
  private boolean passed;

  /** The MVEL expression generated from the rule */
  private String generatedExpression;

  /** Time taken to evaluate the rule in milliseconds */
  private long evaluationTime;

  /** Human-readable explanation of the rule evaluation result */
  private String explanation;

  /** Analysis results for the rule */
  private AnalysisResult analysis;

  /** Any issues detected during rule testing */
  private List<Issue> issues = new ArrayList<>();

  /** Debug information (if requested) */
  private DebugInfo debugInfo;

  @Data
  public static class AnalysisResult {
    private boolean syntaxValid;
    private List<TypeCompatibility> typeCompatibility = new ArrayList<>();
    private PerformanceAnalysis performance;
  }

  @Data
  public static class TypeCompatibility {
    private String field;
    private String declaredType;
    private String operatorId;
    private boolean compatible;
    private String reason;
  }

  @Data
  public static class PerformanceAnalysis {
    private String complexity;
    private List<String> recommendations = new ArrayList<>();
  }

  @Data
  public static class Issue {
    private String type; // INFO, WARNING, ERROR
    private String category;
    private String message;
    private String location;
    private String suggestion;
  }

  @Data
  public static class DebugInfo {
    private Map<String, Object> context;
    private List<EvaluationStep> evaluationTrace = new ArrayList<>();
  }

  @Data
  public static class EvaluationStep {
    private int step;
    private String expression;
    private Object value;
  }
}
