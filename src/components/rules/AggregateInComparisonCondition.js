import React from 'react';
import {
  Box,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Tooltip,
  useTheme,
  Alert
} from '@mui/material';
import ExpressionInput from '../common/ExpressionInput';
import InfoIcon from '@mui/icons-material/Info';

/**
 * A modified version of AggregateCondition specifically for use inside ComparisonCondition.
 * This version doesn't show threshold or operator fields since they're not relevant in comparisons.
 */
const AggregateInComparisonCondition = ({
  condition,
  onChange,
  fields = [],
  aggregateFunctions = [],
  isEditable = true,
  getFieldDisplayName
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Only get aggregatable fields for aggregations
  const aggregatableFields = fields.filter(field => field.aggregatable);

  // Find the selected field object
  const selectedField = fields.find(field => field.id === condition.fieldId) ||
                        fields.find(field => field.name === condition.field) || {};

  // If we have a field name but no fieldId, find the fieldId
  const fieldId = condition.fieldId || (condition.field && fields.find(f => f.name === condition.field)?.id) || '';

  // Handle field change
  const handleFieldChange = (event) => {
    const fieldId = event.target.value;
    const field = fields.find(f => f.id === fieldId);

    if (field) {
      onChange({
        ...condition,
        type: 'AGGREGATE', // Always ensure type is set
        field: field.name,
        fieldId: field.id
      });
    }
  };

  // Handle function change
  const handleFunctionChange = (event) => {
    onChange({
      ...condition,
      type: 'AGGREGATE', // Always ensure type is set
      function: event.target.value
    });
  };

  // Handle period change
  const handlePeriodChange = (event) => {
    const value = parseInt(event.target.value, 10);
    onChange({
      ...condition,
      type: 'AGGREGATE', // Always ensure type is set
      period: isNaN(value) ? 7 : value
    });
  };

  // Handle entity field change
  const handleEntityFieldChange = (event) => {
    // Get the value from the event
    const value = event.target.value;

    // Log the value for debugging
    console.log('Entity field change value:', value);

    // Update the condition with the new value
    onChange({
      ...condition,
      type: 'AGGREGATE', // Always ensure type is set
      entityField: value // Allow empty string
    });
  };

  // Handle start offset change
  const handleStartOffsetChange = (event) => {
    const value = parseInt(event.target.value, 10);
    onChange({
      ...condition,
      type: 'AGGREGATE', // Always ensure type is set
      startOffset: isNaN(value) ? 0 : value
    });
  };

  // Get display name for a field
  const getFieldName = (fieldName) => {
    if (getFieldDisplayName) {
      return getFieldDisplayName(fieldName);
    }

    const field = fields.find(f => f.name === fieldName);
    return field ? field.displayName || field.name : fieldName;
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        position: 'relative',
        borderLeft: '4px solid #4CAF50', // Green for aggregate
        backgroundColor: theme.palette.background.paper,
        opacity: isEditable ? 1 : 0.8
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          Aggregate Value
        </Typography>
        <Tooltip title="Calculate an aggregate value over a time period">
          <IconButton size="small" color="info">
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Alert severity="info" sx={{ mb: 2 }}>
        This aggregate is part of a comparison. Only the raw aggregate value will be used, not a threshold comparison.
      </Alert>

      <Grid container spacing={2}>
        {/* Aggregate Function */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth variant="outlined" size="small">
            <InputLabel id="aggregate-function-label">Function</InputLabel>
            <Select
              labelId="aggregate-function-label"
              value={condition.function || ''}
              onChange={handleFunctionChange}
              label="Function"
              disabled={!isEditable}
            >
              {aggregateFunctions.map((func) => (
                <MenuItem key={func.id} value={func.id}>
                  {func.displayName || func.id}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Field to Aggregate */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth variant="outlined" size="small">
            <InputLabel id="aggregate-field-label">Field</InputLabel>
            <Select
              labelId="aggregate-field-label"
              value={fieldId}
              onChange={handleFieldChange}
              label="Field"
              disabled={!isEditable}
            >
              {aggregatableFields.map((field) => (
                <MenuItem key={field.id} value={field.id}>
                  {field.displayName || field.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Time Period */}
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Time Period (days)"
            type="number"
            variant="outlined"
            size="small"
            value={condition.period || 7}
            onChange={handlePeriodChange}
            disabled={!isEditable}
            InputProps={{
              inputProps: { min: 1 }
            }}
          />
        </Grid>

        {/* Start Offset */}
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Start Offset (days)"
            type="number"
            variant="outlined"
            size="small"
            value={condition.startOffset || 0}
            onChange={handleStartOffsetChange}
            disabled={!isEditable}
            InputProps={{
              inputProps: { min: 0 }
            }}
            helperText="Days in the past to start the time window (0 = today)"
          />
        </Grid>

        {/* Entity Field */}
        <Grid item xs={12} sm={6}>
          <ExpressionInput
            label="Entity Field"
            value={condition.entityField === undefined ? 'customerId' : condition.entityField}
            onChange={(e) => {
              console.log('Direct entity field change event:', e.target.value);
              handleEntityFieldChange(e);
            }}
            type="text"
            fieldType=""
            disabled={!isEditable}
            placeholder="Enter field name or ${variable} expression"
            helperText="Field to identify the entity (e.g., customerId or ${entityIdField})"
          />
        </Grid>
      </Grid>
    </Paper>
  );
};

export default AggregateInComparisonCondition;
