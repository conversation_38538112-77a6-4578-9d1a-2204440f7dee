package com.progize.tms.controllers.datasources.req;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/** Request class for creating a database data source. */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreateDatabaseSourceRequest extends CreateDataSourceRequest {
  @NotBlank(message = "Database type is required")
  private String databaseType;

  @NotBlank(message = "Connection URL is required")
  private String connectionUrl;

  private String driverClass;

  private String username;

  private String password;

  private String schemaName;

  private String additionalProperties;
}
