import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  CircularProgress,
  Snackbar,
  Alert,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import { styled } from '@mui/material/styles';
import VisibilityIcon from '@mui/icons-material/Visibility';
import LibraryAddIcon from '@mui/icons-material/LibraryAdd';
import DataObjectIcon from '@mui/icons-material/DataObject';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import StorageIcon from '@mui/icons-material/Storage';
import WarningIcon from '@mui/icons-material/Warning';
import CategoryIcon from '@mui/icons-material/Category';
import { ruleVariableRegistryService } from '../../services/api';
import { useSpring, animated } from 'react-spring';

// Animated components
const AnimatedCard = animated(Card);

// Styled components
const TableCard = styled(AnimatedCard, {
  shouldForwardProp: (prop) => prop !== 'registered'
})(({ theme, registered }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1),
  boxShadow: registered ? 
    `0 6px 10px -4px ${alpha(theme.palette.success.main, 0.1)}` : 
    '0 4px 12px -2px rgba(0, 0, 0, 0.08)',
  border: registered ? 
    `1px solid ${alpha(theme.palette.success.main, 0.2)}` : 
    `1px solid ${alpha(theme.palette.divider, 0.5)}`,
  overflow: 'hidden',
  position: 'relative',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: registered ? 
      `0 12px 16px -8px ${alpha(theme.palette.success.main, 0.15)}` : 
      '0 8px 16px -4px rgba(0, 0, 0, 0.1)',
  }
}));

const TableHeader = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'registered'
})(({ theme, registered }) => ({
  padding: theme.spacing(2, 3),
  backgroundColor: registered ? 
    alpha(theme.palette.success.main, 0.08) : 
    theme.palette.mode === 'dark' ? alpha(theme.palette.primary.main, 0.1) : alpha(theme.palette.grey[100], 0.8),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: theme.spacing(1),
  borderBottom: `1px solid ${registered ? alpha(theme.palette.success.main, 0.15) : theme.palette.divider}`
}));

const ColumnInfo = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.spacing(1),
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  marginTop: theme.spacing(2),
  overflow: 'hidden'
}));

const ColumnListItem = styled(ListItem)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  padding: theme.spacing(1, 2),
  '&:last-child': {
    borderBottom: 'none'
  }
}));

const TableDiscoveryPanel = ({ tables, registeredTables, onTableRegistered }) => {
  const theme = useTheme();
  const [selectedTable, setSelectedTable] = useState(null);
  const [columns, setColumns] = useState([]);
  const [loadingColumns, setLoadingColumns] = useState(false);
  const [registerDialogOpen, setRegisterDialogOpen] = useState(false);
  const [registering, setRegistering] = useState(false);
  const [tableForm, setTableForm] = useState({ displayName: '', description: '' });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  // Check if a table is already registered
  const isTableRegistered = (schema, table) => {
    return registeredTables.some(
      (registered) => registered.schemaName === schema && registered.tableName === table
    );
  };

  // Load columns for a table
  const handleViewColumns = async (table) => {
    setSelectedTable(table);
    setLoadingColumns(true);
    try {
      const response = await ruleVariableRegistryService.getTableColumns(
        table.schemaName,
        table.tableName
      );
      setColumns(response);
    } catch (error) {
      console.error('Error loading columns:', error);
      setNotification({
        open: true,
        message: 'Failed to load columns',
        severity: 'error'
      });
    } finally {
      setLoadingColumns(false);
    }
  };

  // Open register dialog
  const handleOpenRegisterDialog = () => {
    if (!selectedTable) return;
    
    setTableForm({
      displayName: selectedTable.displayName || selectedTable.name,
      description: ''
    });
    
    setRegisterDialogOpen(true);
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTableForm((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  // Register a table
  const handleRegisterTable = async () => {
    if (!selectedTable) return;
    
    setRegistering(true);
    try {
      await ruleVariableRegistryService.registerTable(
        selectedTable.schema,
        selectedTable.name,
        tableForm.displayName,
        tableForm.description
      );
      
      setNotification({
        open: true,
        message: 'Table registered successfully',
        severity: 'success'
      });
      
      if (onTableRegistered) {
        onTableRegistered();
      }
      
      setRegisterDialogOpen(false);
    } catch (error) {
      console.error('Error registering table:', error);
      setNotification({
        open: true,
        message: 'Failed to register table',
        severity: 'error'
      });
    } finally {
      setRegistering(false);
    }
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification((prev) => ({
      ...prev,
      open: false
    }));
  };

  // Animation for table card
  const getCardAnimation = useMemo(() => (index) => ({
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0)' },
    delay: 50 * index,
    config: { tension: 120, friction: 12 }
  }), []);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Database Tables ({tables.length})
      </Typography>
      
      <Grid container spacing={3}>
        {tables.map((table, index) => {
          const registered = isTableRegistered(table.schemaName, table.tableName);
          const isSelected = selectedTable && selectedTable.tableName === table.tableName;
          
          return (
            <Grid item xs={12} md={6} key={`${table.schemaName}.${table.tableName}`}>
              <TableCard 
                registered={registered}
                style={useSpring(getCardAnimation(index))}
              >
                <TableHeader registered={registered}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar 
                      sx={{ 
                        mr: 2, 
                        bgcolor: registered ? 'success.main' : 'primary.main',
                        width: 40,
                        height: 40
                      }}
                    >
                      {registered ? <CheckCircleIcon /> : <StorageIcon />}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" component="div" sx={{ fontWeight: 500, lineHeight: 1.2 }}>
                        {table.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Schema: {table.tableName} • Engine: {table.engine}
                      </Typography>
                    </Box>
                  </Box>
                  <Box>
                    <Chip
                      label={registered ? "Registered" : "Not Registered"}
                      size="small"
                      variant={registered ? "filled" : "outlined"}
                      color={registered ? "success" : "default"}
                      sx={{
                        mr: 1,
                        ...(registered && {
                          bgcolor: theme.palette.success.main,
                          color: theme.palette.success.contrastText,
                        }),
                      }}
                    />
                    <Tooltip title="View Columns">
                      <IconButton 
                        onClick={() => handleViewColumns(table)}
                        color={isSelected ? "primary" : "default"}
                        size="small"
                        sx={{ 
                          mr: 1,
                          bgcolor: isSelected ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                        }}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    {!registered && (
                      <Tooltip title="Register Table">
                        {!selectedTable || selectedTable.name !== table.name ? (
                          <span>
                            <IconButton
                              color="primary"
                              size="small"
                              disabled={true}
                              sx={{ 
                                bgcolor: 'transparent'
                              }}
                            >
                              <LibraryAddIcon fontSize="small" />
                            </IconButton>
                          </span>
                        ) : (
                          <IconButton
                            color="primary"
                            size="small"
                            onClick={handleOpenRegisterDialog}
                            sx={{ 
                              bgcolor: alpha(theme.palette.primary.main, 0.1)
                            }}
                          >
                            <LibraryAddIcon fontSize="small" />
                          </IconButton>
                        )}
                      </Tooltip>
                    )}
                  </Box>
                </TableHeader>
                
                {selectedTable && selectedTable.name === table.name && (
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1" fontWeight={500}>
                        Columns for {selectedTable.schema}.{selectedTable.name}
                      </Typography>
                      {!registered && (
                        <Button
                          variant="contained"
                          size="small"
                          onClick={handleOpenRegisterDialog}
                          startIcon={<LibraryAddIcon />}
                        >
                          Register Table
                        </Button>
                      )}
                    </Box>
                    
                    {loadingColumns ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                        <CircularProgress />
                      </Box>
                    ) : (
                      <>
                        {columns.length > 0 ? (
                          <ColumnInfo>
                            <List disablePadding>
                              {columns.slice(0, 5).map((column) => (
                                <ColumnListItem key={column.name} disablePadding>
                                  <ListItemIcon sx={{ minWidth: 36 }}>
                                    <DataObjectIcon fontSize="small" color="primary" />
                                  </ListItemIcon>
                                  <ListItemText 
                                    primary={
                                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Typography variant="body2" component="span" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
                                          {column.name}
                                        </Typography>
                                        <Chip 
                                          label={column.mappedDataType}
                                          size="small"
                                          variant="outlined"
                                          sx={{ ml: 1, height: 20, '& .MuiChip-label': { px: 1, py: 0 } }}
                                        />
                                      </Box>
                                    }
                                    secondary={
                                      <Typography variant="caption" color="text.secondary">
                                        {column.type}
                                      </Typography>
                                    }
                                  />
                                </ColumnListItem>
                              ))}
                            </List>
                            {columns.length > 5 && (
                              <Box sx={{ textAlign: 'center', py: 1, borderTop: `1px solid ${theme.palette.divider}` }}>
                                <Typography variant="caption" color="text.secondary">
                                  {columns.length - 5} more columns not shown
                                </Typography>
                              </Box>
                            )}
                          </ColumnInfo>
                        ) : (
                          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', p: 2 }}>
                            No columns available for this table
                          </Typography>
                        )}
                      </>
                    )}
                  </CardContent>
                )}
              </TableCard>
            </Grid>
          );
        })}
      </Grid>

      {/* Register Table Dialog */}
      <Dialog open={registerDialogOpen} onClose={() => setRegisterDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ 
          borderBottom: `1px solid ${theme.palette.divider}`,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <LibraryAddIcon color="primary" />
          Register Table as Variable Source
        </DialogTitle>
        <DialogContent sx={{ pt: 3, mt: 1 }}>
          <Typography variant="subtitle1" gutterBottom>
            {selectedTable?.schema}.{selectedTable?.name}
          </Typography>
          
          <TextField
            autoFocus
            margin="dense"
            name="displayName"
            label="Display Name"
            type="text"
            fullWidth
            variant="outlined"
            value={tableForm.displayName}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            type="text"
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={tableForm.description}
            onChange={handleInputChange}
            placeholder="Optional description for this table"
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setRegisterDialogOpen(false)} color="inherit">Cancel</Button>
          <Button
            onClick={handleRegisterTable}
            variant="contained"
            disabled={registering || !tableForm.displayName}
            startIcon={registering ? <CircularProgress size={20} /> : null}
          >
            {registering ? 'Registering...' : 'Register Table'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notifications */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TableDiscoveryPanel;
