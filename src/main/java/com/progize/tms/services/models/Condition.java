package com.progize.tms.services.models;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({
  @JsonSubTypes.Type(value = SimpleCondition.class, name = "SIMPLE"),
  @JsonSubTypes.Type(value = CompositeCondition.class, name = "AND"),
  @JsonSubTypes.Type(value = CompositeCondition.class, name = "OR"),
  @JsonSubTypes.Type(value = AggregateCondition.class, name = "AGGREGAT<PERSON>"),
  @JsonSubTypes.Type(value = ComparisonCondition.class, name = "COMPARISON")
})
public abstract class Condition {
  private String type;
}
