package com.progize.tms.services.models;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Represents an aggregation-based condition for transaction rules. This allows rules to be applied
 * based on aggregate values over time periods, such as "sum of transactions over the last 7 days
 * exceeds a threshold."
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AggregateCondition extends Condition {
  private String function; // SUM, COUNT, AVG, etc.
  private String field; // The field to aggregate, e.g., "fundNetAmount"
  private Integer period; // Time period in days for the aggregation
  private Integer
      startOffset; // Days in the past to start the time window (0 means start from today)
  private String operator; // Comparison operator: >, <, ==, etc.
  private Object threshold; // The value to compare against
  private Boolean isExpression; // Whether the threshold is an expression with variable references
  private String entityField; // Field to identify the entity, e.g., "customerId"
  private SimpleCondition
      filterCondition; // Optional condition to filter transactions before aggregation

  public AggregateCondition() {
    this.setType("AGGREGATE");
  }
}
