package com.progize.tms.service.impl;

import com.progize.tms.exceptions.TmsBusinessException;
import com.progize.tms.exceptions.TmsError;
import com.progize.tms.repository.DataSourceRepository;
import com.progize.tms.repository.DatabaseSourceRepository;
import com.progize.tms.repository.RestApiSourceRepository;
import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import com.progize.tms.repository.entity.RestApiSourceEntity;
import com.progize.tms.repository.entity.enums.DataSourceType;
import com.progize.tms.service.DataSourceService;
import com.progize.tms.service.datasource.DataSourceHandler;
import com.progize.tms.service.datasource.DatabaseSourceHandler;
import com.progize.tms.service.datasource.RestApiSourceHandler;
import com.progize.tms.service.model.SchemaEntity;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** Implementation of the DataSourceService interface. */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataSourceServiceImpl implements DataSourceService {

  private final DataSourceRepository dataSourceRepository;
  private final DatabaseSourceRepository databaseSourceRepository;
  private final RestApiSourceRepository restApiSourceRepository;

  @Override
  public List<DataSourceEntity> getAllDataSources() {
    return dataSourceRepository.findAll();
  }

  @Override
  public Optional<DataSourceEntity> getDataSourceById(Long id) {
    return dataSourceRepository.findById(id);
  }

  @Override
  public Optional<DatabaseSourceEntity> getDatabaseSourceByDataSourceId(Long dataSourceId) {
    return databaseSourceRepository.findByDataSourceId(dataSourceId);
  }

  @Override
  public Optional<RestApiSourceEntity> getRestApiSourceByDataSourceId(Long dataSourceId) {
    return restApiSourceRepository.findByDataSourceId(dataSourceId);
  }

  @Override
  @Transactional
  public DataSourceEntity createDatabaseSource(
      DataSourceEntity dataSource, DatabaseSourceEntity databaseSource) {
    // Save data source
    DataSourceEntity savedDataSource = dataSourceRepository.save(dataSource);

    // Set data source reference and save database source
    databaseSource.setDataSource(savedDataSource);
    databaseSourceRepository.save(databaseSource);

    return savedDataSource;
  }

  @Override
  @Transactional
  public DataSourceEntity createRestApiSource(
      DataSourceEntity dataSource, RestApiSourceEntity restApiSource) {
    // Save data source
    DataSourceEntity savedDataSource = dataSourceRepository.save(dataSource);

    // Set data source reference and save REST API source
    restApiSource.setDataSource(savedDataSource);
    restApiSourceRepository.save(restApiSource);

    return savedDataSource;
  }

  @Override
  @Transactional
  public DataSourceEntity updateDataSource(Long id, DataSourceEntity dataSource) {
    // Check if data source exists
    DataSourceEntity existingDataSource = findDataSourceOrThrow(id);

    // Update fields
    existingDataSource.setName(dataSource.getName());
    existingDataSource.setDescription(dataSource.getDescription());
    existingDataSource.setActive(dataSource.getActive());

    return dataSourceRepository.save(existingDataSource);
  }

  @Override
  @Transactional
  public DatabaseSourceEntity updateDatabaseSource(
      Long dataSourceId, DatabaseSourceEntity databaseSource) {
    // Check if data source exists
    findDataSourceOrThrow(dataSourceId);

    // Check if database source exists
    DatabaseSourceEntity existingDatabaseSource = findDatabaseSourceOrThrow(dataSourceId);

    // Update fields
    existingDatabaseSource.setConnectionUrl(databaseSource.getConnectionUrl());
    existingDatabaseSource.setDriverClass(databaseSource.getDriverClass());
    existingDatabaseSource.setUsername(databaseSource.getUsername());

    // Only update password if provided
    if (databaseSource.getPassword() != null && !databaseSource.getPassword().isEmpty()) {
      existingDatabaseSource.setPassword(databaseSource.getPassword());
    }

    existingDatabaseSource.setDatabaseType(databaseSource.getDatabaseType());
    existingDatabaseSource.setSchemaName(databaseSource.getSchemaName());
    existingDatabaseSource.setAdditionalProperties(databaseSource.getAdditionalProperties());

    return databaseSourceRepository.save(existingDatabaseSource);
  }

  @Override
  @Transactional
  public RestApiSourceEntity updateRestApiSource(
      Long dataSourceId, RestApiSourceEntity restApiSource) {
    // Check if data source exists
    findDataSourceOrThrow(dataSourceId);

    // Check if REST API source exists
    RestApiSourceEntity existingRestApiSource = findRestApiSourceOrThrow(dataSourceId);

    // Update fields
    existingRestApiSource.setBaseUrl(restApiSource.getBaseUrl());
    existingRestApiSource.setAuthenticationType(restApiSource.getAuthenticationType());
    existingRestApiSource.setUsername(restApiSource.getUsername());

    // Only update password if provided
    if (restApiSource.getPassword() != null && !restApiSource.getPassword().isEmpty()) {
      existingRestApiSource.setPassword(restApiSource.getPassword());
    }

    existingRestApiSource.setApiKeyName(restApiSource.getApiKeyName());

    // Only update API key value if provided
    if (restApiSource.getApiKeyValue() != null && !restApiSource.getApiKeyValue().isEmpty()) {
      existingRestApiSource.setApiKeyValue(restApiSource.getApiKeyValue());
    }

    existingRestApiSource.setApiKeyLocation(restApiSource.getApiKeyLocation());
    existingRestApiSource.setOauthTokenUrl(restApiSource.getOauthTokenUrl());
    existingRestApiSource.setOauthClientId(restApiSource.getOauthClientId());

    // Only update OAuth client secret if provided
    if (restApiSource.getOauthClientSecret() != null
        && !restApiSource.getOauthClientSecret().isEmpty()) {
      existingRestApiSource.setOauthClientSecret(restApiSource.getOauthClientSecret());
    }

    existingRestApiSource.setOauthScope(restApiSource.getOauthScope());
    existingRestApiSource.setHeaders(restApiSource.getHeaders());
    existingRestApiSource.setRequestTimeoutMs(restApiSource.getRequestTimeoutMs());

    return restApiSourceRepository.save(existingRestApiSource);
  }

  @Override
  @Transactional
  public void deleteDataSource(Long id) {
    // Check if data source exists
    DataSourceEntity existingDataSource = findDataSourceOrThrow(id);

    // Delete specific source type first to avoid foreign key constraints
    if (existingDataSource.getType() == DataSourceType.DATABASE) {
      databaseSourceRepository.deleteByDataSourceId(id);
    } else if (existingDataSource.getType() == DataSourceType.REST_API) {
      restApiSourceRepository.deleteByDataSourceId(id);
    }

    // Delete data source
    dataSourceRepository.deleteById(id);
  }

  @Override
  public boolean testConnection(Long id) {
    // Get data source
    DataSourceEntity dataSource = findDataSourceOrThrow(id);

    // Get handler for the data source
    DataSourceHandler handler = getHandlerForDataSource(dataSource);

    // Test connection
    return handler.testConnection();
  }

  @Override
  @Transactional
  public List<SchemaEntity> discoverSchema(Long id) {
    // Get data source
    DataSourceEntity dataSource = findDataSourceOrThrow(id);

    // Get handler for the data source
    DataSourceHandler handler = getHandlerForDataSource(dataSource);

    try {
      // Discover schema
      List<SchemaEntity> schema = handler.discoverSchema();

      // Update last schema discovery timestamp
      dataSource.setLastSchemaDiscovery(LocalDateTime.now());
      dataSourceRepository.save(dataSource);

      return schema;
    } catch (Exception e) {
      log.error("Error discovering schema for data source {}: {}", id, e.getMessage(), e);
      throw new TmsBusinessException(TmsError.SCHEMA_DISCOVERY_ERROR);
    }
  }

  @Override
  public List<Map<String, Object>> executeQuery(
      Long id, String entityName, Map<String, Object> parameters) {
    // Get data source
    DataSourceEntity dataSource = findDataSourceOrThrow(id);

    // Check if data source is active
    if (!dataSource.getActive()) {
      throw new TmsBusinessException(TmsError.DATA_SOURCE_INACTIVE);
    }

    // Get handler for the data source
    DataSourceHandler handler = getHandlerForDataSource(dataSource);

    // Execute query
    return handler.executeQuery(entityName, parameters);
  }

  @Override
  public List<DataSourceType> getDataSourceTypes() {
    return Arrays.asList(DataSourceType.values());
  }

  /**
   * Get the appropriate handler for a data source.
   *
   * @param dataSource Data source entity
   * @return Data source handler
   */
  private DataSourceHandler getHandlerForDataSource(DataSourceEntity dataSource) {
    switch (dataSource.getType()) {
      case DATABASE:
        DatabaseSourceEntity databaseSource = findDatabaseSourceOrThrow(dataSource.getId());
        return new DatabaseSourceHandler(databaseSource);
      case REST_API:
        RestApiSourceEntity restApiSource = findRestApiSourceOrThrow(dataSource.getId());
        return new RestApiSourceHandler(restApiSource);
      default:
        throw new TmsBusinessException(TmsError.UNSUPPORTED_DATA_SOURCE_TYPE);
    }
  }

  /**
   * Find a data source by ID or throw an exception if not found.
   *
   * @param id Data source ID
   * @return Data source entity
   * @throws TmsBusinessException if data source is not found
   */
  private DataSourceEntity findDataSourceOrThrow(Long id) {
    return dataSourceRepository
        .findById(id)
        .orElseThrow(() -> new TmsBusinessException(TmsError.DATA_SOURCE_NOT_FOUND));
  }

  /**
   * Find a database source by data source ID or throw an exception if not found.
   *
   * @param dataSourceId Data source ID
   * @return Database source entity
   * @throws TmsBusinessException if database source is not found
   */
  private DatabaseSourceEntity findDatabaseSourceOrThrow(Long dataSourceId) {
    return databaseSourceRepository
        .findByDataSourceId(dataSourceId)
        .orElseThrow(() -> new TmsBusinessException(TmsError.DATABASE_SOURCE_NOT_FOUND));
  }

  /**
   * Find a REST API source by data source ID or throw an exception if not found.
   *
   * @param dataSourceId Data source ID
   * @return REST API source entity
   * @throws TmsBusinessException if REST API source is not found
   */
  private RestApiSourceEntity findRestApiSourceOrThrow(Long dataSourceId) {
    return restApiSourceRepository
        .findByDataSourceId(dataSourceId)
        .orElseThrow(() -> new TmsBusinessException(TmsError.REST_API_SOURCE_NOT_FOUND));
  }
}
