package com.progize.tms.repository.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

/** Entity representing a history entry for a workflow case. */
@Entity
@Table(name = "case_history")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaseHistory {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "case_id", nullable = false)
  private WorkflowCase workflowCase;

  @Column(name = "process_instance_id")
  private String processInstanceId;

  @Column(name = "task_id")
  private String taskId;

  @Column(name = "task_name")
  private String taskName;

  @Column(name = "event_type", nullable = false)
  private String
      action; // Field name kept as 'action' for backward compatibility with existing code

  @Column(name = "old_status")
  private String oldStatus;

  @Column(name = "new_status")
  private String newStatus;

  @Column(name = "details", columnDefinition = "TEXT")
  private String details;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @Column(name = "created_by")
  private Long createdBy;
}
