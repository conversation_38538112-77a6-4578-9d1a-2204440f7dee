-- Get admin user ID for audit fields
SET @admin_id = (SELECT id FROM users WHERE username = 'admin');
SET @current_timestamp = NOW();

-- Data Types
INSERT INTO data_types (code, display_name, description, metadata, created_at, updated_at, created_by, updated_by) VALUES
('string', 'String', 'Text value', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('integer', 'Integer', 'Whole number value', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('long', 'Long', 'Long integer value', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('float', 'Float', 'Floating point number', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('double', 'Double', 'Double precision floating point number', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('boolean', 'Boolean', 'True/false value', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('date', 'Date', 'Date value', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('bigdecimal', 'Big Decimal', 'High precision decimal value', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('number', 'Number', 'Generic number type (for backward compatibility)', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id);

-- Operators
INSERT INTO operators (code, display_name, description, symbol, metadata, created_at, updated_at, created_by, updated_by) VALUES
('equals', 'Equals', 'Checks if values are equal', '=', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('notEquals', 'Not Equals', 'Checks if values are not equal', '!=', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('greaterThan', 'Greater Than', 'Checks if left value is greater than right value', '>', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('lessThan', 'Less Than', 'Checks if left value is less than right value', '<', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('greaterThanEquals', 'Greater Than or Equal', 'Checks if left value is greater than or equal to right value', '>=', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('lessThanEquals', 'Less Than or Equal', 'Checks if left value is less than or equal to right value', '<=', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('contains', 'Contains', 'Checks if string contains substring', 'contains', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('startsWith', 'Starts With', 'Checks if string starts with substring', 'startsWith', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('endsWith', 'Ends With', 'Checks if string ends with substring', 'endsWith', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('in', 'In', 'Checks if value is in a list of values', 'in', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('notIn', 'Not In', 'Checks if value is not in a list of values', 'not in', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('isTrue', 'Is True', 'Checks if boolean value is true', 'is true', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('isFalse', 'Is False', 'Checks if boolean value is false', 'is false', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('isNull', 'Is Null', 'Checks if value is null', 'is null', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('isNotNull', 'Is Not Null', 'Checks if value is not null', 'is not null', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('isEmpty', 'Is Empty', 'Checks if string or collection is empty', 'is empty', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('isNotEmpty', 'Is Not Empty', 'Checks if string or collection is not empty', 'is not empty', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('matches', 'Matches', 'Checks if string matches a regular expression', 'matches', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
('between', 'Between', 'Checks if value is between two other values', 'between', '{}', @current_timestamp, @current_timestamp, @admin_id, @admin_id);

-- Store IDs for data types and operators for mapping
SET @string_id = (SELECT id FROM data_types WHERE code = 'string');
SET @integer_id = (SELECT id FROM data_types WHERE code = 'integer');
SET @long_id = (SELECT id FROM data_types WHERE code = 'long');
SET @float_id = (SELECT id FROM data_types WHERE code = 'float');
SET @double_id = (SELECT id FROM data_types WHERE code = 'double');
SET @boolean_id = (SELECT id FROM data_types WHERE code = 'boolean');
SET @date_id = (SELECT id FROM data_types WHERE code = 'date');
SET @bigdecimal_id = (SELECT id FROM data_types WHERE code = 'bigdecimal');
SET @number_id = (SELECT id FROM data_types WHERE code = 'number');

SET @equals_id = (SELECT id FROM operators WHERE code = 'equals');
SET @not_equals_id = (SELECT id FROM operators WHERE code = 'notEquals');
SET @gt_id = (SELECT id FROM operators WHERE code = 'greaterThan');
SET @lt_id = (SELECT id FROM operators WHERE code = 'lessThan');
SET @gte_id = (SELECT id FROM operators WHERE code = 'greaterThanEquals');
SET @lte_id = (SELECT id FROM operators WHERE code = 'lessThanEquals');
SET @contains_id = (SELECT id FROM operators WHERE code = 'contains');
SET @starts_with_id = (SELECT id FROM operators WHERE code = 'startsWith');
SET @ends_with_id = (SELECT id FROM operators WHERE code = 'endsWith');
SET @in_id = (SELECT id FROM operators WHERE code = 'in');
SET @not_in_id = (SELECT id FROM operators WHERE code = 'notIn');
SET @is_true_id = (SELECT id FROM operators WHERE code = 'isTrue');
SET @is_false_id = (SELECT id FROM operators WHERE code = 'isFalse');
SET @is_null_id = (SELECT id FROM operators WHERE code = 'isNull');
SET @is_not_null_id = (SELECT id FROM operators WHERE code = 'isNotNull');
SET @is_empty_id = (SELECT id FROM operators WHERE code = 'isEmpty');
SET @is_not_empty_id = (SELECT id FROM operators WHERE code = 'isNotEmpty');
SET @matches_id = (SELECT id FROM operators WHERE code = 'matches');
SET @between_id = (SELECT id FROM operators WHERE code = 'between');

-- Data Type Supported Operators
-- String operators
INSERT INTO data_type_supported_operators (data_type_id, operator_id, created_at, updated_at, created_by, updated_by) 
SELECT @string_id, id, @current_timestamp, @current_timestamp, @admin_id, @admin_id
FROM operators 
WHERE code IN ('equals', 'notEquals', 'contains', 'startsWith', 'endsWith', 'matches', 'in', 'notIn', 'isNull', 'isNotNull', 'isEmpty', 'isNotEmpty');

-- Numeric operators (integer, long, float, double, bigdecimal, number)
INSERT INTO data_type_supported_operators (data_type_id, operator_id, created_at, updated_at, created_by, updated_by)
SELECT dt.id, o.id, @current_timestamp, @current_timestamp, @admin_id, @admin_id
FROM data_types dt
CROSS JOIN operators o
WHERE dt.code IN ('integer', 'long', 'float', 'double', 'bigdecimal', 'number')
AND o.code IN ('equals', 'notEquals', 'greaterThan', 'lessThan', 'greaterThanEquals', 'lessThanEquals', 'in', 'notIn', 'isNull', 'isNotNull');

-- Boolean operators
INSERT INTO data_type_supported_operators (data_type_id, operator_id, created_at, updated_at, created_by, updated_by)
SELECT @boolean_id, id, @current_timestamp, @current_timestamp, @admin_id, @admin_id
FROM operators
WHERE code IN ('equals', 'notEquals', 'isTrue', 'isFalse', 'isNull', 'isNotNull');

-- Date operators
INSERT INTO data_type_supported_operators (data_type_id, operator_id, created_at, updated_at, created_by, updated_by)
SELECT @date_id, id, @current_timestamp, @current_timestamp, @admin_id, @admin_id
FROM operators
WHERE code IN ('equals', 'notEquals', 'greaterThan', 'lessThan', 'greaterThanEquals', 'lessThanEquals', 'between', 'isNull', 'isNotNull');

-- Operator Expressions
INSERT INTO operator_expressions (id, operator_id, data_type_id, expression_template, created_at, updated_at, created_by, updated_by) VALUES
(1, 1, null, '==', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(2, 2, null, '!=', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(3, 3, null, '>', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(4, 4, null, '<', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(5, 5, null, '>=', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(6, 6, null, '<=', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(7, 7, null, '.contains', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(8, 8, null, '.startsWith', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(9, 9, null, '.endsWith', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(10, 10, null, ' in ', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(11, 11, null, ' not in ', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(12, 12, null, '== true', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(13, 13, null, '== false', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(14, 14, null, '== null', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(15, 15, null, '!= null', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(16, 1, 8, '.compareTo(%s) == 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(17, 2, 8, '.compareTo(%s) != 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(18, 3, 8, '.compareTo(%s) > 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(19, 4, 8, '.compareTo(%s) < 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(20, 5, 8, '.compareTo(%s) >= 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(21, 6, 8, '.compareTo(%s) <= 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(22, 1, 9, '.compareTo(%s) == 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(23, 2, 9, '.compareTo(%s) != 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(24, 3, 9, '.compareTo(%s) > 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(25, 4, 9, '.compareTo(%s) < 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(26, 5, 9, '.compareTo(%s) >= 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(27, 6, 9, '.compareTo(%s) <= 0', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(28, 1, 2, '== %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(29, 2, 2, '!= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(30, 3, 2, '> %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(31, 4, 2, '< %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(32, 5, 2, '>= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(33, 6, 2, '<= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(34, 1, 3, '== %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(35, 2, 3, '!= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(36, 3, 3, '> %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(37, 4, 3, '< %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(38, 5, 3, '>= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(39, 6, 3, '<= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(40, 1, 5, '== %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(41, 2, 5, '!= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(42, 3, 5, '> %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(43, 4, 5, '< %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(44, 5, 5, '>= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(45, 6, 5, '<= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(46, 1, 4, '== %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(47, 2, 4, '!= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(48, 3, 4, '> %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(49, 4, 4, '< %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(50, 5, 4, '>= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(51, 6, 4, '<= %s', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(52, 1, 7, '.isEqual(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(53, 2, 7, '!(.isEqual(%s))', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(54, 3, 7, '.isAfter(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(55, 5, 7, '!(.isBefore(%s))', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(56, 4, 7, '.isBefore(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(57, 6, 7, '!(.isAfter(%s))', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(58, 19, 7, '.isAfter(%s) && .isBefore(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(59, 1, 1, '.equals(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(60, 2, 1, '!(.equals(%s))', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(61, 7, 1, '.contains(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(62, 8, 1, '.startsWith(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(63, 9, 1, '.endsWith(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(64, 18, 1, '.matches(%s)', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(65, 16, 1, '.isEmpty()', @current_timestamp, @current_timestamp, @admin_id, @admin_id),
(66, 17, 1, '!(.isEmpty())', @current_timestamp, @current_timestamp, @admin_id, @admin_id);

