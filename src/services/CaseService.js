import axios from 'axios';
import { API_BASE_URL } from '../config';

const CASE_API_URL = `${API_BASE_URL}/api/cases`;

/**
 * Service for interacting with case management APIs
 */
class CaseService {
  /**
   * Get all cases with a specific status
   * @param {string} status - Case status (OPEN, IN_PROGRESS, CLOSED, etc.)
   * @returns {Promise<Array>} - List of cases
   */
  async getCasesByStatus(status) {
    try {
      const response = await axios.get(`${CASE_API_URL}/status/${status}`);
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching cases by status:', error);
      throw error;
    }
  }

  /**
   * Get a specific case by ID
   * @param {number} caseId - Case ID
   * @returns {Promise<Object>} - Case details
   */
  async getCaseById(caseId) {
    try {
      const response = await axios.get(`${CASE_API_URL}/${caseId}`);
      return response.data.data;
    } catch (error) {
      console.error(`Error fetching case ${caseId}:`, error);
      throw error;
    }
  }

  /**
   * Update a case's status
   * @param {number} caseId - Case ID
   * @param {string} status - New status
   * @param {string} userId - User ID making the change
   * @returns {Promise<Object>} - Updated case
   */
  async updateCaseStatus(caseId, status, userId) {
    try {
      const response = await axios.put(`${CASE_API_URL}/${caseId}/status`, { status, userId });
      return response.data.data;
    } catch (error) {
      console.error(`Error updating case ${caseId} status:`, error);
      throw error;
    }
  }

  /**
   * Add a comment to a case
   * @param {number} caseId - Case ID
   * @param {string} userId - User ID adding the comment
   * @param {string} commentText - Comment text
   * @returns {Promise<Object>} - Created comment
   */
  async addComment(caseId, userId, commentText) {
    try {
      const response = await axios.post(`${CASE_API_URL}/${caseId}/comments`, {
        userId,
        commentText
      });
      return response.data.data;
    } catch (error) {
      console.error(`Error adding comment to case ${caseId}:`, error);
      throw error;
    }
  }

  /**
   * Get all comments for a case
   * @param {number} caseId - Case ID
   * @returns {Promise<Array>} - List of comments
   */
  async getComments(caseId) {
    try {
      const response = await axios.get(`${CASE_API_URL}/${caseId}/comments`);
      return response.data.data || [];
    } catch (error) {
      console.error(`Error fetching comments for case ${caseId}:`, error);
      throw error;
    }
  }

  /**
   * Add an attachment to a case
   * @param {number} caseId - Case ID
   * @param {string} userId - User ID uploading the attachment
   * @param {File} file - File to upload
   * @returns {Promise<Object>} - Created attachment
   */
  async addAttachment(caseId, userId, file) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', userId);

      const response = await axios.post(
        `${CASE_API_URL}/${caseId}/attachments`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response.data.data;
    } catch (error) {
      console.error(`Error adding attachment to case ${caseId}:`, error);
      throw error;
    }
  }

  /**
   * Get all attachments for a case
   * @param {number} caseId - Case ID
   * @returns {Promise<Array>} - List of attachments
   */
  async getAttachments(caseId) {
    try {
      const response = await axios.get(`${CASE_API_URL}/${caseId}/attachments`);
      return response.data.data || [];
    } catch (error) {
      console.error(`Error fetching attachments for case ${caseId}:`, error);
      throw error;
    }
  }

  /**
   * Get all transactions associated with a case
   * @param {number} caseId - Case ID
   * @returns {Promise<Array>} - List of transactions
   */
  async getTransactions(caseId) {
    try {
      const response = await axios.get(`${CASE_API_URL}/${caseId}/transactions`);
      return response.data.data || [];
    } catch (error) {
      console.error(`Error fetching transactions for case ${caseId}:`, error);
      throw error;
    }
  }

  /**
   * Close a case
   * @param {number} caseId - Case ID
   * @param {string} userId - User ID closing the case
   * @param {string} reason - Reason for closing
   * @returns {Promise<Object>} - Updated case
   */
  async closeCase(caseId, userId, reason) {
    try {
      const response = await axios.put(`${CASE_API_URL}/${caseId}/close`, {
        userId,
        reason
      });
      return response.data.data;
    } catch (error) {
      console.error(`Error closing case ${caseId}:`, error);
      throw error;
    }
  }
}

export default new CaseService();
