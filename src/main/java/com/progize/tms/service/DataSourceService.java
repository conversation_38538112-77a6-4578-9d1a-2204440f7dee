package com.progize.tms.service;

import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import com.progize.tms.repository.entity.RestApiSourceEntity;
import com.progize.tms.repository.entity.enums.DataSourceType;
import com.progize.tms.service.model.SchemaEntity;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/** Service interface for data sources. */
public interface DataSourceService {

  /**
   * Get all data sources.
   *
   * @return List of data sources
   */
  List<DataSourceEntity> getAllDataSources();

  /**
   * Get data source by ID.
   *
   * @param id Data source ID
   * @return Optional containing the data source if found
   */
  Optional<DataSourceEntity> getDataSourceById(Long id);

  /**
   * Get database source by data source ID.
   *
   * @param dataSourceId Data source ID
   * @return Optional containing the database source if found
   */
  Optional<DatabaseSourceEntity> getDatabaseSourceByDataSourceId(Long dataSourceId);

  /**
   * Get REST API source by data source ID.
   *
   * @param dataSourceId Data source ID
   * @return Optional containing the REST API source if found
   */
  Optional<RestApiSourceEntity> getRestApiSourceByDataSourceId(Long dataSourceId);

  /**
   * Create a database data source.
   *
   * @param dataSource Data source entity
   * @param databaseSource Database source entity
   * @return Created data source entity
   */
  DataSourceEntity createDatabaseSource(
      DataSourceEntity dataSource, DatabaseSourceEntity databaseSource);

  /**
   * Create a REST API data source.
   *
   * @param dataSource Data source entity
   * @param restApiSource REST API source entity
   * @return Created data source entity
   */
  DataSourceEntity createRestApiSource(
      DataSourceEntity dataSource, RestApiSourceEntity restApiSource);

  /**
   * Update a data source.
   *
   * @param id Data source ID
   * @param dataSource Data source entity
   * @return Updated data source entity
   */
  DataSourceEntity updateDataSource(Long id, DataSourceEntity dataSource);

  /**
   * Update a database source.
   *
   * @param dataSourceId Data source ID
   * @param databaseSource Database source entity
   * @return Updated database source entity
   */
  DatabaseSourceEntity updateDatabaseSource(Long dataSourceId, DatabaseSourceEntity databaseSource);

  /**
   * Update a REST API source.
   *
   * @param dataSourceId Data source ID
   * @param restApiSource REST API source entity
   * @return Updated REST API source entity
   */
  RestApiSourceEntity updateRestApiSource(Long dataSourceId, RestApiSourceEntity restApiSource);

  /**
   * Delete a data source.
   *
   * @param id Data source ID
   */
  void deleteDataSource(Long id);

  /**
   * Test connection to a data source.
   *
   * @param id Data source ID
   * @return true if connection is successful, false otherwise
   */
  boolean testConnection(Long id);

  /**
   * Discover schema from a data source.
   *
   * @param id Data source ID
   * @return List of discovered schema entities
   */
  List<SchemaEntity> discoverSchema(Long id);

  /**
   * Execute a query against a data source.
   *
   * @param id Data source ID
   * @param entityName Name of the entity to query
   * @param parameters Query parameters
   * @return List of results as maps
   */
  List<Map<String, Object>> executeQuery(
      Long id, String entityName, Map<String, Object> parameters);

  /**
   * Get data source types.
   *
   * @return List of data source types
   */
  List<DataSourceType> getDataSourceTypes();
}
