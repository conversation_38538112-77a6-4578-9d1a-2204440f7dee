import React from 'react';
import { Box, Typography, Button, Paper, useTheme } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

const NotFound = () => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  return (
    <Box 
      sx={{ 
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 'calc(100vh - 120px)',
      }}
    >
      <Paper 
        elevation={1} 
        sx={{ 
          p: 5, 
          borderRadius: 2, 
          textAlign: 'center',
          maxWidth: 500,
          background: isDarkMode ? 'rgba(255,255,255,0.03)' : 'white',
          border: isDarkMode ? '1px solid rgba(255,255,255,0.05)' : 'none'
        }}
      >
        <ErrorOutlineIcon sx={{ fontSize: 80, color: 'warning.main', mb: 2 }} />
        
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 700 }}>
          404 - Page Not Found
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          The page you are looking for doesn't exist or has been moved.
        </Typography>
        
        <Button 
          component={RouterLink} 
          to="/" 
          variant="contained" 
          color="primary" 
          size="large"
          sx={{ 
            minWidth: 200,
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
            '&:hover': {
              boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
            }
          }}
        >
          Back to Home
        </Button>
      </Paper>
    </Box>
  );
};

export default NotFound;
