import React, { createContext, useState, useContext, useEffect } from 'react';

// Create context with default values
export const ThemeContext = createContext({
  isDarkMode: false,
  toggleTheme: () => {},
});

// Theme provider component
export const ThemeProvider = ({ children }) => {
  // Check local storage or system preference for initial theme
  const getInitialTheme = () => {
    const savedTheme = localStorage.getItem('progize-theme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    // Check system preference
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  };

  const [isDarkMode, setIsDarkMode] = useState(getInitialTheme());

  // Toggle theme function
  const toggleTheme = () => {
    setIsDarkMode(prevMode => !prevMode);
  };

  // Update localStorage when theme changes
  useEffect(() => {
    localStorage.setItem('progize-theme', isDarkMode ? 'dark' : 'light');
    // Update document attributes for global styling
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
    // Add/remove dark class from body
    if (isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [isDarkMode]);

  return (
    <ThemeContext.Provider value={{ isDarkMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook for easy access to theme context
export const useThemeContext = () => useContext(ThemeContext);
