package com.progize.tms.repository;

import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Repository for DatabaseSourceEntity. */
@Repository
public interface DatabaseSourceRepository extends JpaRepository<DatabaseSourceEntity, Long> {
  /**
   * Find database source by data source entity.
   *
   * @param dataSource Data source entity
   * @return Optional database source entity
   */
  Optional<DatabaseSourceEntity> findByDataSource(DataSourceEntity dataSource);

  /**
   * Find database source by data source ID.
   *
   * @param dataSourceId Data source ID
   * @return Optional database source entity
   */
  Optional<DatabaseSourceEntity> findByDataSourceId(Long dataSourceId);

  /**
   * Delete database source by data source ID.
   *
   * @param dataSourceId Data source ID
   */
  void deleteByDataSourceId(Long dataSourceId);
}
