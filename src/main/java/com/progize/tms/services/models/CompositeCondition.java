package com.progize.tms.services.models;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CompositeCondition extends Condition {
  private List<Condition> conditions;

  public CompositeCondition() {
    // Default constructor needed for deserialization
  }

  public CompositeCondition(String type, List<Condition> conditions) {
    this.setType(type);
    this.conditions = conditions;
  }
}
