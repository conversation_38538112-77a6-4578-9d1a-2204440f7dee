import React, { Suspense } from 'react';
import { Await, defer, useLoaderData } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { dataBucketService } from '../../services/api';
import RuleVariableRegistryPage from './RuleVariableRegistryPage';

// Loader function to fetch initial data
export async function ruleVariableRegistryLoader() {
  const bucketsPromise = dataBucketService.getAllBuckets();

  // Defer both the buckets and variables loading
  return defer({
    buckets: bucketsPromise,
    variables: bucketsPromise.then(async (buckets) => {
      const results = await Promise.all(
        buckets.map(bucket => 
          dataBucketService.getVariablesByBucketId(bucket.id)
            .then(variables => ({ bucketId: bucket.id, variables }))
        )
      );
      
      return results.reduce((acc, { bucketId, variables }) => {
        acc[bucketId] = variables;
        return acc;
      }, {});
    })
  });
}

// Loader component that handles the data fetching
export default function RuleVariableRegistryLoader() {
  const data = useLoaderData();

  return (
    <Suspense fallback={
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    }>
      <Await
        resolve={data.buckets}
        errorElement={<div>Error loading buckets</div>}
      >
        {(buckets) => (
          <Await
            resolve={data.variables}
            errorElement={<div>Error loading variables</div>}
          >
            {(variables) => (
              <RuleVariableRegistryPage
                initialBuckets={buckets}
                initialVariables={variables}
              />
            )}
          </Await>
        )}
      </Await>
    </Suspense>
  );
}
