import React, { useState, useEffect, useRef } from 'react';
import {
    <PERSON><PERSON>,
    alpha,
    Avatar,
    Box,
    Button,
    Card,
    CardActionArea,
    CardContent,
    Checkbox,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    Drawer,
    FormControlLabel,
    Grid,
    IconButton,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    MenuItem,
    Paper,
    Snackbar,
    TextField,
    Tooltip,
    Typography,
    useTheme
} from '@mui/material';
import {
    Add as AddIcon,
    Close as CloseIcon,
    DataObject as DataObjectIcon,
    Delete as DeleteIcon,
    Edit as EditIcon,
    Storage as StorageIcon,
    Visibility as VisibilityIcon,
    TableChart as TableChartIcon,
    TableView as TableViewIcon,
    Folder as FolderIcon,
    Link as LinkIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { dataBucketService, bucketRelationshipService } from '../../services/api';
import { ruleVariableRegistryService } from '../../services/api';
import dataSourceService from '../../services/dataSourceService';
import BucketVariableSelectionDialog from '../databuckets/BucketVariableSelectionDialog';
import BucketRelationshipList from './BucketRelationshipList';
import BucketRelationshipDialog from './BucketRelationshipDialog';
import {useNavigate} from 'react-router-dom';
import ActionButton from '../common/ActionButton';

// Bucket types from the backend enum
const BUCKET_TYPES = [
    { value: 'RULE_BUCKET', label: 'Rule Bucket', description: 'For rule-specific variables and transaction data' },
    { value: 'LOOKUP', label: 'Lookup Bucket', description: 'For lookup variables from database tables to enrich rule buckets' }
];

// Type indicator for buckets
const TypeIndicator = styled(Box)(({theme}) => ({
    width: 40,
    height: 40,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: 'bold',
    fontSize: '14px',
    flexShrink: 0,
    position: 'relative'
}));

// Small label for the type indicator
const CountLabel = styled(Box)(({theme}) => ({
    position: 'absolute',
    bottom: -10,
    left: '50%',
    transform: 'translateX(-50%)',
    fontSize: '8px',
    fontWeight: 'bold',
    textTransform: 'uppercase',
    padding: '1px 4px',
    borderRadius: '4px',
    whiteSpace: 'nowrap',
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.secondary,
    border: `1px solid ${theme.palette.divider}`,
    boxShadow: theme.palette.mode === 'dark' ? 'none' : theme.shadows[1]
}));

// Chips for variables and tags
const VariableChip = styled(Chip)(({theme}) => ({
    height: 24,
    fontSize: '0.75rem',
    backgroundColor: theme.palette.mode === 'dark'
        ? alpha(theme.palette.grey[700], 0.4)
        : alpha(theme.palette.grey[200], 0.4),
    color: theme.palette.mode === 'dark'
        ? theme.palette.grey[300]
        : theme.palette.grey[700],
    '& .MuiChip-label': {
        padding: '0 8px',
    }
}));

// Styled drawer
const StyledDrawer = styled(Drawer)(({theme}) => ({
    '& .MuiDrawer-paper': {
        width: '65%',
        minWidth: '800px',
        maxWidth: '1200px',
        borderTopLeftRadius: theme.shape.borderRadius,
        borderBottomLeftRadius: theme.shape.borderRadius,
        boxShadow: '-4px 0 20px rgba(0, 0, 0, 0.1)',
    }
}));

const DrawerHeader = styled(Box)(({theme}) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing(2, 3),
    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.7)}`,
    backgroundColor: theme.palette.mode === 'dark'
        ? alpha(theme.palette.background.paper, 0.9)
        : '#fff',
    '&:hover': {
        backgroundColor: theme.palette.mode === 'dark'
            ? alpha(theme.palette.background.paper, 0.2)
            : alpha(theme.palette.background.default, 0.8),
    }
}));

const DataBucketsPanel = ({initialBuckets = [], createDrawerOpen, setCreateDrawerOpen}) => {
    const theme = useTheme();
    const navigate = useNavigate();
    const [buckets, setBuckets] = useState(initialBuckets);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [bucketVariables, setBucketVariables] = useState({});
    const [variablesDrawerOpen, setVariablesDrawerOpen] = useState(false);
    const [viewingBucket, setViewingBucket] = useState(null);
    const [variablesDialogOpen, setVariablesDialogOpen] = useState(false);
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [promptAddVariables, setPromptAddVariables] = useState(false);
    const [dataSources, setDataSources] = useState([]);
    const [discoveredTables, setDiscoveredTables] = useState([]);
    const [selectedTable, setSelectedTable] = useState(null);
    const [discoveredColumns, setDiscoveredColumns] = useState([]);
    const [loadingTables, setLoadingTables] = useState(false);
    const [loadingColumns, setLoadingColumns] = useState(false);
    const [selectedColumns, setSelectedColumns] = useState([]);
    const [tableColumns, setTableColumns] = useState([]);
    const [newBucket, setNewBucket] = useState({
        name: '', 
        description: '', 
        bucketType: 'RULE_BUCKET', 
        tableName: '', 
        dataSourceId: null, 
        variables: []
    });
    const [relationshipDialogOpen, setRelationshipDialogOpen] = useState(false);
    const [relationshipToEdit, setRelationshipToEdit] = useState(null);
    const [selectedDataSource, setSelectedDataSource] = useState(null);
    const [loadingDataSources, setLoadingDataSources] = useState(false);
    const chipContainerRefs = React.useRef({});

    // Initialize buckets on mount
    useEffect(() => {
        setBuckets(initialBuckets || []);
    }, []);

    // Load data sources on mount
    useEffect(() => {
        loadDataSources();
    }, []);

    // Load buckets if no initial data
    useEffect(() => {
        const loadBucketsIfNeeded = async () => {
            if (!initialBuckets?.length && !loading) {
                setLoading(true);
                try {
                    const newBuckets = await dataBucketService.getAllBuckets();
                    setBuckets(newBuckets);
                } catch (err) {
                    console.error('Error loading buckets:', err);
                    setError('Failed to load buckets');
                } finally {
                    setLoading(false);
                }
            }
        };
        loadBucketsIfNeeded();
    }, []);

    // Load variables for buckets
    useEffect(() => {
        const loadVariables = async () => {
            if (!buckets?.length) return;
            if (loading) return;

            setLoading(true);
            try {
                const promises = buckets.map(async bucket => {
                    try {
                        const vars = await dataBucketService.getVariablesByBucketId(bucket.id);
                        return { bucketId: bucket.id, variables: vars };
                    } catch (error) {
                        console.error(`Error loading variables for bucket ${bucket.id}:`, error);
                        return { bucketId: bucket.id, variables: [] };
                    }
                });

                const results = await Promise.all(promises);
                const newVariables = results.reduce((acc, { bucketId, variables }) => {
                    acc[bucketId] = variables;
                    return acc;
                }, {});

                setBucketVariables(prev => ({
                    ...prev,
                    ...newVariables
                }));
            } catch (err) {
                console.error('Error loading variables:', err);
                setError('Failed to load variables');
            } finally {
                setLoading(false);
            }
        };
        
        loadVariables();
    }, [buckets?.length]);

    useEffect(() => {
        if (createDrawerOpen && newBucket.dataSourceId && discoveredTables.length === 0) {
            // Pass the data source ID directly to loadTables
            loadTables(newBucket.dataSourceId);
        }
    }, [createDrawerOpen, newBucket.dataSourceId, discoveredTables.length]);

    useEffect(() => {
        if (selectedTable && newBucket.dataSourceId) {
            // Pass the current data source ID directly to loadTableColumns
            loadTableColumns(selectedTable, newBucket.dataSourceId);
        } else {
            setTableColumns([]);
        }
    }, [selectedTable, newBucket.dataSourceId]);



    const loadBuckets = async () => {
        try {
            setLoading(true);
            const bucketsData = await dataBucketService.getAllBuckets();
            setBuckets(bucketsData);
        } catch (error) {
            console.error('Error loading buckets:', error);
            setError('Failed to load buckets');
            setBuckets([]);
        } finally {
            setLoading(false);
        }
    };

    const loadBucketVars = async (bucketId) => {
        try {
            console.log(`Fetching variables for bucket ${bucketId}...`);
            const response = await dataBucketService.getVariablesByBucketId(bucketId);
            console.log(`Variables response for bucket ${bucketId}:`, response);

            // Ensure we always have an array of variables
            const variables = Array.isArray(response) ? response : [];
            console.log(`Processed variables for bucket ${bucketId}:`, variables);

            // Update the state with the variables for this bucket
            setBucketVariables(prev => {
                const newState = {
                    ...prev,
                    [bucketId]: variables
                };
                console.log(`Updated bucketVariables state:`, newState);
                return newState;
            });

            return variables;
        } catch (error) {
            console.error(`Error loading variables for bucket ${bucketId}:`, error);
            // Set an empty array for this bucket's variables on error
            setBucketVariables(prev => ({
                ...prev,
                [bucketId]: []
            }));
            return [];
        }
    };

    const handleCreateBucket = async () => {
        try {
            // Validate required fields
            if (!newBucket.name) {
                setSnackbarMessage('Bucket name is required');
                setSnackbarSeverity('error');
                setSnackbarOpen(true);
                return;
            }

            // Check if data source and table are selected
            if (!newBucket.dataSourceId || !newBucket.tableName) {
                setSnackbarMessage('Data source and table are required for all bucket types');
                setSnackbarSeverity('error');
                setSnackbarOpen(true);
                return;
            }

            // No need to validate variables here - we'll handle both cases in the creation logic

            setLoading(true);
            setError(null);

            // Prepare the bucket data
            const bucketData = {
                name: newBucket.name,
                description: newBucket.description || '',
                bucketType: newBucket.bucketType,
                tableName: newBucket.tableName,
                dataSourceId: newBucket.dataSourceId
            };

            let result;

            // Check if we have selected variables
            if (newBucket.variables && newBucket.variables.length > 0) {
                // For buckets with variables, we create the bucket with variables in one call
                const lookupBucketData = {
                    ...bucketData,
                    variables: newBucket.variables.map(variable => ({
                        name: variable.name,
                        code: variable.code,
                        description: variable.description || '',
                        dataTypeCode: variable.dataTypeCode,
                        isUnique: variable.isUnique || false,
                        aggregatable: variable.aggregatable || false,
                        columnName: variable.name // Use column.name directly without toLowerCase()
                    }))
                };

                result = await dataBucketService.createLookupBucket(lookupBucketData);

                // Show success message
                const dataSourceName = dataSources.find(ds => ds.id === newBucket.dataSourceId)?.name || 'Unknown';
                setSnackbarMessage(`${newBucket.bucketType === 'LOOKUP' ? 'Lookup' : 'Rule'} bucket "${newBucket.name}" created successfully with data source "${dataSourceName}", table "${newBucket.tableName}" and ${newBucket.variables.length} variables`);
            } else {
                // For buckets without variables, we just create the bucket
                result = await dataBucketService.createBucket(bucketData);

                // Show success message
                const dataSourceName = dataSources.find(ds => ds.id === newBucket.dataSourceId)?.name || 'Unknown';
                setSnackbarMessage(`Bucket "${newBucket.name}" created successfully with data source "${dataSourceName}" and table "${newBucket.tableName}"`);
            }

            // Add the new bucket to the state
            setBuckets([...buckets, result]);

            // Reset form and close drawer
            setNewBucket({name: '', description: '', bucketType: 'RULE_BUCKET', tableName: '', dataSourceId: null, variables: []});
            setCreateDrawerOpen(false);
            setSelectedTable(null);
            setSelectedColumns([]);
            setSnackbarOpen(true);

            // For non-lookup buckets, prompt to add variables after creation
            if (newBucket.bucketType !== 'LOOKUP') {
                setPromptAddVariables(true);
                setViewingBucket(result);
            }

            // Reload buckets to get the latest data
            await loadBuckets();
        } catch (err) {
            console.error('Failed to create bucket:', err);
            setError(`Failed to create bucket: ${err.response?.data?.message || err.message}`);
            setSnackbarMessage(`Error: ${err.response?.data?.message || err.message}`);
            setSnackbarOpen(true);
        } finally {
            setLoading(false);
        }
    };

    const handleOpenVariablesDrawer = (bucket) => {
        setSelectedBucket(bucket);
        setVariablesDrawerOpen(true);
    };

    // Handle the action when user clicks "Yes" on the prompt to add variables
    const handleAddVariablesPrompt = () => {
        setSnackbarOpen(false);
        setPromptAddVariables(false);
        // Open variables drawer for the newly created bucket
        setVariablesDrawerOpen(true);
    };

    const handleCloseVariablesDrawer = () => {
        setVariablesDrawerOpen(false);
    };

    const handleRegistrationComplete = () => {
        loadBuckets(); // Reload all buckets to get updated variables
        handleCloseVariablesDrawer();
    };

    const handleBucketClick = (bucket) => {
        setSelectedBucket(bucket);
        setVariablesDrawerOpen(true);
    };

    const refreshBucketVariables = async (bucketId) => {
        await loadBucketVars(bucketId);
    };

    const handleDeleteVariable = async (bucketId, variableId, e) => {
        e.stopPropagation(); // Prevent card click

        try {
            await dataBucketService.deleteVariable(bucketId, variableId);

            // Update local state
            setBucketVariables(prev => {
                const updatedVars = (prev[bucketId] || []).filter(v => v.id !== variableId);
                return {
                    ...prev,
                    [bucketId]: updatedVars
                };
            });

            // Show success message
            setSnackbarMessage('Variable deleted successfully');
            setSnackbarOpen(true);
        } catch (err) {
            console.error(`Failed to delete variable ${variableId}:`, err);
            setError('Failed to delete variable. Please try again.');
        }
    };

    const handleSnackbarClose = (event, reason) => {
        if (reason === 'clickaway') {
            return;
        }

        setSnackbarOpen(false);
        // Reset prompt flag when snackbar is closed
        if (promptAddVariables) {
            setPromptAddVariables(false);
        }
    };

    const handleViewAllVariables = (bucket) => {
        console.log(`Opening variables dialog for bucket:`, bucket);
        console.log(`Current bucket variables:`, bucketVariables[bucket.id]);

        // Set the current bucket for the dialog
        setViewingBucket(bucket);

        // Make sure we have the latest variables
        if (!bucketVariables[bucket.id]) {
          loadBucketVars(bucket.id)
            .then(() => {
              setVariablesDialogOpen(true);
            })
            .catch(err => {
              console.error(`Failed to load variables for dialog:`, err);
              setError('Failed to load variables');
              // Still open the dialog, but it will show "No variables"
              setVariablesDialogOpen(true);
            });
        } else {
          // We already have the variables, just open the dialog
          setVariablesDialogOpen(true);
        }
    };

    const handleCloseVariablesDialog = () => {
        setVariablesDialogOpen(false);
        setViewingBucket(null);
    };

    const handleAddRelationship = (bucket) => {
        setSelectedBucket(bucket);
        setRelationshipToEdit(null);
        setRelationshipDialogOpen(true);
    };

    const handleEditRelationship = (relationship) => {
        // Find the bucket this relationship belongs to
        const bucket = buckets.find(b => b.id === relationship.ruleBucketId);
        setSelectedBucket(bucket);
        setRelationshipToEdit(relationship);
        setRelationshipDialogOpen(true);
    };

    const handleRelationshipDialogClose = (refreshNeeded) => {
        setRelationshipDialogOpen(false);
        setRelationshipToEdit(null);

        if (refreshNeeded) {
            // Refresh the buckets to show updated relationships
            loadBuckets();
        }
    };

    useEffect(() => {
        // We're now allowing wrapping, so we don't need to calculate visible chips
        // Just keeping this effect for potential future use
    }, [bucketVariables]);

    const getBucketTypeColor = (bucket, index) => {
        const colors = [
            theme.palette.primary.main,
            theme.palette.secondary.main,
            theme.palette.success.main,
            theme.palette.info.main,
            theme.palette.warning.main
        ];

        // Use modulo to cycle through colors if there are more buckets than colors
        const colorIndex = index % colors.length;
        const baseColor = colors[colorIndex];

        return baseColor;
    };

    const getBucketTypeIcon = (bucket) => {
        switch (bucket.type) {
            case 'transaction':
                return <TableChartIcon fontSize="small"/>;
            case 'customer':
                return <FolderIcon fontSize="small"/>;
            default:
                return <StorageIcon fontSize="small"/>;
        }
    };

    const loadDataSources = async () => {
        try {
            setLoadingDataSources(true);
            setError(null);
            const sources = await dataSourceService.getAllDataSources();
            console.log('Data sources:', sources);
            setDataSources(Array.isArray(sources) ? sources : []);
        } catch (err) {
            console.error('Failed to load data sources:', err);
            setError('Failed to load data sources');
        } finally {
            setLoadingDataSources(false);
        }
    };

    const loadTables = async (dataSourceId) => {
        // Use the passed dataSourceId parameter instead of relying on state
        const sourceId = dataSourceId || newBucket.dataSourceId;

        if (!sourceId) {
            setDiscoveredTables([]);
            return;
        }

        try {
            setLoadingTables(true);
            setError(null);
            console.log('Discovering schema for data source ID:', sourceId);
            const schema = await dataSourceService.discoverSchema(sourceId);
            console.log('Discovered schema:', schema);

            // Extract tables from schema
            const tables = Array.isArray(schema)
                ? schema.filter(entity => entity.entityType === 'TABLE')
                    .map(table => ({
                        schemaName: table.schemaName || 'default',
                        tableName: table.entityName,
                        // Add other properties as needed
                    }))
                : [];

            setDiscoveredTables(tables);
        } catch (err) {
            console.error('Failed to load tables:', err);
            setError('Failed to load tables from the data source');
            setDiscoveredTables([]);
        } finally {
            setLoadingTables(false);
        }
    };

    const loadTableColumns = async (table, dataSourceId) => {
        // Use the passed dataSourceId parameter instead of relying on state
        const sourceId = dataSourceId || newBucket.dataSourceId;

        if (!sourceId) {
            setTableColumns([]);
            return;
        }

        try {
            setLoadingColumns(true);
            setError(null);

            console.log('Getting columns for table', table.tableName, 'from data source ID:', sourceId);

            // Get the schema for the data source
            const schema = await dataSourceService.discoverSchema(sourceId);

            // Find the selected table in the schema
            const tableSchema = schema.find(entity =>
                entity.entityType === 'TABLE' && entity.entityName === table.tableName
            );

            if (tableSchema && tableSchema.fields) {
                // Map the fields to the expected column format
                const columns = tableSchema.fields.map(field => ({
                    name: field.name,
                    type: field.type,
                    // Add other properties as needed
                }));

                console.log('Table columns:', columns);
                setTableColumns(columns);
            } else {
                console.warn(`No fields found for table ${table.tableName}`);
                setTableColumns([]);
            }
        } catch (err) {
            console.error(`Failed to load columns for table ${table.tableName}:`, err);
            setError(`Failed to load columns for table ${table.tableName}`);
            setTableColumns([]);
        } finally {
            setLoadingColumns(false);
        }
    };

    const handleTableSelect = (table) => {
        setSelectedTable(table);
        setSelectedColumns([]);

        // Store the current data source ID before state update
        const currentDataSourceId = newBucket.dataSourceId;

        setNewBucket({
            ...newBucket,
            tableName: table.tableName,
            variables: []
        });

        // Pass the current data source ID directly to loadTableColumns
        if (currentDataSourceId) {
            loadTableColumns(table, currentDataSourceId);
        }
    };

    // Helper function to map database types to system data types
    const mapDatabaseTypeToDataType = (dbType) => {
        // Convert to lowercase for case-insensitive matching
        const type = dbType.toLowerCase();

        if (type.includes('varchar') || type.includes('char') || type.includes('text') || type.includes('string')) {
            return 'VARCHAR';
        } else if (type.includes('int')) {
            return 'INT';
        } else if (type.includes('float') || type.includes('double') || type.includes('real')) {
            return 'FLOAT';
        } else if (type.includes('decimal') || type.includes('numeric')) {
            return 'DECIMAL';
        } else if (type.includes('date') || type.includes('time')) {
            return 'DATE';
        } else if (type.includes('bool')) {
            return 'BOOLEAN';
        }

        // Default to VARCHAR for unknown types
        return 'VARCHAR';
    };

    const handleColumnToggle = (column) => {
        const isSelected = selectedColumns.some(c => c.name === column.name);

        if (isSelected) {
            // Remove column
            setSelectedColumns(selectedColumns.filter(c => c.name !== column.name));
            setNewBucket({
                ...newBucket,
                variables: newBucket.variables.filter(v => v.name !== column.name)
            });
        } else {
            // Add column
            setSelectedColumns([...selectedColumns, column]);

            // Map the database type to our system data type
            const mappedDataType = mapDatabaseTypeToDataType(column.type);

            // Check if the data type is numerical
            const isNumerical = ['INT', 'FLOAT', 'DECIMAL', 'DOUBLE', 'NUMBER'].includes(mappedDataType);

            // Create a new variable for the selected column
            const newVariable = {
                code: `${newBucket.tableName.toUpperCase()}_${column.name.toUpperCase()}`,
                name: column.name,
                description: '',
                dataTypeCode: mappedDataType,
                isUnique: false,
                // For rule buckets, mark numerical variables as aggregatible
                aggregatable: newBucket.bucketType === 'RULE_BUCKET' && isNumerical,
                columnName: column.name // Use column.name directly
            };

            setNewBucket({
                ...newBucket,
                variables: [...newBucket.variables, newVariable]
            });
        }
    };

    return (
        <Box sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: 'transparent' // Ensure transparency to show parent background
        }}>
            <Box sx={{ width: '100%', flexGrow: 1, backgroundColor: 'transparent' }}>
                {/* Error Message */}
                {error && (
                    <Alert severity="error" sx={{mb: 3}}>
                        {error}
                    </Alert>
                )}

                {/* Header with title, description and add button */}
            <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                mb: 3,
                pb: 2,
                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
            }}>
                <Box>
                    <Typography
                        variant="h4"
                        component="h1"
                        sx={{
                            fontWeight: 600,
                            background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            letterSpacing: '-0.5px',
                            mb: 1
                        }}
                    >
                        Data Buckets
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                            Manage your data buckets for rules and lookups. Connect to data sources to use their data in your rules.
                        </Typography>
                        <Chip
                            label={`${buckets.length} buckets`}
                            size="small"
                            sx={{ ml: 2, bgcolor: alpha('#4facfe', 0.05), color: theme.palette.text.secondary }}
                        />
                    </Box>

                    {/* Bucket Type Legend */}
                    {buckets.length > 0 && (
                        <Box sx={{ display: 'flex', gap: 2, mt: 2, alignItems: 'center' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Chip
                                    label="Rule"
                                    size="small"
                                    sx={{
                                        height: '22px',
                                        fontSize: '0.7rem',
                                        mr: 1,
                                        bgcolor: alpha('#4facfe', 0.1),
                                        color: '#0078d4'
                                    }}
                                />
                                <Typography variant="caption">Transaction data</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Chip
                                    label="Lookup"
                                    size="small"
                                    sx={{
                                        height: '22px',
                                        fontSize: '0.7rem',
                                        mr: 1,
                                        bgcolor: alpha('#00d084', 0.1),
                                        color: '#00805a'
                                    }}
                                />
                                <Typography variant="caption">Reference data for enrichment</Typography>
                            </Box>
                        </Box>
                    )}
                </Box>
                <ActionButton
                    startIcon={<AddIcon />}
                    onClick={() => setCreateDrawerOpen(true)}
                >
                    Create New Bucket
                </ActionButton>
            </Box>

            {/* Bucket Cards */}
            {loading ? (
                <Box sx={{display: 'flex', justifyContent: 'center', p: 4}}>
                    <CircularProgress/>
                </Box>
            ) : buckets.length > 0 ? (
                <Grid container spacing={3}>
                    {buckets.map((bucket, index) => (
                        <Grid item xs={12} sm={6} md={4} lg={3} key={bucket.id}>
                            <Card
                                sx={{
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    borderRadius: '10px',
                                    overflow: 'hidden',
                                    transition: 'all 0.3s ease',
                                    boxShadow: '0 2px 8px -4px rgba(0,0,0,0.05), 0 4px 16px -8px rgba(0,0,0,0.04)',
                                    bgcolor: '#ffffff',
                                    position: 'relative',
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: '0 6px 20px -10px rgba(0,0,0,0.08)'
                                    }
                                }}
                            >
                                <CardActionArea
                                    onClick={() => handleBucketClick(bucket)}
                                    sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'stretch',
                                        height: '100%',
                                        padding: 0
                                    }}
                                >
                                    <CardContent sx={{
                                        flexGrow: 1,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        p: 0,
                                        '&:last-child': { pb: 0 },
                                        position: 'relative'
                                    }}>
                                        {/* Header with name, type, count and source info */}
                                        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}` }}>
                                            <Box>
                                                <Typography variant="h6" component="h2" sx={{ fontWeight: 500, fontSize: '1rem' }}>
                                                    {bucket.name}
                                                </Typography>
                                                <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                                    <Box
                                                        sx={{
                                                            display: 'inline-flex',
                                                            alignItems: 'center',
                                                            height: '24px',
                                                            px: 1.5,
                                                            py: 0.5,
                                                            mr: 1,
                                                            borderRadius: '16px',
                                                            bgcolor: bucket.bucketType === 'LOOKUP'
                                                                ? theme.palette.mode === 'dark' ? '#1565C0' : '#0288D1'
                                                                : theme.palette.mode === 'dark' ? '#E65100' : '#F57C00',
                                                            color: '#FFFFFF', // White text for contrast
                                                            fontSize: '13px',
                                                            fontWeight: 600,
                                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                                                        }}
                                                    >
                                                        {bucket.bucketType === 'LOOKUP' ? 'Lookup' : 'Rule'}
                                                    </Box>
                                                    <Typography variant="caption" color="text.secondary">
                                                        {Array.isArray(bucketVariables[bucket.id]) ? bucketVariables[bucket.id].length : 0} variables
                                                    </Typography>
                                                </Box>
                                            </Box>

                                            {/* Data Source and Table Info - Right aligned */}
                                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 0.5 }}>
                                                {bucket.dataSourceName && (
                                                    <Box
                                                        sx={{
                                                            display: 'inline-flex',
                                                            alignItems: 'center',
                                                            height: '22px',
                                                            px: 1,
                                                            py: 0.5,
                                                            borderRadius: 0, // Rectangular shape
                                                            bgcolor: '#E0F7FA', // Light cyan background
                                                            color: '#006064', // Dark cyan text
                                                            fontSize: '12px',
                                                            fontWeight: 500,
                                                            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                                        }}
                                                    >
                                                        <StorageIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                                                        {bucket.dataSourceName}
                                                    </Box>
                                                )}
                                                {bucket.tableName && (
                                                    <Box
                                                        sx={{
                                                            display: 'inline-flex',
                                                            alignItems: 'center',
                                                            height: '22px',
                                                            px: 1,
                                                            py: 0.5,
                                                            borderRadius: 0, // Rectangular shape
                                                            bgcolor: '#E8F5E9', // Light green background
                                                            color: '#2E7D32', // Dark green text
                                                            fontSize: '12px',
                                                            fontWeight: 500,
                                                            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                                        }}
                                                    >
                                                        <TableChartIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                                                        {bucket.tableName}
                                                    </Box>
                                                )}
                                            </Box>
                                        </Box>

                                        {/* Description section */}
                                        <Box sx={{ px: 2, pt: 2, pb: 1, display: 'flex', alignItems: 'center' }}>
                                            <Typography variant="body2" color="text.secondary">
                                                {bucket.description || 'No description provided'}
                                            </Typography>
                                        </Box>

                                        {/* Content section */}
                                        <Box sx={{ px: 2, pt: 0, pb: 2, flexGrow: 1 }}>
                                            {/* Variable chips */}
                                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                                {Array.isArray(bucketVariables[bucket.id]) && bucketVariables[bucket.id].length > 0 ? (
                                                    <>
                                                        {bucketVariables[bucket.id].slice(0, 6).map((variable, idx) => (
                                                            <Box
                                                                key={variable.id || idx}
                                                                sx={{
                                                                    display: 'inline-flex',
                                                                    alignItems: 'center',
                                                                    height: '24px',
                                                                    px: 1,
                                                                    py: 0.5,
                                                                    mr: 0.5,
                                                                    mb: 0.5,
                                                                    borderRadius: '8px',
                                                                    bgcolor: bucket.bucketType === 'LOOKUP' ? '#E3F2FD' : '#FFF3E0', // Blue tint for Lookup, Orange tint for Rule
                                                                    color: bucket.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00', // Blue text for Lookup, Orange text for Rule
                                                                    fontSize: '13px',
                                                                    fontWeight: 500,
                                                                    transition: 'all 0.2s ease',
                                                                    '&:hover': {
                                                                        bgcolor: bucket.bucketType === 'LOOKUP' ? '#BBDEFB' : '#FFE0B2',
                                                                        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                                                                    }
                                                                }}
                                                            >
                                                                {variable.name || `Variable ${idx + 1}`}
                                                            </Box>
                                                        ))}

                                                        {/* "View more" chip if there are more than 6 variables */}
                                                        {bucketVariables[bucket.id].length > 6 && (
                                                            <Box
                                                                sx={{
                                                                    display: 'inline-flex',
                                                                    alignItems: 'center',
                                                                    height: '24px',
                                                                    px: 1,
                                                                    py: 0.5,
                                                                    mr: 0.5,
                                                                    mb: 0.5,
                                                                    borderRadius: '8px',
                                                                    bgcolor: bucket.bucketType === 'LOOKUP' ? '#E3F2FD' : '#FFF3E0', // Blue tint for Lookup, Orange tint for Rule
                                                                    color: bucket.bucketType === 'LOOKUP' ? '#1565C0' : '#EF6C00', // Blue text for Lookup, Orange text for Rule
                                                                    fontSize: '13px',
                                                                    fontWeight: 500,
                                                                    cursor: 'pointer',
                                                                    transition: 'all 0.2s ease',
                                                                    '&:hover': {
                                                                        bgcolor: bucket.bucketType === 'LOOKUP' ? '#BBDEFB' : '#FFE0B2',
                                                                        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                                                                    }
                                                                }}
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleViewAllVariables(bucket);
                                                                }}
                                                            >
                                                                {`+${bucketVariables[bucket.id].length - 6} more`}
                                                            </Box>
                                                        )}
                                                    </>
                                                ) : (
                                                    <Typography variant="caption" color="text.secondary">
                                                        No variables in this bucket
                                                    </Typography>
                                                )}
                                            </Box>
                                        </Box>
                                    </CardContent>

                                    {/* Bottom strip with gradient */}
                                    <Box sx={{
                                        height: '3px',
                                        width: '100%',
                                        background: bucket.bucketType === 'LOOKUP'
                                            ? 'linear-gradient(90deg, rgba(123, 220, 181, 0.4) 0%, rgba(0, 208, 132, 0.3) 100%)'
                                            : 'linear-gradient(90deg, rgba(79, 172, 254, 0.4) 0%, rgba(0, 242, 254, 0.3) 100%)'
                                    }} />
                                </CardActionArea>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            ) : (
                <Paper
                    sx={{
                        p: 4,
                        borderRadius: 2,
                        textAlign: 'center',
                        bgcolor: '#ffffff',
                        boxShadow: '0 6px 16px -8px rgba(0,0,0,0.08)',
                        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                >
                    <Box sx={{mb: 3}}>
                        <Avatar
                            sx={{
                                width: 64,
                                height: 64,
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                color: theme.palette.primary.main,
                                margin: '0 auto'
                            }}
                        >
                            <StorageIcon sx={{fontSize: 32}}/>
                        </Avatar>
                    </Box>
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                        No data buckets found
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{mb: 3}}>
                        Create your first data bucket to start defining rule variables
                    </Typography>
                    <ActionButton
                        startIcon={<AddIcon />}
                        onClick={() => setCreateDrawerOpen(true)}
                    >
                        Create New Bucket
                    </ActionButton>
                </Paper>
            )}

            {/* Right drawer for creating bucket */}
            <Drawer
                anchor="right"
                open={createDrawerOpen}
                onClose={() => {
                    setCreateDrawerOpen(false);
                    setNewBucket({name: '', description: '', bucketType: 'SYSTEM', tableName: '', dataSourceId: null, variables: []});
                    setSelectedTable(null);
                    setSelectedColumns([]);
                }}
                sx={{
                    '& .MuiDrawer-paper': {
                        width: {xs: '100%', sm: '500px'},
                        p: 3,
                        boxSizing: 'border-box',
                    },
                }}
            >
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3}}>
                    <Box>
                        <Typography variant="h6">Create New Data Bucket</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <Chip
                                label={newBucket.bucketType === 'LOOKUP' ? 'Lookup Bucket' : 'Rule Bucket'}
                                size="small"
                                color={newBucket.bucketType === 'LOOKUP' ? 'secondary' : 'primary'}
                                sx={{ height: '22px', fontSize: '0.7rem' }}
                            />
                        </Box>
                    </Box>
                    <IconButton onClick={() => {
                        setCreateDrawerOpen(false);
                        setNewBucket({name: '', description: '', bucketType: 'RULE_BUCKET', tableName: '', dataSourceId: null, variables: []});
                        setSelectedTable(null);
                        setSelectedColumns([]);
                    }}>
                        <CloseIcon />
                    </IconButton>
                </Box>

                <TextField
                    margin="dense"
                    label="Name *"
                    fullWidth
                    variant="outlined"
                    value={newBucket.name}
                    onChange={(e) => setNewBucket({...newBucket, name: e.target.value})}
                    sx={{mb: 3}}
                    required
                    error={!newBucket.name && newBucket.name !== undefined}
                    helperText={!newBucket.name && newBucket.name !== undefined ? 'Bucket name is required' : ''}
                />

                <TextField
                    margin="dense"
                    label="Description"
                    fullWidth
                    variant="outlined"
                    value={newBucket.description}
                    onChange={(e) => setNewBucket({...newBucket, description: e.target.value})}
                    sx={{mb: 3}}
                    multiline
                    rows={2}
                />

                <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                        Bucket Type
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                        {BUCKET_TYPES.map((type) => (
                            <Card
                                key={type.value}
                                sx={{
                                    flex: 1,
                                    cursor: 'pointer',
                                    border: `2px solid ${newBucket.bucketType === type.value ?
                                        (type.value === 'LOOKUP' ? theme.palette.secondary.main : theme.palette.primary.main) :
                                        alpha(theme.palette.divider, 0.2)}`,
                                    borderRadius: 2,
                                    transition: 'all 0.2s',
                                    '&:hover': {
                                        boxShadow: 3,
                                        borderColor: type.value === 'LOOKUP' ? theme.palette.secondary.main : theme.palette.primary.main
                                    }
                                }}
                                onClick={() => setNewBucket({
                                    ...newBucket,
                                    bucketType: type.value
                                })}
                            >
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                        <Chip
                                            label={type.value === 'LOOKUP' ? 'Lookup' : 'Rule'}
                                            size="small"
                                            color={type.value === 'LOOKUP' ? 'secondary' : 'primary'}
                                            sx={{ height: '22px', fontSize: '0.7rem', mr: 1 }}
                                        />
                                        <Typography variant="subtitle1">{type.label}</Typography>
                                    </Box>
                                    <Typography variant="body2" color="textSecondary">
                                        {type.description}
                                    </Typography>
                                </CardContent>
                            </Card>
                        ))}
                    </Box>
                </Box>

                {/* Data Source Selection - Required for all bucket types */}
                    <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                            Select Data Source <Box component="span" sx={{ color: 'error.main' }}>*</Box>
                        </Typography>

                        {loadingDataSources ? (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <CircularProgress size={20} sx={{ mr: 1 }} />
                                <Typography variant="body2">Loading data sources...</Typography>
                            </Box>
                        ) : dataSources.length > 0 ? (
                            <TextField
                                select
                                fullWidth
                                label="Data Source"
                                value={newBucket.dataSourceId || ''}
                                onChange={(e) => {
                                    const dataSourceId = e.target.value;

                                    // Update state
                                    setNewBucket({
                                        ...newBucket,
                                        dataSourceId: dataSourceId,
                                        tableName: '',
                                        variables: []
                                    });
                                    setSelectedTable(null);
                                    setSelectedColumns([]);
                                    setDiscoveredTables([]);

                                    // Pass the ID directly to loadTables instead of relying on state
                                    if (dataSourceId) {
                                        loadTables(dataSourceId);
                                    }
                                }}
                                variant="outlined"
                            >
                                <MenuItem value="">
                                    <em>Select a data source</em>
                                </MenuItem>
                                {dataSources.map((source) => (
                                    <MenuItem key={source.id} value={source.id}>
                                        {source.name} ({source.type})
                                    </MenuItem>
                                ))}
                            </TextField>
                        ) : (
                            <Alert severity="info">
                                No data sources available. Please create a data source first.
                            </Alert>
                        )}
                    </Box>
                )}

                {newBucket.dataSourceId && (
                    <>
                        <Typography variant="subtitle1" sx={{ mb: 2 }}>
                            Select Table <Box component="span" sx={{ color: 'error.main' }}>*</Box>
                            {loadingTables && <CircularProgress size={20} sx={{ ml: 2 }} />}
                        </Typography>

                        {discoveredTables.length > 0 ? (
                            <Box sx={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                gap: 1,
                                mb: 3,
                                maxHeight: '200px',
                                overflowY: 'auto',
                                p: 1,
                                border: '1px solid',
                                borderColor: 'divider',
                                borderRadius: 1
                            }}>
                                {discoveredTables.map((table) => (
                                    <Chip
                                        key={`${table.schemaName}.${table.tableName}`}
                                        label={`${table.schemaName}.${table.tableName}`}
                                        onClick={() => handleTableSelect(table)}
                                        color={selectedTable && selectedTable.tableName === table.tableName ? "primary" : "default"}
                                        variant={selectedTable && selectedTable.tableName === table.tableName ? "filled" : "outlined"}
                                        icon={<TableChartIcon />}
                                        sx={{ m: 0.5 }}
                                    />
                                ))}
                            </Box>
                        ) : !loadingTables && (
                            <Alert severity="info" sx={{ mb: 3 }}>
                                No tables found in the database schema
                            </Alert>
                        )}

                        {selectedTable && (
                            <Box sx={{ mt: 2 }}>
                                <Typography variant="subtitle1" sx={{ mb: 2 }}>
                                    Select Columns from {selectedTable.tableName}
                                    {loadingColumns && <CircularProgress size={20} sx={{ ml: 2 }} />}
                                </Typography>

                                {tableColumns.length > 0 ? (
                                    <Box sx={{
                                        display: 'flex',
                                        flexWrap: 'wrap',
                                        gap: 1,
                                        mb: 3,
                                        maxHeight: '200px',
                                        overflowY: 'auto',
                                        p: 1,
                                        border: '1px solid',
                                        borderColor: 'divider',
                                        borderRadius: 1
                                    }}>
                                        {tableColumns.map((column) => (
                                            <Chip
                                                key={column.name}
                                                label={`${column.name} (${column.type})`}
                                                onClick={() => handleColumnToggle(column)}
                                                color={selectedColumns.some(c => c.name === column.name) ? "primary" : "default"}
                                                variant={selectedColumns.some(c => c.name === column.name) ? "filled" : "outlined"}
                                                sx={{ m: 0.5 }}
                                            />
                                        ))}
                                    </Box>
                                ) : !loadingColumns && (
                                    <Alert severity="info" sx={{ mb: 3 }}>
                                        No columns found for the selected table
                                    </Alert>
                                )}
                            </Box>
                        )}
                    </>
                )}

                {selectedTable && (
                    <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle1" sx={{ mb: 1 }}>
                            Selected Columns ({selectedColumns.length})
                        </Typography>
                        {selectedColumns.length === 0 && (
                            <Alert severity="info" sx={{ mb: 2 }}>
                                Select columns to add as variables to your bucket
                            </Alert>
                        )}
                        {selectedColumns.length > 0 && (
                            <List dense>
                                {selectedColumns.map((column, index) => {
                                // Find the corresponding variable in newBucket.variables
                                const variable = newBucket.variables.find(v => v.name === column.name);

                                return (
                                    <ListItem
                                        key={`${column.name}-${index}`}
                                        secondaryAction={
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <FormControlLabel
                                                    control={
                                                        <Checkbox
                                                            size="small"
                                                            checked={variable?.isUnique || false}
                                                            onChange={(e) => {
                                                                const updatedVariables = newBucket.variables.map(v =>
                                                                    v.name === column.name
                                                                        ? { ...v, isUnique: e.target.checked }
                                                                        : v
                                                                );

                                                                setNewBucket({
                                                                    ...newBucket,
                                                                    variables: updatedVariables
                                                                });
                                                            }}
                                                        />
                                                    }
                                                    label="Unique"
                                                />
                                                <IconButton
                                                    edge="end"
                                                    aria-label="remove"
                                                    onClick={() => handleColumnToggle(column)}
                                                    size="small"
                                                >
                                                    <CloseIcon fontSize="small" />
                                                </IconButton>
                                            </Box>
                                        }
                                    >
                                        <ListItemText
                                            primary={column.name}
                                            secondary={`${column.type} → ${variable?.dataTypeCode || 'Unknown'}`}
                                        />
                                    </ListItem>
                                );
                            })}
                            </List>
                        )}
                    </Box>
                )}

                {newBucket.bucketType === 'LOOKUP' && newBucket.variables.length > 0 && (
                    <>
                        <Typography variant="subtitle1" sx={{ mb: 2 }}>Selected Variables</Typography>

                        <Box sx={{ mb: 3 }}>
                            {newBucket.variables.map((variable, index) => (
                                <Box key={index} sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    mb: 2,
                                    p: 1,
                                    border: '1px solid',
                                    borderColor: 'divider',
                                    borderRadius: 1
                                }}>
                                    <Box sx={{ flexGrow: 1 }}>
                                        <Typography variant="subtitle2">{variable.name}</Typography>
                                        <Typography variant="caption" color="text.secondary">
                                            {variable.dataTypeCode} | Code: {variable.code}
                                        </Typography>
                                    </Box>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={variable.isUnique}
                                                onChange={(e) => {
                                                    const updatedVariables = [...newBucket.variables];
                                                    updatedVariables[index].isUnique = e.target.checked;
                                                    setNewBucket({...newBucket, variables: updatedVariables});
                                                }}
                                                size="small"
                                            />
                                        }
                                        label="Unique"
                                    />
                                    <IconButton
                                        size="small"
                                        onClick={() => {
                                            const updatedVariables = newBucket.variables.filter((_, i) => i !== index);
                                            setNewBucket({...newBucket, variables: updatedVariables});
                                            setSelectedColumns(selectedColumns.filter(c => c.name !== variable.name));
                                        }}
                                    >
                                        <DeleteIcon fontSize="small" />
                                    </IconButton>
                                </Box>
                            ))}
                        </Box>
                    </>
                )}

                <Box sx={{display: 'flex', justifyContent: 'flex-end', mt: 2}}>
                    <Button
                        onClick={() => {
                            setCreateDrawerOpen(false);
                            setNewBucket({name: '', description: '', bucketType: 'RULE_BUCKET', tableName: '', dataSourceId: null, variables: []});
                            setSelectedTable(null);
                            setSelectedColumns([]);
                        }}
                        sx={{mr: 1}}
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleCreateBucket}
                        disabled={!newBucket.name || !newBucket.dataSourceId || !newBucket.tableName}
                    >
                        Create
                    </Button>
                </Box>
            </Drawer>

            {/* Right drawer for variable selection */}
            <StyledDrawer
                anchor="right"
                open={variablesDrawerOpen}
                onClose={handleCloseVariablesDrawer}
            >
                <DrawerHeader>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', width: '100%' }}>
                        <Box>
                            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                {selectedBucket ? selectedBucket.name : 'Bucket Variables'}
                            </Typography>
                            {selectedBucket && (
                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                                    {selectedBucket.dataSourceName && (
                                        <Box
                                            sx={{
                                                display: 'inline-flex',
                                                alignItems: 'center',
                                                height: '22px',
                                                px: 1,
                                                py: 0.5,
                                                borderRadius: 0, // Rectangular shape
                                                bgcolor: '#E0F7FA', // Light cyan background
                                                color: '#006064', // Dark cyan text
                                                fontSize: '12px',
                                                fontWeight: 500,
                                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                            }}
                                        >
                                            <StorageIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                                            {selectedBucket.dataSourceName}
                                        </Box>
                                    )}
                                    {selectedBucket.tableName && (
                                        <Box
                                            sx={{
                                                display: 'inline-flex',
                                                alignItems: 'center',
                                                height: '22px',
                                                px: 1,
                                                py: 0.5,
                                                borderRadius: 0, // Rectangular shape
                                                bgcolor: '#E8F5E9', // Light green background
                                                color: '#2E7D32', // Dark green text
                                                fontSize: '12px',
                                                fontWeight: 500,
                                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                            }}
                                        >
                                            <TableChartIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                                            {selectedBucket.tableName}
                                        </Box>
                                    )}
                                </Box>
                            )}
                        </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {selectedBucket && selectedBucket.bucketType &&
                         (selectedBucket.bucketType === 'RULE_BUCKET' ||
                          selectedBucket.bucketType === 'RULE' ||
                          selectedBucket.bucketType === 3 ||
                          selectedBucket.bucketType.toString().toUpperCase().includes('RULE')) && (
                            <Button
                                variant="outlined"
                                color="primary"
                                size="small"
                                onClick={() => handleViewAllVariables(selectedBucket)}
                                sx={{ mr: 1, borderRadius: '20px', fontSize: '0.75rem' }}
                                startIcon={<LinkIcon fontSize="small" />}
                            >
                                View Enrichment
                            </Button>
                        )}
                        <IconButton onClick={handleCloseVariablesDrawer}>
                            <CloseIcon/>
                        </IconButton>
                    </Box>
                </DrawerHeader>

                <Box sx={{p: 0, height: '100%'}}>
                    {selectedBucket && (
                        <Box sx={{height: '100%'}}>
                            <BucketVariableSelectionDialog
                                useDrawerMode={true}
                                open={variablesDrawerOpen}
                                bucket={selectedBucket}
                                onVariablesAdded={handleRegistrationComplete}
                                bucketExistingVariables={selectedBucket ? bucketVariables[selectedBucket.id] || [] : []}
                                onRefreshVariables={() => refreshBucketVariables(selectedBucket.id)}
                            />
                        </Box>
                    )}
                </Box>
            </StyledDrawer>

            {/* Dialog to show all variables */}
            <Dialog
                open={variablesDialogOpen}
                onClose={handleCloseVariablesDialog}
                maxWidth="md"
                fullWidth
            >
                <DialogTitle sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    bgcolor: theme.palette.background.default,
                    pb: 2
                }}>
                    <Box>
                        <Typography variant="h6" sx={{ mb: 1 }}>
                            {viewingBucket?.name} Details
                        </Typography>
                        {viewingBucket && (
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                                {viewingBucket.dataSourceName && (
                                    <Box
                                        sx={{
                                            display: 'inline-flex',
                                            alignItems: 'center',
                                            height: '22px',
                                            px: 1,
                                            py: 0.5,
                                            borderRadius: 0, // Rectangular shape
                                            bgcolor: '#E0F7FA', // Light cyan background
                                            color: '#006064', // Dark cyan text
                                            fontSize: '12px',
                                            fontWeight: 500,
                                            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                        }}
                                    >
                                        <StorageIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                                        {viewingBucket.dataSourceName}
                                    </Box>
                                )}
                                {viewingBucket.tableName && (
                                    <Box
                                        sx={{
                                            display: 'inline-flex',
                                            alignItems: 'center',
                                            height: '22px',
                                            px: 1,
                                            py: 0.5,
                                            borderRadius: 0, // Rectangular shape
                                            bgcolor: '#E8F5E9', // Light green background
                                            color: '#2E7D32', // Dark green text
                                            fontSize: '12px',
                                            fontWeight: 500,
                                            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                        }}
                                    >
                                        <TableChartIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                                        {viewingBucket.tableName}
                                    </Box>
                                )}
                            </Box>
                        )}
                        <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 1 }}>
                            Relationships and Variables
                        </Typography>
                    </Box>
                    <IconButton onClick={handleCloseVariablesDialog} size="small">
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent dividers>
                    {viewingBucket && (
                        <>
                            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                                {viewingBucket.description || 'No description'}
                            </Typography>

                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Box
                                    sx={{
                                        display: 'inline-flex',
                                        alignItems: 'center',
                                        height: '24px',
                                        px: 1.5,
                                        py: 0.5,
                                        mr: 1,
                                        borderRadius: '16px',
                                        bgcolor: viewingBucket.bucketType === 'LOOKUP' ? '#0288D1' : '#F57C00', // Blue for Lookup, Orange for Rule
                                        color: '#FFFFFF', // White text for contrast
                                        fontSize: '13px',
                                        fontWeight: 600,
                                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                                    }}
                                >
                                    {viewingBucket.bucketType === 'LOOKUP' ? 'Lookup' : 'Rule'}
                                </Box>

                            </Box>

                            {/* Show relationships section only for rule buckets */}
                            {console.log("DEBUG - Viewing bucket:", viewingBucket)}
                            {console.log("DEBUG - Bucket type:", viewingBucket.bucketType)}
                            {console.log("DEBUG - Is rule bucket?", viewingBucket.bucketType === 'RULE_BUCKET')}
                            {console.log("DEBUG - Bucket type comparison:", viewingBucket.bucketType, "===", 'RULE_BUCKET')}

                            {/* Relationships Section with more prominent styling */}
                            <Box sx={{ mt: 3, mb: 3, p: 2, bgcolor: alpha(theme.palette.primary.main, 0.03), borderRadius: 2, border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}` }}>
                                <Typography variant="h6" sx={{ mb: 2, color: theme.palette.primary.main, fontWeight: 600 }}>
                                    Bucket Enrichment
                                </Typography>

                                {/* More flexible check for rule bucket type */}
                                {(viewingBucket.bucketType &&
                                  (viewingBucket.bucketType === 'RULE_BUCKET' ||
                                   viewingBucket.bucketType === 'RULE' ||
                                   viewingBucket.bucketType === 3 ||
                                   viewingBucket.bucketType.toString().toUpperCase().includes('RULE'))) ? (
                                    <BucketRelationshipList
                                        ruleBucketId={viewingBucket.id}
                                        onAddRelationship={() => handleAddRelationship(viewingBucket)}
                                        onEditRelationship={handleEditRelationship}
                                    />
                                ) : (
                                    <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                                        <Typography variant="body2">
                                            Enrichment relationships are only available for Rule Buckets.
                                            This is a {viewingBucket.bucketType} bucket.
                                        </Typography>
                                    </Box>
                                )}
                            </Box>

                            <Divider sx={{ my: 2 }} />
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 500, fontSize: '1rem' }}>
                                Variables
                            </Typography>

                            {console.log("Dialog variables:", bucketVariables[viewingBucket.id])}

                            {Array.isArray(bucketVariables[viewingBucket.id]) && bucketVariables[viewingBucket.id].length > 0 ? (
                                <List>
                                    {bucketVariables[viewingBucket.id].map((variable, index) => (
                                        <ListItem key={variable.id || index} divider>
                                            <ListItemIcon>
                                                <DataObjectIcon color="primary" />
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={variable.name || `Variable ${index + 1}`}
                                                secondary={
                                                    <>
                                                        <Typography variant="body2" component="span" color="textSecondary">
                                                            Type: {variable.dataTypeCode || 'Unknown'}
                                                        </Typography>
                                                        {variable.description && (
                                                            <Typography variant="body2" component="p" color="textSecondary" sx={{ mt: 0.5 }}>
                                                                {variable.description}
                                                            </Typography>
                                                        )}
                                                    </>
                                                }
                                            />
                                        </ListItem>
                                    ))}
                                </List>
                            ) : (
                                <Box sx={{ textAlign: 'center', p: 3 }}>
                                    <Typography variant="body1" color="textSecondary">
                                        No variables found in this bucket
                                    </Typography>
                                </Box>
                            )}

                            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                                <Button
                                    variant="contained"
                                    color="primary"
                                    startIcon={<AddIcon />}
                                    onClick={() => {
                                        handleCloseVariablesDialog();
                                        handleOpenVariableSelection(viewingBucket);
                                    }}
                                >
                                    Manage Variables
                                </Button>
                            </Box>
                        </>
                    )}
                </DialogContent>
            </Dialog>

            {/* Snackbar for notifications and prompts */}
            <Snackbar
                open={snackbarOpen}
                autoHideDuration={promptAddVariables ? 10000 : 6000}
                onClose={handleSnackbarClose}
                anchorOrigin={{vertical: 'bottom', horizontal: 'center'}}
            >
                <Alert
                    onClose={handleSnackbarClose}
                    severity="success"
                    variant="filled"
                    sx={{width: '100%'}}
                    action={
                        promptAddVariables && (
                            <Button
                                color="inherit"
                                size="small"
                                onClick={handleAddVariablesPrompt}
                            >
                                Yes, Add Variables
                            </Button>
                        )
                    }
                >
                    {snackbarMessage}
                </Alert>
            </Snackbar>

            {/* Bucket Relationship Dialog */}
            <BucketRelationshipDialog
                open={relationshipDialogOpen}
                onClose={handleRelationshipDialogClose}
                ruleBucketId={selectedBucket?.id}
                relationshipToEdit={relationshipToEdit}
            />
            </Box>
        </Box>
    );
};

export default DataBucketsPanel;
