-- Audit tables for entities with @Audited annotation
CREATE TABLE data_bucket_aud (
    id BIGINT NOT NULL,
    rev INTEGER NOT NULL,
    revtype TINYINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGIN<PERSON>,
    description VARCHAR(500),
    name VA<PERSON><PERSON><PERSON>(100),
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_data_bucket_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);

CREATE TABLE data_bucket_variable_aud (
    id BIGINT NOT NULL,
    rev INTEGER NOT NULL,
    revtype TINYINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGIN<PERSON>,
    updated_by BIGIN<PERSON>,
    code VA<PERSON>HAR(100),
    name <PERSON><PERSON>HA<PERSON>(255),
    description TEXT,
    table_name VA<PERSON>HA<PERSON>(255),
    column_name <PERSON><PERSON><PERSON><PERSON>(255),
    data_type VARCHAR(50),
    data_bucket_id BIGINT,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_data_bucket_variable_aud_rev FOR<PERSON><PERSON><PERSON> KEY (rev) REFERENCES revinfo(rev)
);

CREATE TABLE refresh_tokens_aud (
    id BIGINT NOT NULL,
    rev INTEGER NOT NULL,
    revtype TINYINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    user_id BIGINT,
    token VARCHAR(255),
    expiry_date TIMESTAMP,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_refresh_tokens_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);

