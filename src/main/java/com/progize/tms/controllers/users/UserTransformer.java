package com.progize.tms.controllers.users;

import com.progize.tms.controllers.users.req.CreateUserRequest;
import com.progize.tms.controllers.users.req.UpdateUserRequest;
import com.progize.tms.controllers.users.res.UserResponse;
import com.progize.tms.repository.entity.Role;
import com.progize.tms.repository.entity.User;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class UserTransformer {

  /** Convert User entity to UserResponse DTO */
  public UserResponse toResponse(User user) {
    return UserResponse.builder()
        .id(user.getId())
        .username(user.getUsername())
        .email(user.getEmail())
        .firstName(user.getFirstName())
        .lastName(user.getLastName())
        .enabled(user.isEnabled())
        .createdAt(user.getCreatedAt())
        .updatedAt(null) // User entity doesn't have updatedAt field
        .roles(convertRoles(user.getRoles()))
        .build();
  }

  /** Convert list of User entities to list of UserResponse DTOs */
  public List<UserResponse> toResponseList(List<User> users) {
    return users.stream().map(this::toResponse).collect(Collectors.toList());
  }

  /** Convert CreateUserRequest DTO to User entity */
  public User toEntity(CreateUserRequest request) {
    return User.builder()
        .username(request.getUsername())
        .email(request.getEmail())
        .firstName(request.getFirstName())
        .lastName(request.getLastName())
        .password(request.getPassword()) // Will be encoded in service
        .enabled(request.getEnabled() != null ? request.getEnabled() : true)
        .build();
  }

  /**
   * Convert UpdateUserRequest DTO to User entity Note: This creates a partial User entity for
   * update operations
   */
  public User toEntity(UpdateUserRequest request) {
    return User.builder()
        .email(request.getEmail())
        .firstName(request.getFirstName())
        .lastName(request.getLastName())
        .password(request.getPassword()) // Will be encoded in service
        .enabled(request.getEnabled() != null ? request.getEnabled() : true)
        .build();
  }

  /** Convert a set of Role entities to a set of UserResponse.RoleDto objects */
  private Set<UserResponse.RoleDto> convertRoles(Set<Role> roles) {
    if (roles == null) {
      return Set.of();
    }
    return roles.stream()
        .map(
            role ->
                UserResponse.RoleDto.builder()
                    .id(role.getId())
                    .name(role.getName())
                    .description(role.getDescription())
                    .build())
        .collect(Collectors.toSet());
  }
}
