package com.progize.tms.controllers.rules;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.controllers.rules.req.CreateRuleRequest;
import com.progize.tms.controllers.rules.req.RuleCondition;
import com.progize.tms.controllers.rules.res.RuleResponse;
import com.progize.tms.controllers.rules.res.ValidationResponse;
import com.progize.tms.repository.entity.DataBucketEntity;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.services.RuleValidationService.ValidationResult;
import com.progize.tms.services.models.AggregateCondition;
import com.progize.tms.services.models.ComparisonCondition;
import com.progize.tms.services.models.CompositeCondition;
import com.progize.tms.services.models.Condition;
import com.progize.tms.services.models.SimpleCondition;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class RuleTransformer {
  private static final Logger log = LoggerFactory.getLogger(RuleTransformer.class);
  private final ObjectMapper objectMapper = new ObjectMapper();

  public Rule toBusinessObject(CreateRuleRequest request) {
    Rule rule = new Rule();
    rule.setName(request.getName());
    rule.setDescription(request.getDescription());
    rule.setExpression(request.getExpression());
    rule.setFlagReason(request.getFlagReason());
    rule.setActive(true);
    rule.setPriority(request.getPriority());

    // Convert REST condition to domain condition
    if (request.getCondition() != null) {
      Condition domainCondition = convertToDomainCondition(request.getCondition());
      // Serialize domain condition to JSON
      try {
        rule.setRuleJson(objectMapper.writeValueAsString(domainCondition));
      } catch (JsonProcessingException e) {
        log.error("Error serializing domain condition to JSON", e);
      }
    }

    rule.setCreatedAt(Instant.now());
    rule.setUpdatedAt(Instant.now());
    return rule;
  }

  public RuleResponse toResponse(Rule rule) {
    RuleResponse response = new RuleResponse();
    response.setId(rule.getId());
    response.setName(rule.getName());
    response.setDescription(rule.getDescription());
    response.setExpression(rule.getExpression());
    response.setFlagReason(rule.getFlagReason());
    response.setActive(rule.isActive());
    response.setPriority(rule.getPriority());
    response.setCreatedAt(rule.getCreatedAt());
    response.setUpdatedAt(rule.getUpdatedAt());
    //    response.setCreatedBy(rule.getCreatedBy() != null ? rule.getCreatedBy().getUsername() :
    // null);
    //    response.setUpdatedBy(rule.getUpdatedBy() != null ? rule.getUpdatedBy().getUsername() :
    // null);
    response.setRuleJson(rule.getRuleJson());

    // Set data bucket ID if available
    if (rule.getDataBucket() != null) {
      response.setDataBucketId(rule.getDataBucket().getId());
    }

    // Deserialize the ruleJson into a domain condition object, then convert to REST layer condition
    if (rule.getRuleJson() != null && !rule.getRuleJson().isEmpty()) {
      try {
        Condition domainCondition = objectMapper.readValue(rule.getRuleJson(), Condition.class);
        response.setCondition(convertToRestCondition(domainCondition));
      } catch (JsonProcessingException e) {
        log.error("Error deserializing rule JSON to condition", e);
      }
    }

    return response;
  }

  public Rule toEntity(CreateRuleRequest request) {
    Rule rule = new Rule();
    rule.setName(request.getName());
    rule.setDescription(request.getDescription());
    rule.setExpression(request.getExpression());
    rule.setFlagReason(request.getFlagReason());
    rule.setActive(request.isActive());
    rule.setPriority(request.getPriority());

    // Set data bucket reference using ID
    if (request.getDataBucketId() != null) {
      DataBucketEntity dataBucket = new DataBucketEntity();
      dataBucket.setId(request.getDataBucketId());
      rule.setDataBucket(dataBucket);
    }

    // Convert REST condition to domain condition
    if (request.getCondition() != null) {
      Condition domainCondition = convertToDomainCondition(request.getCondition());
      // Serialize domain condition to JSON
      try {
        rule.setRuleJson(objectMapper.writeValueAsString(domainCondition));
      } catch (JsonProcessingException e) {
        log.error("Error serializing domain condition to JSON", e);
      }
    }

    return rule;
  }

  /**
   * Converts a REST layer RuleCondition to a domain model Condition.
   *
   * @param restCondition the REST layer condition
   * @return the domain model condition
   */
  private Condition convertToDomainCondition(RuleCondition restCondition) {
    if (restCondition == null) {
      return null;
    }

    switch (restCondition.getType()) {
      case SIMPLE:
        SimpleCondition simpleCondition = new SimpleCondition();
        simpleCondition.setField(restCondition.getField());
        simpleCondition.setOperator(restCondition.getOperator());
        simpleCondition.setValue(restCondition.getValue());
        simpleCondition.setIsExpression(restCondition.getIsExpression());
        return simpleCondition;

      case AND:
      case OR:
      case NOT:
        CompositeCondition compositeCondition = new CompositeCondition();
        compositeCondition.setType(restCondition.getType().toString());

        // Convert child conditions
        if (restCondition.getConditions() != null) {
          List<Condition> domainConditions =
              restCondition.getConditions().stream()
                  .map(this::convertToDomainCondition)
                  .collect(Collectors.toList());
          compositeCondition.setConditions(domainConditions);
        } else {
          compositeCondition.setConditions(Collections.emptyList());
        }

        return compositeCondition;

      case AGGREGATE:
        AggregateCondition aggregateCondition = new AggregateCondition();
        aggregateCondition.setFunction(restCondition.getFunction());
        aggregateCondition.setField(restCondition.getField());
        aggregateCondition.setPeriod(restCondition.getPeriod());
        aggregateCondition.setStartOffset(
            restCondition.getStartOffset() != null ? restCondition.getStartOffset() : 0);
        aggregateCondition.setOperator(restCondition.getOperator());
        aggregateCondition.setThreshold(restCondition.getThreshold());
        aggregateCondition.setIsExpression(restCondition.getIsExpression());
        aggregateCondition.setEntityField(
            restCondition.getEntityField() != null ? restCondition.getEntityField() : "customerId");

        // Handle filter condition if present
        if (restCondition.getFilterCondition() != null) {
          SimpleCondition filterCondition = new SimpleCondition();
          filterCondition.setType("SIMPLE");
          filterCondition.setField(restCondition.getFilterCondition().getField());
          filterCondition.setOperator(restCondition.getFilterCondition().getOperator());
          filterCondition.setValue(restCondition.getFilterCondition().getValue());
          filterCondition.setIsExpression(restCondition.getFilterCondition().getIsExpression());
          aggregateCondition.setFilterCondition(filterCondition);
        }

        return aggregateCondition;

      case COMPARISON:
        ComparisonCondition comparisonCondition = new ComparisonCondition();
        comparisonCondition.setOperator(restCondition.getOperator());
        comparisonCondition.setMultiplier(restCondition.getMultiplier());

        // Convert left and right conditions
        if (restCondition.getLeft() != null) {
          comparisonCondition.setLeft(convertToDomainCondition(restCondition.getLeft()));
        }

        if (restCondition.getRight() != null) {
          comparisonCondition.setRight(convertToDomainCondition(restCondition.getRight()));
        }

        return comparisonCondition;

      default:
        throw new IllegalArgumentException(
            "Unsupported condition type: " + restCondition.getType());
    }
  }

  /**
   * Converts a domain model Condition to a REST layer RuleCondition.
   *
   * @param domainCondition the domain model condition
   * @return the REST layer condition
   */
  private RuleCondition convertToRestCondition(Condition domainCondition) {
    if (domainCondition == null) {
      return null;
    }

    String type = domainCondition.getType();

    if ("SIMPLE".equals(type)) {
      SimpleCondition simpleCondition = (SimpleCondition) domainCondition;
      RuleCondition ruleCondition =
          new RuleCondition(
              RuleCondition.ConditionType.SIMPLE,
              simpleCondition.getField(),
              simpleCondition.getOperator(),
              simpleCondition.getValue());

      // Pass along the expression flag
      ruleCondition.setIsExpression(simpleCondition.getIsExpression());

      return ruleCondition;

    } else if ("AND".equals(type) || "OR".equals(type)) {
      CompositeCondition compositeCondition = (CompositeCondition) domainCondition;
      List<RuleCondition> children = new ArrayList<>();

      if (compositeCondition.getConditions() != null) {
        for (Condition child : compositeCondition.getConditions()) {
          children.add(convertToRestCondition(child));
        }
      }

      RuleCondition.ConditionType conditionType =
          "AND".equals(type) ? RuleCondition.ConditionType.AND : RuleCondition.ConditionType.OR;

      return new RuleCondition(conditionType, children);

    } else if ("NOT".equals(type)) {
      CompositeCondition compositeCondition = (CompositeCondition) domainCondition;
      List<RuleCondition> children = new ArrayList<>();

      if (compositeCondition.getConditions() != null
          && !compositeCondition.getConditions().isEmpty()) {
        children.add(convertToRestCondition(compositeCondition.getConditions().get(0)));
      }

      return new RuleCondition(RuleCondition.ConditionType.NOT, children);

    } else if ("AGGREGATE".equals(type)) {
      AggregateCondition aggregateCondition = (AggregateCondition) domainCondition;
      RuleCondition ruleCondition = new RuleCondition();
      ruleCondition.setType(RuleCondition.ConditionType.AGGREGATE);
      ruleCondition.setFunction(aggregateCondition.getFunction());
      ruleCondition.setAggregateField(aggregateCondition.getField());
      ruleCondition.setPeriod(aggregateCondition.getPeriod());
      ruleCondition.setStartOffset(aggregateCondition.getStartOffset());
      ruleCondition.setOperator(aggregateCondition.getOperator());
      ruleCondition.setThreshold(aggregateCondition.getThreshold());
      ruleCondition.setIsExpression(aggregateCondition.getIsExpression());
      ruleCondition.setEntityField(aggregateCondition.getEntityField());

      // Handle optional filter condition
      if (aggregateCondition.getFilterCondition() != null) {
        RuleCondition filterRuleCondition = new RuleCondition();
        filterRuleCondition.setType(RuleCondition.ConditionType.SIMPLE);
        filterRuleCondition.setField(aggregateCondition.getFilterCondition().getField());
        filterRuleCondition.setOperator(aggregateCondition.getFilterCondition().getOperator());
        filterRuleCondition.setValue(aggregateCondition.getFilterCondition().getValue());
        filterRuleCondition.setIsExpression(
            aggregateCondition.getFilterCondition().getIsExpression());
        ruleCondition.setFilterCondition(filterRuleCondition);
      }

      return ruleCondition;
    } else if ("COMPARISON".equals(type)) {
      ComparisonCondition comparisonCondition = (ComparisonCondition) domainCondition;

      // Convert left and right conditions
      RuleCondition leftRuleCondition = null;
      if (comparisonCondition.getLeft() != null) {
        leftRuleCondition = convertToRestCondition(comparisonCondition.getLeft());
      }

      RuleCondition rightRuleCondition = null;
      if (comparisonCondition.getRight() != null) {
        rightRuleCondition = convertToRestCondition(comparisonCondition.getRight());
      }

      // Create the comparison condition
      RuleCondition ruleCondition =
          new RuleCondition(
              RuleCondition.ConditionType.COMPARISON,
              leftRuleCondition,
              comparisonCondition.getOperator(),
              rightRuleCondition,
              comparisonCondition.getMultiplier());

      return ruleCondition;
    }

    throw new IllegalArgumentException("Unsupported domain condition type: " + type);
  }

  /**
   * Converts a list of rule entities to a list of rule response DTOs.
   *
   * @param rules The list of rule entities to convert
   * @return A list of rule response DTOs
   */
  public List<RuleResponse> toResponseList(List<Rule> rules) {
    if (rules == null) {
      return Collections.emptyList();
    }
    return rules.stream().map(this::toResponse).toList();
  }

  /**
   * Converts a validation result to a validation response.
   *
   * @param validationResult The validation result to convert
   * @return A validation response DTO
   */
  public ValidationResponse toValidationResponse(ValidationResult validationResult) {
    return ValidationResponse.builder()
        .valid(validationResult.isValid())
        .errors(validationResult.getErrors())
        .build();
  }
}
