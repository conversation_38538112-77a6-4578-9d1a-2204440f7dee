package com.progize.tms.service;

import com.progize.tms.repository.entity.CaseAttachment;
import com.progize.tms.repository.entity.WorkflowCase;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/** Service for managing case attachments. */
public interface CaseAttachmentService {

  /**
   * Add an attachment to a workflow case.
   *
   * @param workflowCase the workflow case
   * @param processInstanceId the process instance ID
   * @param taskId the task ID
   * @param file the file to attach
   * @param createdBy the user ID who created the attachment
   * @return the created attachment
   */
  CaseAttachment addAttachment(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      MultipartFile file,
      Long createdBy);

  /**
   * Add multiple attachments to a workflow case.
   *
   * @param workflowCase the workflow case
   * @param processInstanceId the process instance ID
   * @param taskId the task ID
   * @param files the files to attach
   * @param createdBy the user ID who created the attachments
   * @return the list of created attachments
   */
  List<CaseAttachment> addAttachments(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      List<MultipartFile> files,
      Long createdBy);

  /**
   * Get all attachments for a workflow case.
   *
   * @param workflowCase the workflow case
   * @return list of attachments
   */
  List<CaseAttachment> getAttachmentsByCase(WorkflowCase workflowCase);

  /**
   * Get all attachments for a task.
   *
   * @param taskId the task ID
   * @return list of attachments
   */
  List<CaseAttachment> getAttachmentsByTask(String taskId);

  /**
   * Get all attachments for a process instance.
   *
   * @param processInstanceId the process instance ID
   * @return list of attachments
   */
  List<CaseAttachment> getAttachmentsByProcessInstance(String processInstanceId);
}
