package com.progize.tms.controllers.workflow.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.flowable.task.api.Task;

/**
 * Data Transfer Object for Flowable Task entities. Prevents lazy loading exceptions by extracting
 * all necessary data.
 */
@Data
public class TaskDTO {
  private String id;
  private String name;
  private String description;
  private String assignee;
  private Date createTime;
  private Date dueDate;
  private String processInstanceId;
  private String processDefinitionId;
  private String taskDefinitionKey;
  private String category;
  private String formKey;
  private int priority;
  private Map<String, Object> variables = new HashMap<>();
  private List<FormPropertyDTO> formProperties = new ArrayList<>();

  /**
   * Creates a TaskDTO from a Flowable Task and its variables.
   *
   * @param task The Flowable Task entity
   * @param variables Task variables
   * @return A new TaskDTO instance
   */
  public static TaskDTO fromTask(Task task, Map<String, Object> variables) {
    TaskDTO dto = new TaskDTO();
    dto.setId(task.getId());
    dto.setName(task.getName());
    dto.setDescription(task.getDescription());
    dto.setAssignee(task.getAssignee());
    dto.setCreateTime(task.getCreateTime());
    dto.setDueDate(task.getDueDate());
    dto.setProcessInstanceId(task.getProcessInstanceId());
    dto.setProcessDefinitionId(task.getProcessDefinitionId());
    dto.setTaskDefinitionKey(task.getTaskDefinitionKey());
    dto.setCategory(task.getCategory());
    dto.setFormKey(task.getFormKey());
    dto.setPriority(task.getPriority());

    // Only add variables if they're provided
    if (variables != null) {
      dto.setVariables(variables);
    }

    return dto;
  }

  /**
   * Creates a TaskDTO from a Flowable Task, its variables, and form properties.
   *
   * @param task The Flowable Task entity
   * @param variables Task variables
   * @param formProperties Form properties associated with the task
   * @return A new TaskDTO instance with form properties
   */
  public static TaskDTO fromTask(
      Task task, Map<String, Object> variables, List<FormPropertyDTO> formProperties) {
    TaskDTO dto = fromTask(task, variables);
    if (formProperties != null) {
      dto.setFormProperties(formProperties);
    }
    return dto;
  }
}
