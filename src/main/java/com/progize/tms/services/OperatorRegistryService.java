package com.progize.tms.services;

import com.progize.tms.repository.DataTypeRepository;
import com.progize.tms.repository.DataTypeSupportedOperatorRepository;
import com.progize.tms.repository.OperatorExpressionRepository;
import com.progize.tms.repository.OperatorRepository;
import com.progize.tms.repository.entity.DataType;
import com.progize.tms.repository.entity.DataTypeSupportedOperator;
import com.progize.tms.repository.entity.Operator;
import com.progize.tms.repository.entity.OperatorExpression;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** Service for managing operator registry data in the database. */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperatorRegistryService {

  private final OperatorRepository operatorRepository;
  private final DataTypeRepository dataTypeRepository;
  private final OperatorExpressionRepository operatorExpressionRepository;
  private final DataTypeSupportedOperatorRepository dataTypeSupportedOperatorRepository;

  /** Get all operators */
  public List<Operator> getAllOperators() {
    return operatorRepository.findAll();
  }

  /** Get operator by code */
  public Optional<Operator> getOperatorByCode(String code) {
    return operatorRepository.findByCode(code);
  }

  /** Get all data types */
  public List<DataType> getAllDataTypes() {
    return dataTypeRepository.findAll();
  }

  /** Get data type by code */
  public Optional<DataType> getDataTypeByCode(String code) {
    return dataTypeRepository.findByCode(code);
  }

  /** Get all expressions for a data type */
  public List<OperatorExpression> getExpressionsForDataType(String dataTypeCode) {
    return operatorExpressionRepository.findByDataTypeCode(dataTypeCode);
  }

  /** Get all generic expressions (not specific to any data type) */
  public List<OperatorExpression> getGenericExpressions() {
    return operatorExpressionRepository.findGenericExpressions();
  }

  /** Get all supported operators for a data type */
  public List<DataTypeSupportedOperator> getSupportedOperatorsForDataType(String dataTypeCode) {
    return dataTypeSupportedOperatorRepository.findByDataTypeCodeOrderByDisplayOrder(dataTypeCode);
  }

  /** Create or update an operator */
  @Transactional
  public Operator saveOperator(Operator operator) {
    return operatorRepository.save(operator);
  }

  /** Create or update a data type */
  @Transactional
  public DataType saveDataType(DataType dataType) {
    return dataTypeRepository.save(dataType);
  }

  /** Create or update an operator expression */
  @Transactional
  public OperatorExpression saveOperatorExpression(
      String operatorCode, String dataTypeCode, String expressionTemplate) {

    Operator operator =
        operatorRepository
            .findByCode(operatorCode)
            .orElseThrow(() -> new IllegalArgumentException("Operator not found: " + operatorCode));

    OperatorExpression expression = new OperatorExpression();
    expression.setOperator(operator);
    expression.setExpressionTemplate(expressionTemplate);

    if (dataTypeCode != null) {
      DataType dataType =
          dataTypeRepository
              .findByCode(dataTypeCode)
              .orElseThrow(
                  () -> new IllegalArgumentException("Data type not found: " + dataTypeCode));
      expression.setDataType(dataType);

      // Check if expression already exists
      Optional<OperatorExpression> existingExpr =
          operatorExpressionRepository.findByOperatorCodeAndDataTypeCode(
              operatorCode, dataTypeCode);

      existingExpr.ifPresent(operatorExpression -> expression.setId(operatorExpression.getId()));
    } else {
      // Check if generic expression already exists
      Optional<OperatorExpression> existingExpr =
          operatorExpressionRepository.findGenericByOperatorCode(operatorCode);

      existingExpr.ifPresent(operatorExpression -> expression.setId(operatorExpression.getId()));
    }

    return operatorExpressionRepository.save(expression);
  }

  /** Add a supported operator to a data type */
  @Transactional
  public DataTypeSupportedOperator addSupportedOperator(
      String dataTypeCode, String operatorCode, int displayOrder, Map<String, Object> config) {

    DataType dataType =
        dataTypeRepository
            .findByCode(dataTypeCode)
            .orElseThrow(
                () -> new IllegalArgumentException("Data type not found: " + dataTypeCode));

    Operator operator =
        operatorRepository
            .findByCode(operatorCode)
            .orElseThrow(() -> new IllegalArgumentException("Operator not found: " + operatorCode));

    // Check if already exists
    List<DataTypeSupportedOperator> existing =
        dataTypeSupportedOperatorRepository.findByDataTypeCodeOrderByDisplayOrder(dataTypeCode);

    for (DataTypeSupportedOperator supportedOp : existing) {
      if (supportedOp.getOperator().getCode().equals(operatorCode)) {
        // Update existing
        supportedOp.setDisplayOrder(displayOrder);
        supportedOp.setConfig(config);
        return dataTypeSupportedOperatorRepository.save(supportedOp);
      }
    }

    // Create new
    DataTypeSupportedOperator supportedOp = new DataTypeSupportedOperator();
    supportedOp.setDataType(dataType);
    supportedOp.setOperator(operator);
    supportedOp.setDisplayOrder(displayOrder);
    supportedOp.setConfig(config);

    return dataTypeSupportedOperatorRepository.save(supportedOp);
  }

  /** Remove a supported operator from a data type */
  @Transactional
  public void removeSupportedOperator(String dataTypeCode, String operatorCode) {
    // No need to fetch entities since we only need them for validation
    // and we're searching by code in the collection anyway
    if (!dataTypeRepository.findByCode(dataTypeCode).isPresent()) {
      throw new IllegalArgumentException("Data type not found: " + dataTypeCode);
    }

    if (!operatorRepository.findByCode(operatorCode).isPresent()) {
      throw new IllegalArgumentException("Operator not found: " + operatorCode);
    }

    List<DataTypeSupportedOperator> existing =
        dataTypeSupportedOperatorRepository.findByDataTypeCodeOrderByDisplayOrder(dataTypeCode);

    for (DataTypeSupportedOperator supportedOp : existing) {
      if (supportedOp.getOperator().getCode().equals(operatorCode)) {
        dataTypeSupportedOperatorRepository.delete(supportedOp);
        break;
      }
    }
  }

  /** Create a new operator with expression */
  @Transactional
  public Operator createOperator(
      String code,
      String displayName,
      String description,
      String symbol,
      String standardExpression) {

    // Create operator
    Operator operator = new Operator();
    operator.setCode(code);
    operator.setDisplayName(displayName);
    operator.setDescription(description);
    operator.setSymbol(symbol);
    operator.setMetadata(new HashMap<>());

    operator = operatorRepository.save(operator);

    // Create standard expression
    if (standardExpression != null) {
      OperatorExpression expression = new OperatorExpression();
      expression.setOperator(operator);
      expression.setExpressionTemplate(standardExpression);
      operatorExpressionRepository.save(expression);
    }

    return operator;
  }

  /** Create a new data type */
  @Transactional
  public DataType createDataType(String code, String displayName, String description) {

    DataType dataType = new DataType();
    dataType.setCode(code);
    dataType.setDisplayName(displayName);
    dataType.setDescription(description);
    dataType.setMetadata(new HashMap<>());

    return dataTypeRepository.save(dataType);
  }
}
