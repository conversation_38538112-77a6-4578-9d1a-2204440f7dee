import React, { useState } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    Button,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Chip,
    Switch,
    TextField,
    Grid,
    Card,
    CardContent,
    Stack,
    Alert,
    Breadcrumbs,
    Link,
    FormControlLabel,
    Divider
} from '@mui/material';
import {
    Add as AddIcon,
    Save as SaveIcon,
    Cancel as CancelIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Key as KeyIcon,
    Timeline as TimelineIcon,
    Analytics as AnalyticsIcon
} from '@mui/icons-material';

const CleanVariableManager = ({ bucket, onBack, onSave }) => {
    const [variables, setVariables] = useState([
        {
            id: 1,
            name: 'customer_id',
            dataType: 'integer',
            description: 'Unique customer identifier',
            isUnique: true,
            aggregatable: false,
            isPrimaryEntityKey: true,
            isSecondaryEntityKey: false,
            isDateDimension: false
        },
        {
            id: 2,
            name: 'transaction_id',
            dataType: 'string',
            description: 'Transaction reference number',
            isUnique: true,
            aggregatable: false,
            isPrimaryEntityKey: false,
            isSecondaryEntityKey: true,
            isDateDimension: false
        },
        {
            id: 3,
            name: 'amount',
            dataType: 'decimal',
            description: 'Transaction amount',
            isUnique: false,
            aggregatable: true,
            isPrimaryEntityKey: false,
            isSecondaryEntityKey: false,
            isDateDimension: false
        },
        {
            id: 4,
            name: 'transaction_date',
            dataType: 'date',
            description: 'Date of transaction',
            isUnique: false,
            aggregatable: false,
            isPrimaryEntityKey: false,
            isSecondaryEntityKey: false,
            isDateDimension: true
        }
    ]);

    const [editingVariable, setEditingVariable] = useState(null);

    const handlePropertyChange = (variableId, property, value) => {
        setVariables(prev => prev.map(variable => {
            if (variable.id === variableId) {
                const updated = { ...variable, [property]: value };
                
                // Handle exclusive properties
                if (property === 'isPrimaryEntityKey' && value) {
                    // Unset primary key for all other variables
                    setVariables(prevVars => prevVars.map(v => 
                        v.id !== variableId ? { ...v, isPrimaryEntityKey: false, isSecondaryEntityKey: false } : v
                    ));
                    updated.isSecondaryEntityKey = false;
                } else if (property === 'isSecondaryEntityKey' && value) {
                    // Unset secondary key for all other variables
                    setVariables(prevVars => prevVars.map(v => 
                        v.id !== variableId ? { ...v, isSecondaryEntityKey: false, isPrimaryEntityKey: false } : v
                    ));
                    updated.isPrimaryEntityKey = false;
                } else if (property === 'isDateDimension' && value) {
                    // Unset date dimension for all other variables
                    setVariables(prevVars => prevVars.map(v => 
                        v.id !== variableId ? { ...v, isDateDimension: false } : v
                    ));
                }
                
                return updated;
            }
            return variable;
        }));
    };

    const getEntityKeyType = (variable) => {
        if (variable.isPrimaryEntityKey) return { label: 'Primary Key', color: 'primary', icon: <KeyIcon /> };
        if (variable.isSecondaryEntityKey) return { label: 'Secondary Key', color: 'secondary', icon: <KeyIcon /> };
        if (variable.isDateDimension) return { label: 'Date Dimension', color: 'success', icon: <TimelineIcon /> };
        return null;
    };

    const renderVariableRow = (variable) => (
        <TableRow key={variable.id} hover>
            <TableCell>
                <Box>
                    <Typography variant="subtitle2" fontWeight="medium">
                        {variable.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                        {variable.description}
                    </Typography>
                </Box>
            </TableCell>
            
            <TableCell>
                <Chip
                    label={variable.dataType}
                    size="small"
                    variant="outlined"
                />
            </TableCell>
            
            <TableCell align="center">
                <Switch
                    checked={variable.isUnique}
                    onChange={(e) => handlePropertyChange(variable.id, 'isUnique', e.target.checked)}
                    size="small"
                />
            </TableCell>
            
            <TableCell align="center">
                <Switch
                    checked={variable.aggregatable}
                    onChange={(e) => handlePropertyChange(variable.id, 'aggregatable', e.target.checked)}
                    size="small"
                    color="info"
                />
            </TableCell>
            
            <TableCell>
                {getEntityKeyType(variable) ? (
                    <Chip
                        label={getEntityKeyType(variable).label}
                        color={getEntityKeyType(variable).color}
                        size="small"
                        icon={getEntityKeyType(variable).icon}
                    />
                ) : (
                    <Typography variant="caption" color="text.secondary">
                        None
                    </Typography>
                )}
            </TableCell>
            
            <TableCell align="center">
                <Stack direction="row" spacing={1} justifyContent="center">
                    <Button
                        size="small"
                        variant="outlined"
                        onClick={() => setEditingVariable(variable)}
                    >
                        Configure
                    </Button>
                </Stack>
            </TableCell>
        </TableRow>
    );

    const renderConfigurationPanel = () => {
        if (!editingVariable) return null;

        return (
            <Card sx={{ mt: 3, border: '2px solid', borderColor: 'primary.main' }}>
                <CardContent sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                        <EditIcon sx={{ mr: 1 }} />
                        Configure: {editingVariable.name}
                    </Typography>
                    
                    <Grid container spacing={4}>
                        <Grid item xs={12} md={4}>
                            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                                Basic Properties
                            </Typography>
                            <Stack spacing={2}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={editingVariable.isUnique}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ ...prev, isUnique: e.target.checked }));
                                                handlePropertyChange(editingVariable.id, 'isUnique', e.target.checked);
                                            }}
                                        />
                                    }
                                    label="Unique Values"
                                />
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={editingVariable.aggregatable}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ ...prev, aggregatable: e.target.checked }));
                                                handlePropertyChange(editingVariable.id, 'aggregatable', e.target.checked);
                                            }}
                                            color="info"
                                        />
                                    }
                                    label="Aggregatable (SUM, AVG, COUNT)"
                                />
                            </Stack>
                        </Grid>
                        
                        <Grid item xs={12} md={8}>
                            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                                Entity Key Configuration
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                                Only one variable can be assigned to each entity key type
                            </Typography>
                            
                            <Grid container spacing={3}>
                                <Grid item xs={12} sm={4}>
                                    <Card sx={{ p: 2, textAlign: 'center', bgcolor: editingVariable.isPrimaryEntityKey ? 'primary.50' : 'grey.50' }}>
                                        <KeyIcon color="primary" sx={{ mb: 1 }} />
                                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                            Primary Key
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                                            Main entity identifier
                                        </Typography>
                                        <Switch
                                            checked={editingVariable.isPrimaryEntityKey}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ 
                                                    ...prev, 
                                                    isPrimaryEntityKey: e.target.checked,
                                                    isSecondaryEntityKey: false,
                                                    isDateDimension: false
                                                }));
                                                handlePropertyChange(editingVariable.id, 'isPrimaryEntityKey', e.target.checked);
                                            }}
                                            color="primary"
                                        />
                                    </Card>
                                </Grid>
                                
                                <Grid item xs={12} sm={4}>
                                    <Card sx={{ p: 2, textAlign: 'center', bgcolor: editingVariable.isSecondaryEntityKey ? 'secondary.50' : 'grey.50' }}>
                                        <KeyIcon color="secondary" sx={{ mb: 1 }} />
                                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                            Secondary Key
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                                            Secondary identifier
                                        </Typography>
                                        <Switch
                                            checked={editingVariable.isSecondaryEntityKey}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ 
                                                    ...prev, 
                                                    isSecondaryEntityKey: e.target.checked,
                                                    isPrimaryEntityKey: false,
                                                    isDateDimension: false
                                                }));
                                                handlePropertyChange(editingVariable.id, 'isSecondaryEntityKey', e.target.checked);
                                            }}
                                            color="secondary"
                                        />
                                    </Card>
                                </Grid>
                                
                                <Grid item xs={12} sm={4}>
                                    <Card sx={{ p: 2, textAlign: 'center', bgcolor: editingVariable.isDateDimension ? 'success.50' : 'grey.50' }}>
                                        <TimelineIcon color="success" sx={{ mb: 1 }} />
                                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                            Date Dimension
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                                            Time-based analysis
                                        </Typography>
                                        <Switch
                                            checked={editingVariable.isDateDimension}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ 
                                                    ...prev, 
                                                    isDateDimension: e.target.checked,
                                                    isPrimaryEntityKey: false,
                                                    isSecondaryEntityKey: false
                                                }));
                                                handlePropertyChange(editingVariable.id, 'isDateDimension', e.target.checked);
                                            }}
                                            color="success"
                                        />
                                    </Card>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                    
                    <Divider sx={{ my: 3 }} />
                    
                    <Stack direction="row" spacing={2} justifyContent="flex-end">
                        <Button
                            variant="outlined"
                            onClick={() => setEditingVariable(null)}
                        >
                            Close
                        </Button>
                        <Button
                            variant="contained"
                            startIcon={<SaveIcon />}
                            onClick={() => setEditingVariable(null)}
                        >
                            Save Configuration
                        </Button>
                    </Stack>
                </CardContent>
            </Card>
        );
    };

    return (
        <Box sx={{ p: 3 }}>
            <Breadcrumbs sx={{ mb: 3 }}>
                <Link color="inherit" href="#" onClick={onBack} sx={{ textDecoration: 'none' }}>
                    Data Buckets
                </Link>
                <Typography color="text.primary">{bucket?.name} Variables</Typography>
            </Breadcrumbs>
            
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" sx={{ mb: 1, fontWeight: 600 }}>
                    {bucket?.name} Variables
                </Typography>
                <Typography variant="body1" color="text.secondary">
                    Configure variable properties and entity keys for workflow case management
                </Typography>
            </Box>

            <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                    <strong>Entity Keys:</strong> Only one variable can be assigned as Primary Key, Secondary Key, or Date Dimension. 
                    These are used for workflow case management and data aggregation.
                </Typography>
            </Alert>

            <Card>
                <CardContent sx={{ p: 0 }}>
                    <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography variant="h6" fontWeight="600">
                                Variables ({variables.length})
                            </Typography>
                            <Button variant="contained" startIcon={<AddIcon />}>
                                Add Variables
                            </Button>
                        </Stack>
                    </Box>
                    
                    <TableContainer>
                        <Table>
                            <TableHead>
                                <TableRow sx={{ bgcolor: 'grey.50' }}>
                                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }}>Data Type</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Unique</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Aggregatable</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }}>Entity Key</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {variables.map(renderVariableRow)}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </CardContent>
            </Card>

            {renderConfigurationPanel()}

            <Box sx={{ mt: 4 }}>
                <Stack direction="row" spacing={2} justifyContent="flex-end">
                    <Button variant="outlined" onClick={onBack}>
                        Back to Buckets
                    </Button>
                    <Button variant="contained" startIcon={<SaveIcon />} onClick={onSave}>
                        Save All Changes
                    </Button>
                </Stack>
            </Box>
        </Box>
    );
};

export default CleanVariableManager;
