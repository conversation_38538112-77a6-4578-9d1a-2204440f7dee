import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import RuleTableDesigns from './RuleTableDesigns';

// Create a theme instance
const theme = createTheme({
  palette: {
    primary: {
      main: '#0c4e7a',
    },
    secondary: {
      main: '#7bdcb5',
    },
    background: {
      default: '#f8fafc',
    }
  },
  typography: {
    fontFamily: "'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif",
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
    subtitle1: {
      fontWeight: 500,
    }
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          fontWeight: 500,
        },
      },
    },
  },
});

const UIDesignDemo = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <RuleTableDesigns />
      </Router>
    </ThemeProvider>
  );
};

export default UIDesignDemo;
