-- Create database if not exists
CREATE DATABASE IF NOT EXISTS tms;

-- Use the database
USE tms;

-- Create transactions table with optimized columns
CREATE TABLE transactions (
    -- Primary key and technical fields
    id UUID,
    created_at DateTime DEFAULT now(),
    
    -- Fund related fields (high cardinality)
    fund_id String,
    fund_name LowCardinality(String),
    fund_short_name LowCardinality(String),
    fund_bank_account String,
    fund_category LowCardinality(String),
    
    -- Transaction details
    trans_decs LowCardinality(String),
    transaction_ref String,
    deal_date DateTime,
    trade_date_time DateTime,
    month LowCardinality(String),
    trans_type LowCardinality(String),
    
    -- Customer information
    customer_id String,
    portfolio_id String,
    customer_name String,
    crm_bank_account_no String,
    
    -- Order details
    order_type LowCardinality(String),
    unit_type_code LowCardinality(String),
    unit_type_name LowCardinality(String),
    
    -- Financial amounts
    fund_net_amount Decimal64(2),
    amount_per_unit Decimal64(2),
    nav Decimal64(2),
    transaction_unit Decimal64(2),
    entry_load Decimal64(2),
    discount_amt Decimal64(2),
    gross_load Decimal64(2),
    net_load Decimal64(2),
    exit_load Decimal64(2),
    zakat_amt Decimal64(2),
    cgt Decimal64(2),
    trans_cost Decimal64(2),
    
    -- Additional fields
    col22 String,
    rm_code LowCardinality(String),
    rm_name String,
    branch LowCardinality(String),
    new_exist LowCardinality(String),
    region LowCardinality(String),
    industry_desc LowCardinality(String),
    secp_sector LowCardinality(String),
    city LowCardinality(String),
    channel LowCardinality(String),
    
    -- Rule engine results
    flagged UInt8 DEFAULT 0,
    flag_reason String,
    
    -- Additional customer context
    customer_risk_level LowCardinality(String),
    kyc_status LowCardinality(String),
    profile_last_updated DateTime,
    login_attempts UInt8
) ENGINE = ReplacingMergeTree()
ORDER BY (customer_id, deal_date, id);

-- Create materialized view for 30-day customer rollups
CREATE MATERIALIZED VIEW customer_rollups_30d
ENGINE = AggregatingMergeTree()
ORDER BY (customer_id, window_start)
AS SELECT
    customer_id,
    toStartOfDay(deal_date) as window_start,
    count() as transaction_count,
    sum(fund_net_amount) as total_amount,
    groupArray(trans_type) as transaction_types,
    groupArray(fund_category) as fund_categories,
    countIf(trans_type = 'REDEMPTION') as redemption_count,
    uniqArray(crm_bank_account_no) as unique_bank_accounts,
    max(customer_risk_level) as risk_level,
    sum(if(trans_type = 'REDEMPTION', fund_net_amount, 0)) as total_redemptions,
    sum(if(trans_type = 'SUBSCRIPTION', fund_net_amount, 0)) as total_subscriptions
FROM transactions
WHERE deal_date >= (now() - INTERVAL 30 DAY)
GROUP BY customer_id, window_start;

-- Create materialized view for 7-day customer rollups
CREATE MATERIALIZED VIEW customer_rollups_7d
ENGINE = AggregatingMergeTree()
ORDER BY (customer_id, window_start)
AS SELECT
    customer_id,
    toStartOfDay(deal_date) as window_start,
    count() as transaction_count,
    sum(fund_net_amount) as total_amount,
    groupArray(trans_type) as transaction_types,
    countIf(trans_type = 'REDEMPTION') as redemption_count,
    uniqArray(crm_bank_account_no) as unique_bank_accounts
FROM transactions
WHERE deal_date >= (now() - INTERVAL 7 DAY)
GROUP BY customer_id, window_start;

-- Create materialized view for customer profile changes
CREATE MATERIALIZED VIEW customer_profile_changes
ENGINE = AggregatingMergeTree()
ORDER BY (customer_id, change_date)
AS SELECT
    customer_id,
    toDate(profile_last_updated) as change_date,
    count() as update_count
FROM transactions
WHERE profile_last_updated IS NOT NULL
GROUP BY customer_id, change_date;
