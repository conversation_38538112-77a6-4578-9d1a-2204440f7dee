package com.progize.tms.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.services.models.AggregationRequirement;
import com.progize.tms.services.models.Condition;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mvel2.MVEL;
import org.springframework.stereotype.Service;

/**
 * Service responsible for executing rules against customer transactions. This service coordinates
 * the aggregation data collection and rule evaluation.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RuleExecutionService {

  private final AggregationRegistryService aggregationRegistryService;
  private final ClickHouseQueryService clickHouseQueryService;
  private final RuleExpressionBuilder ruleExpressionBuilder;
  private final ObjectMapper objectMapper;

  /**
   * Evaluates all active rules for a specific customer.
   *
   * @param customerId The customer ID to evaluate rules for
   * @return Map of rule IDs to their evaluation results (true/false)
   */
  public Map<String, Boolean> evaluateRulesForCustomer(String customerId) {
    Map<String, Boolean> results = new HashMap<>();

    // Get all aggregation requirements
    Set<AggregationRequirement> requirements =
        aggregationRegistryService.getAllAggregationRequirements();

    // Execute all aggregations and build the aggregates map
    Map<String, Object> aggregates =
        clickHouseQueryService.executeAggregationsForCustomer(customerId, requirements);

    // Get all active rules
    Set<Rule> activeRules = new HashSet<>(/* Get active rules from repository */ );

    return new HashMap<>();
  }

  /**
   * Evaluates a single rule for a customer.
   *
   * @param rule The rule to evaluate
   * @param customerId The customer ID to evaluate the rule for
   * @param aggregates Map of pre-computed aggregation values
   * @return The rule evaluation result (true/false)
   */
  private boolean evaluateRule(Rule rule, String customerId, Map<String, Object> aggregates) {
    try {
      // Parse the rule condition from JSON
      Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

      // Build the MVEL expression for the rule
      String mvelExpression = ruleExpressionBuilder.buildMvelExpression(condition);

      // Create the context map for MVEL evaluation
      Map<String, Object> context = new ConcurrentHashMap<>();

      // Add the customer ID to the context
      context.put("customerId", customerId);

      // Add the aggregates map to the context
      context.put("aggregates", aggregates);

      // Evaluate the expression with MVEL
      return (Boolean) MVEL.eval(mvelExpression, context);
    } catch (Exception e) {
      log.error("Failed to evaluate rule: {}", rule.getId(), e);
      return false; // Default to false on error
    }
  }
}
