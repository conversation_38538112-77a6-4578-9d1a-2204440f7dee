/* This ensures the app styles load properly */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
  margin: 0;
  font-family: 'Inter', 'Roboto', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  overflow-x: hidden;
  font-size: 14px;
}

* {
  box-sizing: border-box;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
  font-size: 0.9em;
}

/* Add some global styles for form controls */
.MuiInputBase-root {
  font-size: 0.875rem !important;
}

.MuiFormLabel-root {
  font-size: 0.875rem !important;
}

.MuiMenuItem-root {
  font-size: 0.875rem !important;
}

.MuiListItem-root {
  font-size: 0.875rem !important;
}

/* Ensure modals aren't too wide */
.MuiDialog-paper {
  max-width: 500px !important;
  width: 100%;
}

/* Smaller form fields */
.MuiOutlinedInput-input {
  padding: 10px 14px !important;
}

.MuiInputLabel-outlined {
  transform: translate(14px, 12px) scale(1) !important;
}

.MuiInputLabel-outlined.MuiInputLabel-shrink {
  transform: translate(14px, -6px) scale(0.75) !important;
}

/* Smoother transitions */
.MuiButtonBase-root,
.MuiPaper-root,
.MuiCard-root {
  transition: all 0.2s ease-in-out !important;
}

/* Better table spacing */
.MuiTableCell-root {
  padding: 8px 12px !important;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
