import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Box,
  TextField,
  Button,
  Typography,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Paper,
  alpha
} from '@mui/material';
import { styled } from '@mui/material/styles';
import AuthService from '../../services/AuthService';
import { useTheme } from '@mui/material/styles';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: 12,
  boxShadow: '0 8px 40px -12px rgba(0,0,0,0.2)',
  overflow: 'hidden',
  transition: 'transform 0.3s, box-shadow 0.3s',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 16px 70px -12px rgba(0,0,0,0.25)',
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  borderRadius: '50%',
  padding: theme.spacing(2),
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  color: theme.palette.common.white,
  marginBottom: theme.spacing(2),
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 8,
  padding: '12px 0',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: '0 4px 10px 0 rgba(0,0,0,0.1)',
  transition: 'all 0.3s',
  '&:hover': {
    boxShadow: '0 6px 15px 0 rgba(0,0,0,0.15)',
    transform: 'translateY(-2px)',
  },
}));

const Login = () => {
  const theme = useTheme();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();

  // Get the redirect path from location state or default to /rules
  const from = location.state?.from?.pathname || '/rules';

  const handleLogin = (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    AuthService.login(username, password)
      .then(() => {
        // Navigate to the intended destination or to /rules by default
        navigate(from, { replace: true });
      })
      .catch(err => {
        setError(err.response?.data?.message || 'Login failed. Please check your credentials.');
        setLoading(false);
      });
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{
        mt: 8,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <Paper
          elevation={0}
          sx={{
            p: 4,
            width: '100%',
            backgroundColor: alpha(theme.palette.background.paper, 0.8),
            backdropFilter: 'blur(20px)',
            borderRadius: 3
          }}
        >
          <StyledCard>
            <CardContent sx={{ p: 4 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <IconWrapper>
                  <LockOutlinedIcon fontSize="large" />
                </IconWrapper>

                <Typography component="h1" variant="h4" align="center" gutterBottom fontWeight="bold">
                  SoniQue Login
                </Typography>

                <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3 }}>
                  Enter your credentials to access the SoniQue Transaction Monitoring System
                </Typography>

                {error && (
                  <Alert
                    severity="error"
                    sx={{
                      mb: 3,
                      width: '100%',
                      borderRadius: 2,
                      '& .MuiAlert-icon': {
                        alignItems: 'center'
                      }
                    }}
                  >
                    {error}
                  </Alert>
                )}

                <Box component="form" onSubmit={handleLogin} sx={{ mt: 1, width: '100%' }}>
                  <TextField
                    margin="normal"
                    required
                    fullWidth
                    id="username"
                    label="Username"
                    name="username"
                    autoComplete="username"
                    autoFocus
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                  <TextField
                    margin="normal"
                    required
                    fullWidth
                    name="password"
                    label="Password"
                    type="password"
                    id="password"
                    autoComplete="current-password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    sx={{
                      mb: 3,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                  <StyledButton
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={loading}
                    sx={{ mt: 2, mb: 2 }}
                  >
                    {loading ? <CircularProgress size={24} /> : 'Sign In'}
                  </StyledButton>
                </Box>
              </Box>
            </CardContent>
          </StyledCard>
        </Paper>
      </Box>
    </Container>
  );
};

export default Login;
