package com.progize.tms.controllers.datasources.res;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Configuration class for data source type UI form. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSourceTypeConfig {
  private String type;
  private String displayName;
  private List<FormField> fields;
  private Map<String, Map<String, List<FormField>>> conditionalFields;

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class FormField {
    private String name;
    private String label;
    private String type;
    private boolean required;
    private List<SelectOption> options;
    private String defaultValue;
    private String helperText;
  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class SelectOption {
    private String value;
    private String label;
  }
}
