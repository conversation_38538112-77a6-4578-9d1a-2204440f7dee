package com.progize.tms.controllers.databuckets.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/** Request DTO for creating a new data bucket. */
@Getter
@Setter
public class CreateDataBucketRequest {
  @NotBlank(message = "Name is required")
  @Size(max = 255, message = "Name cannot exceed 255 characters")
  private String name;

  private String description;

  private String bucketType;

  private String tableName;

  private Long dataSourceId;
}
