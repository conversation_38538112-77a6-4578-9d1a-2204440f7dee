package com.progize.tms.controllers.databuckets;

import com.progize.tms.controllers.databuckets.req.CreateDataBucketRequest;
import com.progize.tms.controllers.databuckets.req.CreateDataBucketVariableRequest;
import com.progize.tms.controllers.databuckets.req.CreateLookupBucketRequest;
import com.progize.tms.controllers.databuckets.res.DataBucketResponse;
import com.progize.tms.controllers.databuckets.res.DataBucketVariableResponse;
import com.progize.tms.controllers.databuckets.res.LookupBucketResponse;
import com.progize.tms.repository.entity.DataBucketEntity;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.services.DataBucketService;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** Controller for data bucket operations. */
@RestController
@RequestMapping("/api/data-buckets")
@CrossOrigin(origins = "*")
public class DataBucketController {

  private final DataBucketService bucketService;
  private final DataBucketTransformer transformer;

  @Autowired
  public DataBucketController(DataBucketService bucketService, DataBucketTransformer transformer) {
    this.bucketService = bucketService;
    this.transformer = transformer;
  }

  /**
   * Get all data buckets.
   *
   * @return List of data buckets
   */
  @GetMapping
  public ResponseEntity<List<DataBucketResponse>> getAllBuckets() {
    List<DataBucketEntity> buckets = bucketService.getAllBuckets();
    return ResponseEntity.ok(buckets.stream().map(transformer::toDataBucketResponse).toList());
  }

  /** Create a new data bucket. */
  @PostMapping
  public ResponseEntity<DataBucketResponse> createBucket(
      @RequestBody CreateDataBucketRequest request) {
    DataBucketEntity bucket = transformer.toDataBucketEntity(request);
    bucket = bucketService.createBucket(bucket);
    return ResponseEntity.ok(transformer.toDataBucketResponse(bucket));
  }

  /** Create a new lookup bucket with variables. */
  @PostMapping("/lookup")
  public ResponseEntity<LookupBucketResponse> createLookupBucket(
      @Valid @RequestBody CreateLookupBucketRequest request) {
    DataBucketEntity bucket = transformer.toDataBucketEntity(request);
    bucket = bucketService.createBucket(bucket);
    return ResponseEntity.ok(transformer.toLookupBucketResponse(bucket));
  }

  /** Get a data bucket by ID. */
  @GetMapping("/{id}")
  public ResponseEntity<DataBucketResponse> getBucketById(@PathVariable Long id) {
    DataBucketEntity bucket = bucketService.getBucketById(id);
    return ResponseEntity.ok(transformer.toDataBucketResponse(bucket));
  }

  /** Update a data bucket. */
  @PutMapping("/{id}")
  public ResponseEntity<DataBucketResponse> updateBucket(
      @PathVariable Long id, @RequestBody() CreateDataBucketRequest request) {
    DataBucketEntity bucket = transformer.toDataBucketEntity(request);
    bucket.setId(id);
    bucket = bucketService.updateBucket(bucket);
    return ResponseEntity.ok(transformer.toDataBucketResponse(bucket));
  }

  /** Delete a data bucket. */
  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteBucket(@PathVariable Long id) {
    bucketService.deleteBucket(id);
    return ResponseEntity.noContent().build();
  }

  /** Get all variables for a data bucket. */
  @GetMapping("/{bucketId}/variables")
  public ResponseEntity<List<DataBucketVariableResponse>> getVariablesByBucketId(
      @PathVariable Long bucketId) {
    List<DataBucketVariableEntity> variables = bucketService.getVariablesByBucketId(bucketId);
    return ResponseEntity.ok(
        variables.stream().map(transformer::toDataBucketVariableResponse).toList());
  }

  /**
   * Get all variables for a data bucket, including enrichment variables from related lookup
   * buckets. This is used for rule definition to include variables from lookup buckets that have a
   * relationship with this bucket.
   */
  @GetMapping("/{bucketId}/variables/enriched")
  @CrossOrigin(
      origins = "*",
      methods = {RequestMethod.GET})
  public ResponseEntity<List<DataBucketVariableResponse>> getEnrichedVariablesByBucketId(
      @PathVariable Long bucketId) {
    // Get the bucket to get its name
    DataBucketEntity bucket = bucketService.getBucketById(bucketId);
    String bucketName = bucket.getName();

    // Get all variables including enrichment
    List<DataBucketVariableEntity> variables =
        bucketService.getVariablesWithEnrichment(bucketId, true);

    // Convert to response DTOs with source information
    List<DataBucketVariableResponse> responseList = new ArrayList<>();

    for (DataBucketVariableEntity variable : variables) {
      // Check if this is an enriched variable (from a different bucket)
      if (variable.getDataBucket() != null && !variable.getDataBucket().getId().equals(bucketId)) {
        // This is an enriched variable
        responseList.add(
            transformer.toEnrichedDataBucketVariableResponse(variable, bucketId, bucketName));
      } else {
        // This is a regular variable
        responseList.add(transformer.toDataBucketVariableResponse(variable));
      }
    }

    return ResponseEntity.ok(responseList);
  }

  /** Create a new variable for a data bucket. */
  @PostMapping("/{bucketId}/variables")
  public ResponseEntity<DataBucketVariableResponse> createVariable(
      @PathVariable Long bucketId, @RequestBody CreateDataBucketVariableRequest request) {
    DataBucketVariableEntity variable = transformer.toDataBucketVariableEntity(request);
    variable = bucketService.createVariable(bucketId, variable);
    return ResponseEntity.ok(transformer.toDataBucketVariableResponse(variable));
  }

  /** Update a variable for a data bucket. */
  @PutMapping("/{bucketId}/variables/{variableId}")
  public ResponseEntity<DataBucketVariableResponse> updateVariable(
      @PathVariable Long bucketId,
      @PathVariable Long variableId,
      @RequestBody CreateDataBucketVariableRequest request) {
    DataBucketVariableEntity variable = transformer.toDataBucketVariableEntity(request);
    variable.setId(variableId);
    variable = bucketService.updateVariable(bucketId, variable);
    return ResponseEntity.ok(transformer.toDataBucketVariableResponse(variable));
  }

  /** Delete a variable from a data bucket. */
  @DeleteMapping("/{bucketId}/variables/{variableId}")
  public ResponseEntity<Void> deleteVariable(
      @PathVariable Long bucketId, @PathVariable Long variableId) {
    bucketService.deleteVariable(bucketId, variableId);
    return ResponseEntity.noContent().build();
  }
}
