import React, { useState, useEffect } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Button,
    Grid,
    Chip,
    IconButton,
    Avatar,
    Divider,
    Tabs,
    Tab,
    Badge,
    Tooltip,
    LinearProgress,
    Fab,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Switch,
    FormControlLabel,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    useTheme,
    alpha
} from '@mui/material';
import {
    Add as AddIcon,
    Storage as StorageIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as ViewIcon,
    Settings as SettingsIcon,
    DataObject as DataObjectIcon,
    Timeline as TimelineIcon,
    Key as KeyIcon,
    Analytics as AnalyticsIcon,
    ExpandMore as ExpandMoreIcon,
    FilterList as FilterIcon,
    Search as SearchIcon,
    CloudUpload as CloudUploadIcon,
    Speed as SpeedIcon,
    Security as SecurityIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components for modern look
const StyledCard = styled(Card)(({ theme }) => ({
    borderRadius: 16,
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: '0 16px 48px rgba(0, 0, 0, 0.12)',
    }
}));

const GradientHeader = styled(Box)(({ theme }) => ({
    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    borderRadius: '16px 16px 0 0',
    padding: theme.spacing(3),
    color: 'white',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
    }
}));

const MetricCard = styled(Card)(({ theme }) => ({
    borderRadius: 12,
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
    padding: theme.spacing(2),
    textAlign: 'center',
    transition: 'all 0.3s ease',
    '&:hover': {
        transform: 'scale(1.02)',
        boxShadow: theme.shadows[4],
    }
}));

const VariableChip = styled(Chip)(({ theme, variant }) => {
    const colors = {
        primary: { bg: theme.palette.primary.main, color: 'white' },
        secondary: { bg: theme.palette.secondary.main, color: 'white' },
        success: { bg: theme.palette.success.main, color: 'white' },
        warning: { bg: theme.palette.warning.main, color: 'white' },
        info: { bg: theme.palette.info.main, color: 'white' },
        default: { bg: theme.palette.grey[200], color: theme.palette.text.primary }
    };
    
    const colorScheme = colors[variant] || colors.default;
    
    return {
        backgroundColor: colorScheme.bg,
        color: colorScheme.color,
        fontWeight: 600,
        borderRadius: 8,
        height: 28,
        '& .MuiChip-label': {
            fontSize: '0.75rem',
            padding: '0 8px',
        },
        '&:hover': {
            backgroundColor: alpha(colorScheme.bg, 0.8),
        }
    };
});

const ModernDataBucketsManager = () => {
    const theme = useTheme();
    const [activeTab, setActiveTab] = useState(0);
    const [buckets, setBuckets] = useState([]);
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [variablesDialogOpen, setVariablesDialogOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('all');

    // Mock data for demonstration
    const mockBuckets = [
        {
            id: 1,
            name: 'Customer Transactions',
            description: 'High-value customer transaction monitoring',
            type: 'RULE_BUCKET',
            variableCount: 12,
            dataSource: 'PostgreSQL Main',
            tableName: 'customer_transactions',
            status: 'active',
            lastUpdated: '2024-01-15',
            variables: [
                { name: 'customer_id', type: 'Primary Key', dataType: 'integer' },
                { name: 'transaction_id', type: 'Secondary Key', dataType: 'string' },
                { name: 'amount', type: 'Aggregatable', dataType: 'decimal' },
                { name: 'transaction_date', type: 'Date Dimension', dataType: 'date' },
            ]
        },
        {
            id: 2,
            name: 'Employee Directory',
            description: 'Employee information lookup table',
            type: 'LOOKUP',
            variableCount: 8,
            dataSource: 'MySQL HR',
            tableName: 'employees',
            status: 'active',
            lastUpdated: '2024-01-10',
            variables: [
                { name: 'employee_id', type: 'Primary Key', dataType: 'integer' },
                { name: 'department_id', type: 'Secondary Key', dataType: 'integer' },
                { name: 'salary', type: 'Aggregatable', dataType: 'decimal' },
                { name: 'hire_date', type: 'Date Dimension', dataType: 'date' },
            ]
        }
    ];

    useEffect(() => {
        setBuckets(mockBuckets);
    }, []);

    const getTypeIcon = (type) => {
        switch (type) {
            case 'RULE_BUCKET': return <AnalyticsIcon />;
            case 'LOOKUP': return <SearchIcon />;
            default: return <DataObjectIcon />;
        }
    };

    const getTypeColor = (type) => {
        switch (type) {
            case 'RULE_BUCKET': return 'primary';
            case 'LOOKUP': return 'secondary';
            default: return 'default';
        }
    };

    const getVariableTypeColor = (type) => {
        switch (type) {
            case 'Primary Key': return 'primary';
            case 'Secondary Key': return 'secondary';
            case 'Date Dimension': return 'success';
            case 'Aggregatable': return 'info';
            default: return 'default';
        }
    };

    const filteredBuckets = buckets.filter(bucket => {
        const matchesSearch = bucket.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            bucket.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesFilter = filterType === 'all' || bucket.type === filterType;
        return matchesSearch && matchesFilter;
    });

    const renderOverviewTab = () => (
        <Box>
            {/* Metrics Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <MetricCard>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                            <Avatar sx={{ bgcolor: 'primary.main', mr: 1 }}>
                                <StorageIcon />
                            </Avatar>
                            <Typography variant="h4" fontWeight="bold">
                                {buckets.length}
                            </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                            Total Buckets
                        </Typography>
                    </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <MetricCard>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                            <Avatar sx={{ bgcolor: 'success.main', mr: 1 }}>
                                <DataObjectIcon />
                            </Avatar>
                            <Typography variant="h4" fontWeight="bold">
                                {buckets.reduce((sum, bucket) => sum + bucket.variableCount, 0)}
                            </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                            Total Variables
                        </Typography>
                    </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <MetricCard>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                            <Avatar sx={{ bgcolor: 'warning.main', mr: 1 }}>
                                <SpeedIcon />
                            </Avatar>
                            <Typography variant="h4" fontWeight="bold">
                                {buckets.filter(b => b.status === 'active').length}
                            </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                            Active Buckets
                        </Typography>
                    </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <MetricCard>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                            <Avatar sx={{ bgcolor: 'info.main', mr: 1 }}>
                                <SecurityIcon />
                            </Avatar>
                            <Typography variant="h4" fontWeight="bold">
                                {new Set(buckets.map(b => b.dataSource)).size}
                            </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                            Data Sources
                        </Typography>
                    </MetricCard>
                </Grid>
            </Grid>

            {/* Search and Filter Bar */}
            <Card sx={{ mb: 3, borderRadius: 2 }}>
                <CardContent>
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} md={6}>
                            <TextField
                                fullWidth
                                placeholder="Search buckets..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                InputProps={{
                                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                                }}
                                sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                            />
                        </Grid>
                        <Grid item xs={12} md={4}>
                            <FormControl fullWidth>
                                <InputLabel>Filter by Type</InputLabel>
                                <Select
                                    value={filterType}
                                    onChange={(e) => setFilterType(e.target.value)}
                                    sx={{ borderRadius: 2 }}
                                >
                                    <MenuItem value="all">All Types</MenuItem>
                                    <MenuItem value="RULE_BUCKET">Rule Buckets</MenuItem>
                                    <MenuItem value="LOOKUP">Lookup Buckets</MenuItem>
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} md={2}>
                            <Button
                                fullWidth
                                variant="contained"
                                startIcon={<AddIcon />}
                                onClick={() => setCreateDialogOpen(true)}
                                sx={{ borderRadius: 2, height: 56 }}
                            >
                                Create
                            </Button>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>

            {/* Buckets Grid */}
            <Grid container spacing={3}>
                {filteredBuckets.map((bucket) => (
                    <Grid item xs={12} md={6} lg={4} key={bucket.id}>
                        <StyledCard>
                            <GradientHeader>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', position: 'relative', zIndex: 1 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}>
                                            {getTypeIcon(bucket.type)}
                                        </Avatar>
                                        <Box>
                                            <Typography variant="h6" fontWeight="bold">
                                                {bucket.name}
                                            </Typography>
                                            <Chip
                                                label={bucket.type.replace('_', ' ')}
                                                size="small"
                                                sx={{
                                                    bgcolor: 'rgba(255,255,255,0.2)',
                                                    color: 'white',
                                                    fontWeight: 600
                                                }}
                                            />
                                        </Box>
                                    </Box>
                                    <Badge
                                        badgeContent={bucket.variableCount}
                                        color="secondary"
                                        sx={{
                                            '& .MuiBadge-badge': {
                                                bgcolor: 'rgba(255,255,255,0.9)',
                                                color: theme.palette.primary.main,
                                                fontWeight: 'bold'
                                            }
                                        }}
                                    >
                                        <DataObjectIcon />
                                    </Badge>
                                </Box>
                            </GradientHeader>
                            
                            <CardContent sx={{ p: 3 }}>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2, minHeight: 40 }}>
                                    {bucket.description}
                                </Typography>
                                
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="caption" color="text.secondary" display="block">
                                        Data Source
                                    </Typography>
                                    <Typography variant="body2" fontWeight="medium">
                                        {bucket.dataSource}
                                    </Typography>
                                </Box>
                                
                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 1 }}>
                                        Variable Types
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                        {bucket.variables.slice(0, 3).map((variable, index) => (
                                            <VariableChip
                                                key={index}
                                                label={variable.type}
                                                size="small"
                                                variant={getVariableTypeColor(variable.type)}
                                            />
                                        ))}
                                        {bucket.variables.length > 3 && (
                                            <Chip
                                                label={`+${bucket.variables.length - 3} more`}
                                                size="small"
                                                variant="outlined"
                                            />
                                        )}
                                    </Box>
                                </Box>
                                
                                <Divider sx={{ my: 2 }} />
                                
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Typography variant="caption" color="text.secondary">
                                        Updated {bucket.lastUpdated}
                                    </Typography>
                                    <Box>
                                        <Tooltip title="View Variables">
                                            <IconButton
                                                size="small"
                                                onClick={() => {
                                                    setSelectedBucket(bucket);
                                                    setVariablesDialogOpen(true);
                                                }}
                                            >
                                                <ViewIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Edit Bucket">
                                            <IconButton
                                                size="small"
                                                onClick={() => {
                                                    setSelectedBucket(bucket);
                                                    setEditDialogOpen(true);
                                                }}
                                            >
                                                <EditIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Configure">
                                            <IconButton size="small">
                                                <SettingsIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </Box>
                                </Box>
                            </CardContent>
                        </StyledCard>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );

    const renderVariablesTab = () => (
        <Box>
            {selectedBucket && (
                <StyledCard>
                    <GradientHeader>
                        <Typography variant="h5" fontWeight="bold">
                            {selectedBucket.name} Variables
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                            Manage and configure bucket variables
                        </Typography>
                    </GradientHeader>

                    <CardContent sx={{ p: 0 }}>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                                        <TableCell sx={{ fontWeight: 'bold' }}>Variable Name</TableCell>
                                        <TableCell sx={{ fontWeight: 'bold' }}>Data Type</TableCell>
                                        <TableCell sx={{ fontWeight: 'bold' }}>Type</TableCell>
                                        <TableCell sx={{ fontWeight: 'bold' }}>Properties</TableCell>
                                        <TableCell sx={{ fontWeight: 'bold' }} align="center">Actions</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {selectedBucket.variables.map((variable, index) => (
                                        <TableRow key={index} hover>
                                            <TableCell>
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <Avatar sx={{ width: 32, height: 32, mr: 2, bgcolor: 'primary.main' }}>
                                                        {variable.name.charAt(0).toUpperCase()}
                                                    </Avatar>
                                                    <Typography variant="body2" fontWeight="medium">
                                                        {variable.name}
                                                    </Typography>
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                <Chip
                                                    label={variable.dataType}
                                                    size="small"
                                                    variant="outlined"
                                                    sx={{ borderRadius: 2 }}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <VariableChip
                                                    label={variable.type}
                                                    size="small"
                                                    variant={getVariableTypeColor(variable.type)}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Box sx={{ display: 'flex', gap: 0.5 }}>
                                                    <Chip label="Unique" size="small" variant="outlined" />
                                                    <Chip label="Indexed" size="small" variant="outlined" />
                                                </Box>
                                            </TableCell>
                                            <TableCell align="center">
                                                <IconButton size="small">
                                                    <EditIcon />
                                                </IconButton>
                                                <IconButton size="small" color="error">
                                                    <DeleteIcon />
                                                </IconButton>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </CardContent>
                </StyledCard>
            )}
        </Box>
    );

    const renderCreateDialog = () => (
        <Dialog
            open={createDialogOpen}
            onClose={() => setCreateDialogOpen(false)}
            maxWidth="md"
            fullWidth
            PaperProps={{
                sx: { borderRadius: 3 }
            }}
        >
            <DialogTitle sx={{ pb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <AddIcon />
                    </Avatar>
                    <Box>
                        <Typography variant="h6" fontWeight="bold">
                            Create New Data Bucket
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Set up a new data bucket for your monitoring rules
                        </Typography>
                    </Box>
                </Box>
            </DialogTitle>

            <DialogContent sx={{ pt: 2 }}>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Bucket Name"
                            placeholder="e.g., Customer Transactions"
                            sx={{ mb: 2 }}
                        />
                        <TextField
                            fullWidth
                            label="Description"
                            placeholder="Brief description of the bucket purpose"
                            multiline
                            rows={3}
                            sx={{ mb: 2 }}
                        />
                        <FormControl fullWidth sx={{ mb: 2 }}>
                            <InputLabel>Bucket Type</InputLabel>
                            <Select defaultValue="RULE_BUCKET">
                                <MenuItem value="RULE_BUCKET">Rule Bucket</MenuItem>
                                <MenuItem value="LOOKUP">Lookup Bucket</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <FormControl fullWidth sx={{ mb: 2 }}>
                            <InputLabel>Data Source</InputLabel>
                            <Select defaultValue="">
                                <MenuItem value="postgres_main">PostgreSQL Main</MenuItem>
                                <MenuItem value="mysql_hr">MySQL HR</MenuItem>
                                <MenuItem value="oracle_finance">Oracle Finance</MenuItem>
                            </Select>
                        </FormControl>
                        <TextField
                            fullWidth
                            label="Table Name"
                            placeholder="e.g., customer_transactions"
                            sx={{ mb: 2 }}
                        />

                        <Card sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), border: `1px solid ${alpha(theme.palette.info.main, 0.2)}` }}>
                            <CardContent sx={{ p: 2 }}>
                                <Typography variant="subtitle2" color="info.main" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                                    <CloudUploadIcon sx={{ mr: 1, fontSize: 18 }} />
                                    Quick Setup
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                    After creating the bucket, you'll be able to select and configure variables from your chosen table.
                                </Typography>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </DialogContent>

            <DialogActions sx={{ p: 3, pt: 1 }}>
                <Button onClick={() => setCreateDialogOpen(false)}>
                    Cancel
                </Button>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    sx={{ borderRadius: 2 }}
                >
                    Create Bucket
                </Button>
            </DialogActions>
        </Dialog>
    );

    const renderVariablesDialog = () => (
        <Dialog
            open={variablesDialogOpen}
            onClose={() => setVariablesDialogOpen(false)}
            maxWidth="lg"
            fullWidth
            PaperProps={{
                sx: { borderRadius: 3, minHeight: '70vh' }
            }}
        >
            <DialogTitle sx={{ pb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                            <DataObjectIcon />
                        </Avatar>
                        <Box>
                            <Typography variant="h6" fontWeight="bold">
                                {selectedBucket?.name} Variables
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                Configure variable properties and relationships
                            </Typography>
                        </Box>
                    </Box>
                    <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        size="small"
                        sx={{ borderRadius: 2 }}
                    >
                        Add Variable
                    </Button>
                </Box>
            </DialogTitle>

            <DialogContent sx={{ pt: 2 }}>
                {selectedBucket && (
                    <Box>
                        {/* Variable Configuration Accordion */}
                        {selectedBucket.variables.map((variable, index) => (
                            <Accordion key={index} sx={{ mb: 2, borderRadius: 2, '&:before': { display: 'none' } }}>
                                <AccordionSummary
                                    expandIcon={<ExpandMoreIcon />}
                                    sx={{
                                        bgcolor: alpha(theme.palette.primary.main, 0.05),
                                        borderRadius: 2,
                                        '&.Mui-expanded': {
                                            borderRadius: '8px 8px 0 0'
                                        }
                                    }}
                                >
                                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                                        <Avatar sx={{ width: 32, height: 32, mr: 2, bgcolor: 'primary.main' }}>
                                            {variable.name.charAt(0).toUpperCase()}
                                        </Avatar>
                                        <Box sx={{ flexGrow: 1 }}>
                                            <Typography variant="subtitle1" fontWeight="medium">
                                                {variable.name}
                                            </Typography>
                                            <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                                                <Chip label={variable.dataType} size="small" variant="outlined" />
                                                <VariableChip
                                                    label={variable.type}
                                                    size="small"
                                                    variant={getVariableTypeColor(variable.type)}
                                                />
                                            </Box>
                                        </Box>
                                    </Box>
                                </AccordionSummary>
                                <AccordionDetails sx={{ bgcolor: 'background.paper' }}>
                                    <Grid container spacing={3}>
                                        <Grid item xs={12} md={6}>
                                            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                                                Basic Properties
                                            </Typography>
                                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                                <FormControlLabel
                                                    control={<Switch defaultChecked />}
                                                    label="Unique Values"
                                                />
                                                <FormControlLabel
                                                    control={<Switch defaultChecked />}
                                                    label="Aggregatable"
                                                />
                                                <FormControlLabel
                                                    control={<Switch />}
                                                    label="Indexed"
                                                />
                                            </Box>
                                        </Grid>
                                        <Grid item xs={12} md={6}>
                                            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                                                Entity Keys
                                            </Typography>
                                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                                <FormControlLabel
                                                    control={<Switch color="primary" defaultChecked={variable.type === 'Primary Key'} />}
                                                    label="Primary Entity Key"
                                                />
                                                <FormControlLabel
                                                    control={<Switch color="secondary" defaultChecked={variable.type === 'Secondary Key'} />}
                                                    label="Secondary Entity Key"
                                                />
                                                <FormControlLabel
                                                    control={<Switch color="success" defaultChecked={variable.type === 'Date Dimension'} />}
                                                    label="Date Dimension"
                                                />
                                            </Box>
                                        </Grid>
                                    </Grid>
                                </AccordionDetails>
                            </Accordion>
                        ))}
                    </Box>
                )}
            </DialogContent>

            <DialogActions sx={{ p: 3, pt: 1 }}>
                <Button onClick={() => setVariablesDialogOpen(false)}>
                    Cancel
                </Button>
                <Button
                    variant="contained"
                    startIcon={<SettingsIcon />}
                    sx={{ borderRadius: 2 }}
                >
                    Save Configuration
                </Button>
            </DialogActions>
        </Dialog>
    );

    return (
        <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
            {/* Header */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                    Data Bucket Manager
                </Typography>
                <Typography variant="body1" color="text.secondary">
                    Create, manage, and configure data buckets for your monitoring rules
                </Typography>
            </Box>

            {/* Tabs */}
            <Box sx={{ mb: 3 }}>
                <Tabs
                    value={activeTab}
                    onChange={(e, newValue) => setActiveTab(newValue)}
                    sx={{
                        '& .MuiTab-root': {
                            borderRadius: 2,
                            mr: 1,
                            fontWeight: 600,
                        },
                        '& .Mui-selected': {
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                        }
                    }}
                >
                    <Tab label="Overview" />
                    <Tab label="Variables" disabled={!selectedBucket} />
                    <Tab label="Relationships" />
                    <Tab label="Analytics" />
                </Tabs>
            </Box>

            {/* Tab Content */}
            {activeTab === 0 && renderOverviewTab()}
            {activeTab === 1 && renderVariablesTab()}

            {/* Floating Action Button */}
            <Fab
                color="primary"
                sx={{
                    position: 'fixed',
                    bottom: 24,
                    right: 24,
                    borderRadius: 3,
                }}
                onClick={() => setCreateDialogOpen(true)}
            >
                <AddIcon />
            </Fab>

            {/* Dialogs */}
            {renderCreateDialog()}
            {renderVariablesDialog()}
        </Box>
    );
};

export default ModernDataBucketsManager;
