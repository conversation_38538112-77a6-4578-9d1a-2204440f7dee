package com.progize.tms.services;

import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.services.models.AggregationRequirement;
import com.progize.tms.services.models.RuleVariable;
import com.progize.tms.services.models.SimpleCondition;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * Service for executing consolidated ClickHouse queries for rule aggregations. This optimizes
 * performance by grouping similar aggregation requirements.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ConsolidatedClickHouseQueryService {

  @Qualifier("clickHouseJdbcTemplate")
  private final JdbcTemplate clickHouseJdbcTemplate;

  private final RuleVariableService ruleVariableService;
  private final DataBucketService dataBucketService;

  // Pattern for converting camelCase to snake_case
  private static final java.util.regex.Pattern CAMEL_CASE_PATTERN =
      java.util.regex.Pattern.compile("([a-z])([A-Z])");

  /**
   * Executes aggregations for a customer by consolidating requirements into minimal number of
   * efficient queries.
   *
   * @param customerId Customer ID to execute aggregations for
   * @param requirements Set of aggregation requirements to execute
   * @return Map of aggregation keys to their results
   */
  public Map<String, Object> executeAggregationsForCustomer(
      String customerId, Set<AggregationRequirement> requirements) {
    return executeAggregationsForCustomer(customerId, requirements, null);
  }

  /**
   * Executes aggregations for a customer by consolidating requirements into minimal number of
   * efficient queries with bucket context.
   *
   * @param customerId Customer ID to execute aggregations for
   * @param requirements Set of aggregation requirements to execute
   * @param bucketId The ID of the data bucket to use for variable context, or null to use global
   *     variables
   * @return Map of aggregation keys to their results
   */
  public Map<String, Object> executeAggregationsForCustomer(
      String customerId, Set<AggregationRequirement> requirements, Long bucketId) {

    if (requirements == null || requirements.isEmpty()) {
      log.info("No aggregation requirements to execute");
      return new HashMap<>();
    }

    log.info(
        "Executing {} aggregation requirements for customer: {} with bucketId: {}",
        requirements.size(),
        customerId,
        bucketId);

    // Group requirements by time period for efficient querying
    Map<String, Object> results = new ConcurrentHashMap<>();
    Map<Integer, List<AggregationRequirement>> requirementsByPeriod =
        groupRequirementsByPeriod(requirements);

    // Execute one consolidated query per time period
    for (Map.Entry<Integer, List<AggregationRequirement>> entry : requirementsByPeriod.entrySet()) {

      int period = entry.getKey();
      List<AggregationRequirement> periodRequirements = entry.getValue();

      try {
        Map<String, Object> periodResults =
            executeConsolidatedQuery(customerId, period, periodRequirements, bucketId);
        results.putAll(periodResults);

        log.info(
            "Successfully executed consolidated query for period {} days with {} aggregations",
            period,
            periodRequirements.size());
      } catch (Exception e) {
        log.error("Error executing consolidated query for period {} days", period, e);
        // Store fallback values for failed requirements
        periodRequirements.forEach(req -> storeFallbackValue(results, req));
      }
    }

    return results;
  }

  /** Groups aggregation requirements by their time period for consolidated execution. */
  private Map<Integer, List<AggregationRequirement>> groupRequirementsByPeriod(
      Set<AggregationRequirement> requirements) {

    return requirements.stream().collect(Collectors.groupingBy(AggregationRequirement::getPeriod));
  }

  /**
   * Executes a consolidated query for a specific time period, containing multiple aggregation
   * requirements efficiently combined.
   */
  private Map<String, Object> executeConsolidatedQuery(
      String customerId, int period, List<AggregationRequirement> requirements) {
    return executeConsolidatedQuery(customerId, period, requirements, null);
  }

  /**
   * Executes a consolidated query for a specific time period, containing multiple aggregation
   * requirements efficiently combined with bucket context.
   */
  private Map<String, Object> executeConsolidatedQuery(
      String customerId, int period, List<AggregationRequirement> requirements, Long bucketId) {

    // Build the consolidated query
    String sql = buildConsolidatedQuery(period, requirements, bucketId);
    log.debug("Executing consolidated query: {}", sql);

    // Execute the query and get results
    Map<String, Object> results = new HashMap<>();
    Map<String, Object> queryResults = executeQuery(sql, customerId);

    // Map results back to individual aggregation keys
    for (AggregationRequirement req : requirements) {
      String columnAlias = getColumnAliasForRequirement(req);
      Object value = queryResults.get(columnAlias);

      if (value != null) {
        results.put(req.getAggregationKey(), value);
      } else {
        // If the query didn't return a value, use a fallback
        storeFallbackValue(results, req);
      }
    }

    return results;
  }

  /**
   * Builds a consolidated ClickHouse query that combines multiple aggregation requirements with the
   * same time period.
   */
  private String buildConsolidatedQuery(int period, List<AggregationRequirement> requirements) {
    return buildConsolidatedQuery(period, requirements, null);
  }

  /**
   * Builds a consolidated ClickHouse query that combines multiple aggregation requirements with the
   * same time period and bucket context.
   */
  private String buildConsolidatedQuery(
      int period, List<AggregationRequirement> requirements, Long bucketId) {
    StringBuilder queryBuilder = new StringBuilder();
    String tableName;
    queryBuilder.append("SELECT ");

    // Add each aggregation as a separate column
    boolean firstAggregation = true;
    for (AggregationRequirement req : requirements) {
      if (!firstAggregation) {
        queryBuilder.append(", ");
      }

      appendAggregationExpression(queryBuilder, req, bucketId);
      firstAggregation = false;
    }

    List<DataBucketVariableEntity> bucketVariables = dataBucketService.getVariables(bucketId);
    // Find the variable by name
    Optional<DataBucketVariableEntity> bucketVariable =
        bucketVariables.stream()
            .filter(var -> var.getName().equals(requirements.get(0).getField()))
            .findFirst();
    // Add FROM clause with time period filter
    queryBuilder.append(" FROM ");
    queryBuilder.append(bucketVariable.get().getTableName());
    queryBuilder.append(" WHERE customer_id = ? ");
    if (period > 0) {
      queryBuilder.append("AND deal_date >= NOW() - INTERVAL ").append(period).append(" DAY");
    }

    return queryBuilder.toString();
  }

  /**
   * Appends a single aggregation expression to the query builder with proper handling of filter
   * conditions.
   */
  private void appendAggregationExpression(StringBuilder queryBuilder, AggregationRequirement req) {
    appendAggregationExpression(queryBuilder, req, null);
  }

  /**
   * Appends a single aggregation expression to the query builder with proper handling of filter
   * conditions and bucket context.
   */
  private void appendAggregationExpression(
      StringBuilder queryBuilder, AggregationRequirement req, Long bucketId) {
    String function = req.getFunction().toUpperCase();
    String fieldId = req.getField();
    String columnAlias = getColumnAliasForRequirement(req);

    // Start the aggregation function
    queryBuilder.append(function);
    queryBuilder.append("(");

    // For COUNT, we might use COUNT(*) or COUNT(DISTINCT field)
    if ("COUNT".equals(function) && fieldId == null) {
      queryBuilder.append("*");
    } else {
      // For other functions, add the field name
      String columnName = getColumnNameForField(fieldId, bucketId);
      queryBuilder.append(columnName);
    }

    // Close the function
    queryBuilder.append(")");

    // Add filter conditions if present
    SimpleCondition filterCondition = req.getFilterCondition();
    if (filterCondition != null) {
      queryBuilder.append(" FILTER (WHERE ");
      appendFilterCondition(queryBuilder, filterCondition, bucketId);
      queryBuilder.append(")");
    }

    // Add column alias
    queryBuilder.append(" AS ");
    queryBuilder.append(columnAlias);
  }

  /** Appends a filter condition to the query builder. */
  private void appendFilterCondition(
      StringBuilder queryBuilder, SimpleCondition condition, Long bucketId) {
    String fieldId = condition.getField();
    String operator = condition.getOperator();
    Object value = condition.getValue();

    // Get the column name for the field
    String columnName = getColumnNameForField(fieldId, bucketId);
    queryBuilder.append(columnName);
    queryBuilder.append(" ");

    // Handle special operators
    switch (operator) {
      case "contains":
        queryBuilder.append("LIKE ");
        // Wrap the value with wildcards for contains
        value = "%" + value + "%";
        appendFormattedValue(queryBuilder, value);
        break;
      case "startsWith":
        queryBuilder.append("LIKE ");
        // Append wildcard at the end for startsWith
        value = value + "%";
        appendFormattedValue(queryBuilder, value);
        break;
      case "endsWith":
        queryBuilder.append("LIKE ");
        // Prepend wildcard for endsWith
        value = "%" + value;
        appendFormattedValue(queryBuilder, value);
        break;
      case "in":
        queryBuilder.append("IN (");
        if (value instanceof Collection) {
          appendCollectionValues(queryBuilder, (Collection<?>) value);
        } else if (value != null && value.getClass().isArray()) {
          appendArrayValues(queryBuilder, value);
        } else {
          // Single value in
          appendFormattedValue(queryBuilder, value);
        }
        queryBuilder.append(")");
        break;
      case "isNull":
        queryBuilder.append("IS NULL");
        break;
      case "isNotNull":
        queryBuilder.append("IS NOT NULL");
        break;
      default:
        // Standard operators
        String sqlOperator = convertOperator(operator);
        queryBuilder.append(sqlOperator);
        if (!sqlOperator.endsWith("NULL")) {
          queryBuilder.append(" ");
          appendFormattedValue(queryBuilder, value);
        }
        break;
    }
  }

  /** Formats a value according to its type for SQL inclusion. */
  private void appendFormattedValue(StringBuilder queryBuilder, Object value) {
    if (value == null) {
      queryBuilder.append("NULL");
      return;
    }

    if (value instanceof String) {
      queryBuilder.append("'");
      queryBuilder.append(escapeStringValue((String) value));
      queryBuilder.append("'");
    } else if (value instanceof Date) {
      queryBuilder.append("'");
      queryBuilder.append(new java.sql.Timestamp(((Date) value).getTime()));
      queryBuilder.append("'");
    } else if (value instanceof Boolean) {
      queryBuilder.append(((Boolean) value) ? "1" : "0");
    } else {
      // Numbers and other types
      queryBuilder.append(value.toString());
    }
  }

  /** Appends a collection of values in SQL format, properly formatted and separated by commas. */
  private void appendCollectionValues(StringBuilder queryBuilder, Collection<?> values) {
    if (values == null || values.isEmpty()) {
      queryBuilder.append("NULL"); // Empty IN clause will always be false
      return;
    }

    boolean first = true;
    for (Object value : values) {
      if (!first) {
        queryBuilder.append(", ");
      }
      appendFormattedValue(queryBuilder, value);
      first = false;
    }
  }

  /** Appends an array of values in SQL format. */
  private void appendArrayValues(StringBuilder queryBuilder, Object array) {
    if (array == null) {
      queryBuilder.append("NULL");
      return;
    }

    int length = Array.getLength(array);
    if (length == 0) {
      queryBuilder.append("NULL"); // Empty IN clause will always be false
      return;
    }

    boolean first = true;
    for (int i = 0; i < length; i++) {
      if (!first) {
        queryBuilder.append(", ");
      }
      appendFormattedValue(queryBuilder, Array.get(array, i));
      first = false;
    }
  }

  /** Escapes special characters in string values to prevent SQL injection. */
  private String escapeStringValue(String value) {
    if (value == null) {
      return "";
    }
    return value.replace("'", "''");
  }

  /**
   * Converts a logical operator from the rule condition to a SQL operator.
   *
   * @param operator The operator string from the rule condition
   * @return The corresponding SQL operator
   */
  private String convertOperator(String operator) {
    if (operator == null) {
      throw new IllegalArgumentException("Operator cannot be null");
    }

    switch (operator) {
        // Standard operators
      case "eq":
      case "equals":
        return "=";
      case "neq":
      case "notEquals":
        return "!=";
      case "lt":
      case "lessThan":
        return "<";
      case "gt":
      case "greaterThan":
        return ">";
      case "lte":
      case "lessThanEquals":
        return "<=";
      case "gte":
      case "greaterThanEquals":
        return ">=";

        // Special operators
      case "contains":
        return "LIKE"; // Will need special handling in the SQL generation
      case "startsWith":
        return "LIKE"; // Will need special handling in the SQL generation
      case "endsWith":
        return "LIKE"; // Will need special handling in the SQL generation
      case "in":
        return "IN"; // Will need special handling in the SQL generation
      case "isNull":
        return "IS NULL"; // Special case, value not needed
      case "isNotNull":
        return "IS NOT NULL"; // Special case, value not needed
      default:
        throw new IllegalArgumentException("Unsupported operator: " + operator);
    }
  }

  /** Generates a unique column alias for each aggregation requirement. */
  private String getColumnAliasForRequirement(AggregationRequirement req) {
    String baseKey = req.getAggregationKey().replace('.', '_');
    // Use Math.abs to ensure we never have negative hash codes in column aliases
    // Also, add 'c' prefix to ensure it always starts with a letter (SQL requirement)
    return "agg_c" + Math.abs(baseKey.hashCode());
  }

  /** Executes the query and returns results mapped by column alias. */
  private Map<String, Object> executeQuery(String sql, String customerId) {
    try {
      log.debug("Executing ClickHouse query: {} with customerId: {}", sql, customerId);

      return clickHouseJdbcTemplate.query(
          sql,
          ps -> ps.setString(1, customerId),
          rs -> {
            Map<String, Object> results = new HashMap<>();
            if (rs.next()) {
              int columnCount = rs.getMetaData().getColumnCount();
              for (int i = 1; i <= columnCount; i++) {
                String columnName = rs.getMetaData().getColumnLabel(i);
                Object value = rs.getObject(i);
                results.put(
                    columnName,
                    (value instanceof BigDecimal) ? ((BigDecimal) value).doubleValue() : value);
              }
            }
            return results;
          });
    } catch (Exception e) {
      log.error("Error executing ClickHouse query: {}", sql, e);
      throw e;
    }
  }

  /** Gets the database column name for a field, with snake_case conversion if needed. */
  private String getColumnNameForField(String fieldId) {
    return getColumnNameForField(fieldId, null);
  }

  /**
   * Gets the database column name for a field, with bucket-specific context if provided.
   *
   * @param fieldId The field ID to get the column name for
   * @param bucketId The ID of the data bucket to use for variable context, or null to use global
   *     variables
   * @return The database column name for the field
   */
  private String getColumnNameForField(String fieldId, Long bucketId) {
    if (bucketId != null) {
      // Try to find the variable in the bucket
      try {
        List<DataBucketVariableEntity> bucketVariables = dataBucketService.getVariables(bucketId);

        // Find the variable by name
        Optional<DataBucketVariableEntity> bucketVariable =
            bucketVariables.stream().filter(var -> var.getName().equals(fieldId)).findFirst();

        if (bucketVariable.isPresent()) {
          DataBucketVariableEntity variable = bucketVariable.get();
          // Use column name if available, otherwise use the variable's column name property
          if (variable.getColumnName() != null && !variable.getColumnName().isEmpty()) {
            return variable.getColumnName();
          }

          // If table name is provided, use it as a prefix
          if (variable.getTableName() != null && !variable.getTableName().isEmpty()) {
            return variable.getTableName() + "." + camelToSnakeCase(fieldId);
          }
        }
      } catch (Exception e) {
        log.warn("Error getting bucket variable for field {}: {}", fieldId, e.getMessage());
        // Fall through to global variable lookup
      }
    }

    // Try to find explicit column mapping in RuleVariable
    RuleVariable variable = ruleVariableService.getVariable(fieldId);
    if (variable != null
        && variable.getColumnName() != null
        && !variable.getColumnName().isEmpty()) {
      return variable.getColumnName();
    }

    // Fall back to camelCase to snake_case conversion
    return camelToSnakeCase(fieldId);
  }

  /** Converts camelCase to snake_case for database column names. */
  private String camelToSnakeCase(String input) {
    return CAMEL_CASE_PATTERN.matcher(input).replaceAll("$1_$2").toLowerCase();
  }

  /** Stores a fallback value for a failed aggregation to prevent rule failures. */
  private void storeFallbackValue(
      Map<String, Object> aggregationResults, AggregationRequirement requirement) {
    String key = requirement.getAggregationKey();
    String function = requirement.getFunction().toLowerCase();

    // Use appropriate fallback values based on function type
    switch (function) {
      case "count" -> aggregationResults.put(key, 0L);
      case "sum", "avg" -> aggregationResults.put(key, 0.0);
      default -> aggregationResults.put(key, null);
    }

    log.warn("Using fallback value for aggregation: {}", key);
  }
}
