import { axios } from './axiosConfig';
// This imports the configured axios instance with auth token handling

/**
 * Service for handling compliance workflow tasks and actions
 */
class ComplianceWorkflowService {
  /**
   * Get all compliance tasks for the current user
   * @returns {Promise<Array>} List of compliance tasks
   */
  async getComplianceTasks() {
    try {
      const response = await axios.get('/api/workflow/compliance/tasks');
      console.log('Compliance tasks:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching compliance tasks:', error);
      throw error;
    }
  }

  /**
   * Get details of a specific compliance task
   * @param {string} taskId Task ID
   * @returns {Promise<Object>} Task details with form properties
   */
  async getTaskDetails(taskId) {
    try {
      const response = await axios.get(`/api/workflow/compliance/tasks/${taskId}`);
      console.log('Task details:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching task details for ${taskId}:`, error);
      throw error;
    }
  }

  /**
   * Complete an officer review task
   * @param {string} taskId Task ID
   * @param {string} decision Officer decision
   * @param {string} comments Officer comments
   * @param {Array} attachments Optional attachments
   * @returns {Promise<Object>} Response
   */
  async completeOfficerReview(taskId, decision, comments, attachments = []) {
    try {
      const formData = new FormData();
      formData.append('decision', decision);
      formData.append('comments', comments);
      
      if (attachments && attachments.length > 0) {
        attachments.forEach(file => {
          formData.append('attachments', file);
        });
      }
      
      const response = await axios.post(
        `/api/workflow/compliance/tasks/${taskId}/officer-review`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      console.log('Officer review completed:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error completing officer review for task ${taskId}:`, error);
      throw error;
    }
  }

  /**
   * Complete a manager review task
   * @param {string} taskId Task ID
   * @param {string} managerDecision Manager decision
   * @param {string} managerComments Manager comments
   * @param {Array} attachments Optional attachments
   * @returns {Promise<Object>} Response
   */
  async completeManagerReview(taskId, managerDecision, managerComments, attachments = []) {
    try {
      const formData = new FormData();
      formData.append('managerDecision', managerDecision);
      formData.append('managerComments', managerComments);
      
      if (attachments && attachments.length > 0) {
        attachments.forEach(file => {
          formData.append('attachments', file);
        });
      }
      
      const response = await axios.post(
        `/api/workflow/compliance/tasks/${taskId}/manager-review`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      console.log('Manager review completed:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error completing manager review for task ${taskId}:`, error);
      throw error;
    }
  }
  
  /**
   * Complete a generic task with dynamic form properties
   * @param {string} taskId Task ID
   * @param {Object} formValues Form values as key-value pairs
   * @param {Array} attachments Optional attachments
   * @returns {Promise<Object>} Response
   */
  async completeGenericTask(taskId, formValues, attachments = []) {
    try {
      console.log(`Completing generic task ${taskId} with values:`, formValues);
      
      const formData = new FormData();
      
      // Add all form values to the form data
      Object.entries(formValues).forEach(([key, value]) => {
        formData.append(key, value);
      });
      
      // Add attachments if any
      if (attachments && attachments.length > 0) {
        attachments.forEach(file => {
          formData.append('attachments', file);
        });
      }
      
      const response = await axios.post(
        `/api/workflow/compliance/tasks/${taskId}/complete`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      console.log('Task completed successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error completing task ${taskId}:`, error);
      throw error;
    }
  }
}

export default new ComplianceWorkflowService();
