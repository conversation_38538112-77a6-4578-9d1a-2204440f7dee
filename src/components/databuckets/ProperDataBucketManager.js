import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Box,
    Typography,
    Button,
    Alert,
    TextField,
    alpha,
    useTheme
} from '@mui/material';
import {
    Add as AddIcon,
    Search as SearchIcon
} from '@mui/icons-material';
import { dataBucketService } from '../../services/api';
import DataBucketsPanel from '../rulevariables/DataBucketsPanel';
import ProperBucketCreator from './ProperBucketCreator';
import ProperBucketEditor from './ProperBucketEditor';
import ProperVariableManager from './ProperVariableManager';

const ProperDataBucketManager = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const [buckets, setBuckets] = useState([]);
    const [dataSources, setDataSources] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    
    // View states
    const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'variables'
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [createDrawerOpen, setCreateDrawerOpen] = useState(false);

    useEffect(() => {
        fetchBuckets();
    }, []);

    const fetchBuckets = async () => {
        try {
            setLoading(true);
            const data = await dataBucketService.getAllBuckets();
            setBuckets(Array.isArray(data) ? data : []);
            setError(null);
        } catch (err) {
            setError('Failed to load data buckets. Please try again later.');
            console.error('Error fetching buckets:', err);
            setBuckets([]);
        } finally {
            setLoading(false);
        }
    };

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    // Filter buckets based on search query
    const filteredBuckets = buckets.filter(bucket =>
        bucket.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const handleCreateBucket = () => {
        setCurrentView('create');
    };

    const handleEditBucket = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('edit');
    };

    const handleManageVariables = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('variables');
    };

    const handleBackToList = () => {
        setCurrentView('list');
        setSelectedBucket(null);
        fetchBuckets(); // Refresh the list
    };

    const handleBucketCreated = (newBucket) => {
        setBuckets(prev => [...prev, newBucket]);
        setCurrentView('list');
    };

    const handleBucketUpdated = (updatedBucket) => {
        setBuckets(prev => prev.map(bucket => 
            bucket.id === updatedBucket.id ? updatedBucket : bucket
        ));
        setCurrentView('list');
    };

    const renderHeader = () => (
        currentView === 'list' && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
                <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateBucket}
                    sx={{
                        borderRadius: '28px',
                        px: 3,
                        py: 1,
                        boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                        '&:hover': {
                            transform: 'translateY(-1px)',
                            boxShadow: '0 6px 20px rgba(0,118,255,0.39)',
                        }
                    }}
                >
                    Create New Bucket
                </Button>
            </Box>
        )
    );

    const renderSearchBar = () => (
        currentView === 'list' && (
            <Box sx={{ mb: 3 }}>
                <Box sx={{ width: { xs: '100%', sm: 400 } }}>
                    <TextField
                        fullWidth
                        size="small"
                        placeholder="Search buckets by name..."
                        value={searchQuery}
                        onChange={handleSearchChange}
                        InputProps={{
                            startAdornment: (
                                <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                                    <SearchIcon sx={{ color: theme.palette.text.secondary, fontSize: '1.2rem' }} />
                                </Box>
                            ),
                        }}
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                fontSize: '0.875rem',
                                '& fieldset': {
                                    borderColor: theme.palette.grey[300]
                                }
                            }
                        }}
                    />
                </Box>
            </Box>
        )
    );

    const renderContent = () => {
        switch (currentView) {
            case 'create':
                return (
                    <ProperBucketCreator
                        onCancel={handleBackToList}
                        onBucketCreated={handleBucketCreated}
                    />
                );

            case 'edit':
                return (
                    <ProperBucketEditor
                        bucket={selectedBucket}
                        onCancel={handleBackToList}
                        onBucketUpdated={handleBucketUpdated}
                    />
                );
            
            case 'variables':
                return (
                    <ProperVariableManager
                        bucket={selectedBucket}
                        onBack={handleBackToList}
                    />
                );
            
            default:
                return (
                    <DataBucketsPanel
                        initialBuckets={filteredBuckets}
                        createDrawerOpen={createDrawerOpen}
                        setCreateDrawerOpen={setCreateDrawerOpen}
                        onEditBucket={handleEditBucket}
                        onManageVariables={handleManageVariables}
                    />
                );
        }
    };

    return (
        <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
            {renderHeader()}
            {renderSearchBar()}

            {/* Error Message */}
            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {renderContent()}
        </Box>
    );
};

export default ProperDataBucketManager;
