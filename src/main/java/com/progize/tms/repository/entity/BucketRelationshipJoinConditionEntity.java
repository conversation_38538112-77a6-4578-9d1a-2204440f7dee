package com.progize.tms.repository.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "bucket_relationship_join_condition")
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class BucketRelationshipJoinConditionEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "bucket_relationship_id", nullable = false)
  private BucketRelationshipEntity bucketRelationship;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "rule_bucket_variable_id", nullable = false)
  private DataBucketVariableEntity ruleBucketVariable;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "lookup_bucket_variable_id", nullable = false)
  private DataBucketVariableEntity lookupBucketVariable;

  @CreatedDate
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @LastModifiedDate
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;
}
