package com.progize.tms.controllers.bucketrelationship.req;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateBucketRelationshipRequest {
  public Long getRuleBucketId() {
    return ruleBucketId;
  }

  public void setRuleBucketId(Long ruleBucketId) {
    this.ruleBucketId = ruleBucketId;
  }

  public Long getLookupBucketId() {
    return lookupBucketId;
  }

  public void setLookupBucketId(Long lookupBucketId) {
    this.lookupBucketId = lookupBucketId;
  }

  public List<JoinConditionRequest> getJoinConditions() {
    return joinConditions;
  }

  public void setJoinConditions(List<JoinConditionRequest> joinConditions) {
    this.joinConditions = joinConditions;
  }

  @NotNull(message = "Rule bucket ID is required")
  private Long ruleBucketId;

  @NotNull(message = "Lookup bucket ID is required")
  private Long lookupBucketId;

  @NotEmpty(message = "At least one join condition is required")
  private List<@Valid JoinConditionRequest> joinConditions;

  @Getter
  @Setter
  public static class JoinConditionRequest {
    public Long getRuleBucketVariableId() {
      return ruleBucketVariableId;
    }

    public void setRuleBucketVariableId(Long ruleBucketVariableId) {
      this.ruleBucketVariableId = ruleBucketVariableId;
    }

    public Long getLookupBucketVariableId() {
      return lookupBucketVariableId;
    }

    public void setLookupBucketVariableId(Long lookupBucketVariableId) {
      this.lookupBucketVariableId = lookupBucketVariableId;
    }

    @NotNull(message = "Rule bucket variable ID is required")
    private Long ruleBucketVariableId;

    @NotNull(message = "Lookup bucket variable ID is required")
    private Long lookupBucketVariableId;
  }
}
