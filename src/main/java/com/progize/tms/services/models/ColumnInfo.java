package com.progize.tms.services.models;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Model representing a database column with metadata. */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ColumnInfo {
  private String name;
  private String dataType;
  private int position;
  private String mappedDataType;
  private String description;

  // Convenience method to create display name from column name
  public String generateDisplayName() {
    if (name == null || name.isEmpty()) {
      return "";
    }

    // Convert snake_case to Title Case Words
    StringBuilder result = new StringBuilder();
    boolean capitalizeNext = true;

    for (char c : name.toCharArray()) {
      if (c == '_') {
        result.append(' ');
        capitalizeNext = true;
      } else {
        if (capitalizeNext) {
          result.append(Character.toUpperCase(c));
          capitalizeNext = false;
        } else {
          result.append(c);
        }
      }
    }

    return result.toString();
  }
}
