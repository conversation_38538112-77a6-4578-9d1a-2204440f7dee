package com.progize.tms.services;

import com.progize.tms.repository.DataTypeRepository;
import com.progize.tms.repository.RuleVariableRepository;
import com.progize.tms.repository.RuleVariableSourceTableRepository;
import com.progize.tms.repository.entity.DataType;
import com.progize.tms.repository.entity.RuleVariableEntity;
import com.progize.tms.repository.entity.RuleVariableSourceTable;
import com.progize.tms.services.models.ColumnInfo;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for managing rule variables and their source tables. Handles creation, update, and
 * deletion of rule variables.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleVariableManagementService {
  private final RuleVariableRepository ruleVariableRepository;
  private final RuleVariableSourceTableRepository sourceTableRepository;
  private final DataTypeRepository dataTypeRepository;
  private final SchemaDiscoveryService schemaDiscoveryService;

  /**
   * Registers a source table for rule variables.
   *
   * @param schemaName Database/schema name
   * @param tableName Table name
   * @param displayName Display name for the table
   * @param description Description of the table
   * @return The created source table entity
   */
  @Transactional
  public RuleVariableSourceTable registerSourceTable(
      String schemaName, String tableName, String displayName, String description) {

    // Check if table already exists in the registry
    Optional<RuleVariableSourceTable> existingTable =
        sourceTableRepository.findByTableNameAndSchemaNameAndConnectionName(
            tableName, schemaName, "clickhouse");

    if (existingTable.isPresent()) {
      RuleVariableSourceTable table = existingTable.get();

      // Update display name and description if provided
      if (displayName != null && !displayName.isEmpty()) {
        table.setDisplayName(displayName);
      }
      if (description != null) {
        table.setDescription(description);
      }

      table.setUpdatedAt(LocalDateTime.now());
      return sourceTableRepository.save(table);
    }

    // Create new table entry
    RuleVariableSourceTable table = new RuleVariableSourceTable();
    table.setTableName(tableName);
    table.setSchemaName(schemaName);
    table.setDisplayName(displayName != null ? displayName : tableName);
    table.setDescription(description);
    table.setConnectionName("clickhouse");
    table.setCreatedAt(LocalDateTime.now());
    table.setUpdatedAt(LocalDateTime.now());

    // Additional metadata
    Map<String, Object> metadata = new HashMap<>();
    metadata.put("registered_at", LocalDateTime.now().toString());
    table.setMetadata(metadata);

    return sourceTableRepository.save(table);
  }

  /**
   * Registers columns from a source table as rule variables.
   *
   * @param sourceTableId The ID of the source table
   * @param columnInfos List of columns to register as variables
   * @return List of created/updated rule variable entities
   */
  @Transactional
  public List<RuleVariableEntity> registerColumns(
      Long sourceTableId, List<ColumnInfo> columnInfos) {
    RuleVariableSourceTable sourceTable =
        sourceTableRepository
            .findById(sourceTableId)
            .orElseThrow(
                () ->
                    new EntityNotFoundException(
                        "Source table not found with ID: " + sourceTableId));

    List<RuleVariableEntity> result = new ArrayList<>();

    for (ColumnInfo columnInfo : columnInfos) {
      // Find data type entity
      DataType dataType =
          dataTypeRepository
              .findByCode(columnInfo.getMappedDataType())
              .orElseThrow(
                  () ->
                      new EntityNotFoundException(
                          "Data type not found: " + columnInfo.getMappedDataType()));

      // Generate variable code from table and column
      String variableCode = generateVariableCode(sourceTable.getTableName(), columnInfo.getName());

      // Check if variable already exists
      Optional<RuleVariableEntity> existingVariable =
          ruleVariableRepository.findByCode(variableCode);

      RuleVariableEntity variable;
      if (existingVariable.isPresent()) {
        // Update existing variable
        variable = existingVariable.get();
        variable.setDisplayName(columnInfo.generateDisplayName());
        variable.setDescription(columnInfo.getDescription());
        variable.setDataType(dataType);
        variable.setUpdatedAt(LocalDateTime.now());
      } else {
        // Create new variable
        variable = new RuleVariableEntity();
        variable.setCode(variableCode);
        variable.setDisplayName(columnInfo.generateDisplayName());
        variable.setDescription(columnInfo.getDescription());
        variable.setSourceTable(sourceTable);
        variable.setColumnName(columnInfo.getName());
        variable.setDataType(dataType);
        variable.setAggregatable(isAggregatable(columnInfo.getMappedDataType()));
        variable.setCreatedAt(LocalDateTime.now());
        variable.setUpdatedAt(LocalDateTime.now());

        // Add metadata
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("ui_display_order", columnInfo.getPosition());
        metadata.put("original_type", columnInfo.getDataType());
        variable.setMetadata(metadata);
      }

      result.add(ruleVariableRepository.save(variable));
    }

    return result;
  }

  /**
   * Deletes a rule variable.
   *
   * @param variableId The ID of the variable to delete
   */
  @Transactional
  public void deleteRuleVariable(Long variableId) {
    ruleVariableRepository.deleteById(variableId);
  }

  /**
   * Gets all registered source tables.
   *
   * @return List of all registered source tables
   */
  public List<RuleVariableSourceTable> getAllSourceTables() {
    return sourceTableRepository.findAll();
  }

  /**
   * Updates the aggregatable flag of a rule variable.
   *
   * @param variableId The ID of the variable to update
   * @param aggregatable The new aggregatable value
   * @return The updated rule variable entity
   */
  @Transactional
  public RuleVariableEntity updateAggregatable(Long variableId, boolean aggregatable) {
    RuleVariableEntity variable =
        ruleVariableRepository
            .findById(variableId)
            .orElseThrow(
                () ->
                    new EntityNotFoundException("Rule variable not found with ID: " + variableId));

    variable.setAggregatable(aggregatable);
    variable.setUpdatedAt(LocalDateTime.now());

    return ruleVariableRepository.save(variable);
  }

  /**
   * Generates a camelCase variable code from table and column names.
   *
   * @param tableName The table name
   * @param columnName The column name
   * @return The generated variable code
   */
  private String generateVariableCode(String tableName, String columnName) {
    // Use just the column name for most cases
    String baseCode = columnName;

    // For common tables like transactions, customers, etc., prefix with table name
    if (isCommonTable(tableName)) {
      baseCode = tableName + "_" + columnName;
    }

    // Convert to camelCase
    return toCamelCase(baseCode);
  }

  /** Determines if a table is a common table that needs column name prefixing. */
  private boolean isCommonTable(String tableName) {
    List<String> commonTables =
        List.of("transactions", "customers", "accounts", "merchants", "users");
    return commonTables.contains(tableName.toLowerCase());
  }

  /** Converts a snake_case string to camelCase. */
  private String toCamelCase(String input) {
    if (input == null || input.isEmpty()) {
      return input;
    }

    StringBuilder result = new StringBuilder();
    boolean capitalizeNext = false;

    for (char c : input.toCharArray()) {
      if (c == '_') {
        capitalizeNext = true;
      } else {
        if (capitalizeNext) {
          result.append(Character.toUpperCase(c));
          capitalizeNext = false;
        } else {
          result.append(Character.toLowerCase(c));
        }
      }
    }

    return result.toString();
  }

  /** Determines if a data type is aggregatable by default. */
  private boolean isAggregatable(String dataType) {
    List<String> aggregatableTypes = List.of("integer", "float", "double", "bigdecimal");
    return aggregatableTypes.contains(dataType);
  }
}
