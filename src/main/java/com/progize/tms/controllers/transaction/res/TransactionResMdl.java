package com.progize.tms.controllers.transaction.res;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/** Response model for transaction data. */
@Data
public class TransactionResMdl {
  private Long id;
  private String fundId;
  private String fundName;
  private String fundShortName;
  private String fundBankAccount;
  private String transDecs;
  private String transaction;
  private String customerId;
  private String portfolioId;
  private String customerName;
  private String crmBankAccountNo;
  private String orderType;
  private String unitTypeCode;
  private String unitTypeName;
  private LocalDateTime dealDate;
  private String month;
  private String transType;
  private BigDecimal fundNetAmount;
  private BigDecimal amountPerUnit;
  private BigDecimal nav;
  private BigDecimal transactionUnit;
  private BigDecimal entryLoad;
  private BigDecimal discountAmt;
  private BigDecimal grossLoad;
  private BigDecimal netLoad;
  private BigDecimal exitLoad;
  private BigDecimal zakatAmt;
  private BigDecimal cgt;
  private BigDecimal transCost;
  private String col22;
  private String rmCode;
  private String rmName;
  private String branch;
  private String newExist;
  private String region;
  private String fundCategory;
  private String industryDesc;
  private String secpSector;
  private String city;
  private String channel;
  private LocalDateTime tradeDateTime;
  private boolean flagged;
  private String flagReason;
  private String customerRiskLevel;
  private String kycStatus;
  private LocalDateTime profileLastUpdated;
  private int loginAttempts;
}
