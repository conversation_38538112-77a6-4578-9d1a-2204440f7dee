-- Insert compliance roles
INSERT INTO roles (name, description) VALUES 
('ROLE_COMPLIANCE_OFFICER', 'Can review and make initial decisions on compliance cases'),
('ROLE_COMPLIANCE_MANAGER', 'Can review and make final decisions on compliance cases');

-- Create a test user with compliance officer role
INSERT INTO users (username, password, email, first_name, last_name, enabled, created_at)
VALUES ('compliance.officer', '$2a$10$rTh6E4VzwGFB.tCT3QsQYeA8A6gVaUFzHHGBh7TuTVXZf0QDtPtIi', '<EMAIL>', 'Compliance', 'Officer', true, NOW());

-- Create a test user with compliance manager role
INSERT INTO users (username, password, email, first_name, last_name, enabled, created_at)
VALUES ('compliance.manager', '$2a$10$rTh6E4VzwGFB.tCT3QsQYeA8A6gVaUFzHHGBh7TuTVXZf0QDtPtIi', '<EMAIL>', 'Compliance', 'Manager', true, NOW());

-- Assign roles to users
INSERT INTO users_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u, roles r
WHERE u.username = 'compliance.officer' AND r.name = 'ROLE_COMPLIANCE_OFFICER';

INSERT INTO users_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u, roles r
WHERE u.username = 'compliance.manager' AND r.name = 'ROLE_COMPLIANCE_MANAGER';
