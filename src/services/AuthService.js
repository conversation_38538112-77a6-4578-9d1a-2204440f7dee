import axios from 'axios';

const API_URL = '/api/auth/';

class AuthService {
  login(username, password) {
    return axios
      .post(API_URL + 'login', {
        username,
        password
      })
      .then(response => {
        if (response.data.token) {
          localStorage.setItem('user', JSON.stringify(response.data));
        }
        return response.data;
      });
  }

  logout() {
    const user = this.getCurrentUser();
    if (user) {
      return axios.post(API_URL + 'logout', { userId: user.id })
        .then(() => {
          localStorage.removeItem('user');
        });
    }
    return Promise.resolve();
  }

  register(username, email, password, firstName, lastName) {
    return axios.post(API_URL + 'register', {
      username,
      email,
      password,
      firstName,
      lastName
    });
  }

  getCurrentUser() {
    return JSON.parse(localStorage.getItem('user'));
  }

  refreshToken() {
    const user = this.getCurrentUser();
    
    if (!user) {
      return Promise.reject("No user found");
    }
    
    return axios.post(API_URL + 'refresh', {
      refreshToken: user.refreshToken
    })
    .then(response => {
      const updatedUser = {
        ...user,
        token: response.data.accessToken,
        refreshToken: response.data.refreshToken
      };
      localStorage.setItem('user', JSON.stringify(updatedUser));
      return updatedUser;
    });
  }

  isAuthenticated() {
    const user = this.getCurrentUser();
    
    if (!user || !user.token) {
      return false;
    }
    
    // Check if token is expired by decoding it
    // JWT tokens are in format: header.payload.signature
    try {
      const payload = user.token.split('.')[1];
      if (!payload) return false;
      
      // Decode base64
      const tokenData = JSON.parse(atob(payload));
      
      // Check if token is expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (tokenData.exp && tokenData.exp < currentTime) {
        // Token is expired, try to refresh
        console.log('Token expired, attempting refresh');
        // We don't want to block the UI here, so we'll return false
        // and let the token refresh happen asynchronously
        this.refreshToken().catch(() => {
          // If refresh fails, clear user data
          localStorage.removeItem('user');
        });
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error validating token:', error);
      return false;
    }
  }
}

export default new AuthService();
