package com.progize.tms.repository.entity;

import com.progize.tms.repository.entity.base.BaseEntity;
import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "bucket_relationship")
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class BucketRelationshipEntity extends BaseEntity {
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "rule_bucket_id", nullable = false)
  private DataBucketEntity ruleBucket;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "lookup_bucket_id", nullable = false)
  private DataBucketEntity lookupBucket;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "join_conditions", columnDefinition = "json")
  private List<JoinCondition> joinConditions = new ArrayList<>();

  public DataBucketEntity getRuleBucket() {
    return ruleBucket;
  }

  public void setRuleBucket(DataBucketEntity ruleBucket) {
    this.ruleBucket = ruleBucket;
  }

  public DataBucketEntity getLookupBucket() {
    return lookupBucket;
  }

  public void setLookupBucket(DataBucketEntity lookupBucket) {
    this.lookupBucket = lookupBucket;
  }

  public List<JoinCondition> getJoinConditions() {
    return joinConditions;
  }

  public void setJoinConditions(List<JoinCondition> joinConditions) {
    this.joinConditions = joinConditions;
  }

  @Getter
  @Setter
  public static class JoinCondition {
    private Long ruleBucketVariableId;
    private Long lookupBucketVariableId;

    public Long getRuleBucketVariableId() {
      return ruleBucketVariableId;
    }

    public void setRuleBucketVariableId(Long ruleBucketVariableId) {
      this.ruleBucketVariableId = ruleBucketVariableId;
    }

    public Long getLookupBucketVariableId() {
      return lookupBucketVariableId;
    }

    public void setLookupBucketVariableId(Long lookupBucketVariableId) {
      this.lookupBucketVariableId = lookupBucketVariableId;
    }
  }
}
