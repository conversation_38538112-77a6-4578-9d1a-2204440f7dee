package com.progize.tms.controllers.roles;

import com.progize.tms.controllers.roles.req.CreateRoleRequest;
import com.progize.tms.controllers.roles.req.UpdateRoleRequest;
import com.progize.tms.controllers.roles.res.RoleResponse;
import com.progize.tms.repository.entity.Role;
import com.progize.tms.services.RoleService;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/roles")
@CrossOrigin(origins = "*")
public class RoleController {

  private final RoleService roleService;
  private final RoleTransformer roleTransformer;

  @Autowired
  public RoleController(RoleService roleService, RoleTransformer roleTransformer) {
    this.roleService = roleService;
    this.roleTransformer = roleTransformer;
  }

  @GetMapping
  public ResponseEntity<List<RoleResponse>> getAllRoles() {
    List<Role> roles = roleService.findAll();
    return ResponseEntity.ok(roleTransformer.toRoleResponseList(roles));
  }

  @GetMapping("/{id}")
  public ResponseEntity<RoleResponse> getRoleById(@PathVariable Long id) {
    return roleService
        .findById(id)
        .map(roleTransformer::toRoleResponse)
        .map(ResponseEntity::ok)
        .orElse(ResponseEntity.notFound().build());
  }

  @PostMapping
  public ResponseEntity<RoleResponse> createRole(@Valid @RequestBody CreateRoleRequest request) {
    if (roleService.existsByName(request.getName())) {
      return ResponseEntity.badRequest().build();
    }

    Role role = roleTransformer.toEntity(request);
    Role savedRole = roleService.save(role);

    // If permission IDs were provided, assign them to the role
    if (!request.getPermissionIds().isEmpty()) {
      savedRole = roleService.setPermissions(savedRole.getId(), request.getPermissionIds());
    }

    return ResponseEntity.status(HttpStatus.CREATED)
        .body(roleTransformer.toRoleResponse(savedRole));
  }

  @PutMapping("/{id}")
  public ResponseEntity<RoleResponse> updateRole(
      @PathVariable Long id, @Valid @RequestBody UpdateRoleRequest request) {
    try {
      Role existingRole =
          roleService
              .findById(id)
              .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + id));

      // Check if the new name is already taken by another role
      if (!existingRole.getName().equals(request.getName())
          && roleService.existsByName(request.getName())) {
        return ResponseEntity.badRequest().build();
      }

      roleTransformer.updateEntity(existingRole, request);
      Role updatedRole = roleService.save(existingRole);

      return ResponseEntity.ok(roleTransformer.toRoleResponse(updatedRole));
    } catch (EntityNotFoundException e) {
      return ResponseEntity.notFound().build();
    }
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
    if (!roleService.existsById(id)) {
      return ResponseEntity.notFound().build();
    }

    roleService.deleteById(id);
    return ResponseEntity.noContent().build();
  }

  @PutMapping("/{id}/permissions")
  public ResponseEntity<RoleResponse> updateRolePermissions(
      @PathVariable Long id, @RequestBody Set<Long> permissionIds) {
    try {
      Role updatedRole = roleService.setPermissions(id, permissionIds);
      return ResponseEntity.ok(roleTransformer.toRoleResponse(updatedRole));
    } catch (EntityNotFoundException e) {
      return ResponseEntity.notFound().build();
    }
  }

  @PostMapping("/{id}/permissions")
  public ResponseEntity<RoleResponse> assignPermissions(
      @PathVariable Long id, @RequestBody Set<Long> permissionIds) {
    try {
      Role updatedRole = roleService.assignPermissions(id, permissionIds);
      return ResponseEntity.ok(roleTransformer.toRoleResponse(updatedRole));
    } catch (EntityNotFoundException e) {
      return ResponseEntity.notFound().build();
    }
  }

  @DeleteMapping("/{id}/permissions")
  public ResponseEntity<RoleResponse> removePermissions(
      @PathVariable Long id, @RequestBody Set<Long> permissionIds) {
    try {
      Role updatedRole = roleService.removePermissions(id, permissionIds);
      return ResponseEntity.ok(roleTransformer.toRoleResponse(updatedRole));
    } catch (EntityNotFoundException e) {
      return ResponseEntity.notFound().build();
    }
  }
}
