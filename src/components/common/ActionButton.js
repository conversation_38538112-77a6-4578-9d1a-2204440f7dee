import React from 'react';
import { Button, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';

// Standardized action button for consistent styling across the application
const StyledButton = styled(Button)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: '#fff',
  borderRadius: theme.shape.borderRadius,
  textTransform: 'none',
  padding: theme.spacing(0.5, 2),
  fontSize: '0.9rem',
  fontWeight: 500,
  boxShadow: `0 2px 4px ${alpha(theme.palette.primary.main, 0.2)}`,
  '&:hover': {
    backgroundColor: theme.palette.primary.dark,
    boxShadow: `0 3px 6px ${alpha(theme.palette.primary.main, 0.3)}`,
  },
  '&.Mui-disabled': {
    backgroundColor: alpha(theme.palette.primary.main, 0.5),
    color: '#fff',
  }
}));

const ActionButton = ({ children, ...props }) => {
  return (
    <StyledButton variant="contained" {...props}>
      {children}
    </StyledButton>
  );
};

export default ActionButton;
