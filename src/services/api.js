import axios from 'axios';
import AuthService from './AuthService';

// Update the API URL to properly point to the backend API
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8081';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Request interceptor for API calls
api.interceptors.request.use(
  (config) => {
    const user = AuthService.getCurrentUser();
    if (user && user.token) {
      config.headers['Authorization'] = `Bearer ${user.token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 and hasn't already been retried
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const user = await AuthService.refreshToken();

        // Update the authorization header
        originalRequest.headers['Authorization'] = `Bearer ${user.token}`;

        // Return the original request with the new token
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh token fails, redirect to login
        AuthService.logout();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export const ruleService = {
  // Rule Config API calls

  // Validate a rule without saving it
  validateRule: async (rule) => {
    try {
      // The rule object should already include dataBucketId
      const response = await api.post('/api/rules/validate', rule);
      return response.data;
    } catch (error) {
      console.error('Error validating rule:', error);
      return { valid: false, errors: ['Failed to validate rule. Server error.'] };
    }
  },

  // Create a new rule
  createRule: async (rule) => {
    try {
      const response = await api.post('/api/rules', rule);
      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && typeof response.data === 'object') {
        return response.data;
      } else {
        console.warn('Unexpected response format from create rule API:', response.data);
        return null;
      }
    } catch (error) {
      console.error('Error creating rule:', error);
      throw error;
    }
  },

  // Update an existing rule
  updateRule: async (id, rule) => {
    try {
      const response = await api.put(`/api/rules/${id}`, rule);
      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && typeof response.data === 'object') {
        return response.data;
      } else {
        console.warn(`Unexpected response format from update rule API for rule ${id}:`, response.data);
        return null;
      }
    } catch (error) {
      console.error(`Error updating rule ${id}:`, error);
      throw error;
    }
  },

  // Get a single rule by ID
  getRule: async (id) => {
    try {
      const response = await api.get(`/api/rules/${id}`);
      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && typeof response.data === 'object') {
        return response.data;
      } else {
        console.warn(`Unexpected response format from rule API for rule ${id}:`, response.data);
        return null;
      }
    } catch (error) {
      console.error(`Error fetching rule ${id}:`, error);
      throw error;
    }
  },

  // Get all rules
  getRules: async () => {
    try {
      const response = await api.get('/api/rules');
      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected response format from rules API:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching rules:', error);
      throw error;
    }
  },

  // Get active rules by bucket ID
  getActiveRulesByBucket: async (bucketId) => {
    try {
      const response = await api.get(`/api/rules/active/bucket/${bucketId}`);
      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn(`Unexpected response format from bucket rules API for bucket ${bucketId}:`, response.data);
        return [];
      }
    } catch (error) {
      console.error(`Error fetching active rules for bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Delete a rule
  deleteRule: async (id) => {
    try {
      await api.delete(`/api/rules/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting rule ${id}:`, error);
      throw error;
    }
  },

  // Get bucket-specific variables with enrichment for rule definition
  getBucketVariables: async (bucketId) => {
    try {
      // Use the enriched endpoint to get variables including those from related lookup buckets
      const response = await api.get(`/api/data-buckets/${bucketId}/variables/enriched`);
      console.log(`Fetched enriched variables for bucket ${bucketId}:`, response.data);

      // Process the response data to normalize property names
      let variables = [];

      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        variables = response.data.data;
      } else if (Array.isArray(response.data)) {
        variables = response.data;
      } else {
        console.warn('Unexpected response format from bucket variables API:', response.data);
        return [];
      }

      // Normalize property names to handle potential camelCase vs snake_case issues
      return variables.map(variable => {
        // Create a normalized copy of the variable
        const normalizedVariable = { ...variable };

        // Handle isEnriched property (could be isEnriched, is_enriched, or enriched)
        if (variable.isEnriched !== undefined) {
          normalizedVariable.isEnriched = variable.isEnriched;
        } else if (variable.is_enriched !== undefined) {
          normalizedVariable.isEnriched = variable.is_enriched;
        } else if (variable.enriched !== undefined) {
          normalizedVariable.isEnriched = variable.enriched;
        }

        // Handle sourceBucketName property
        if (variable.sourceBucketName !== undefined) {
          normalizedVariable.sourceBucketName = variable.sourceBucketName;
        } else if (variable.source_bucket_name !== undefined) {
          normalizedVariable.sourceBucketName = variable.source_bucket_name;
        }

        // Handle sourceBucketId property
        if (variable.sourceBucketId !== undefined) {
          normalizedVariable.sourceBucketId = variable.sourceBucketId;
        } else if (variable.source_bucket_id !== undefined) {
          normalizedVariable.sourceBucketId = variable.source_bucket_id;
        }

        return normalizedVariable;
      });
    } catch (error) {
      console.error(`Error fetching enriched variables for bucket ${bucketId}:`, error);
      // Fall back to regular variables if enriched endpoint fails
      try {
        console.log(`Falling back to regular variables endpoint for bucket ${bucketId}`);
        const fallbackResponse = await api.get(`/api/data-buckets/${bucketId}/variables`);
        return Array.isArray(fallbackResponse.data) ? fallbackResponse.data : [];
      } catch (fallbackError) {
        console.error(`Error fetching fallback variables for bucket ${bucketId}:`, fallbackError);
        return [];
      }
    }
  },

  // Get operators - still needed for all rule types
  getOperators: async () => {
    try {
      const response = await api.get('/api/rule-variables/operators');
      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected response format from operators API:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching operators:', error);
      throw error;
    }
  },

  // Get aggregate functions - still needed for all rule types
  getAggregateFunctions: async () => {
    try {
      const response = await api.get('/api/rule-variables/aggregate-functions');
      // Handle both wrapped and unwrapped response formats
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected response format from aggregate functions API:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching aggregate functions:', error);
      throw error;
    }
  },

  // Test a rule against sample data
  testRule: async (ruleExpression, sampleData) => {
    // In a real implementation, this would call the backend
    // For now, we'll simulate it client-side
    const response = await api.post('/api/rules/test', {
      ruleExpression,
      sampleData
    });
    return response.data;
  }
};

// Rule Variable Registry API calls
export const ruleVariableRegistryService = {
  // Get all tables from the database schema
  discoverTables: async () => {
    try {
      const response = await api.get('/api/rule-registry/schema/tables');
      return response.data;
    } catch (error) {
      console.error('Error discovering tables:', error);
      throw error;
    }
  },

  // Get all columns for a specific table
  getTableColumns: async (schema, table) => {
    try {
      const response = await api.get(`/api/rule-registry/schema/tables/${schema}/${table}/columns`);
      return response.data;
    } catch (error) {
      console.error(`Error getting columns for table ${schema}.${table}:`, error);
      throw error;
    }
  },

  // Register a table as a source for rule variables
  registerTable: async (schema, table, displayName, description) => {
    const response = await api.post(`/api/rule-registry/schema/tables/${schema}/${table}/register`, {
      displayName,
      description
    });
    return response.data;
  },

  // Register columns from a table as rule variables
  registerColumns: async (tableId, columns) => {
    const response = await api.post(`/api/rule-registry/schema/tables/${tableId}/columns/register`, {
      columns
    });
    return response.data;
  },

  // Get all registered rule variables
  getAllVariables: async () => {
    const response = await api.get('/api/rule-registry');
    return response.data;
  },

  // Get rule variables by source table
  getVariablesByTable: async (tableName) => {
    const response = await api.get(`/api/rule-registry/table/${tableName}`);
    return response.data;
  },

  // Get all registered source tables
  getAllSourceTables: async () => {
    const response = await api.get('/api/rule-registry/source-tables');
    return response.data;
  },

  // Update the aggregatable flag for a rule variable
  updateAggregatable: async (variableId, aggregatable) => {
    const response = await api.put(`/api/rule-registry/${variableId}/aggregatable`, {
      aggregatable
    });
    return response.data;
  },

  // Delete a rule variable
  deleteVariable: async (variableId) => {
    const response = await api.delete(`/api/rule-registry/${variableId}`);
    return response.data;
  }
};

// Data Bucket API calls
export const dataBucketService = {
  // Get all data buckets
  getAllBuckets: async () => {
    try {
      const response = await api.get('/api/data-buckets');
      console.log('Data buckets response:', response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data || [];
    } catch (error) {
      console.error('Error fetching data buckets:', error);
      return [];
    }
  },

  // Get bucket by ID
  getBucket: async (id) => {
    try {
      const response = await api.get(`/api/data-buckets/${id}`);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data;
    } catch (error) {
      console.error(`Error fetching data bucket ${id}:`, error);
      return null;
    }
  },

  // Get variables for a specific bucket
  getVariablesByBucketId: async (bucketId) => {
    try {
      const response = await api.get(`/api/data-buckets/${bucketId}/variables`);
      console.log(`API response for bucket ${bucketId} variables:`, response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data || [];
    } catch (error) {
      console.error(`Error fetching variables for bucket ${bucketId}:`, error);
      return [];
    }
  },

  // Add variables to a bucket
  addVariablesToBucket: async (bucketId, variables) => {
    try {
      // Ensure variables have the aggregatable property set
      const processedVariables = variables.map(variable => ({
        ...variable,
        // For rule buckets, set aggregatable to true for numerical variables
        aggregatable: variable.aggregatable || false
      }));

      console.log(`Adding variables to bucket ${bucketId}:`, processedVariables);
      const response = await api.post(`/api/data-buckets/${bucketId}/variables`, processedVariables);
      console.log(`API response for adding variables to bucket ${bucketId}:`, response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data || [];
    } catch (error) {
      console.error(`Error adding variables to bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Create a new bucket
  createBucket: async (bucket) => {
    try {
      console.log('Creating new bucket:', bucket);
      const response = await api.post('/api/data-buckets', bucket);
      console.log('API response for creating bucket:', response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data;
    } catch (error) {
      console.error('Error creating bucket:', error);
      throw error;
    }
  },

  // Create a new lookup bucket with variables
  createLookupBucket: async (bucket) => {
    try {
      // Process the bucket to ensure variables have the aggregatable property
      const processedBucket = {
        ...bucket,
        variables: bucket.variables.map(variable => ({
          ...variable,
          // For rule buckets, numerical variables should be aggregatable
          aggregatable: bucket.bucketType === 'RULE_BUCKET' &&
            ['INT', 'FLOAT', 'DECIMAL', 'DOUBLE', 'NUMBER'].includes(variable.dataTypeCode) ?
            true : (variable.aggregatable || false)
        }))
      };

      console.log('Creating new lookup bucket:', processedBucket);
      const response = await api.post('/api/data-buckets/lookup', processedBucket);
      console.log('API response for creating lookup bucket:', response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data;
    } catch (error) {
      console.error('Error creating lookup bucket:', error);
      throw error;
    }
  },

  // Delete a variable from a bucket
  deleteVariable: async (bucketId, variableId) => {
    try {
      console.log(`Deleting variable ${variableId} from bucket ${bucketId}`);
      const response = await api.delete(`/api/data-buckets/${bucketId}/variables/${variableId}`);
      console.log(`API response for deleting variable ${variableId}:`, response);
      return true;
    } catch (error) {
      console.error(`Error deleting variable ${variableId} from bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Update a bucket
  updateBucket: async (bucketId, bucketData) => {
    try {
      console.log(`Updating bucket ${bucketId}:`, bucketData);
      const response = await api.put(`/api/data-buckets/${bucketId}`, bucketData);
      console.log(`API response for updating bucket ${bucketId}:`, response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data;
    } catch (error) {
      console.error(`Error updating bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Update a variable
  updateVariable: async (bucketId, variableId, variableData) => {
    try {
      console.log(`Updating variable ${variableId} in bucket ${bucketId}:`, variableData);
      const response = await api.put(`/api/data-buckets/${bucketId}/variables/${variableId}`, variableData);
      console.log(`API response for updating variable ${variableId}:`, response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data;
    } catch (error) {
      console.error(`Error updating variable ${variableId} in bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Get buckets by data source
  getBucketsByDataSource: async (dataSourceId) => {
    try {
      console.log(`Fetching buckets for data source ${dataSourceId}`);
      const response = await api.get(`/api/data-buckets/data-source/${dataSourceId}`);
      console.log(`API response for buckets by data source ${dataSourceId}:`, response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data || [];
    } catch (error) {
      console.error(`Error fetching buckets for data source ${dataSourceId}:`, error);
      return [];
    }
  }
};

// User Management API calls
export const userService = {
  // Get all users
  getAllUsers: async () => {
    try {
      const response = await api.get('/api/users');
      console.log('Users response:', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  // Get user by ID
  getUser: async (id) => {
    try {
      const response = await api.get(`/api/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user ${id}:`, error);
      throw error;
    }
  },

  // Create new user
  createUser: async (user) => {
    try {
      const response = await api.post('/api/users', user);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  // Update user
  updateUser: async (id, user) => {
    try {
      const response = await api.put(`/api/users/${id}`, user);
      return response.data;
    } catch (error) {
      console.error(`Error updating user ${id}:`, error);
      throw error;
    }
  },

  // Delete user
  deleteUser: async (id) => {
    try {
      await api.delete(`/api/users/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting user ${id}:`, error);
      throw error;
    }
  },

  // Get user roles
  getUserRoles: async (id) => {
    try {
      const response = await api.get(`/api/users/${id}/roles`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching roles for user ${id}:`, error);
      throw error;
    }
  },

  // Assign role to user
  assignRoleToUser: async (userId, roleId) => {
    try {
      const response = await api.post(`/api/users/${userId}/roles/${roleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error assigning role ${roleId} to user ${userId}:`, error);
      throw error;
    }
  },

  // Remove role from user
  removeRoleFromUser: async (userId, roleId) => {
    try {
      const response = await api.delete(`/api/users/${userId}/roles/${roleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing role ${roleId} from user ${userId}:`, error);
      throw error;
    }
  },

  // Set user roles (replace all existing roles)
  setUserRoles: async (userId, roleIds) => {
    try {
      const response = await api.put(`/api/users/${userId}/roles`, roleIds);
      return response.data;
    } catch (error) {
      console.error(`Error setting roles for user ${userId}:`, error);
      throw error;
    }
  }
};

// Role Management API calls
export const roleService = {
  // Get all roles
  getAllRoles: async () => {
    try {
      const response = await api.get('/api/roles');
      console.log('Roles response:', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching roles:', error);
      throw error;
    }
  },

  // Get role by ID
  getRole: async (id) => {
    try {
      const response = await api.get(`/api/roles/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching role ${id}:`, error);
      throw error;
    }
  },

  // Create new role
  createRole: async (role) => {
    try {
      const response = await api.post('/api/roles', role);
      return response.data;
    } catch (error) {
      console.error('Error creating role:', error);
      throw error;
    }
  },

  // Update role
  updateRole: async (id, role) => {
    try {
      const response = await api.put(`/api/roles/${id}`, role);
      return response.data;
    } catch (error) {
      console.error(`Error updating role ${id}:`, error);
      throw error;
    }
  },

  // Delete role
  deleteRole: async (id) => {
    try {
      await api.delete(`/api/roles/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting role ${id}:`, error);
      throw error;
    }
  },

  // Get role permissions
  getRolePermissions: async (id) => {
    try {
      const response = await api.get(`/api/roles/${id}/permissions`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching permissions for role ${id}:`, error);
      throw error;
    }
  },

  // Set role permissions
  setRolePermissions: async (roleId, permissionIds) => {
    try {
      const response = await api.put(`/api/roles/${roleId}/permissions`, permissionIds);
      return response.data;
    } catch (error) {
      console.error(`Error setting permissions for role ${roleId}:`, error);
      throw error;
    }
  }
};

// Permission Management API calls
export const permissionService = {
  // Get all permissions
  getAllPermissions: async () => {
    try {
      const response = await api.get('/api/permissions');
      console.log('Permissions response:', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching permissions:', error);
      throw error;
    }
  },

  // Get permission by ID
  getPermission: async (id) => {
    try {
      const response = await api.get(`/api/permissions/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching permission ${id}:`, error);
      throw error;
    }
  },

  // Create new permission
  createPermission: async (permission) => {
    try {
      const response = await api.post('/api/permissions', permission);
      return response.data;
    } catch (error) {
      console.error('Error creating permission:', error);
      throw error;
    }
  },

  // Update permission
  updatePermission: async (id, permission) => {
    try {
      const response = await api.put(`/api/permissions/${id}`, permission);
      return response.data;
    } catch (error) {
      console.error(`Error updating permission ${id}:`, error);
      throw error;
    }
  },

  // Delete permission
  deletePermission: async (id) => {
    try {
      await api.delete(`/api/permissions/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting permission ${id}:`, error);
      throw error;
    }
  }
};

export const bucketRelationshipService = {
  // Get relationships for a rule bucket
  getRelationshipsForRuleBucket: async (ruleBucketId) => {
    try {
      console.log(`DEBUG - API - Fetching relationships for rule bucket ${ruleBucketId}`);
      if (!ruleBucketId) {
        console.error('DEBUG - API - ruleBucketId is undefined or null');
        return [];
      }

      const url = `/api/bucket-relationships/rule-bucket/${ruleBucketId}`;
      console.log(`DEBUG - API - Making GET request to: ${url}`);

      const response = await api.get(url);
      console.log(`DEBUG - API - Response status:`, response.status);
      console.log(`DEBUG - API - Response data:`, response.data);

      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        console.log(`DEBUG - API - Returning wrapped data:`, response.data.data);
        return response.data.data;
      }

      // Otherwise, return the direct response
      console.log(`DEBUG - API - Returning direct data:`, response.data || []);
      return response.data || [];
    } catch (error) {
      console.error(`DEBUG - API - Error fetching relationships for bucket ${ruleBucketId}:`, error);
      console.error(`DEBUG - API - Error details:`, error.response?.data || error.message);
      throw error;
    }
  },

  // Create a new relationship
  createRelationship: async (relationship) => {
    try {
      console.log('Creating new bucket relationship:', relationship);
      const response = await api.post('/api/bucket-relationships', relationship);
      console.log('API response for creating bucket relationship:', response);
      // Check if the response has a data property (wrapped in ApiResponse)
      if (response.data && response.data.data) {
        return response.data.data;
      }
      // Otherwise, return the direct response
      return response.data;
    } catch (error) {
      console.error('Error creating bucket relationship:', error);
      throw error;
    }
  },

  // Delete a relationship
  deleteRelationship: async (relationshipId) => {
    try {
      console.log(`Deleting bucket relationship ${relationshipId}`);
      await api.delete(`/api/bucket-relationships/${relationshipId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting bucket relationship ${relationshipId}:`, error);
      throw error;
    }
  }
};

export default api;
