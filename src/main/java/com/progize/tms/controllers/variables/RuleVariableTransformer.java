package com.progize.tms.controllers.variables;

import com.progize.tms.controllers.variables.res.AggregateFunctionResponse;
import com.progize.tms.controllers.variables.res.OperatorResponse;
import com.progize.tms.controllers.variables.res.RuleVariableResponse;
import com.progize.tms.services.models.AggregateFunction;
import com.progize.tms.services.models.Operator;
import com.progize.tms.services.models.RuleVariable;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * Transformer responsible for converting between service layer models and REST layer DTOs. This
 * follows the architecture pattern of separating domain models from REST DTOs.
 */
@Component
public class RuleVariableTransformer {

  /**
   * Convert a service layer RuleVariable to a REST layer RuleVariableResponse.
   *
   * @param variable The service layer variable
   * @return The REST layer response DTO
   */
  public RuleVariableResponse toResponse(RuleVariable variable) {
    if (variable == null) {
      return null;
    }

    return RuleVariableResponse.builder()
        .id(variable.getId())
        .name(variable.getName())
        .description(variable.getDescription())
        .type(variable.getType())
        .category(variable.getCategory())
        .aggregatable(variable.isAggregatable())
        .supportedOperators(variable.getSupportedOperators())
        .build();
  }

  /**
   * Convert a list of service layer RuleVariables to a list of REST layer RuleVariableResponses.
   *
   * @param variables The list of service layer variables
   * @return The list of REST layer response DTOs
   */
  public List<RuleVariableResponse> toResponseList(List<RuleVariable> variables) {
    return variables.stream().map(this::toResponse).collect(Collectors.toList());
  }

  /**
   * Convert a service layer Operator to a REST layer OperatorResponse.
   *
   * @param operator The service layer operator
   * @return The REST layer response DTO
   */
  public OperatorResponse toResponse(Operator operator) {
    if (operator == null) {
      return null;
    }

    return OperatorResponse.builder()
        .id(operator.getId())
        .name(operator.getName())
        .description(operator.getDescription())
        .symbol(operator.getSymbol())
        .applicableTypes(operator.getApplicableTypes())
        .build();
  }

  /**
   * Convert a list of service layer Operators to a list of REST layer OperatorResponses.
   *
   * @param operators The list of service layer operators
   * @return The list of REST layer response DTOs
   */
  public List<OperatorResponse> toOperatorResponseList(List<Operator> operators) {
    return operators.stream().map(this::toResponse).collect(Collectors.toList());
  }

  /**
   * Convert a service layer AggregateFunction to a REST layer AggregateFunctionResponse.
   *
   * @param function The service layer aggregate function
   * @return The REST layer response DTO
   */
  public AggregateFunctionResponse toResponse(AggregateFunction function) {
    if (function == null) {
      return null;
    }

    return AggregateFunctionResponse.builder()
        .id(function.getId())
        .name(function.getName())
        .description(function.getDescription())
        .applicableTypes(function.getApplicableTypes())
        .build();
  }

  /**
   * Convert a list of service layer AggregateFunctions to a list of REST layer
   * AggregateFunctionResponses.
   *
   * @param functions The list of service layer aggregate functions
   * @return The list of REST layer response DTOs
   */
  public List<AggregateFunctionResponse> toAggregateFunctionResponseList(
      List<AggregateFunction> functions) {
    return functions.stream().map(this::toResponse).collect(Collectors.toList());
  }
}
