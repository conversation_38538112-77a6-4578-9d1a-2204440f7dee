package com.progize.tms.repository.entity;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

import com.progize.tms.repository.entity.enums.BucketType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "data_bucket")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Audited(targetAuditMode = NOT_AUDITED)
public class DataBucketEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "name", nullable = false, unique = true, length = 255)
  private String name;

  @Column(name = "description")
  private String description;

  @Column(name = "table_name")
  private String tableName;

  @Enumerated(EnumType.ORDINAL)
  @Column(name = "bucket_type", nullable = false)
  private BucketType bucketType;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_source_id")
  private DataSourceEntity dataSource;

  @CreatedDate
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @OneToMany(mappedBy = "dataBucket", cascade = CascadeType.ALL, orphanRemoval = true)
  @NotAudited
  private List<DataBucketVariableEntity> variables;
}
