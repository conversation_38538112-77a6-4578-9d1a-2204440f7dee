import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Checkbox,
    Switch,
    Chip,
    Alert,
    CircularProgress,
    FormControlLabel,
    Grid,
    Card,
    CardContent,
    Divider
} from '@mui/material';
import {
    Key as KeyIcon,
    Timeline as TimelineIcon,
    Analytics as AnalyticsIcon,
    Security as SecurityIcon
} from '@mui/icons-material';
import dataSourceService from '../../services/dataSourceService';

const ProperVariableSelector = ({ 
    dataSourceId, 
    tableName, 
    selectedVariables, 
    onVariablesChange 
}) => {
    const [columns, setColumns] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (dataSourceId && tableName) {
            loadColumns();
        }
    }, [dataSourceId, tableName]);

    const loadColumns = async () => {
        try {
            setLoading(true);
            setError(null);
            const columnsData = await dataSourceService.getColumnsByTable(dataSourceId, tableName);
            setColumns(Array.isArray(columnsData) ? columnsData : []);
        } catch (err) {
            console.error('Error loading columns:', err);
            setError('Failed to load table columns');
            setColumns([]);
        } finally {
            setLoading(false);
        }
    };

    const handleVariableToggle = (column) => {
        const isSelected = selectedVariables.some(v => v.name === column.name);
        
        if (isSelected) {
            // Remove variable
            const updated = selectedVariables.filter(v => v.name !== column.name);
            onVariablesChange(updated);
        } else {
            // Add variable with default configuration
            const newVariable = {
                name: column.name,
                code: `${tableName.toUpperCase()}_${column.name.toUpperCase()}`,
                description: column.description || '',
                dataTypeCode: column.dataType || 'string',
                isUnique: false,
                aggregatable: false,
                isPrimaryEntityKey: false,
                isSecondaryEntityKey: false,
                isDateDimension: false
            };
            onVariablesChange([...selectedVariables, newVariable]);
        }
    };

    const handlePropertyChange = (variableName, property, value) => {
        const updated = selectedVariables.map(variable => {
            if (variable.name === variableName) {
                const updatedVariable = { ...variable, [property]: value };
                
                // Handle exclusive properties
                if (property === 'isPrimaryEntityKey' && value) {
                    // Unset primary key for all other variables
                    const resetOthers = selectedVariables.map(v => 
                        v.name !== variableName ? { 
                            ...v, 
                            isPrimaryEntityKey: false,
                            isSecondaryEntityKey: false 
                        } : v
                    );
                    updatedVariable.isSecondaryEntityKey = false;
                    updatedVariable.isDateDimension = false;
                    onVariablesChange(resetOthers.map(v => 
                        v.name === variableName ? updatedVariable : v
                    ));
                    return updatedVariable;
                } else if (property === 'isSecondaryEntityKey' && value) {
                    // Unset secondary key for all other variables
                    const resetOthers = selectedVariables.map(v => 
                        v.name !== variableName ? { 
                            ...v, 
                            isSecondaryEntityKey: false,
                            isPrimaryEntityKey: false 
                        } : v
                    );
                    updatedVariable.isPrimaryEntityKey = false;
                    updatedVariable.isDateDimension = false;
                    onVariablesChange(resetOthers.map(v => 
                        v.name === variableName ? updatedVariable : v
                    ));
                    return updatedVariable;
                } else if (property === 'isDateDimension' && value) {
                    // Unset date dimension for all other variables
                    const resetOthers = selectedVariables.map(v => 
                        v.name !== variableName ? { ...v, isDateDimension: false } : v
                    );
                    onVariablesChange(resetOthers.map(v => 
                        v.name === variableName ? updatedVariable : v
                    ));
                    return updatedVariable;
                }
                
                return updatedVariable;
            }
            return variable;
        });
        
        onVariablesChange(updated);
    };

    const getEntityKeyType = (variable) => {
        if (variable.isPrimaryEntityKey) return { label: 'Primary Key', color: 'primary', icon: <KeyIcon /> };
        if (variable.isSecondaryEntityKey) return { label: 'Secondary Key', color: 'secondary', icon: <KeyIcon /> };
        if (variable.isDateDimension) return { label: 'Date Dimension', color: 'success', icon: <TimelineIcon /> };
        return null;
    };

    const getSelectedVariable = (columnName) => {
        return selectedVariables.find(v => v.name === columnName);
    };

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                <CircularProgress />
                <Typography sx={{ ml: 2 }}>Loading table columns...</Typography>
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mb: 3 }}>
                {error}
            </Alert>
        );
    }

    if (columns.length === 0) {
        return (
            <Alert severity="info" sx={{ mb: 3 }}>
                No columns found in the selected table. Please check your table selection.
            </Alert>
        );
    }

    return (
        <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>
                Configure Variables from {tableName}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Select columns to include as variables and configure their properties. 
                Entity keys are used for workflow case management and data relationships.
            </Typography>

            <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                    <strong>Entity Key Rules:</strong> Only one Primary Key, one Secondary Key, and one Date Dimension allowed per bucket.
                    These are used for workflow case management and data aggregation.
                </Typography>
            </Alert>

            {/* Summary Cards */}
            <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="primary">
                                {selectedVariables.length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Selected Variables
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="primary">
                                {selectedVariables.filter(v => v.isPrimaryEntityKey).length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Primary Key
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="secondary">
                                {selectedVariables.filter(v => v.isSecondaryEntityKey).length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Secondary Key
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="success.main">
                                {selectedVariables.filter(v => v.isDateDimension).length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Date Dimension
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow sx={{ bgcolor: 'grey.50' }}>
                            <TableCell padding="checkbox">
                                <Typography variant="caption" fontWeight="bold">Select</Typography>
                            </TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }}>Column</TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }}>Data Type</TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }} align="center">Unique</TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }} align="center">Aggregatable</TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }}>Entity Key</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {columns.map((column) => {
                            const isSelected = selectedVariables.some(v => v.name === column.name);
                            const selectedVar = getSelectedVariable(column.name);
                            
                            return (
                                <TableRow key={column.name} hover>
                                    <TableCell padding="checkbox">
                                        <Checkbox
                                            checked={isSelected}
                                            onChange={() => handleVariableToggle(column)}
                                        />
                                    </TableCell>
                                    
                                    <TableCell>
                                        <Box>
                                            <Typography variant="subtitle2" fontWeight="medium">
                                                {column.name}
                                            </Typography>
                                            {column.description && (
                                                <Typography variant="caption" color="text.secondary">
                                                    {column.description}
                                                </Typography>
                                            )}
                                        </Box>
                                    </TableCell>
                                    
                                    <TableCell>
                                        <Chip
                                            label={column.dataType || 'string'}
                                            size="small"
                                            variant="outlined"
                                        />
                                    </TableCell>
                                    
                                    <TableCell align="center">
                                        <Switch
                                            checked={selectedVar?.isUnique || false}
                                            onChange={(e) => handlePropertyChange(column.name, 'isUnique', e.target.checked)}
                                            disabled={!isSelected}
                                            size="small"
                                        />
                                    </TableCell>
                                    
                                    <TableCell align="center">
                                        <Switch
                                            checked={selectedVar?.aggregatable || false}
                                            onChange={(e) => handlePropertyChange(column.name, 'aggregatable', e.target.checked)}
                                            disabled={!isSelected}
                                            size="small"
                                            color="info"
                                        />
                                    </TableCell>
                                    
                                    <TableCell>
                                        {isSelected ? (
                                            <Box sx={{ display: 'flex', gap: 1 }}>
                                                <FormControlLabel
                                                    control={
                                                        <Switch
                                                            checked={selectedVar?.isPrimaryEntityKey || false}
                                                            onChange={(e) => handlePropertyChange(column.name, 'isPrimaryEntityKey', e.target.checked)}
                                                            size="small"
                                                            color="primary"
                                                        />
                                                    }
                                                    label={<Typography variant="caption">Primary</Typography>}
                                                />
                                                <FormControlLabel
                                                    control={
                                                        <Switch
                                                            checked={selectedVar?.isSecondaryEntityKey || false}
                                                            onChange={(e) => handlePropertyChange(column.name, 'isSecondaryEntityKey', e.target.checked)}
                                                            size="small"
                                                            color="secondary"
                                                        />
                                                    }
                                                    label={<Typography variant="caption">Secondary</Typography>}
                                                />
                                                <FormControlLabel
                                                    control={
                                                        <Switch
                                                            checked={selectedVar?.isDateDimension || false}
                                                            onChange={(e) => handlePropertyChange(column.name, 'isDateDimension', e.target.checked)}
                                                            size="small"
                                                            color="success"
                                                        />
                                                    }
                                                    label={<Typography variant="caption">Date</Typography>}
                                                />
                                            </Box>
                                        ) : (
                                            <Typography variant="caption" color="text.secondary">
                                                Select variable first
                                            </Typography>
                                        )}
                                    </TableCell>
                                </TableRow>
                            );
                        })}
                    </TableBody>
                </Table>
            </TableContainer>

            {selectedVariables.length > 0 && (
                <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                        Selected Variables Summary
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {selectedVariables.map((variable, index) => {
                            const entityKey = getEntityKeyType(variable);
                            return (
                                <Chip
                                    key={index}
                                    label={variable.name}
                                    color={entityKey?.color || 'default'}
                                    icon={entityKey?.icon}
                                    variant={entityKey ? 'filled' : 'outlined'}
                                />
                            );
                        })}
                    </Box>
                </Box>
            )}
        </Box>
    );
};

export default ProperVariableSelector;
