package com.progize.tms.repository.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * Entity representing a source table for rule variables. Each rule variable is linked to a column
 * in a source table.
 */
@Entity
@Table(name = "rule_variable_source_tables")
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class RuleVariableSourceTable {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "table_name", nullable = false)
  private String tableName;

  @Column(name = "display_name", nullable = false, length = 100)
  private String displayName;

  @Column(name = "schema_name")
  private String schemaName = "default";

  @Column(length = 500)
  private String description;

  @Column(name = "connection_name")
  private String connectionName = "clickhouse";

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "json")
  private Map<String, Object> metadata;

  @CreatedDate
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @LastModifiedDate
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;
}
