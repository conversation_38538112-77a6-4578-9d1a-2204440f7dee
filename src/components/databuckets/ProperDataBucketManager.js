import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Box,
    Typography,
    Button,
    Alert,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Tooltip,
    alpha,
    useTheme
} from '@mui/material';
import {
    Add as AddIcon,
    FilterList as FilterListIcon,
    Storage as StorageIcon
} from '@mui/icons-material';
import { dataBucketService, dataSourceService } from '../../services/api';
import DataBucketsPanel from '../rulevariables/DataBucketsPanel';
import ProperBucketCreator from './ProperBucketCreator';
import ProperBucketEditor from './ProperBucketEditor';
import ProperVariableManager from './ProperVariableManager';

const ProperDataBucketManager = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const [buckets, setBuckets] = useState([]);
    const [dataSources, setDataSources] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedDataSource, setSelectedDataSource] = useState('');
    const [loadingDataSources, setLoadingDataSources] = useState(true);
    const [dataSourceError, setDataSourceError] = useState(null);
    
    // View states
    const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'variables'
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [createDrawerOpen, setCreateDrawerOpen] = useState(false);

    useEffect(() => {
        fetchBuckets();
        fetchDataSources();
    }, []);

    useEffect(() => {
        if (selectedDataSource) {
            fetchBucketsByDataSource(selectedDataSource);
        } else {
            fetchBuckets();
        }
    }, [selectedDataSource]);

    const fetchBuckets = async () => {
        try {
            setLoading(true);
            const data = await dataBucketService.getAllBuckets();
            setBuckets(Array.isArray(data) ? data : []);
            setError(null);
        } catch (err) {
            setError('Failed to load data buckets. Please try again later.');
            console.error('Error fetching buckets:', err);
            setBuckets([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchBucketsByDataSource = async (dataSourceId) => {
        try {
            setLoading(true);
            const data = await dataBucketService.getBucketsByDataSource(dataSourceId);
            setBuckets(Array.isArray(data) ? data : []);
            setError(null);
        } catch (err) {
            setError(`Failed to load buckets for selected data source. Please try again later.`);
            console.error('Error fetching buckets by data source:', err);
            setBuckets([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchDataSources = async () => {
        try {
            setLoadingDataSources(true);
            const response = await dataSourceService.getAllDataSources();
            const dataSourcesArray = Array.isArray(response) ? response : [];
            setDataSources(dataSourcesArray);
            
            if (dataSourcesArray.length === 0) {
                setDataSourceError('No data sources available. Please configure data sources first.');
            } else {
                setDataSourceError(null);
            }
        } catch (err) {
            console.error('Error fetching data sources:', err);
            setDataSourceError('Failed to load data sources. Please try again later.');
            setDataSources([]);
        } finally {
            setLoadingDataSources(false);
        }
    };

    const handleDataSourceChange = (event) => {
        setSelectedDataSource(event.target.value);
    };

    const handleCreateBucket = () => {
        setCurrentView('create');
    };

    const handleEditBucket = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('edit');
    };

    const handleManageVariables = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('variables');
    };

    const handleBackToList = () => {
        setCurrentView('list');
        setSelectedBucket(null);
        fetchBuckets(); // Refresh the list
    };

    const handleBucketCreated = (newBucket) => {
        setBuckets(prev => [...prev, newBucket]);
        setCurrentView('list');
    };

    const handleBucketUpdated = (updatedBucket) => {
        setBuckets(prev => prev.map(bucket => 
            bucket.id === updatedBucket.id ? updatedBucket : bucket
        ));
        setCurrentView('list');
    };

    const renderHeader = () => (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                mb: 3,
                pb: 2,
                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
            }}
        >
            <Box>
                <Typography
                    variant="h4"
                    component="h1"
                    sx={{
                        fontWeight: 600,
                        color: theme.palette.primary.main,
                        mb: 1
                    }}
                >
                    Data Buckets
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                        Manage data buckets and configure variables for transaction monitoring rules.
                    </Typography>
                    <Box
                        sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            height: '24px',
                            px: 1.5,
                            py: 0.5,
                            ml: 2,
                            borderRadius: '16px',
                            bgcolor: '#0288D1',
                            color: '#FFFFFF',
                            fontSize: '13px',
                            fontWeight: 600,
                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                        }}
                    >
                        {buckets.length} buckets
                    </Box>
                </Box>
            </Box>
            {currentView === 'list' && (
                <Tooltip title={!selectedDataSource ? "Select a data source to create buckets efficiently" : ""}>
                    <span>
                        <Button
                            variant="contained"
                            color="primary"
                            startIcon={<AddIcon />}
                            onClick={handleCreateBucket}
                            sx={{
                                borderRadius: '28px',
                                px: 3,
                                py: 1,
                                boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                                '&:hover': {
                                    transform: 'translateY(-1px)',
                                    boxShadow: '0 6px 20px rgba(0,118,255,0.39)',
                                }
                            }}
                        >
                            Create New Bucket
                        </Button>
                    </span>
                </Tooltip>
            )}
        </Box>
    );

    const renderDataSourceFilter = () => (
        currentView === 'list' && (
            <Box sx={{ mb: 3 }}>
                <Box sx={{ width: { xs: '100%', sm: 300 } }}>
                    {loadingDataSources ? (
                        <Box sx={{
                            height: 40,
                            width: '100%',
                            bgcolor: 'background.paper',
                            borderRadius: 1,
                            animation: 'pulse 1.5s ease-in-out infinite',
                            '@keyframes pulse': {
                                '0%, 100%': { opacity: 0.6 },
                                '50%': { opacity: 0.3 }
                            }
                        }} />
                    ) : (
                        <FormControl fullWidth size="small" variant="outlined">
                            <InputLabel id="datasource-select-label">
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <FilterListIcon fontSize="small" sx={{ mr: 0.5 }} />
                                    Filter by Data Source
                                </Box>
                            </InputLabel>
                            <Select
                                labelId="datasource-select-label"
                                value={selectedDataSource}
                                onChange={handleDataSourceChange}
                                disabled={loadingDataSources}
                                label={
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <FilterListIcon fontSize="small" sx={{ mr: 0.5 }} />
                                        Filter by Data Source
                                    </Box>
                                }
                            >
                                <MenuItem value="">
                                    <em>All Data Sources</em>
                                </MenuItem>
                                {dataSources.map(dataSource => (
                                    <MenuItem key={dataSource.id} value={dataSource.id}>
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                            <StorageIcon fontSize="small" sx={{ mr: 1 }} />
                                            {dataSource.name}
                                        </Box>
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    )}

                    {dataSourceError && (
                        <Alert severity="error" sx={{ mt: 1 }}>
                            {dataSourceError}
                        </Alert>
                    )}
                </Box>
            </Box>
        )
    );

    const renderContent = () => {
        switch (currentView) {
            case 'create':
                return (
                    <ProperBucketCreator
                        dataSources={dataSources}
                        onCancel={handleBackToList}
                        onBucketCreated={handleBucketCreated}
                    />
                );
            
            case 'edit':
                return (
                    <ProperBucketEditor
                        bucket={selectedBucket}
                        dataSources={dataSources}
                        onCancel={handleBackToList}
                        onBucketUpdated={handleBucketUpdated}
                    />
                );
            
            case 'variables':
                return (
                    <ProperVariableManager
                        bucket={selectedBucket}
                        onBack={handleBackToList}
                    />
                );
            
            default:
                return (
                    <DataBucketsPanel
                        initialBuckets={buckets}
                        createDrawerOpen={createDrawerOpen}
                        setCreateDrawerOpen={setCreateDrawerOpen}
                        onEditBucket={handleEditBucket}
                        onManageVariables={handleManageVariables}
                    />
                );
        }
    };

    return (
        <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
            {renderHeader()}
            {renderDataSourceFilter()}
            
            {/* Error Message */}
            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {renderContent()}
        </Box>
    );
};

export default ProperDataBucketManager;
