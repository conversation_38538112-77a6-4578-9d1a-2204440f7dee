import api from './api';

// Data Bucket Service
export const dataBucketService = {
  // Get all buckets
  getAllBuckets: async () => {
    try {
      console.log('Fetching all buckets');
      const response = await api.get('/api/data-buckets');
      console.log('Buckets response:', response);
      return response.data;
    } catch (error) {
      console.error('Error fetching all buckets:', error);
      throw error;
    }
  },

  // Create a new bucket
  createBucket: async (bucket) => {
    try {
      // Ensure bucket has the required fields
      const bucketData = {
        name: bucket.name,
        description: bucket.description || '',
        bucketType: bucket.bucketType || 'SYSTEM',
        isUnique: bucket.isUnique || false
      };

      console.log('Creating new bucket:', bucketData);
      const response = await api.post('/api/data-buckets', bucketData);
      console.log('API response for creating bucket:', response);
      return response.data;
    } catch (error) {
      console.error('Error creating bucket:', error);
      throw error;
    }
  },

  // Create a new lookup bucket with variables
  createLookupBucket: async (bucket) => {
    try {
      // Ensure bucket has the required fields
      const bucketData = {
        name: bucket.name,
        description: bucket.description || '',
        bucketType: bucket.bucketType || 'LOOKUP',
        tableName: bucket.tableName,
        dataSourceId: bucket.dataSourceId,
        isUnique: bucket.isUnique || false,
        variables: bucket.variables.map(variable => ({
          name: variable.name,
          code: variable.code,
          description: variable.description || '',
          dataTypeCode: variable.dataTypeCode,
          isUnique: variable.isUnique || false,
          aggregatable: variable.aggregatable || false,
          isPrimaryEntityKey: variable.isPrimaryEntityKey || false,
          isSecondaryEntityKey: variable.isSecondaryEntityKey || false
        }))
      };

      console.log('Creating new lookup bucket:', bucketData);
      const response = await api.post('/api/data-buckets/lookup', bucketData);
      console.log('API response for creating lookup bucket:', response);
      return response.data;
    } catch (error) {
      console.error('Error creating lookup bucket:', error);
      throw error;
    }
  },

  // Get variables for a bucket
  getBucketVars: async (bucketId) => {
    try {
      console.log(`API call: Getting variables for bucket ${bucketId}`);
      const response = await api.get(`/api/data-buckets/${bucketId}/variables`);
      console.log(`API response for bucket ${bucketId}:`, response);

      // Ensure we're returning an array
      let variables = response.data;

      // Handle different response formats
      if (!Array.isArray(variables)) {
        console.warn(`API response for bucket ${bucketId} is not an array:`, variables);

        // If response.data is an object with a variables property, use that
        if (variables && Array.isArray(variables.variables)) {
          variables = variables.variables;
        } else if (variables && Array.isArray(variables.data)) {
          // Some APIs return data in a 'data' property
          variables = variables.data;
        } else if (variables && typeof variables === 'object') {
          // If it's an object but not in a standard format, try to extract values
          const possibleArrays = Object.values(variables).filter(val => Array.isArray(val));
          if (possibleArrays.length > 0) {
            // Use the first array found in the object
            variables = possibleArrays[0];
          } else {
            // If all else fails, return an empty array
            variables = [];
          }
        } else {
          // Otherwise, return an empty array
          variables = [];
        }
      }

      console.log(`Returning variables for bucket ${bucketId}:`, variables);
      return variables;
    } catch (error) {
      console.error(`Error fetching variables for bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Add variables to a bucket
  addVariablesToBucket: async (bucketId, variables) => {
    try {
      // Ensure variables have the required fields
      const variablesData = variables.map(variable => ({
        name: variable.name,
        code: variable.code,
        description: variable.description || '',
        dataTypeCode: variable.dataTypeCode,
        isUnique: variable.isUnique || false,
        aggregatable: variable.aggregatable || false,
        isPrimaryEntityKey: variable.isPrimaryEntityKey || false,
        isSecondaryEntityKey: variable.isSecondaryEntityKey || false
      }));

      console.log(`Adding variables to bucket ${bucketId}:`, variablesData);
      const response = await api.post(`/api/data-buckets/${bucketId}/variables`, variablesData);
      console.log(`API response for adding variables to bucket ${bucketId}:`, response);
      return response.data;
    } catch (error) {
      console.error(`Error adding variables to bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Delete a variable from a bucket
  deleteVariable: async (bucketId, variableId) => {
    try {
      const response = await api.delete(`/api/data-buckets/${bucketId}/variables/${variableId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting variable ${variableId} from bucket ${bucketId}:`, error);
      throw error;
    }
  },

  // Get registered tables for variable selection
  getRegisteredTables: async () => {
    try {
      console.log('Fetching registered tables from API');
      const response = await api.get('/api/rule-registry/schema/tables');
      console.log('API response for tables:', response);
      return response.data;
    } catch (error) {
      console.error('Error fetching registered tables:', error);
      return [];
    }
  },

  // Get columns for a specific table
  getTableColumns: async (schemaName, tableName) => {
    try {
      console.log(`Fetching columns for table ${schemaName}.${tableName}`);
      const response = await api.get(`/api/rule-registry/schema/tables/${schemaName}/${tableName}/columns`);
      console.log(`API response for columns of ${schemaName}.${tableName}:`, response);
      return response.data;
    } catch (error) {
      console.error(`Error fetching columns for table ${schemaName}.${tableName}:`, error);
      return [];
    }
  },

  // Get a bucket by its ID
  getBucket: async (bucketId) => {
    const response = await api.get(`/api/data-buckets/${bucketId}`);
    return response.data;
  },

  // Delete a bucket
  deleteBucket: async (bucketId) => {
    await api.delete(`/api/data-buckets/${bucketId}`);
  },

  // Register variables to a bucket
  registerVariables: async (bucketId, variables) => {
    const response = await api.post(`/api/data-buckets/${bucketId}/variables`, variables);
    return response.data;
  }
};
