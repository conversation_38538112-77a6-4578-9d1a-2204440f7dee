package com.progize.tms.services;

import com.progize.tms.services.models.AggregationRequirement;
import com.progize.tms.services.models.RuleVariable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/** Service for executing ClickHouse queries for rule evaluations. */
@Service
@Slf4j
@RequiredArgsConstructor
public class ClickHouseQueryService {

  @Qualifier("clickHouseJdbcTemplate")
  private final JdbcTemplate clickHouseJdbcTemplate;

  private final RuleVariableService ruleVariableService;
  private final ConsolidatedClickHouseQueryService consolidatedQueryService;

  /**
   * Execute all required aggregations for a customer.
   *
   * @param customerId The customer ID to calculate aggregations for
   * @param requirements Set of aggregation requirements to calculate
   * @return Map of aggregation keys to their calculated values
   */
  public Map<String, Object> executeAggregationsForCustomer(
      String customerId, Set<AggregationRequirement> requirements) {
    log.info("Executing aggregations for customer: {}", customerId);

    // Return empty map if no requirements
    if (requirements == null || requirements.isEmpty()) {
      log.info("No aggregation requirements found.");
      return new HashMap<>();
    }

    log.info("Found {} aggregation requirements", requirements.size());

    // Delegate to the consolidated query service for optimized execution
    return consolidatedQueryService.executeAggregationsForCustomer(customerId, requirements);
  }

  /**
   * Execute all required aggregations for a customer with bucket context.
   *
   * @param customerId The customer ID to calculate aggregations for
   * @param requirements Set of aggregation requirements to calculate
   * @param bucketId The ID of the data bucket to use for variable context, or null to use global
   *     variables
   * @return Map of aggregation keys to their calculated values
   */
  public Map<String, Object> executeAggregationsForCustomer(
      String customerId, Set<AggregationRequirement> requirements, Long bucketId) {
    log.info("Executing aggregations for customer: {} with bucketId: {}", customerId, bucketId);

    // Return empty map if no requirements
    if (requirements == null || requirements.isEmpty()) {
      log.info("No aggregation requirements found.");
      return new HashMap<>();
    }

    log.info("Found {} aggregation requirements", requirements.size());

    // Delegate to the consolidated query service for optimized execution with bucket context
    return consolidatedQueryService.executeAggregationsForCustomer(
        customerId, requirements, bucketId);
  }

  /**
   * Gets the database column name for a field. First checks if it's defined in RuleVariable, then
   * falls back to converting from camelCase to snake_case.
   *
   * @param fieldId The field ID from the rule
   * @return The corresponding database column name
   */
  public String getColumnNameForField(String fieldId) {
    // Check if we have an explicit mapping for this field
    RuleVariable variable = ruleVariableService.getVariable(fieldId);
    if (variable != null
        && variable.getColumnName() != null
        && !variable.getColumnName().isEmpty()) {
      return variable.getColumnName();
    }

    // Otherwise, convert from camelCase to snake_case
    return camelToSnakeCase(fieldId);
  }

  /**
   * Converts a camelCase string to snake_case.
   *
   * @param input The camelCase input string
   * @return The snake_case output string
   */
  private String camelToSnakeCase(String input) {
    // Use a regex to find camelCase boundaries and insert underscores
    Pattern pattern = Pattern.compile("([a-z])([A-Z])");
    return pattern.matcher(input).replaceAll("$1_$2").toLowerCase();
  }
}
