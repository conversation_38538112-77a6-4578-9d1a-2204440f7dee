import React from 'react';
import { Box, Typography, Grid, Paper, useTheme } from '@mui/material';

const Dashboard = () => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 4, fontWeight: 600 }}>
        Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Paper 
            elevation={1} 
            sx={{ 
              p: 3, 
              borderRadius: 2,
              height: '100%',
              background: isDarkMode ? 'rgba(255,255,255,0.03)' : 'white',
              border: isDarkMode ? '1px solid rgba(255,255,255,0.05)' : 'none'
            }}
          >
            <Typography variant="h6" sx={{ mb: 1 }}>
              Active Rules
            </Typography>
            <Typography variant="h3" sx={{ color: 'primary.main', fontWeight: 700 }}>
              24
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Paper 
            elevation={1} 
            sx={{ 
              p: 3, 
              borderRadius: 2,
              height: '100%',
              background: isDarkMode ? 'rgba(255,255,255,0.03)' : 'white',
              border: isDarkMode ? '1px solid rgba(255,255,255,0.05)' : 'none'
            }}
          >
            <Typography variant="h6" sx={{ mb: 1 }}>
              Transactions Today
            </Typography>
            <Typography variant="h3" sx={{ color: 'secondary.main', fontWeight: 700 }}>
              1,248
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Paper 
            elevation={1} 
            sx={{ 
              p: 3, 
              borderRadius: 2,
              height: '100%',
              background: isDarkMode ? 'rgba(255,255,255,0.03)' : 'white',
              border: isDarkMode ? '1px solid rgba(255,255,255,0.05)' : 'none'
            }}
          >
            <Typography variant="h6" sx={{ mb: 1 }}>
              Rules Triggered
            </Typography>
            <Typography variant="h3" sx={{ color: 'info.main', fontWeight: 700 }}>
              347
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Paper 
            elevation={1} 
            sx={{ 
              p: 3, 
              borderRadius: 2,
              height: '100%',
              background: isDarkMode ? 'rgba(255,255,255,0.03)' : 'white',
              border: isDarkMode ? '1px solid rgba(255,255,255,0.05)' : 'none'
            }}
          >
            <Typography variant="h6" sx={{ mb: 1 }}>
              Alerts Generated
            </Typography>
            <Typography variant="h3" sx={{ color: 'warning.main', fontWeight: 700 }}>
              18
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <Paper 
            elevation={1} 
            sx={{ 
              p: 3, 
              mt: 2, 
              borderRadius: 2,
              background: isDarkMode ? 'rgba(255,255,255,0.03)' : 'white',
              border: isDarkMode ? '1px solid rgba(255,255,255,0.05)' : 'none'
            }}
          >
            <Typography variant="h6" gutterBottom>
              Coming Soon
            </Typography>
            <Typography variant="body1" color="text.secondary">
              The dashboard functionality is under development. This is a placeholder for future analytics and visualization components.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
