package com.progize.tms.controllers.users;

import com.progize.tms.exceptions.ResourceNotFoundException;
import com.progize.tms.repository.RoleRepository;
import com.progize.tms.repository.UserRepository;
import com.progize.tms.repository.entity.Role;
import com.progize.tms.repository.entity.User;
import jakarta.persistence.EntityNotFoundException;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UserService {

  private final UserRepository userRepository;
  private final RoleRepository roleRepository;
  private final PasswordEncoder passwordEncoder;

  /** Get all users */
  public List<User> getAllUsers() {
    return userRepository.findAll();
  }

  /** Get user by ID */
  public User getUserById(Long id) {
    return userRepository
        .findById(id)
        .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
  }

  /** Create a new user */
  @Transactional
  public User createUser(User user) {
    // Encode the password before saving
    user.setPassword(passwordEncoder.encode(user.getPassword()));

    // Set default values if not provided
    if (user.getRoles() == null) {
      user.setRoles(new HashSet<>());
    }

    return userRepository.save(user);
  }

  /** Update user */
  @Transactional
  public User updateUser(Long id, User userDetails) {
    User user = getUserById(id);

    // Update user fields
    user.setEmail(userDetails.getEmail());
    user.setFirstName(userDetails.getFirstName());
    user.setLastName(userDetails.getLastName());

    // Only update password if provided
    if (userDetails.getPassword() != null && !userDetails.getPassword().isEmpty()) {
      user.setPassword(passwordEncoder.encode(userDetails.getPassword()));
    }

    // Only update enabled status if provided
    if (userDetails.isEnabled() != user.isEnabled()) {
      user.setEnabled(userDetails.isEnabled());
    }

    return userRepository.save(user);
  }

  /** Delete user */
  @Transactional
  public void deleteUser(Long id) {
    User user = getUserById(id);
    userRepository.delete(user);
  }

  /** Get user roles */
  public Set<Role> getUserRoles(Long userId) {
    User user = getUserById(userId);
    return user.getRoles();
  }

  /** Assign role to user */
  @Transactional
  public User assignRoleToUser(Long userId, Long roleId) {
    User user = getUserById(userId);
    Role role =
        roleRepository
            .findById(roleId)
            .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + roleId));

    user.getRoles().add(role);
    return userRepository.save(user);
  }

  /** Remove role from user */
  @Transactional
  public User removeRoleFromUser(Long userId, Long roleId) {
    User user = getUserById(userId);
    Role role =
        roleRepository
            .findById(roleId)
            .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + roleId));

    user.getRoles().remove(role);
    return userRepository.save(user);
  }

  /** Set user roles (replace all existing roles) */
  @Transactional
  public User setUserRoles(Long userId, Set<Long> roleIds) {
    User user = getUserById(userId);
    Set<Role> roles = new HashSet<>();

    for (Long roleId : roleIds) {
      Role role =
          roleRepository
              .findById(roleId)
              .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + roleId));
      roles.add(role);
    }

    user.setRoles(roles);
    return userRepository.save(user);
  }

  /**
   * Get the current authenticated user.
   *
   * @return The current user
   * @throws ResourceNotFoundException if no user is authenticated or the user is not found
   */
  public User getCurrentUser() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication == null || !authentication.isAuthenticated()) {
      throw new ResourceNotFoundException("No authenticated user found");
    }

    String username = authentication.getName();
    return userRepository
        .findByUsername(username)
        .orElseThrow(
            () -> new ResourceNotFoundException("User not found with username: " + username));
  }

  /**
   * Find a user with a specific role.
   *
   * @param roleId The role ID to look for
   * @return An optional containing a user with the specified role, or empty if none found
   */
  public Optional<User> findUserWithRole(Long roleId) {
    Role role =
        roleRepository
            .findById(roleId)
            .orElseThrow(() -> new ResourceNotFoundException("Role not found with id: " + roleId));

    return userRepository.findByRolesContaining(role).stream().findFirst();
  }

  /**
   * Get the system user for automated operations.
   *
   * @return The system user
   * @throws ResourceNotFoundException if the system user is not found
   */
  public User getSystemUser() {
    return userRepository
        .findByUsername("system")
        .orElseThrow(() -> new ResourceNotFoundException("System user not found"));
  }
}
