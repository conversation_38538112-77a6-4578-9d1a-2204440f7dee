import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Divider,
  Typography,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import DashboardIcon from '@mui/icons-material/Dashboard';
import RuleIcon from '@mui/icons-material/Gavel';
import SettingsIcon from '@mui/icons-material/Settings';
import BarChartIcon from '@mui/icons-material/BarChart';
import PersonIcon from '@mui/icons-material/Person';
import NotificationsIcon from '@mui/icons-material/Notifications';
import ReportIcon from '@mui/icons-material/Assessment';
import SecurityIcon from '@mui/icons-material/Security';
import HistoryIcon from '@mui/icons-material/History';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import MenuIcon from '@mui/icons-material/Menu';
import FolderSpecialIcon from '@mui/icons-material/FolderSpecial';
import StorageIcon from '@mui/icons-material/Storage';
import AssignmentIcon from '@mui/icons-material/Assignment';
import PolicyIcon from '@mui/icons-material/Policy';

const Sidebar = ({ mobileOpen, handleDrawerToggle }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const isDarkMode = theme.palette.mode === 'dark';

  const drawerWidth = isCollapsed ? 65 : 240;

  const [openMenus, setOpenMenus] = useState({
    rules: true,
    analytics: false,
    transactions: false,
    cases: false,
    compliance: false,
    access: false,
    settings: false,
    datasources: false
  });

  const handleMenuClick = (menu) => {
    if (isCollapsed) {
      setIsCollapsed(false);
      setTimeout(() => {
        setOpenMenus({
          ...openMenus,
          [menu]: !openMenus[menu]
        });
      }, 200);
    } else {
      setOpenMenus({
        ...openMenus,
        [menu]: !openMenus[menu]
      });
    }
  };

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    // Close all submenus when collapsing
    if (!isCollapsed) {
      setOpenMenus({
        rules: false,
        analytics: false,
        transactions: false,
        cases: false,
        compliance: false,
        access: false,
        settings: false,
        datasources: false
      });
    }
  };

  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const menuItems = [
    {
      title: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/dashboard',
      exact: true
    },
    {
      title: 'Rule Management',
      icon: <RuleIcon />,
      menu: 'rules',
      submenu: [
        { title: 'Rule List', path: '/' },
        { title: 'Data Buckets', path: '/rule-variable-registry' },
        { title: 'Proper Data Buckets', path: '/proper-data-buckets' },
        { title: 'Clean Data Buckets', path: '/clean-data-buckets' },
        { title: 'Modern Data Buckets', path: '/modern-data-buckets' },
        { title: 'Data Sources', path: '/data-sources' }
      ]
    },
    {
      title: 'Analytics',
      icon: <BarChartIcon />,
      menu: 'analytics',
      submenu: [
        { title: 'Dashboards', path: '/analytics/dashboards' },
        { title: 'Reports', path: '/analytics/reports' },
        { title: 'Metrics', path: '/analytics/metrics' }
      ]
    },
    {
      title: 'Transaction Management',
      icon: <HistoryIcon />,
      menu: 'transactions',
      submenu: [
        { title: 'Transaction List', path: '/transactions' },
        { title: 'Transaction Stats', path: '/transactions/stats' }
      ]
    },
    {
      title: 'Case Management',
      icon: <FolderSpecialIcon />,
      menu: 'cases',
      submenu: [
        { title: 'Open Cases', path: '/cases' },
        { title: 'Closed Cases', path: '/cases/closed' },
        { title: 'Case Analytics', path: '/cases/analytics' },
        { title: 'Workflow Designer', path: '/workflow-designer' },
        { title: 'Workflow Test', path: '/workflow-test' }
      ]
    },
    {
      title: 'Compliance',
      icon: <PolicyIcon />,
      menu: 'compliance',
      submenu: [
        { title: 'Task Inbox', path: '/compliance/tasks' },
        { title: 'Case Dashboard', path: '/compliance/cases' }
      ]
    },
    {
      title: 'User Management',
      icon: <PersonIcon />,
      menu: 'access',
      submenu: [
        { title: 'Users', path: '/users' },
        { title: 'Roles', path: '/roles' },
        { title: 'Permissions', path: '/permissions' }
      ]
    },
    {
      title: 'Notifications',
      icon: <NotificationsIcon />,
      path: '/notifications',
      exact: false
    },
    {
      title: 'Reports',
      icon: <ReportIcon />,
      path: '/reports',
      exact: false
    },
    {
      title: 'Security',
      icon: <SecurityIcon />,
      path: '/security',
      exact: false
    },
    {
      title: 'Settings',
      icon: <SettingsIcon />,
      menu: 'settings',
      submenu: [
        { title: 'General', path: '/settings/general' },
        { title: 'API Configuration', path: '/settings/api' },
        { title: 'User Preferences', path: '/settings/preferences' }
      ]
    }
  ];

  const content = (
    <Box sx={{
      overflow: 'auto',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      transition: 'width 0.2s ease',
      width: drawerWidth,
      backgroundColor: theme.palette.background.paper,
    }}>
      <Box sx={{
        p: isCollapsed ? 1 : 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: isCollapsed ? 'center' : 'space-between',
        borderBottom: isDarkMode
          ? '1px solid rgba(255, 255, 255, 0.05)'
          : '1px solid rgba(0, 0, 0, 0.06)',
        mb: 1
      }}>
        {!isCollapsed && (
          <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 600, letterSpacing: '0.5px' }}>
            SoniQue Console
          </Typography>
        )}

        <IconButton onClick={handleToggleCollapse} size="small" sx={{ color: 'primary.main' }}>
          {isCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
        </IconButton>
      </Box>

      <List sx={{ flexGrow: 1, px: 0.5 }}>
        {menuItems.map((item, index) => (
          <React.Fragment key={index}>
            {item.submenu ? (
              <>
                <ListItem disablePadding>
                  <Tooltip title={isCollapsed ? item.title : ""} placement="right" arrow>
                    <ListItemButton
                      onClick={() => handleMenuClick(item.menu)}
                      sx={{
                        borderRadius: 1,
                        mb: 0.5,
                        px: isCollapsed ? 1 : 2,
                        justifyContent: isCollapsed ? 'center' : 'flex-start',
                        backgroundColor: openMenus[item.menu]
                          ? isDarkMode
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(12, 78, 122, 0.04)'
                          : 'transparent',
                        '&:hover': {
                          backgroundColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.08)'
                            : 'rgba(12, 78, 122, 0.08)'
                        }
                      }}
                    >
                      <ListItemIcon sx={{
                        minWidth: isCollapsed ? 0 : 40,
                        color: 'primary.main',
                        mr: isCollapsed ? 0 : 1
                      }}>
                        {item.icon}
                      </ListItemIcon>
                      {!isCollapsed && (
                        <>
                          <ListItemText
                            primary={
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {item.title}
                              </Typography>
                            }
                          />
                          {openMenus[item.menu] ? <ExpandLess /> : <ExpandMore />}
                        </>
                      )}
                    </ListItemButton>
                  </Tooltip>
                </ListItem>
                {!isCollapsed && (
                  <Collapse in={openMenus[item.menu]} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      {item.submenu.map((subitem, subindex) => (
                        <ListItem key={subindex} disablePadding>
                          <ListItemButton
                            component={Link}
                            to={subitem.path}
                            sx={{
                              pl: 4,
                              py: 0.5,
                              borderRadius: 1,
                              mb: 0.5,
                              backgroundColor: isActive(subitem.path)
                                ? isDarkMode
                                  ? 'rgba(30, 150, 243, 0.1)'
                                  : 'rgba(123, 220, 181, 0.1)'
                                : 'transparent',
                              '&:hover': {
                                backgroundColor: isDarkMode
                                  ? 'rgba(30, 150, 243, 0.15)'
                                  : 'rgba(123, 220, 181, 0.2)'
                              }
                            }}
                          >
                            <ListItemText
                              primary={
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontWeight: isActive(subitem.path) ? 600 : 400,
                                    color: isActive(subitem.path) ? 'primary.main' : 'text.primary',
                                  }}
                                >
                                  {subitem.title}
                                </Typography>
                              }
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  </Collapse>
                )}
              </>
            ) : (
              <ListItem disablePadding>
                <Tooltip title={isCollapsed ? item.title : ""} placement="right" arrow>
                  <ListItemButton
                    component={Link}
                    to={item.path}
                    sx={{
                      borderRadius: 1,
                      mb: 0.5,
                      px: isCollapsed ? 1 : 2,
                      justifyContent: isCollapsed ? 'center' : 'flex-start',
                      backgroundColor: isActive(item.path)
                        ? isDarkMode
                          ? 'rgba(30, 150, 243, 0.1)'
                          : 'rgba(12, 78, 122, 0.08)'
                        : 'transparent',
                      '&:hover': {
                        backgroundColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.05)'
                          : 'rgba(12, 78, 122, 0.08)'
                      }
                    }}
                  >
                    <ListItemIcon sx={{
                      minWidth: isCollapsed ? 0 : 40,
                      color: isActive(item.path) ? 'primary.main' : 'text.secondary',
                      mr: isCollapsed ? 0 : 1
                    }}>
                      {item.icon}
                    </ListItemIcon>
                    {!isCollapsed && (
                      <ListItemText
                        primary={
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: isActive(item.path) ? 600 : 500,
                              color: isActive(item.path) ? 'primary.main' : 'text.primary',
                            }}
                          >
                            {item.title}
                          </Typography>
                        }
                      />
                    )}
                  </ListItemButton>
                </Tooltip>
              </ListItem>
            )}
          </React.Fragment>
        ))}
      </List>

      {!isCollapsed && (
        <Box sx={{
          p: 2,
          borderTop: isDarkMode
            ? '1px solid rgba(255, 255, 255, 0.05)'
            : '1px solid rgba(0, 0, 0, 0.06)'
        }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center' }}>
            Progize SoniQue v1.0
          </Typography>
        </Box>
      )}
    </Box>
  );

  return (
    <Box
      component="nav"
      sx={{
        width: { md: drawerWidth },
        flexShrink: { md: 0 },
        transition: 'width 0.2s ease'
      }}
    >
      {/* Mobile drawer */}
      {isMobile && (
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              boxSizing: 'border-box',
              transition: 'width 0.2s ease',
              backgroundColor: theme.palette.background.paper,
            },
          }}
        >
          {content}
        </Drawer>
      )}

      {/* Desktop drawer */}
      {!isMobile && (
        <Drawer
          variant="permanent"
          sx={{
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              boxSizing: 'border-box',
              borderRight: isDarkMode
                ? '1px solid rgba(255, 255, 255, 0.05)'
                : '1px solid rgba(0, 0, 0, 0.08)',
              boxShadow: isDarkMode
                ? '2px 0 8px rgba(0, 0, 0, 0.5)'
                : '2px 0 8px rgba(0, 0, 0, 0.02)',
              overflowX: 'hidden',
              transition: 'width 0.2s ease'
            },
            width: drawerWidth,
            transition: 'width 0.2s ease'
          }}
          open
        >
          {content}
        </Drawer>
      )}
    </Box>
  );
};

export default Sidebar;
