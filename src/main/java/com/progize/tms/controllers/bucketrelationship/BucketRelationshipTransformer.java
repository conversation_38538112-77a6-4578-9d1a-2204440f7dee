package com.progize.tms.controllers.bucketrelationship;

import com.progize.tms.controllers.bucketrelationship.req.CreateBucketRelationshipRequest;
import com.progize.tms.controllers.bucketrelationship.res.BucketRelationshipResponse;
import com.progize.tms.repository.entity.BucketRelationshipEntity;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.services.DataBucketService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class BucketRelationshipTransformer {

  private final DataBucketService dataBucketService;

  public BucketRelationshipResponse toResponse(BucketRelationshipEntity entity) {
    BucketRelationshipResponse response = new BucketRelationshipResponse();
    response.setId(entity.getId());
    response.setRuleBucketId(entity.getRuleBucket().getId());
    response.setRuleBucketName(entity.getRuleBucket().getName());
    response.setLookupBucketId(entity.getLookupBucket().getId());
    response.setLookupBucketName(entity.getLookupBucket().getName());
    response.setCreatedAt(entity.getCreatedAt());
    response.setUpdatedAt(entity.getUpdatedAt());

    // Get variable names for the join conditions
    Map<Long, String> ruleVariableNames = getVariableNames(entity.getRuleBucket().getId());
    Map<Long, String> lookupVariableNames = getVariableNames(entity.getLookupBucket().getId());

    List<BucketRelationshipResponse.JoinConditionResponse> joinConditions = new ArrayList<>();
    for (BucketRelationshipEntity.JoinCondition condition : entity.getJoinConditions()) {
      BucketRelationshipResponse.JoinConditionResponse conditionResponse =
          new BucketRelationshipResponse.JoinConditionResponse();
      conditionResponse.setRuleBucketVariableId(condition.getRuleBucketVariableId());
      conditionResponse.setRuleBucketVariableName(
          ruleVariableNames.getOrDefault(condition.getRuleBucketVariableId(), "Unknown"));
      conditionResponse.setLookupBucketVariableId(condition.getLookupBucketVariableId());
      conditionResponse.setLookupBucketVariableName(
          lookupVariableNames.getOrDefault(condition.getLookupBucketVariableId(), "Unknown"));
      joinConditions.add(conditionResponse);
    }

    response.setJoinConditions(joinConditions);
    return response;
  }

  public List<BucketRelationshipEntity.JoinCondition> toJoinConditions(
      List<CreateBucketRelationshipRequest.JoinConditionRequest> requests) {
    return requests.stream().map(this::toJoinCondition).collect(Collectors.toList());
  }

  private BucketRelationshipEntity.JoinCondition toJoinCondition(
      CreateBucketRelationshipRequest.JoinConditionRequest request) {
    BucketRelationshipEntity.JoinCondition condition = new BucketRelationshipEntity.JoinCondition();
    condition.setRuleBucketVariableId(request.getRuleBucketVariableId());
    condition.setLookupBucketVariableId(request.getLookupBucketVariableId());
    return condition;
  }

  private Map<Long, String> getVariableNames(Long bucketId) {
    Map<Long, String> variableNames = new HashMap<>();
    List<DataBucketVariableEntity> variables = dataBucketService.getVariablesByBucketId(bucketId);
    for (DataBucketVariableEntity variable : variables) {
      variableNames.put(variable.getId(), variable.getName());
    }
    return variableNames;
  }
}
