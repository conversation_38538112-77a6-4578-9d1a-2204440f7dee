package com.progize.tms.controllers.databuckets.res;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LookupBucketResponse {
  private Long id;
  private String name;
  private String description;
  private String tableName;
  private String bucketType;
  private Long dataSourceId;
  private String dataSourceName;
  private List<LookupBucketVariableResponse> variables;

  @Getter
  @Setter
  public static class LookupBucketVariableResponse {
    private Long id;
    private String code;
    private String name;
    private String description;
    private String dataTypeCode;
    private Boolean isUnique;
  }
}
