package com.progize.tms.repository.entity;

import com.progize.tms.repository.entity.base.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Entity
@Audited
@Table(name = "rules")
@Getter
@Setter
public class Rule extends BaseEntity {
  private String name;
  private String description;
  private String expression;
  private String flagReason;
  private boolean active;
  private int priority;
  private String ruleJson;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_bucket_id", referencedColumnName = "id")
  private DataBucketEntity dataBucket;
}
