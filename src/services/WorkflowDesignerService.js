import axios from 'axios';

/**
 * Service for interacting with workflow designer-related APIs
 */
class WorkflowDesignerService {
  /**
   * Get all workflow definitions
   * @returns {Promise<Array>} List of workflow definitions
   */
  async getWorkflows() {
    try {
      const response = await axios.get('/api/workflows');
      console.log('Fetched workflows:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching workflows:', error);
      throw error;
    }
  }

  /**
   * Get a workflow definition by ID
   * @param {string} id Workflow definition ID
   * @returns {Promise<Object>} Workflow definition details
   */
  async getWorkflowById(id) {
    try {
      const response = await axios.get(`/api/workflows/${id}`);
      console.log('Fetched workflow:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching workflow ${id}:`, error);
      throw error;
    }
  }

  /**
   * Save a workflow definition
   * @param {Object} workflowData Workflow definition data
   * @returns {Promise<Object>} Saved workflow definition
   */
  async saveWorkflow(workflowData) {
    try {
      let response;
      
      if (workflowData.id) {
        // Update existing workflow
        response = await axios.put(`/api/workflows/${workflowData.id}`, workflowData);
        console.log('Updated workflow:', response.data);
      } else {
        // Create new workflow
        response = await axios.post('/api/workflows', workflowData);
        console.log('Created workflow:', response.data);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error saving workflow:', error);
      throw error;
    }
  }

  /**
   * Delete a workflow definition
   * @param {string} id Workflow definition ID
   * @returns {Promise<void>}
   */
  async deleteWorkflow(id) {
    try {
      await axios.delete(`/api/workflows/${id}`);
      console.log(`Deleted workflow ${id}`);
    } catch (error) {
      console.error(`Error deleting workflow ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all workflow steps for a workflow
   * @param {string} workflowId Workflow definition ID
   * @returns {Promise<Array>} List of workflow steps
   */
  async getWorkflowSteps(workflowId) {
    try {
      const response = await axios.get(`/api/workflows/${workflowId}/steps`);
      console.log(`Fetched steps for workflow ${workflowId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching steps for workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Save a workflow step
   * @param {Object} stepData Workflow step data
   * @returns {Promise<Object>} Saved workflow step
   */
  async saveWorkflowStep(stepData) {
    try {
      let response;
      
      if (stepData.id) {
        // Update existing step
        response = await axios.put(`/api/workflows/steps/${stepData.id}`, stepData);
        console.log('Updated workflow step:', response.data);
      } else {
        // Create new step
        response = await axios.post('/api/workflows/steps', stepData);
        console.log('Created workflow step:', response.data);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error saving workflow step:', error);
      throw error;
    }
  }

  /**
   * Delete a workflow step
   * @param {string} id Workflow step ID
   * @returns {Promise<void>}
   */
  async deleteWorkflowStep(id) {
    try {
      await axios.delete(`/api/workflows/steps/${id}`);
      console.log(`Deleted workflow step ${id}`);
    } catch (error) {
      console.error(`Error deleting workflow step ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all transitions for a workflow step
   * @param {string} stepId Workflow step ID
   * @returns {Promise<Array>} List of transitions
   */
  async getTransitionsFromStep(stepId) {
    try {
      const response = await axios.get(`/api/workflows/steps/${stepId}/transitions`);
      console.log(`Fetched transitions for step ${stepId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching transitions for step ${stepId}:`, error);
      throw error;
    }
  }

  /**
   * Save a workflow transition
   * @param {Object} transitionData Workflow transition data
   * @returns {Promise<Object>} Saved workflow transition
   */
  async saveTransition(transitionData) {
    try {
      let response;
      
      if (transitionData.id) {
        // Update existing transition
        response = await axios.put(`/api/workflows/transitions/${transitionData.id}`, transitionData);
        console.log('Updated workflow transition:', response.data);
      } else {
        // Create new transition
        response = await axios.post('/api/workflows/transitions', transitionData);
        console.log('Created workflow transition:', response.data);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error saving workflow transition:', error);
      throw error;
    }
  }

  /**
   * Delete a workflow transition
   * @param {string} id Workflow transition ID
   * @returns {Promise<void>}
   */
  async deleteTransition(id) {
    try {
      await axios.delete(`/api/workflows/transitions/${id}`);
      console.log(`Deleted workflow transition ${id}`);
    } catch (error) {
      console.error(`Error deleting workflow transition ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all picklists
   * @returns {Promise<Array>} List of picklists
   */
  async getPicklists() {
    try {
      const response = await axios.get('/api/picklists');
      console.log('Fetched picklists:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching picklists:', error);
      throw error;
    }
  }

  /**
   * Get a picklist by ID
   * @param {string} id Picklist ID
   * @returns {Promise<Object>} Picklist details
   */
  async getPicklistById(id) {
    try {
      const response = await axios.get(`/api/picklists/${id}`);
      console.log('Fetched picklist:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching picklist ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new picklist
   * @param {Object} picklistData Picklist data
   * @returns {Promise<Object>} Created picklist
   */
  async createPicklist(picklistData) {
    try {
      const response = await axios.post('/api/picklists', picklistData);
      console.log('Created picklist:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating picklist:', error);
      throw error;
    }
  }

  /**
   * Update a picklist
   * @param {Object} picklistData Picklist data
   * @returns {Promise<Object>} Updated picklist
   */
  async updatePicklist(picklistData) {
    try {
      const response = await axios.put(`/api/picklists/${picklistData.id}`, picklistData);
      console.log('Updated picklist:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating picklist ${picklistData.id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a picklist
   * @param {string} id Picklist ID
   * @returns {Promise<void>}
   */
  async deletePicklist(id) {
    try {
      await axios.delete(`/api/picklists/${id}`);
      console.log(`Deleted picklist ${id}`);
    } catch (error) {
      console.error(`Error deleting picklist ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get picklist options for a picklist
   * @param {string} picklistId Picklist ID
   * @returns {Promise<Array>} List of picklist options
   */
  async getPicklistOptions(picklistId) {
    try {
      const response = await axios.get(`/api/picklists/${picklistId}/options`);
      console.log(`Fetched options for picklist ${picklistId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching options for picklist ${picklistId}:`, error);
      throw error;
    }
  }

  /**
   * Create a picklist option
   * @param {Object} optionData Picklist option data
   * @returns {Promise<Object>} Created picklist option
   */
  async createPicklistOption(optionData) {
    try {
      const response = await axios.post(`/api/picklists/${optionData.picklistId}/options`, optionData);
      console.log('Created picklist option:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating picklist option:', error);
      throw error;
    }
  }

  /**
   * Update a picklist option
   * @param {Object} optionData Picklist option data
   * @returns {Promise<Object>} Updated picklist option
   */
  async updatePicklistOption(optionData) {
    try {
      const response = await axios.put(`/api/picklists/options/${optionData.id}`, optionData);
      console.log('Updated picklist option:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating picklist option ${optionData.id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a picklist option
   * @param {string} id Picklist option ID
   * @returns {Promise<void>}
   */
  async deletePicklistOption(id) {
    try {
      await axios.delete(`/api/picklists/options/${id}`);
      console.log(`Deleted picklist option ${id}`);
    } catch (error) {
      console.error(`Error deleting picklist option ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all roles
   * @returns {Promise<Array>} List of roles
   */
  async getRoles() {
    try {
      const response = await axios.get('/api/roles');
      console.log('Fetched roles:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching roles:', error);
      throw error;
    }
  }
}

export default new WorkflowDesignerService();
