package com.progize.tms.controllers.datasources;

import com.progize.tms.controllers.datasources.req.CreateDatabaseSourceRequest;
import com.progize.tms.controllers.datasources.req.CreateRestApiSourceRequest;
import com.progize.tms.controllers.datasources.res.DataSourceResponse;
import com.progize.tms.controllers.datasources.res.DataSourceTypeConfig;
import com.progize.tms.controllers.datasources.res.DatabaseSourceResponse;
import com.progize.tms.controllers.datasources.res.RestApiSourceResponse;
import com.progize.tms.controllers.datasources.res.SchemaResponse;
import com.progize.tms.exceptions.TmsBusinessException;
import com.progize.tms.exceptions.TmsError;
import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import com.progize.tms.repository.entity.RestApiSourceEntity;
import com.progize.tms.repository.entity.enums.DataSourceType;
import com.progize.tms.service.DataSourceService;
import com.progize.tms.service.model.SchemaEntity;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** Controller for data sources. */
@RestController
@RequestMapping("/api/data-sources")
@RequiredArgsConstructor
@Slf4j
public class DataSourceController {

  private final DataSourceService dataSourceService;
  private final DataSourceTransformer transformer;

  /**
   * Get all data sources.
   *
   * @return List of data sources
   */
  @GetMapping
  public ResponseEntity<List<DataSourceResponse>> getAllDataSources() {
    List<DataSourceEntity> dataSources = dataSourceService.getAllDataSources();
    List<DataSourceResponse> response =
        dataSources.stream().map(transformer::toDataSourceResponse).collect(Collectors.toList());
    return ResponseEntity.ok(response);
  }

  /**
   * Get data source by ID.
   *
   * @param id Data source ID
   * @return Data source
   */
  @GetMapping("/{id}")
  public ResponseEntity<?> getDataSourceById(@PathVariable Long id) {
    DataSourceEntity dataSource = findDataSourceOrThrow(id);

    if (dataSource.getType() == DataSourceType.DATABASE) {
      DatabaseSourceEntity databaseSource = findDatabaseSourceOrThrow(id);
      return ResponseEntity.ok(transformer.toDatabaseSourceResponse(dataSource, databaseSource));
    } else if (dataSource.getType() == DataSourceType.REST_API) {
      RestApiSourceEntity restApiSource = findRestApiSourceOrThrow(id);
      return ResponseEntity.ok(transformer.toRestApiSourceResponse(dataSource, restApiSource));
    } else {
      return ResponseEntity.ok(transformer.toDataSourceResponse(dataSource));
    }
  }

  /**
   * Create a database data source.
   *
   * @param request Create database source request
   * @return Created data source
   */
  @PostMapping("/database")
  public ResponseEntity<DatabaseSourceResponse> createDatabaseSource(
      @Valid @RequestBody CreateDatabaseSourceRequest request) {
    // Transform request to entities
    DataSourceEntity dataSource = transformer.toDataSourceEntity(request);
    DatabaseSourceEntity databaseSource = transformer.toDatabaseSourceEntity(request, dataSource);

    // Create data source
    DataSourceEntity createdDataSource =
        dataSourceService.createDatabaseSource(dataSource, databaseSource);

    // Get created database source
    DatabaseSourceEntity createdDatabaseSource =
        findDatabaseSourceOrThrow(createdDataSource.getId());

    // Transform to response
    DatabaseSourceResponse response =
        transformer.toDatabaseSourceResponse(createdDataSource, createdDatabaseSource);

    return ResponseEntity.status(HttpStatus.CREATED).body(response);
  }

  /**
   * Create a REST API data source.
   *
   * @param request Create REST API source request
   * @return Created data source
   */
  @PostMapping("/rest-api")
  public ResponseEntity<RestApiSourceResponse> createRestApiSource(
      @Valid @RequestBody CreateRestApiSourceRequest request) {
    // Transform request to entities
    DataSourceEntity dataSource = transformer.toDataSourceEntity(request);
    RestApiSourceEntity restApiSource = transformer.toRestApiSourceEntity(request, dataSource);

    // Create data source
    DataSourceEntity createdDataSource =
        dataSourceService.createRestApiSource(dataSource, restApiSource);

    // Get created REST API source
    RestApiSourceEntity createdRestApiSource = findRestApiSourceOrThrow(createdDataSource.getId());

    // Transform to response
    RestApiSourceResponse response =
        transformer.toRestApiSourceResponse(createdDataSource, createdRestApiSource);

    return ResponseEntity.status(HttpStatus.CREATED).body(response);
  }

  /**
   * Update a database data source.
   *
   * @param id Data source ID
   * @param request Update database source request
   * @return Updated data source
   */
  @PutMapping("/database/{id}")
  public ResponseEntity<DatabaseSourceResponse> updateDatabaseSource(
      @PathVariable Long id, @Valid @RequestBody CreateDatabaseSourceRequest request) {
    // Transform request to entities
    DataSourceEntity dataSource = transformer.toDataSourceEntity(request);
    DatabaseSourceEntity databaseSource = transformer.toDatabaseSourceEntity(request, dataSource);

    // Update data source
    dataSourceService.updateDataSource(id, dataSource);
    DatabaseSourceEntity updatedDatabaseSource =
        dataSourceService.updateDatabaseSource(id, databaseSource);

    // Get updated data source
    DataSourceEntity updatedDataSource = findDataSourceOrThrow(id);

    // Transform to response
    DatabaseSourceResponse response =
        transformer.toDatabaseSourceResponse(updatedDataSource, updatedDatabaseSource);

    return ResponseEntity.ok(response);
  }

  /**
   * Update a REST API data source.
   *
   * @param id Data source ID
   * @param request Update REST API source request
   * @return Updated data source
   */
  @PutMapping("/rest-api/{id}")
  public ResponseEntity<RestApiSourceResponse> updateRestApiSource(
      @PathVariable Long id, @Valid @RequestBody CreateRestApiSourceRequest request) {
    // Transform request to entities
    DataSourceEntity dataSource = transformer.toDataSourceEntity(request);
    RestApiSourceEntity restApiSource = transformer.toRestApiSourceEntity(request, dataSource);

    // Update data source
    dataSourceService.updateDataSource(id, dataSource);
    RestApiSourceEntity updatedRestApiSource =
        dataSourceService.updateRestApiSource(id, restApiSource);

    // Get updated data source
    DataSourceEntity updatedDataSource = findDataSourceOrThrow(id);

    // Transform to response
    RestApiSourceResponse response =
        transformer.toRestApiSourceResponse(updatedDataSource, updatedRestApiSource);

    return ResponseEntity.ok(response);
  }

  /**
   * Delete a data source.
   *
   * @param id Data source ID
   * @return No content
   */
  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteDataSource(@PathVariable Long id) {
    dataSourceService.deleteDataSource(id);
    return ResponseEntity.noContent().build();
  }

  /**
   * Test connection to a data source.
   *
   * @param id Data source ID
   * @return Connection status
   */
  @GetMapping("/{id}/test-connection")
  public ResponseEntity<Map<String, Boolean>> testConnection(@PathVariable Long id) {
    boolean success = dataSourceService.testConnection(id);
    Map<String, Boolean> response = new HashMap<>();
    response.put("success", success);
    return ResponseEntity.ok(response);
  }

  /**
   * Discover schema from a data source.
   *
   * @param id Data source ID
   * @return Discovered schema
   */
  @GetMapping("/{id}/schema")
  public ResponseEntity<List<SchemaResponse>> discoverSchema(@PathVariable Long id) {
    List<SchemaEntity> schema = dataSourceService.discoverSchema(id);
    List<SchemaResponse> response = transformer.toSchemaResponseList(schema);
    return ResponseEntity.ok(response);
  }

  /**
   * Execute a query against a data source.
   *
   * @param id Data source ID
   * @param entityName Name of the entity to query
   * @param parameters Query parameters
   * @return Query results
   */
  @GetMapping("/{id}/query")
  public ResponseEntity<List<Map<String, Object>>> executeQuery(
      @PathVariable Long id,
      @RequestParam String entityName,
      @RequestParam(required = false) Map<String, Object> parameters) {
    List<Map<String, Object>> results = dataSourceService.executeQuery(id, entityName, parameters);
    return ResponseEntity.ok(results);
  }

  /**
   * Get data source types.
   *
   * @return List of data source types
   */
  @GetMapping("/types")
  public ResponseEntity<List<String>> getDataSourceTypes() {
    List<DataSourceType> types = dataSourceService.getDataSourceTypes();
    List<String> response = types.stream().map(Enum::name).collect(Collectors.toList());
    return ResponseEntity.ok(response);
  }

  /**
   * Get data source configurations for UI form.
   *
   * @return List of data source configurations
   */
  @GetMapping("/configurations")
  public ResponseEntity<List<DataSourceTypeConfig>> getDataSourceConfigurations() {
    List<DataSourceTypeConfig> configs = transformer.getDataSourceConfigurations();
    return ResponseEntity.ok(configs);
  }

  /**
   * Find a data source by ID or throw an exception if not found.
   *
   * @param id Data source ID
   * @return Data source entity
   * @throws TmsBusinessException if data source is not found
   */
  private DataSourceEntity findDataSourceOrThrow(Long id) {
    return dataSourceService
        .getDataSourceById(id)
        .orElseThrow(() -> new TmsBusinessException(TmsError.DATA_SOURCE_NOT_FOUND));
  }

  /**
   * Find a database source by data source ID or throw an exception if not found.
   *
   * @param dataSourceId Data source ID
   * @return Database source entity
   * @throws TmsBusinessException if database source is not found
   */
  private DatabaseSourceEntity findDatabaseSourceOrThrow(Long dataSourceId) {
    return dataSourceService
        .getDatabaseSourceByDataSourceId(dataSourceId)
        .orElseThrow(() -> new TmsBusinessException(TmsError.DATABASE_SOURCE_NOT_FOUND));
  }

  /**
   * Find a REST API source by data source ID or throw an exception if not found.
   *
   * @param dataSourceId Data source ID
   * @return REST API source entity
   * @throws TmsBusinessException if REST API source is not found
   */
  private RestApiSourceEntity findRestApiSourceOrThrow(Long dataSourceId) {
    return dataSourceService
        .getRestApiSourceByDataSourceId(dataSourceId)
        .orElseThrow(() -> new TmsBusinessException(TmsError.REST_API_SOURCE_NOT_FOUND));
  }
}
