package com.progize.tms.service.datasource;

import com.progize.tms.exceptions.TmsBusinessException;
import com.progize.tms.service.model.SchemaEntity;
import java.util.List;
import java.util.Map;

/** Interface for data source handlers. */
public interface DataSourceHandler {

  /**
   * Test connection to the data source.
   *
   * @return true if connection is successful, false otherwise
   */
  boolean testConnection();

  /**
   * Discover schema from the data source.
   *
   * @return List of schema entities
   * @throws TmsBusinessException if there is an error discovering the schema
   */
  List<SchemaEntity> discoverSchema();

  /**
   * Execute a query against the data source.
   *
   * @param entityName Name of the entity to query
   * @param parameters Query parameters
   * @return Query results as a list of maps
   * @throws TmsBusinessException if there is an error executing the query
   */
  List<Map<String, Object>> executeQuery(String entityName, Map<String, Object> parameters);
}
