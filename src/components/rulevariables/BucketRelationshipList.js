import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Chip,
  Tooltip,
  CircularProgress,
  alpha,
  useTheme
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import LinkIcon from '@mui/icons-material/Link';
import { bucketRelationshipService } from '../../services/api';

const BucketRelationshipList = ({ ruleBucketId, onAddRelationship, onEditRelationship }) => {
  const [relationships, setRelationships] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const theme = useTheme();

  useEffect(() => {
    console.log("DEBUG - BucketRelationshipList - ruleBucketId:", ruleBucketId);

    const loadRelationships = async () => {
      try {
        setLoading(true);
        console.log("DEBUG - BucketRelationshipList - Making API call with ruleBucketId:", ruleBucketId);
        const data = await bucketRelationshipService.getRelationshipsForRuleBucket(ruleBucketId);
        console.log("DEBUG - BucketRelationshipList - API response:", data);
        setRelationships(data);
        setError(null);
      } catch (err) {
        console.error('Error loading bucket relationships:', err);
        setError('Failed to load relationships');
      } finally {
        setLoading(false);
      }
    };

    if (ruleBucketId) {
      loadRelationships();
    } else {
      console.log("DEBUG - BucketRelationshipList - No ruleBucketId provided, skipping API call");
    }
  }, [ruleBucketId]);

  const handleDeleteRelationship = async (relationshipId) => {
    if (window.confirm('Are you sure you want to delete this relationship? This action cannot be undone.')) {
      try {
        await bucketRelationshipService.deleteRelationship(relationshipId);
        setRelationships(relationships.filter(r => r.id !== relationshipId));
      } catch (err) {
        console.error('Error deleting relationship:', err);
        setError('Failed to delete relationship');
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress size={24} />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: 500, fontSize: '1rem' }}>
          Enrichment Relationships
        </Typography>
        <Button
          startIcon={<AddIcon />}
          onClick={onAddRelationship}
          size="small"
          variant="outlined"
          sx={{
            borderRadius: '20px',
            fontSize: '0.75rem',
            py: 0.5,
            px: 1.5,
            borderColor: alpha(theme.palette.primary.main, 0.5),
            '&:hover': {
              borderColor: theme.palette.primary.main,
              backgroundColor: alpha(theme.palette.primary.main, 0.05)
            }
          }}
        >
          Add Lookup
        </Button>
      </Box>

      {error && (
        <Box sx={{ p: 2, color: 'error.main', bgcolor: alpha(theme.palette.error.main, 0.1), borderRadius: 1, mb: 2 }}>
          <Typography variant="body2">{error}</Typography>
        </Box>
      )}

      {relationships.length === 0 ? (
        <Box sx={{
          p: 2,
          textAlign: 'center',
          color: 'text.secondary',
          bgcolor: alpha(theme.palette.background.default, 0.5),
          borderRadius: 1,
          border: `1px dashed ${alpha(theme.palette.divider, 0.3)}`
        }}>
          <Typography variant="body2">
            No lookup relationships defined. Add a lookup relationship to enrich this rule bucket with reference data.
          </Typography>
        </Box>
      ) : (
        <List sx={{
          bgcolor: alpha(theme.palette.background.paper, 0.5),
          borderRadius: 1,
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}>
          {relationships.map((relationship) => (
            <React.Fragment key={relationship.id}>
              <ListItem>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LinkIcon sx={{ mr: 1, fontSize: '1rem', color: 'primary.main' }} />
                      <Typography variant="subtitle2">{relationship.lookupBucketName}</Typography>
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 1 }}>
                      {relationship.joinConditions.map((condition, idx) => (
                        <Chip
                          key={idx}
                          size="small"
                          label={`${condition.ruleBucketVariableName} = ${condition.lookupBucketVariableName}`}
                          sx={{
                            mr: 1,
                            mb: 1,
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            color: theme.palette.text.secondary,
                            fontSize: '0.7rem',
                            height: '22px'
                          }}
                        />
                      ))}
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Tooltip title="Edit relationship">
                    <IconButton
                      edge="end"
                      onClick={() => onEditRelationship(relationship)}
                      size="small"
                      sx={{ mr: 1 }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete relationship">
                    <IconButton
                      edge="end"
                      onClick={() => handleDeleteRelationship(relationship.id)}
                      size="small"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </ListItemSecondaryAction>
              </ListItem>
              <Divider component="li" />
            </React.Fragment>
          ))}
        </List>
      )}
    </Box>
  );
};

export default BucketRelationshipList;
