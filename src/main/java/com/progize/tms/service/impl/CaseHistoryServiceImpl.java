package com.progize.tms.service.impl;

import com.progize.tms.repository.CaseHistoryRepository;
import com.progize.tms.repository.entity.CaseHistory;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.service.CaseHistoryService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** Implementation of the CaseHistoryService. */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseHistoryServiceImpl implements CaseHistoryService {

  private final CaseHistoryRepository caseHistoryRepository;

  @Override
  @Transactional
  public CaseHistory addHistoryEntry(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      String taskName,
      String action,
      String oldStatus,
      String newStatus,
      String details,
      Long createdBy) {

    log.debug(
        "Adding history entry to case {}: {} -> {}", workflowCase.getId(), oldStatus, newStatus);

    CaseHistory historyEntry =
        CaseHistory.builder()
            .workflowCase(workflowCase)
            .processInstanceId(processInstanceId)
            .taskId(taskId)
            .taskName(taskName)
            .action(action)
            .oldStatus(oldStatus)
            .newStatus(newStatus)
            .details(details)
            .createdBy(createdBy)
            .build();

    return caseHistoryRepository.save(historyEntry);
  }

  @Override
  public List<CaseHistory> getHistoryByCase(WorkflowCase workflowCase) {
    log.debug("Getting history for case {}", workflowCase.getId());
    return caseHistoryRepository.findByWorkflowCaseOrderByCreatedAtDesc(workflowCase);
  }

  @Override
  public List<CaseHistory> getHistoryByTask(String taskId) {
    log.debug("Getting history for task {}", taskId);
    return caseHistoryRepository.findByTaskIdOrderByCreatedAtDesc(taskId);
  }

  @Override
  public List<CaseHistory> getHistoryByProcessInstance(String processInstanceId) {
    log.debug("Getting history for process instance {}", processInstanceId);
    return caseHistoryRepository.findByProcessInstanceIdOrderByCreatedAtDesc(processInstanceId);
  }
}
