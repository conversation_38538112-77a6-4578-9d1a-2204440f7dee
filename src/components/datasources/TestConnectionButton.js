import React, { useState } from 'react';
import {
  Button,
  CircularProgress,
  Snack<PERSON>,
  <PERSON>ert,
  Tooltip,
  useTheme,
  alpha,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography
} from '@mui/material';
import NetworkCheckIcon from '@mui/icons-material/NetworkCheck';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CancelIcon from '@mui/icons-material/Cancel';
import dataSourceService from '../../services/dataSourceService';

/**
 * Button component to test connection to a data source
 *
 * @param {Object} props - Component props
 * @param {number} props.dataSourceId - ID of the data source to test
 * @param {string} props.variant - Button variant (contained, outlined, text)
 * @param {string} props.size - Button size (small, medium, large)
 * @param {Function} props.onSuccess - Callback function called on successful connection
 * @param {Function} props.onError - Callback function called on connection error
 */
const TestConnectionButton = ({
  dataSourceId,
  variant = 'contained',
  size = 'medium',
  onSuccess,
  onError,
  sx
}) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [resultDialog, setResultDialog] = useState({
    open: false,
    success: false,
    message: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Test connection to the data source
  const handleTestConnection = async () => {
    try {
      setLoading(true);
      const result = await dataSourceService.testConnection(dataSourceId);

      const message = result.success
        ? 'Connection successful! The data source is properly configured and accessible.'
        : 'Connection failed. Please check your configuration and try again.';

      // Show result dialog instead of snackbar for more visibility
      setResultDialog({
        open: true,
        success: result.success,
        message
      });

      if (result.success && onSuccess) {
        onSuccess(result);
      } else if (!result.success && onError) {
        onError(new Error(message));
      }
    } catch (err) {
      const errorMessage = `Connection test failed: ${err.message}`;

      setResultDialog({
        open: true,
        success: false,
        message: errorMessage
      });

      if (onError) {
        onError(err);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Handle result dialog close
  const handleResultDialogClose = () => {
    setResultDialog({ ...resultDialog, open: false });
  };

  return (
    <>
      <Tooltip title="Test the connection to this data source">
        <Button
          variant={variant}
          color="info"
          size={size}
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <NetworkCheckIcon />}
          onClick={handleTestConnection}
          disabled={loading || !dataSourceId}
          sx={{
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'linear-gradient(120deg, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 70%)',
              transform: 'translateX(-100%)',
              transition: 'all 0.6s ease',
            },
            '&:hover::after': {
              transform: 'translateX(100%)',
            },
            ...sx
          }}
        >
          Test Connection
        </Button>
      </Tooltip>

      {/* Result Dialog */}
      <Dialog
        open={resultDialog.open}
        onClose={handleResultDialogClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle
          sx={{
            bgcolor: resultDialog.success
              ? alpha(theme.palette.success.main, 0.1)
              : alpha(theme.palette.error.main, 0.1),
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            py: 2
          }}
        >
          {resultDialog.success ? (
            <CheckCircleOutlineIcon color="success" />
          ) : (
            <ErrorOutlineIcon color="error" />
          )}
          <Typography variant="h6" component="div">
            {resultDialog.success ? 'Connection Successful' : 'Connection Failed'}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 2, pb: 1 }}>
          <Typography variant="body1">
            {resultDialog.message}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={handleResultDialogClose}
            color={resultDialog.success ? "success" : "error"}
            variant="contained"
            sx={{
              borderRadius: '20px',
              px: 3
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default TestConnectionButton;
