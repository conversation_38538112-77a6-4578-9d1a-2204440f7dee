package com.progize.tms.repository.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "operator_expressions")
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class OperatorExpression {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "operator_id", nullable = false)
  private Operator operator;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_type_id")
  private DataType dataType; // Null for standard expressions

  @Column(name = "expression_template", nullable = false, length = 255)
  private String expressionTemplate;

  @CreatedDate
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @LastModifiedDate
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;
}
