package com.progize.tms.controllers.datasources;

import com.progize.tms.controllers.datasources.req.CreateDatabaseSourceRequest;
import com.progize.tms.controllers.datasources.req.CreateRestApiSourceRequest;
import com.progize.tms.controllers.datasources.res.DataSourceResponse;
import com.progize.tms.controllers.datasources.res.DataSourceTypeConfig;
import com.progize.tms.controllers.datasources.res.DatabaseSourceResponse;
import com.progize.tms.controllers.datasources.res.RestApiSourceResponse;
import com.progize.tms.controllers.datasources.res.SchemaResponse;
import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import com.progize.tms.repository.entity.RestApiSourceEntity;
import com.progize.tms.repository.entity.enums.AuthenticationType;
import com.progize.tms.repository.entity.enums.DataSourceType;
import com.progize.tms.repository.entity.enums.DatabaseType;
import com.progize.tms.service.model.SchemaEntity;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/** Transformer for data sources. */
@Component
public class DataSourceTransformer {

  /** Transform a DataSourceEntity to a DataSourceResponse. */
  public DataSourceResponse toDataSourceResponse(DataSourceEntity entity) {
    return DataSourceResponse.builder()
        .id(entity.getId())
        .name(entity.getName())
        .description(entity.getDescription())
        .type(entity.getType().name())
        .active(entity.getActive())
        .createdAt(entity.getCreatedAt())
        .lastSchemaDiscovery(entity.getLastSchemaDiscovery())
        .build();
  }

  /** Transform a DatabaseSourceEntity to a DatabaseSourceResponse. */
  public DatabaseSourceResponse toDatabaseSourceResponse(
      DataSourceEntity dataSource, DatabaseSourceEntity entity) {
    return DatabaseSourceResponse.builder()
        .id(dataSource.getId())
        .name(dataSource.getName())
        .description(dataSource.getDescription())
        .type(dataSource.getType().name())
        .active(dataSource.getActive())
        .createdAt(dataSource.getCreatedAt())
        .lastSchemaDiscovery(dataSource.getLastSchemaDiscovery())
        .databaseType(entity.getDatabaseType().name())
        .connectionUrl(entity.getConnectionUrl())
        .driverClass(entity.getDriverClass())
        .username(entity.getUsername())
        .schemaName(entity.getSchemaName())
        .build();
  }

  /** Transform a RestApiSourceEntity to a RestApiSourceResponse. */
  public RestApiSourceResponse toRestApiSourceResponse(
      DataSourceEntity dataSource, RestApiSourceEntity entity) {
    return RestApiSourceResponse.builder()
        .id(dataSource.getId())
        .name(dataSource.getName())
        .description(dataSource.getDescription())
        .type(dataSource.getType().name())
        .active(dataSource.getActive())
        .createdAt(dataSource.getCreatedAt())
        .lastSchemaDiscovery(dataSource.getLastSchemaDiscovery())
        .baseUrl(entity.getBaseUrl())
        .authenticationType(entity.getAuthenticationType().name())
        .username(entity.getUsername())
        .apiKeyName(entity.getApiKeyName())
        .apiKeyLocation(entity.getApiKeyLocation())
        .oauthTokenUrl(entity.getOauthTokenUrl())
        .oauthClientId(entity.getOauthClientId())
        .oauthScope(entity.getOauthScope())
        .requestTimeoutMs(entity.getRequestTimeoutMs())
        .build();
  }

  /** Transform a CreateDatabaseSourceRequest to a DataSourceEntity. */
  public DataSourceEntity toDataSourceEntity(CreateDatabaseSourceRequest request) {
    return DataSourceEntity.builder()
        .name(request.getName())
        .description(request.getDescription())
        .type(DataSourceType.DATABASE)
        .active(true)
        .build();
  }

  /** Transform a CreateDatabaseSourceRequest to a DatabaseSourceEntity. */
  public DatabaseSourceEntity toDatabaseSourceEntity(
      CreateDatabaseSourceRequest request, DataSourceEntity dataSource) {
    return DatabaseSourceEntity.builder()
        .dataSource(dataSource)
        .connectionUrl(request.getConnectionUrl())
        .driverClass(request.getDriverClass())
        .username(request.getUsername())
        .password(request.getPassword())
        .databaseType(DatabaseType.valueOf(request.getDatabaseType()))
        .schemaName(request.getSchemaName())
        .additionalProperties(request.getAdditionalProperties())
        .build();
  }

  /** Transform a CreateRestApiSourceRequest to a DataSourceEntity. */
  public DataSourceEntity toDataSourceEntity(CreateRestApiSourceRequest request) {
    return DataSourceEntity.builder()
        .name(request.getName())
        .description(request.getDescription())
        .type(DataSourceType.REST_API)
        .active(true)
        .build();
  }

  /** Transform a CreateRestApiSourceRequest to a RestApiSourceEntity. */
  public RestApiSourceEntity toRestApiSourceEntity(
      CreateRestApiSourceRequest request, DataSourceEntity dataSource) {
    return RestApiSourceEntity.builder()
        .dataSource(dataSource)
        .baseUrl(request.getBaseUrl())
        .authenticationType(AuthenticationType.valueOf(request.getAuthenticationType()))
        .username(request.getUsername())
        .password(request.getPassword())
        .apiKeyName(request.getApiKeyName())
        .apiKeyValue(request.getApiKeyValue())
        .apiKeyLocation(request.getApiKeyLocation())
        .oauthTokenUrl(request.getOauthTokenUrl())
        .oauthClientId(request.getOauthClientId())
        .oauthClientSecret(request.getOauthClientSecret())
        .oauthScope(request.getOauthScope())
        .headers(request.getHeaders())
        .requestTimeoutMs(request.getRequestTimeoutMs())
        .build();
  }

  /** Transform a SchemaEntity to a SchemaResponse. */
  public SchemaResponse toSchemaResponse(SchemaEntity entity) {
    List<SchemaResponse.SchemaFieldResponse> fields =
        entity.getFields().stream()
            .map(
                field ->
                    SchemaResponse.SchemaFieldResponse.builder()
                        .name(field.getName())
                        .type(field.getType())
                        .nullable(field.isNullable())
                        .description(field.getDescription())
                        .build())
            .collect(Collectors.toList());

    return SchemaResponse.builder()
        .entityName(entity.getEntityName())
        .entityType(entity.getEntityType().name())
        .fields(fields)
        .discoveredAt(entity.getDiscoveredAt())
        .build();
  }

  /** Transform a list of SchemaEntity to a list of SchemaResponse. */
  public List<SchemaResponse> toSchemaResponseList(List<SchemaEntity> entities) {
    return entities.stream().map(this::toSchemaResponse).collect(Collectors.toList());
  }

  /** Get data source configurations for UI form. */
  public List<DataSourceTypeConfig> getDataSourceConfigurations() {
    List<DataSourceTypeConfig> configs = new ArrayList<>();

    // Database configuration
    DataSourceTypeConfig dbConfig = new DataSourceTypeConfig();
    dbConfig.setType("DATABASE");
    dbConfig.setDisplayName("Database");
    dbConfig.setFields(
        Arrays.asList(
            createFormField(
                "databaseType",
                "Database Type",
                "select",
                true,
                Arrays.asList(
                    createSelectOption("MYSQL", "MySQL"),
                    createSelectOption("MSSQL", "Microsoft SQL Server"),
                    createSelectOption("CLICKHOUSE", "ClickHouse")),
                null,
                null),
            createFormField(
                "connectionUrl",
                "Connection URL",
                "text",
                true,
                null,
                null,
                "Example: **************************************** or ************************************"),
            createFormField("username", "Username", "text", false, null, null, null),
            createFormField("password", "Password", "password", false, null, null, null),
            createFormField("schemaName", "Schema Name", "text", false, null, null, null),
            createFormField(
                "driverClass",
                "Driver Class",
                "text",
                false,
                null,
                null,
                "Optional: Will use default driver for selected database type if not specified")));

    // REST API configuration
    DataSourceTypeConfig apiConfig = new DataSourceTypeConfig();
    apiConfig.setType("REST_API");
    apiConfig.setDisplayName("REST API");
    apiConfig.setFields(
        Arrays.asList(
            createFormField(
                "baseUrl",
                "Base URL",
                "text",
                true,
                null,
                null,
                "Example: https://api.example.com"),
            createFormField(
                "authenticationType",
                "Authentication Type",
                "select",
                true,
                Arrays.asList(
                    createSelectOption("NONE", "None"),
                    createSelectOption("BASIC", "Basic Auth"),
                    createSelectOption("API_KEY", "API Key"),
                    createSelectOption("OAUTH2", "OAuth 2.0")),
                null,
                null)));

    // Add conditional fields based on authentication type
    Map<String, Map<String, List<DataSourceTypeConfig.FormField>>> conditionalFields =
        new HashMap<>();

    Map<String, List<DataSourceTypeConfig.FormField>> authTypeConditions = new HashMap<>();

    authTypeConditions.put(
        "BASIC",
        Arrays.asList(
            createFormField("username", "Username", "text", true, null, null, null),
            createFormField("password", "Password", "password", true, null, null, null)));

    authTypeConditions.put(
        "API_KEY",
        Arrays.asList(
            createFormField("apiKeyName", "API Key Name", "text", true, null, null, null),
            createFormField("apiKeyValue", "API Key Value", "text", true, null, null, null),
            createFormField(
                "apiKeyLocation",
                "API Key Location",
                "select",
                true,
                Arrays.asList(
                    createSelectOption("HEADER", "Header"),
                    createSelectOption("QUERY", "Query Parameter")),
                null,
                null)));

    authTypeConditions.put(
        "OAUTH2",
        Arrays.asList(
            createFormField("oauthTokenUrl", "Token URL", "text", true, null, null, null),
            createFormField("oauthClientId", "Client ID", "text", true, null, null, null),
            createFormField(
                "oauthClientSecret", "Client Secret", "password", true, null, null, null),
            createFormField("oauthScope", "Scope", "text", false, null, null, null)));

    conditionalFields.put("authenticationType", authTypeConditions);
    apiConfig.setConditionalFields(conditionalFields);

    configs.add(dbConfig);
    configs.add(apiConfig);

    return configs;
  }

  private DataSourceTypeConfig.FormField createFormField(
      String name,
      String label,
      String type,
      boolean required,
      List<DataSourceTypeConfig.SelectOption> options,
      String defaultValue,
      String helperText) {
    return DataSourceTypeConfig.FormField.builder()
        .name(name)
        .label(label)
        .type(type)
        .required(required)
        .options(options)
        .defaultValue(defaultValue)
        .helperText(helperText)
        .build();
  }

  private DataSourceTypeConfig.SelectOption createSelectOption(String value, String label) {
    return DataSourceTypeConfig.SelectOption.builder().value(value).label(label).build();
  }
}
