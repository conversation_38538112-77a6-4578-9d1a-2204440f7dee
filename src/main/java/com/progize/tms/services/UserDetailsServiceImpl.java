package com.progize.tms.services;

import com.progize.tms.repository.UserRepository;
import com.progize.tms.repository.entity.Role;
import com.progize.tms.repository.entity.User;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

  private final UserRepository userRepository;

  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    User user =
        userRepository
            .findByUsername(username)
            .orElseThrow(
                () -> new UsernameNotFoundException("User not found with username: " + username));

    // Convert roles to Spring Security authorities
    List<SimpleGrantedAuthority> authorities =
        user.getRoles().stream()
            .map(Role::getName)
            .map(
                roleName -> {
                  // Only add ROLE_ prefix if it's not already there
                  String prefix = roleName.startsWith("ROLE_") ? "" : "ROLE_";
                  return new SimpleGrantedAuthority(prefix + roleName);
                })
            .collect(Collectors.toList());

    log.debug(
        "Loaded user {} with roles: {}",
        username,
        user.getRoles().stream().map(Role::getName).collect(Collectors.toList()));

    return org.springframework.security.core.userdetails.User.withUsername(user.getUsername())
        .password(user.getPassword())
        .authorities(authorities)
        .accountExpired(!user.isEnabled())
        .accountLocked(!user.isEnabled())
        .credentialsExpired(!user.isEnabled())
        .disabled(!user.isEnabled())
        .build();
  }
}
