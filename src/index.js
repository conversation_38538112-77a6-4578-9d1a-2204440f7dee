import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { router } from './router';
import App from './App';
import './index.css';
import './styles/index.css';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { ThemeProvider, useThemeContext } from './contexts/ThemeContext';
import { createAppTheme } from './styles/theme';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ThemeProvider>
      <ThemeContextConsumer />
    </ThemeProvider>
  </React.StrictMode>
);

// This component consumes the theme context and provides the MUI theme
function ThemeContextConsumer() {
  const { isDarkMode } = useThemeContext();
  const theme = React.useMemo(
    () => createAppTheme(isDarkMode ? 'dark' : 'light'),
    [isDarkMode]
  );

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      <RouterProvider router={router} />
    </MuiThemeProvider>
  );
}
