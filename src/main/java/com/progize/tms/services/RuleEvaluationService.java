package com.progize.tms.services;

import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.services.impl.ClickHouseAggregationProvider;
import com.progize.tms.services.impl.DynamicAggregationProvider;
import com.progize.tms.services.impl.MockAggregationProvider;
import com.progize.tms.services.models.AggregationRequirement;
import com.progize.tms.services.models.Condition;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.mvel2.MVEL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Unified service for rule evaluation that can be used by both test and production environments.
 */
@Service
@RequiredArgsConstructor
public class RuleEvaluationService {
  private static final Logger log = LoggerFactory.getLogger(RuleEvaluationService.class);

  private final RuleExpressionBuilder ruleExpressionBuilder;
  private final ClickHouseAggregationProvider clickHouseAggregationProvider;
  private final MockAggregationProvider mockAggregationProvider;
  private final DynamicAggregationProvider dynamicAggregationProvider;
  private final RuleVariableService ruleVariableService;
  private final AggregationRegistryService aggregationRegistryService;
  private final DataBucketService dataBucketService;

  /**
   * Evaluate a rule condition with transaction data.
   *
   * @param condition The rule condition to evaluate
   * @param customerId The customer ID
   * @param transactionData The transaction data as a map
   * @param useRealAggregations Whether to use real ClickHouse aggregations or mock data
   * @return true if the rule condition matches
   */
  public boolean evaluateRule(
      Condition condition,
      String customerId,
      Map<String, Object> transactionData,
      boolean useRealAggregations) {
    return evaluateRule(condition, customerId, transactionData, useRealAggregations, null);
  }

  /**
   * Evaluate a rule condition with transaction data and bucket context.
   *
   * @param condition The rule condition to evaluate
   * @param customerId The customer ID
   * @param transactionData The transaction data as a map
   * @param useRealAggregations Whether to use real ClickHouse aggregations or mock data
   * @param bucketId The ID of the data bucket to use for variable context, or null to use global
   *     variables
   * @return true if the rule condition matches
   */
  public boolean evaluateRule(
      Condition condition,
      String customerId,
      Map<String, Object> transactionData,
      boolean useRealAggregations,
      Long bucketId) {

    try {
      log.debug(
          "Evaluating rule for customer: {}, using real aggregations: {}, bucketId: {}",
          customerId,
          useRealAggregations,
          bucketId);

      // Build the MVEL expression with bucket context if provided
      String mvelExpression =
          bucketId != null
              ? ruleExpressionBuilder.buildMvelExpression(condition, bucketId)
              : ruleExpressionBuilder.buildMvelExpression(condition);

      log.debug("Generated MVEL expression: {}", mvelExpression);

      // Create the evaluation context (copy the transaction data)
      Map<String, Object> context = new HashMap<>(transactionData);

      // Add customer ID to context if not already present
      if (!context.containsKey("customerId")) {
        context.put("customerId", customerId);
      }

      // Extract aggregation requirements directly from the condition
      Set<AggregationRequirement> requirements =
          aggregationRegistryService.extractAggregationRequirements(condition);

      log.debug("Extracted {} aggregation requirements", requirements.size());

      // Log more details about the requirements for debugging
      if ("COMPARISON".equals(condition.getType())) {
        log.debug("Processing COMPARISON condition in rule evaluation");
        requirements.forEach(
            req -> {
              log.debug(
                  "Comparison aggregation requirement: function={}, field={}, period={}, entityField={}",
                  req.getFunction(),
                  req.getField(),
                  req.getPeriod(),
                  req.getEntityField());
            });
      }

      // Choose the appropriate aggregation provider
      AggregationProvider aggregationProvider;
      if (!useRealAggregations) {
        // Use mock provider for testing
        aggregationProvider = mockAggregationProvider;
        log.debug("Using mock aggregation provider for testing");
      } else if (bucketId != null) {
        // Use dynamic provider for bucket-specific aggregations
        aggregationProvider = dynamicAggregationProvider;
        log.debug("Using dynamic aggregation provider with bucket ID: {}", bucketId);
      } else {
        // Use ClickHouse provider for global aggregations
        aggregationProvider = clickHouseAggregationProvider;
        log.debug("Using ClickHouse aggregation provider for global aggregations");
      }

      // Get aggregations and add to context, passing the bucketId for bucket-aware aggregation
      Map<String, Object> aggregates =
          aggregationProvider.getAggregations(customerId, requirements, bucketId);

      log.debug("Added {} aggregation values to evaluation context", aggregates.size());

      context.put("aggregates", aggregates);

      // Add variables to context based on bucket or global registry
      if (bucketId != null) {
        // Use bucket-specific variables
        addBucketVariablesToContext(bucketId, context);
      } else {
        // Use global variables from the variable service
        addGlobalVariablesToContext(context);
      }

      // Evaluate with MVEL
      Object result = MVEL.eval(mvelExpression, context);

      if (result instanceof Boolean) {
        boolean evaluationResult = (Boolean) result;
        log.debug("Rule evaluation result: {}", evaluationResult);
        return evaluationResult;
      } else {
        log.warn("Rule expression did not return a boolean result: {}", result);
        return false;
      }
    } catch (Exception e) {
      log.error("Error evaluating rule: {}", e.getMessage(), e);
      return false;
    }
  }

  /**
   * Adds bucket-specific variables to the evaluation context.
   *
   * @param bucketId The ID of the bucket
   * @param context The evaluation context to update
   */
  private void addBucketVariablesToContext(Long bucketId, Map<String, Object> context) {
    try {
      List<DataBucketVariableEntity> bucketVariables =
          dataBucketService.getVariablesWithEnrichment(bucketId, true);
      //      List<DataBucketVariableEntity> bucketVariables =
      // dataBucketService.getVariables(bucketId);

      for (DataBucketVariableEntity variable : bucketVariables) {
        String variableName = variable.getName();
        if (!context.containsKey(variableName)) {
          String dataType = variable.getDataType().getCode();
          context.put(variableName, getDefaultValueForType(dataType));
        }
      }

      log.debug("Added {} bucket variables to evaluation context", bucketVariables.size());
    } catch (Exception e) {
      log.warn("Error adding bucket variables to context: {}", e.getMessage());
    }
  }

  /**
   * Adds global variables to the evaluation context.
   *
   * @param context The evaluation context to update
   */
  private void addGlobalVariablesToContext(Map<String, Object> context) {
    ruleVariableService
        .getAllVariables()
        .forEach(
            variable -> {
              if (!context.containsKey(variable.getId())) {
                context.put(variable.getId(), getDefaultValueForType(variable.getType()));
              }
            });

    log.debug("Added global variables to evaluation context");
  }

  /**
   * Generates a default value for a variable based on its type.
   *
   * @param type The variable type
   * @return A default value appropriate for the type
   */
  private Object getDefaultValueForType(String type) {
    if (type == null) {
      return null;
    }

    switch (type.toLowerCase()) {
      case "string":
        return "";
      case "number":
      case "bigdecimal":
        return new BigDecimal("0");
      case "integer":
      case "int":
        return 0;
      case "long":
        return 0L;
      case "double":
      case "float":
        return 0.0;
      case "boolean":
        return false;
      case "date":
        return new Date();
      default:
        return null;
    }
  }
}
