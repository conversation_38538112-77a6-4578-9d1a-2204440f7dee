import React, { memo } from 'react';
import { getBezierPath, EdgeLabelRenderer } from 'reactflow';
import { Chip, alpha, useTheme } from '@mui/material';
import { ArrowForward as ArrowForwardIcon } from '@mui/icons-material';

const TransitionEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  selected,
  markerEnd
}) => {
  const theme = useTheme();
  
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition
  });
  
  const edgeColor = selected 
    ? theme.palette.primary.main 
    : theme.palette.text.secondary;
  
  return (
    <>
      <path
        id={id}
        style={{
          ...style,
          strokeWidth: selected ? 3 : 2,
          stroke: edgeColor
        }}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
      />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            pointerEvents: 'all',
            background: alpha(theme.palette.background.paper, 0.9),
            padding: 4,
            borderRadius: 16,
            fontSize: 12,
            fontWeight: selected ? 'bold' : 'normal',
            border: selected ? `1px solid ${theme.palette.primary.main}` : 'none',
            boxShadow: selected ? theme.shadows[3] : 'none',
            zIndex: 1000
          }}
        >
          <Chip
            label={data?.name || 'Transition'}
            size="small"
            color={selected ? 'primary' : 'default'}
            variant={selected ? 'filled' : 'outlined'}
            icon={<ArrowForwardIcon fontSize="small" />}
          />
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

export default memo(TransitionEdge);
