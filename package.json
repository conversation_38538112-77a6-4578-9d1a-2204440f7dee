{"name": "sonique-rule-configurator", "version": "0.1.0", "private": true, "proxy": "http://localhost:8081", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "ace-builds": "^1.40.0", "axios": "^1.8.4", "bootstrap": "^5.3.5", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "react": "^18.3.1", "react-ace": "^14.0.1", "react-bootstrap": "^2.10.9", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "react-spring": "^9.7.5", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.5", "reactflow": "^11.11.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}