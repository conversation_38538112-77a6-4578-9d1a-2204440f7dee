import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Grid,
  Button,
  IconButton,
  Tooltip,
  Skeleton,
  Alert,
  useTheme,
  Divider,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  Avatar,
  Chip
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  AccountBalance as AccountBalanceIcon,
  Gavel as GavelIcon,
  Timeline as TimelineIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  TrendingUp as EscalateIcon,
  Assignment as AssignmentIcon,
  Comment as CommentIcon,
  AttachFile as AttachFileIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';

/**
 * Comprehensive case detail view for compliance managers/admins
 * Shows complete case information, transaction details, customer info, and case history timeline
 */
const ComplianceCaseDetail = () => {
  const { caseId } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const [caseData, setCaseData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Sample comprehensive case data
  const sampleCaseData = {
    id: 'CASE-2024-001',
    customerId: 'CUST-12345',
    customerName: 'Acme Corporation Ltd',
    customerType: 'Corporate',
    customerRiskProfile: 'Medium',
    customerNationality: 'United States',
    customerKycStatus: 'Verified',
    
    // Transaction Details
    transactionId: 'TXN-789012',
    transactionAmount: 250000,
    transactionCurrency: 'USD',
    transactionDate: '2024-01-15T10:30:00Z',
    transactionType: 'Wire Transfer',
    sourceAccount: 'ACC-001-ACME',
    destinationAccount: 'ACC-002-GLOBAL',
    
    // Rule and Risk Information
    ruleTriggered: 'High Transaction Amount Alert',
    ruleId: 'RULE-001',
    ruleDescription: 'Transactions exceeding $200,000 require additional review',
    riskScore: 85,
    riskFactors: ['High Amount', 'Cross-Border', 'New Beneficiary'],
    
    // Case Management
    currentStatus: 'Under Review',
    priority: 'High',
    assignedOfficer: 'John Smith',
    assignedOfficerId: 'OFF-001',
    createdDate: '2024-01-15T10:35:00Z',
    lastUpdated: '2024-01-16T14:20:00Z',
    slaDeadline: '2024-01-17T18:00:00Z',
    escalationLevel: 'L1',
    
    // Case History Timeline
    history: [
      {
        id: 1,
        timestamp: '2024-01-15T10:35:00Z',
        action: 'Case Created',
        actor: 'System',
        actorType: 'system',
        description: 'Case automatically created due to rule trigger: High Transaction Amount Alert',
        details: {
          ruleTriggered: 'High Transaction Amount Alert',
          transactionAmount: 250000
        }
      },
      {
        id: 2,
        timestamp: '2024-01-15T11:00:00Z',
        action: 'Case Assigned',
        actor: 'Sarah Manager',
        actorType: 'manager',
        description: 'Case assigned to compliance officer John Smith',
        details: {
          assignedTo: 'John Smith',
          assignedBy: 'Sarah Manager'
        }
      },
      {
        id: 3,
        timestamp: '2024-01-15T14:30:00Z',
        action: 'Initial Review',
        actor: 'John Smith',
        actorType: 'officer',
        description: 'Officer started initial review and requested additional documentation',
        details: {
          status: 'Under Review',
          documentsRequested: ['Bank Statements', 'Source of Funds Declaration']
        }
      },
      {
        id: 4,
        timestamp: '2024-01-16T09:15:00Z',
        action: 'Documentation Received',
        actor: 'Customer Service',
        actorType: 'system',
        description: 'Customer provided requested documentation',
        details: {
          documentsReceived: ['Bank Statement Q4 2023.pdf', 'SOF Declaration.pdf']
        }
      },
      {
        id: 5,
        timestamp: '2024-01-16T14:20:00Z',
        action: 'Officer Comment',
        actor: 'John Smith',
        actorType: 'officer',
        description: 'Added review comments and marked for manager approval',
        details: {
          comment: 'Documentation appears legitimate. Transaction is for equipment purchase from verified supplier. Recommend approval.',
          recommendation: 'Approve'
        }
      }
    ]
  };

  useEffect(() => {
    const fetchCaseData = async () => {
      try {
        setLoading(true);
        // For now, use sample data. Later replace with actual API call
        // const caseDetails = await ComplianceService.getCaseDetails(caseId);
        setCaseData(sampleCaseData);
        setError(null);
      } catch (err) {
        console.error('Error fetching case details:', err);
        setError('Failed to load case details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCaseData();
  }, [caseId]);

  // Helper functions
  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch(status?.toLowerCase()) {
      case 'approved':
        return theme.palette.success.main;
      case 'closed - false positive':
        return theme.palette.info.main;
      case 'under review':
        return theme.palette.warning.main;
      case 'escalated':
        return theme.palette.error.main;
      case 'open':
        return theme.palette.primary.main;
      case 'rejected':
        return theme.palette.error.dark;
      default:
        return theme.palette.grey[500];
    }
  };

  const getRiskScoreColor = (score) => {
    if (score >= 80) return theme.palette.error.main;
    if (score >= 60) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getTimelineIcon = (action) => {
    switch(action.toLowerCase()) {
      case 'case created':
        return <AssignmentIcon />;
      case 'case assigned':
        return <PersonIcon />;
      case 'initial review':
        return <VisibilityIcon />;
      case 'documentation received':
        return <AttachFileIcon />;
      case 'officer comment':
        return <CommentIcon />;
      case 'escalated':
        return <EscalateIcon />;
      case 'approved':
        return <CheckCircleIcon />;
      case 'rejected':
        return <ErrorIcon />;
      default:
        return <TimelineIcon />;
    }
  };

  const getTimelineDotColor = (action, actorType) => {
    if (actorType === 'system') return theme.palette.info.main;
    if (actorType === 'manager') return theme.palette.primary.main;
    if (actorType === 'officer') return theme.palette.success.main;
    
    switch(action.toLowerCase()) {
      case 'approved':
        return theme.palette.success.main;
      case 'rejected':
        return theme.palette.error.main;
      case 'escalated':
        return theme.palette.warning.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Loading state
  if (loading) {
    return (
      <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
        <Skeleton variant="text" width={300} height={40} sx={{ mb: 2 }} />
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Skeleton variant="rectangular" width="100%" height={300} sx={{ borderRadius: 1, mb: 2 }} />
            <Skeleton variant="rectangular" width="100%" height={400} sx={{ borderRadius: 1 }} />
          </Grid>
          <Grid item xs={12} md={4}>
            <Skeleton variant="rectangular" width="100%" height={500} sx={{ borderRadius: 1 }} />
          </Grid>
        </Grid>
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button 
          variant="outlined" 
          onClick={() => navigate('/compliance/cases')}
          startIcon={<ArrowBackIcon />}
        >
          Back to Cases
        </Button>
      </Box>
    );
  }

  // No case found
  if (!caseData) {
    return (
      <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          Case not found or has been removed.
        </Alert>
        <Button 
          variant="outlined" 
          onClick={() => navigate('/compliance/cases')}
          startIcon={<ArrowBackIcon />}
        >
          Back to Cases
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
      {/* Header with back button */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Tooltip title="Back to Cases">
          <IconButton
            onClick={() => navigate('/compliance/cases')}
            sx={{ mr: 2 }}
          >
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h5" sx={{ fontWeight: 600, fontSize: '1.5rem' }}>
            Case Details: {caseData.id}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.9rem' }}>
            Created {formatDateTime(caseData.createdDate)} • Last updated {formatDateTime(caseData.lastUpdated)}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Box 
            sx={{ 
              px: 1.5, 
              py: 0.5, 
              bgcolor: getStatusColor(caseData.currentStatus) + '20',
              color: getStatusColor(caseData.currentStatus),
              borderRadius: 0.5,
              fontSize: '0.8rem',
              fontWeight: 500
            }}
          >
            {caseData.currentStatus}
          </Box>
          <Box 
            sx={{ 
              px: 1.5, 
              py: 0.5, 
              bgcolor: caseData.priority === 'High' ? theme.palette.error.light + '20' : theme.palette.warning.light + '20',
              color: caseData.priority === 'High' ? theme.palette.error.main : theme.palette.warning.main,
              borderRadius: 0.5,
              fontSize: '0.8rem',
              fontWeight: 600,
              textTransform: 'uppercase'
            }}
          >
            {caseData.priority} Priority
          </Box>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Main Content */}
        <Grid item xs={12} md={8}>
          {/* Transaction Information */}
          <Paper
            elevation={0}
            sx={{
              mb: 3,
              p: 2,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <AccountBalanceIcon sx={{ fontSize: '1.2rem', color: theme.palette.primary.main }} />
              <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1.1rem' }}>
                Transaction Details
              </Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    TRANSACTION ID
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.transactionId}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    AMOUNT
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1.2rem', mt: 0.5, color: theme.palette.primary.main }}>
                    {formatCurrency(caseData.transactionAmount, caseData.transactionCurrency)}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    DATE & TIME
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {formatDateTime(caseData.transactionDate)}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    TYPE
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.transactionType}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    SOURCE ACCOUNT
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.sourceAccount}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    DESTINATION ACCOUNT
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.destinationAccount}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Customer Information */}
          <Paper
            elevation={0}
            sx={{
              mb: 3,
              p: 2,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <BusinessIcon sx={{ fontSize: '1.2rem', color: theme.palette.primary.main }} />
              <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1.1rem' }}>
                Customer Information
              </Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    CUSTOMER ID
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.customerId}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    NAME
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.customerName}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    TYPE
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.customerType}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    RISK PROFILE
                  </Typography>
                  <Box sx={{ mt: 0.5 }}>
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.25,
                        bgcolor: caseData.customerRiskProfile === 'High' ? theme.palette.error.light + '20' : theme.palette.warning.light + '20',
                        color: caseData.customerRiskProfile === 'High' ? theme.palette.error.main : theme.palette.warning.main,
                        borderRadius: 0.5,
                        fontSize: '0.75rem',
                        fontWeight: 600
                      }}
                    >
                      {caseData.customerRiskProfile}
                    </Box>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    NATIONALITY
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {caseData.customerNationality}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    KYC STATUS
                  </Typography>
                  <Box sx={{ mt: 0.5 }}>
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.25,
                        bgcolor: theme.palette.success.light + '20',
                        color: theme.palette.success.main,
                        borderRadius: 0.5,
                        fontSize: '0.75rem',
                        fontWeight: 600
                      }}
                    >
                      {caseData.customerKycStatus}
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Sidebar - Case Timeline */}
        <Grid item xs={12} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1,
              height: 'fit-content'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <TimelineIcon sx={{ fontSize: '1.2rem', color: theme.palette.primary.main }} />
              <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1.1rem' }}>
                Case History
              </Typography>
            </Box>

            {/* Case Management Info */}
            <Box sx={{ mb: 3, p: 1.5, bgcolor: theme.palette.grey[50], borderRadius: 1 }}>
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                    ASSIGNED OFFICER
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 500 }}>
                    {caseData.assignedOfficer}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 500 }}>
                    SLA DEADLINE
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 500 }}>
                    {formatDateTime(caseData.slaDeadline)}
                  </Typography>
                </Grid>
              </Grid>
            </Box>

            {/* Timeline */}
            <Timeline sx={{ p: 0, m: 0 }}>
              {caseData.history.map((event, index) => (
                <TimelineItem key={event.id} sx={{ minHeight: 'auto', '&:before': { display: 'none' } }}>
                  <TimelineSeparator>
                    <TimelineDot
                      sx={{
                        bgcolor: getTimelineDotColor(event.action, event.actorType),
                        width: 32,
                        height: 32,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      {React.cloneElement(getTimelineIcon(event.action), {
                        sx: { fontSize: '1rem', color: 'white' }
                      })}
                    </TimelineDot>
                    {index < caseData.history.length - 1 && (
                      <TimelineConnector sx={{ bgcolor: theme.palette.grey[300] }} />
                    )}
                  </TimelineSeparator>

                  <TimelineContent sx={{ pb: 2 }}>
                    <Box sx={{ ml: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.85rem' }}>
                        {event.action}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                        {formatDateTime(event.timestamp)} • {event.actor}
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.8rem', mt: 0.5, lineHeight: 1.4 }}>
                        {event.description}
                      </Typography>

                      {/* Event Details */}
                      {event.details && (
                        <Box sx={{ mt: 1, p: 1, bgcolor: theme.palette.grey[50], borderRadius: 0.5 }}>
                          {event.details.comment && (
                            <Typography variant="caption" sx={{ fontSize: '0.7rem', fontStyle: 'italic' }}>
                              "{event.details.comment}"
                            </Typography>
                          )}
                          {event.details.documentsRequested && (
                            <Box sx={{ mt: 0.5 }}>
                              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                                Documents Requested:
                              </Typography>
                              {event.details.documentsRequested.map((doc, idx) => (
                                <Typography key={idx} variant="caption" sx={{ fontSize: '0.7rem', display: 'block', ml: 1 }}>
                                  • {doc}
                                </Typography>
                              ))}
                            </Box>
                          )}
                          {event.details.documentsReceived && (
                            <Box sx={{ mt: 0.5 }}>
                              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                                Documents Received:
                              </Typography>
                              {event.details.documentsReceived.map((doc, idx) => (
                                <Typography key={idx} variant="caption" sx={{ fontSize: '0.7rem', display: 'block', ml: 1 }}>
                                  • {doc}
                                </Typography>
                              ))}
                            </Box>
                          )}
                          {event.details.recommendation && (
                            <Box sx={{ mt: 0.5 }}>
                              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                                Recommendation:
                              </Typography>
                              <Typography variant="caption" sx={{ fontSize: '0.7rem', fontWeight: 600, ml: 0.5 }}>
                                {event.details.recommendation}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      )}
                    </Box>
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ComplianceCaseDetail;
