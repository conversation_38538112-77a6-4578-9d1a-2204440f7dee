import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useThemeContext } from '../../contexts/ThemeContext';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  IconButton,
  Chip,
  Tooltip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Grid,
  Avatar,
  useTheme,
  alpha,
  Divider
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import NetworkCheckIcon from '@mui/icons-material/NetworkCheck';
import StorageIcon from '@mui/icons-material/Storage';
import ApiIcon from '@mui/icons-material/Api';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import dataSourceService from '../../services/dataSourceService';

/**
 * Component to display a list of data sources
 */
const DataSourceList = () => {
  const navigate = useNavigate();
  const { isDarkMode } = useThemeContext();
  const [dataSources, setDataSources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [dataSourceToDelete, setDataSourceToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Get color scheme for progize branding
  const getProgizeColor = (variant = 'primary') => {
    const colors = {
      primary: isDarkMode ? '#63B3ED' : '#3182CE', // Blue
      secondary: isDarkMode ? '#68D391' : '#38A169', // Green
      accent: isDarkMode ? '#F6AD55' : '#DD6B20', // Orange
      light: isDarkMode ? '#2D3748' : '#F7FAFC', // Background
      dark: isDarkMode ? '#E2E8F0' : '#1A202C', // Text
    };
    return colors[variant] || colors.primary;
  };

  // Load data sources on component mount
  useEffect(() => {
    fetchDataSources();
  }, []);

  // Fetch data sources from API
  const fetchDataSources = async () => {
    try {
      setLoading(true);
      const data = await dataSourceService.getAllDataSources();
      setDataSources(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching data sources:', err);
      setError('Failed to load data sources. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (dataSource) => {
    setDataSourceToDelete(dataSource);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      await dataSourceService.deleteDataSource(dataSourceToDelete.id);
      setSnackbar({
        open: true,
        message: `Data source "${dataSourceToDelete.name}" deleted successfully`,
        severity: 'success'
      });
      fetchDataSources(); // Refresh the list
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Failed to delete data source: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setDeleteDialogOpen(false);
      setDataSourceToDelete(null);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setDataSourceToDelete(null);
  };

  // Handle test connection
  const handleTestConnection = async (id) => {
    try {
      setLoading(true);
      const result = await dataSourceService.testConnection(id);
      setSnackbar({
        open: true,
        message: result.success
          ? 'Connection successful!'
          : 'Connection failed. Please check your configuration.',
        severity: result.success ? 'success' : 'error'
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Connection test failed: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Render loading state
  if (loading && dataSources.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error && dataSources.length === 0) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  const theme = useTheme();

  // Function to get icon based on data source type
  const getTypeIcon = (type) => {
    switch(type) {
      case 'DATABASE':
        return <StorageIcon />;
      case 'REST_API':
        return <ApiIcon />;
      default:
        return <StorageIcon />;
    }
  };

  // Function to format date in a more readable way
  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <Box>
      {/* Header with title and add button */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          mb: 3,
          pb: 2,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 600,
              color: theme.palette.primary.main,
              mb: 1
            }}
          >
            Data Sources
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              Manage your data sources for enriching rule buckets. Connect to databases or REST APIs to use their data in your rules.
            </Typography>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                height: '24px',
                px: 1.5,
                py: 0.5,
                ml: 2,
                borderRadius: '16px',
                bgcolor: '#0288D1', // Blue background
                color: '#FFFFFF', // White text
                fontSize: '13px',
                fontWeight: 600,
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}
            >
              {dataSources.length} sources
            </Box>
          </Box>
        </Box>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => navigate('/data-sources/new')}
          sx={{
            borderRadius: '28px',
            px: 3,
            py: 1,
            boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
            bgcolor: getProgizeColor ? getProgizeColor() : theme.palette.primary.main,
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0 6px 20px rgba(0,118,255,0.39)',
              bgcolor: getProgizeColor ? alpha(getProgizeColor(), 1.2) : theme.palette.primary.dark
            }
          }}
        >
          Add Data Source
        </Button>
      </Box>

      {/* Data sources grid */}
      {dataSources.length === 0 ? (
        <Paper
          sx={{
            p: 4,
            textAlign: 'center',
            borderRadius: 2,
            bgcolor: alpha(getProgizeColor(), 0.03),
            border: `1px dashed ${alpha(getProgizeColor(), 0.2)}`
          }}
        >
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No data sources found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Create your first data source to start enriching your rule buckets
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/data-sources/new')}
            sx={{
              borderRadius: '28px',
              px: 3,
              py: 1,
              boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
              bgcolor: getProgizeColor(),
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: '0 6px 20px rgba(0,118,255,0.39)',
                bgcolor: alpha(getProgizeColor(), 1.2)
              }
            }}
          >
            Add Data Source
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {dataSources.map((dataSource) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={dataSource.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: '10px',
                  overflow: 'hidden',
                  transition: 'all 0.3s ease',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 2px 8px -4px rgba(0,0,0,0.2), 0 4px 16px -8px rgba(0,0,0,0.4)'
                    : '0 2px 8px -4px rgba(0,0,0,0.05), 0 4px 16px -8px rgba(0,0,0,0.04)',
                  bgcolor: theme.palette.background.paper,
                  position: 'relative',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 10px 15px -3px rgba(0,0,0,0.3), 0 4px 6px -2px rgba(0,0,0,0.2)'
                      : '0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05)'
                  }
                }}
              >
                <CardContent sx={{ p: 0, flexGrow: 1 }}>
                  {/* Header with name, type, status and connection info */}
                  <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}` }}>
                    <Box>
                      <Typography variant="h6" component="h2" sx={{ fontWeight: 500, fontSize: '1rem' }}>
                        {dataSource.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                        <Box
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            height: '24px',
                            px: 1.5,
                            py: 0.5,
                            mr: 1,
                            borderRadius: '16px',
                            bgcolor: dataSource.type === 'DATABASE'
                              ? theme.palette.mode === 'dark' ? '#1565C0' : '#0288D1'
                              : theme.palette.mode === 'dark' ? '#E65100' : '#F57C00',
                            color: '#FFFFFF', // White text for contrast
                            fontSize: '13px',
                            fontWeight: 600,
                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                          }}
                        >
                          {dataSource.type === 'DATABASE' ? <StorageIcon sx={{ fontSize: '14px', mr: 0.5 }} /> : <ApiIcon sx={{ fontSize: '14px', mr: 0.5 }} />}
                          {dataSource.type === 'DATABASE' ? 'Database' : 'REST API'}
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {dataSource.type === 'DATABASE' ? 'Database Connection' : 'REST API Connection'}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Status - Right aligned */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 0.5 }}>
                      <Box
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: '22px',
                          px: 1,
                          py: 0.5,
                          borderRadius: 0, // Rectangular shape
                          bgcolor: dataSource.active
                            ? theme.palette.mode === 'dark' ? alpha(theme.palette.success.main, 0.2) : '#E8F5E9'
                            : theme.palette.mode === 'dark' ? alpha(theme.palette.grey[700], 0.3) : '#FAFAFA',
                          color: dataSource.active
                            ? theme.palette.mode === 'dark' ? theme.palette.success.light : '#2E7D32'
                            : theme.palette.mode === 'dark' ? theme.palette.grey[400] : '#757575',
                          fontSize: '12px',
                          fontWeight: 500,
                          boxShadow: theme.palette.mode === 'dark' ? 'none' : '0 1px 2px rgba(0, 0, 0, 0.05)'
                        }}
                      >
                        {dataSource.active && (
                          <Box
                            component="span"
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: '#2E7D32',
                              display: 'inline-block',
                              mr: 0.5
                            }}
                          />
                        )}
                        {dataSource.active ? 'Active' : 'Inactive'}
                      </Box>
                    </Box>
                  </Box>

                  {/* Description section */}
                  <Box sx={{ px: 2, pt: 2, pb: 1, display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      {dataSource.description || 'No description provided'}
                    </Typography>
                  </Box>

                  {/* Footer section */}
                  <Box sx={{ px: 2, pt: 0, pb: 2, flexGrow: 1 }}>

                    {/* Last discovery info */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                      <Box
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: '22px',
                          px: 1,
                          py: 0.5,
                          borderRadius: 0, // Rectangular shape
                          bgcolor: '#F3F4F6', // Light grey background
                          color: '#616161', // Medium grey text
                          fontSize: '12px',
                          fontWeight: 500,
                          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                        }}
                      >
                        <CalendarTodayIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                        Last discovery: {formatDate(dataSource.lastSchemaDiscovery)}
                      </Box>
                    </Box>

                    {/* Action buttons */}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 0.5 }}>
                      <Tooltip title="View Schema">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => navigate(`/data-sources/${dataSource.id}`)}
                          sx={{
                            p: 0.75,
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) }
                          }}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Connection">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => navigate(`/data-sources/${dataSource.id}/edit`)}
                          sx={{
                            p: 0.75,
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) }
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Reconnect to Database">
                        <IconButton
                          size="small"
                          color="info"
                          onClick={() => handleTestConnection(dataSource.id)}
                          sx={{
                            p: 0.75,
                            bgcolor: alpha(theme.palette.info.main, 0.05),
                            '&:hover': { bgcolor: alpha(theme.palette.info.main, 0.1) }
                          }}
                        >
                          <NetworkCheckIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Connection">
                        <IconButton
                          size="small"
                          sx={{
                            p: 0.75,
                            bgcolor: alpha(theme.palette.error.main, 0.05),
                            color: theme.palette.error.main,
                            '&:hover': {
                              bgcolor: alpha(theme.palette.error.main, 0.1),
                              color: theme.palette.error.main
                            }
                          }}
                          onClick={() => handleDeleteClick(dataSource)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                </CardContent>

                {/* Bottom strip with gradient */}
                <Box sx={{
                  height: '3px',
                  width: '100%',
                  background: dataSource.type === 'DATABASE'
                    ? theme.palette.mode === 'dark'
                      ? 'linear-gradient(90deg, rgba(79, 172, 254, 0.6) 0%, rgba(0, 242, 254, 0.5) 100%)' // Brighter blue gradient for dark mode
                      : 'linear-gradient(90deg, rgba(79, 172, 254, 0.4) 0%, rgba(0, 242, 254, 0.3) 100%)' // Blue gradient for light mode
                    : theme.palette.mode === 'dark'
                      ? 'linear-gradient(90deg, rgba(240, 147, 251, 0.6) 0%, rgba(245, 87, 108, 0.5) 100%)' // Brighter pink/purple gradient for dark mode
                      : 'linear-gradient(90deg, rgba(240, 147, 251, 0.4) 0%, rgba(245, 87, 108, 0.3) 100%)' // Pink/Purple gradient for light mode
                }} />
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the data source "{dataSourceToDelete?.name}"?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DataSourceList;
