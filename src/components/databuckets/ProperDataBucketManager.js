import React, { useState, useEffect } from 'react';
import {
    Box,
    CircularProgress,
    Alert,
    TextField,
    Paper,
    Grid,
    Tabs,
    Tab,
    InputAdornment,
    useTheme
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { dataBucketService } from '../../services/api';
import DataBucketsPanel from '../rulevariables/DataBucketsPanel';
import ProperBucketCreator from './ProperBucketCreator';
import ProperBucketEditor from './ProperBucketEditor';
import ProperVariableManager from './ProperVariableManager';

const ProperDataBucketManager = () => {
    const theme = useTheme();
    const [buckets, setBuckets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [filterTab, setFilterTab] = useState(0); // 0: All, 1: Rule Buckets, 2: Lookup Buckets

    // View states
    const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'variables'
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [createDrawerOpen, setCreateDrawerOpen] = useState(false);

    useEffect(() => {
        fetchBuckets();
    }, []);

    const fetchBuckets = async () => {
        try {
            setLoading(true);
            const data = await dataBucketService.getAllBuckets();
            setBuckets(Array.isArray(data) ? data : []);
            setError(null);
        } catch (err) {
            setError('Failed to load data buckets. Please try again later.');
            console.error('Error fetching buckets:', err);
            setBuckets([]);
        } finally {
            setLoading(false);
        }
    };

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    const handleTabChange = (event, newValue) => {
        setFilterTab(newValue);
    };

    // Filter buckets based on search query and tab selection
    const filteredBuckets = (() => {
        let filtered = [...buckets];

        // Apply tab filter
        switch (filterTab) {
            case 1: // Rule Buckets
                filtered = filtered.filter(bucket => bucket.bucketType === 'RULE_BUCKET' || bucket.type === 'RULE_BUCKET');
                break;
            case 2: // Lookup Buckets
                filtered = filtered.filter(bucket => bucket.bucketType === 'LOOKUP' || bucket.type === 'LOOKUP');
                break;
            default: // All
                break;
        }

        // Apply search filter
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase().trim();
            filtered = filtered.filter(bucket =>
                bucket.name.toLowerCase().includes(query) ||
                bucket.description?.toLowerCase().includes(query) ||
                bucket.tableName?.toLowerCase().includes(query)
            );
        }

        return filtered;
    })();

    const handleCreateBucket = () => {
        setCurrentView('create');
    };

    const handleEditBucket = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('edit');
    };

    const handleManageVariables = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('variables');
    };

    const handleBackToList = () => {
        setCurrentView('list');
        setSelectedBucket(null);
        fetchBuckets(); // Refresh the list
    };

    const handleBucketCreated = (newBucket) => {
        setBuckets(prev => [...prev, newBucket]);
        setCurrentView('list');
    };

    const handleBucketUpdated = (updatedBucket) => {
        setBuckets(prev => prev.map(bucket =>
            bucket.id === updatedBucket.id ? updatedBucket : bucket
        ));
        setCurrentView('list');
    };

    // Custom DataBucketsPanel with search and wizard integration
    const CustomDataBucketsPanel = () => {
        return (
            <Box>
                {/* Search and Filters - Exact copy from compliance cases */}
                <Paper
                    elevation={0}
                    sx={{
                        mb: 2,
                        p: 2,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 1
                    }}
                >
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} md={6}>
                            <TextField
                                fullWidth
                                size="small"
                                placeholder="Search by bucket name, description, or table..."
                                value={searchQuery}
                                onChange={handleSearchChange}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon sx={{ color: theme.palette.text.secondary, fontSize: '1.2rem' }} />
                                        </InputAdornment>
                                    ),
                                }}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        fontSize: '0.875rem',
                                        '& fieldset': {
                                            borderColor: theme.palette.grey[300]
                                        }
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
                                <Tabs
                                    value={filterTab}
                                    onChange={handleTabChange}
                                    sx={{
                                        minHeight: 36,
                                        '& .MuiTab-root': {
                                            minHeight: 36,
                                            fontSize: '0.8rem',
                                            fontWeight: 500,
                                            textTransform: 'none',
                                            color: theme.palette.text.secondary,
                                            '&.Mui-selected': {
                                                color: theme.palette.primary.main,
                                                fontWeight: 600
                                            }
                                        },
                                        '& .MuiTabs-indicator': {
                                            height: 2
                                        }
                                    }}
                                >
                                    <Tab label="All" />
                                    <Tab label="Rule Buckets" />
                                    <Tab label="Lookup Buckets" />
                                </Tabs>
                            </Box>
                        </Grid>
                    </Grid>
                </Paper>

                {/* DataBucketsPanel with custom create handler */}
                <DataBucketsPanel
                    initialBuckets={filteredBuckets}
                    createDrawerOpen={createDrawerOpen}
                    setCreateDrawerOpen={(open) => {
                        if (open) {
                            handleCreateBucket(); // Use our wizard instead
                        } else {
                            setCreateDrawerOpen(false);
                        }
                    }}
                    onEditBucket={handleEditBucket}
                    onManageVariables={handleManageVariables}
                />
            </Box>
        );
    };

    const renderContent = () => {
        switch (currentView) {
            case 'create':
                return (
                    <ProperBucketCreator
                        onCancel={handleBackToList}
                        onBucketCreated={handleBucketCreated}
                    />
                );

            case 'edit':
                return (
                    <ProperBucketEditor
                        bucket={selectedBucket}
                        onCancel={handleBackToList}
                        onBucketUpdated={handleBucketUpdated}
                    />
                );

            case 'variables':
                return (
                    <ProperVariableManager
                        bucket={selectedBucket}
                        onBack={handleBackToList}
                    />
                );

            default:
                return <CustomDataBucketsPanel />;
        }
    };

    // Match the exact structure of RuleVariableRegistryPage
    return (
        <Box sx={{ width: '100%', height: '100%', backgroundColor: 'transparent' }}>
            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
            )}

            {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                    <CircularProgress />
                </Box>
            ) : (
                renderContent()
            )}
        </Box>
    );
};

export default ProperDataBucketManager;
