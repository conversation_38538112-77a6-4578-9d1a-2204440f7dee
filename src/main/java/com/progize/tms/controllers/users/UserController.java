package com.progize.tms.controllers.users;

import com.progize.tms.controllers.users.req.CreateUserRequest;
import com.progize.tms.controllers.users.req.UpdateUserRequest;
import com.progize.tms.controllers.users.res.UserResponse;
import com.progize.tms.repository.entity.User;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class UserController {

  private final UserService userService;
  private final UserTransformer userTransformer;

  @GetMapping
  public ResponseEntity<List<UserResponse>> getAllUsers() {
    return ResponseEntity.ok(userTransformer.toResponseList(userService.getAllUsers()));
  }

  @GetMapping("/{id}")
  public ResponseEntity<UserResponse> getUserById(@PathVariable Long id) {
    return ResponseEntity.ok(userTransformer.toResponse(userService.getUserById(id)));
  }

  @PostMapping
  public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
    User user = userService.createUser(userTransformer.toEntity(request));
    return ResponseEntity.ok(userTransformer.toResponse(user));
  }

  @PutMapping("/{id}")
  public ResponseEntity<UserResponse> updateUser(
      @PathVariable Long id, @Valid @RequestBody UpdateUserRequest request) {
    return ResponseEntity.ok(
        userTransformer.toResponse(userService.updateUser(id, userTransformer.toEntity(request))));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
    userService.deleteUser(id);
    return ResponseEntity.noContent().build();
  }

  @GetMapping("/{id}/roles")
  public ResponseEntity<UserResponse> getUserRoles(@PathVariable Long id) {
    User user = userService.getUserById(id);
    return ResponseEntity.ok(userTransformer.toResponse(user));
  }

  @PostMapping("/{userId}/roles/{roleId}")
  public ResponseEntity<UserResponse> assignRoleToUser(
      @PathVariable Long userId, @PathVariable Long roleId) {
    User user = userService.assignRoleToUser(userId, roleId);
    return ResponseEntity.ok(userTransformer.toResponse(user));
  }

  @DeleteMapping("/{userId}/roles/{roleId}")
  public ResponseEntity<UserResponse> removeRoleFromUser(
      @PathVariable Long userId, @PathVariable Long roleId) {
    User user = userService.removeRoleFromUser(userId, roleId);
    return ResponseEntity.ok(userTransformer.toResponse(user));
  }

  @PutMapping("/{id}/roles")
  public ResponseEntity<UserResponse> setUserRoles(
      @PathVariable Long id, @RequestBody Set<Long> roleIds) {
    User user = userService.setUserRoles(id, roleIds);
    return ResponseEntity.ok(userTransformer.toResponse(user));
  }
}
