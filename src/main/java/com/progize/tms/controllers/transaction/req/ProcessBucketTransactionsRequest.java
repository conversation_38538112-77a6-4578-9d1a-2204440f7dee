package com.progize.tms.controllers.transaction.req;

import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * Request model for processing transactions with a specific data bucket. This allows for dynamic
 * data structures based on the bucket's variables.
 */
@Data
public class ProcessBucketTransactionsRequest {
  /** The ID of the data bucket that defines the structure of the transaction data */
  private Long bucketId;

  /**
   * List of transaction data maps. Each map contains variable names as keys and their values. The
   * variable names must match those defined in the specified bucket.
   */
  private List<Map<String, Object>> transactions;
}
