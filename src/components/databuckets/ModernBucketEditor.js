import React, { useState, useEffect } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Button,
    Grid,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Switch,
    FormControlLabel,
    Chip,
    Avatar,
    Divider,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Tabs,
    Tab,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    ListItemSecondaryAction,
    Paper,
    Alert,
    LinearProgress,
    useTheme,
    alpha
} from '@mui/material';
import {
    Edit as EditIcon,
    Save as SaveIcon,
    Cancel as CancelIcon,
    Storage as StorageIcon,
    DataObject as DataObjectIcon,
    Settings as SettingsIcon,
    Security as SecurityIcon,
    Analytics as AnalyticsIcon,
    Timeline as TimelineIcon,
    Key as KeyIcon,
    Delete as DeleteIcon,
    Add as AddIcon,
    Visibility as VisibilityIcon,
    CloudUpload as CloudUploadIcon,
    CheckCircle as CheckCircleIcon,
    Warning as WarningIcon,
    Info as InfoIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme }) => ({
    borderRadius: 16,
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
}));

const GradientHeader = styled(Box)(({ theme }) => ({
    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    borderRadius: '16px 16px 0 0',
    padding: theme.spacing(3),
    color: 'white',
    position: 'relative',
    overflow: 'hidden',
}));

const VariableCard = styled(Card)(({ theme }) => ({
    borderRadius: 12,
    border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
    transition: 'all 0.3s ease',
    '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: theme.shadows[4],
        borderColor: theme.palette.primary.main,
    }
}));

const StatusChip = styled(Chip)(({ theme, status }) => {
    const colors = {
        active: { bg: theme.palette.success.main, color: 'white' },
        inactive: { bg: theme.palette.grey[400], color: 'white' },
        error: { bg: theme.palette.error.main, color: 'white' },
        warning: { bg: theme.palette.warning.main, color: 'white' }
    };
    
    const colorScheme = colors[status] || colors.active;
    
    return {
        backgroundColor: colorScheme.bg,
        color: colorScheme.color,
        fontWeight: 600,
        borderRadius: 8,
    };
});

const ModernBucketEditor = ({ 
    open, 
    onClose, 
    bucket = null, 
    onSave,
    dataSources = [],
    isLoading = false 
}) => {
    const theme = useTheme();
    const [activeTab, setActiveTab] = useState(0);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        bucketType: 'RULE_BUCKET',
        dataSourceId: '',
        tableName: '',
        status: 'active'
    });
    const [variables, setVariables] = useState([]);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (bucket) {
            setFormData({
                name: bucket.name || '',
                description: bucket.description || '',
                bucketType: bucket.type || 'RULE_BUCKET',
                dataSourceId: bucket.dataSourceId || '',
                tableName: bucket.tableName || '',
                status: bucket.status || 'active'
            });
            setVariables(bucket.variables || []);
        } else {
            // Reset for new bucket
            setFormData({
                name: '',
                description: '',
                bucketType: 'RULE_BUCKET',
                dataSourceId: '',
                tableName: '',
                status: 'active'
            });
            setVariables([]);
        }
        setErrors({});
    }, [bucket, open]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.name.trim()) {
            newErrors.name = 'Bucket name is required';
        }
        
        if (!formData.description.trim()) {
            newErrors.description = 'Description is required';
        }
        
        if (!formData.dataSourceId) {
            newErrors.dataSourceId = 'Data source is required';
        }
        
        if (!formData.tableName.trim()) {
            newErrors.tableName = 'Table name is required';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSave = () => {
        if (validateForm()) {
            onSave({
                ...formData,
                variables
            });
        }
    };

    const getVariableTypeColor = (type) => {
        switch (type) {
            case 'Primary Key': return 'primary';
            case 'Secondary Key': return 'secondary';
            case 'Date Dimension': return 'success';
            case 'Aggregatable': return 'info';
            default: return 'default';
        }
    };

    const renderBasicInfoTab = () => (
        <Box>
            <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                    <StyledCard>
                        <CardContent sx={{ p: 3 }}>
                            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                                <StorageIcon sx={{ mr: 1 }} />
                                Basic Information
                            </Typography>
                            
                            <TextField
                                fullWidth
                                label="Bucket Name"
                                value={formData.name}
                                onChange={(e) => handleInputChange('name', e.target.value)}
                                error={!!errors.name}
                                helperText={errors.name}
                                sx={{ mb: 3 }}
                                placeholder="e.g., Customer Transactions"
                            />
                            
                            <TextField
                                fullWidth
                                label="Description"
                                value={formData.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                error={!!errors.description}
                                helperText={errors.description}
                                multiline
                                rows={3}
                                sx={{ mb: 3 }}
                                placeholder="Brief description of the bucket purpose"
                            />
                            
                            <FormControl fullWidth sx={{ mb: 3 }}>
                                <InputLabel>Bucket Type</InputLabel>
                                <Select
                                    value={formData.bucketType}
                                    onChange={(e) => handleInputChange('bucketType', e.target.value)}
                                    label="Bucket Type"
                                >
                                    <MenuItem value="RULE_BUCKET">
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                            <AnalyticsIcon sx={{ mr: 1 }} />
                                            Rule Bucket
                                        </Box>
                                    </MenuItem>
                                    <MenuItem value="LOOKUP">
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                            <DataObjectIcon sx={{ mr: 1 }} />
                                            Lookup Bucket
                                        </Box>
                                    </MenuItem>
                                </Select>
                            </FormControl>
                            
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.status === 'active'}
                                        onChange={(e) => handleInputChange('status', e.target.checked ? 'active' : 'inactive')}
                                        color="success"
                                    />
                                }
                                label="Active Status"
                            />
                        </CardContent>
                    </StyledCard>
                </Grid>
                
                <Grid item xs={12} md={6}>
                    <StyledCard>
                        <CardContent sx={{ p: 3 }}>
                            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                                <CloudUploadIcon sx={{ mr: 1 }} />
                                Data Source Configuration
                            </Typography>
                            
                            <FormControl fullWidth sx={{ mb: 3 }}>
                                <InputLabel>Data Source</InputLabel>
                                <Select
                                    value={formData.dataSourceId}
                                    onChange={(e) => handleInputChange('dataSourceId', e.target.value)}
                                    error={!!errors.dataSourceId}
                                    label="Data Source"
                                >
                                    {dataSources.map((source) => (
                                        <MenuItem key={source.id} value={source.id}>
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <Avatar sx={{ width: 24, height: 24, mr: 1, bgcolor: 'primary.main' }}>
                                                    {source.name.charAt(0)}
                                                </Avatar>
                                                {source.name}
                                            </Box>
                                        </MenuItem>
                                    ))}
                                </Select>
                                {errors.dataSourceId && (
                                    <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                                        {errors.dataSourceId}
                                    </Typography>
                                )}
                            </FormControl>
                            
                            <TextField
                                fullWidth
                                label="Table Name"
                                value={formData.tableName}
                                onChange={(e) => handleInputChange('tableName', e.target.value)}
                                error={!!errors.tableName}
                                helperText={errors.tableName}
                                sx={{ mb: 3 }}
                                placeholder="e.g., customer_transactions"
                            />
                            
                            {bucket && (
                                <Alert severity="info" sx={{ mt: 2 }}>
                                    <Typography variant="body2">
                                        <strong>Created:</strong> {bucket.createdAt || 'Unknown'}<br/>
                                        <strong>Last Updated:</strong> {bucket.lastUpdated || 'Unknown'}<br/>
                                        <strong>Variables:</strong> {variables.length} configured
                                    </Typography>
                                </Alert>
                            )}
                        </CardContent>
                    </StyledCard>
                </Grid>
            </Grid>
        </Box>
    );

    const renderVariablesTab = () => (
        <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" fontWeight="bold">
                    Variables ({variables.length})
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    sx={{ borderRadius: 2 }}
                >
                    Add Variables
                </Button>
            </Box>
            
            {variables.length === 0 ? (
                <StyledCard>
                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                        <DataObjectIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                            No Variables Configured
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                            Add variables to start using this bucket for monitoring rules
                        </Typography>
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            sx={{ borderRadius: 2 }}
                        >
                            Add Your First Variable
                        </Button>
                    </CardContent>
                </StyledCard>
            ) : (
                <Grid container spacing={2}>
                    {variables.map((variable, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index}>
                            <VariableCard>
                                <CardContent sx={{ p: 2 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                        <Avatar sx={{ width: 32, height: 32, mr: 2, bgcolor: 'primary.main' }}>
                                            {variable.name.charAt(0).toUpperCase()}
                                        </Avatar>
                                        <Box sx={{ flexGrow: 1 }}>
                                            <Typography variant="subtitle2" fontWeight="medium">
                                                {variable.name}
                                            </Typography>
                                            <Typography variant="caption" color="text.secondary">
                                                {variable.dataType}
                                            </Typography>
                                        </Box>
                                        <IconButton size="small">
                                            <EditIcon fontSize="small" />
                                        </IconButton>
                                    </Box>
                                    
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                                        {variable.type && (
                                            <Chip
                                                label={variable.type}
                                                size="small"
                                                color={getVariableTypeColor(variable.type)}
                                                sx={{ borderRadius: 1 }}
                                            />
                                        )}
                                        {variable.isUnique && (
                                            <Chip label="Unique" size="small" variant="outlined" />
                                        )}
                                        {variable.aggregatable && (
                                            <Chip label="Aggregatable" size="small" variant="outlined" />
                                        )}
                                    </Box>
                                    
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                        <Typography variant="caption" color="text.secondary">
                                            Column: {variable.columnName || variable.name}
                                        </Typography>
                                        <IconButton size="small" color="error">
                                            <DeleteIcon fontSize="small" />
                                        </IconButton>
                                    </Box>
                                </CardContent>
                            </VariableCard>
                        </Grid>
                    ))}
                </Grid>
            )}
        </Box>
    );

    const renderSettingsTab = () => (
        <Box>
            <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                    <StyledCard>
                        <CardContent sx={{ p: 3 }}>
                            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                                <SecurityIcon sx={{ mr: 1 }} />
                                Security & Access
                            </Typography>
                            
                            <List>
                                <ListItem>
                                    <ListItemIcon>
                                        <VisibilityIcon />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Public Access"
                                        secondary="Allow other users to view this bucket"
                                    />
                                    <ListItemSecondaryAction>
                                        <Switch defaultChecked />
                                    </ListItemSecondaryAction>
                                </ListItem>
                                
                                <ListItem>
                                    <ListItemIcon>
                                        <EditIcon />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Edit Permissions"
                                        secondary="Allow other users to modify variables"
                                    />
                                    <ListItemSecondaryAction>
                                        <Switch />
                                    </ListItemSecondaryAction>
                                </ListItem>
                                
                                <ListItem>
                                    <ListItemIcon>
                                        <DeleteIcon />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Delete Protection"
                                        secondary="Prevent accidental deletion"
                                    />
                                    <ListItemSecondaryAction>
                                        <Switch defaultChecked />
                                    </ListItemSecondaryAction>
                                </ListItem>
                            </List>
                        </CardContent>
                    </StyledCard>
                </Grid>
                
                <Grid item xs={12} md={6}>
                    <StyledCard>
                        <CardContent sx={{ p: 3 }}>
                            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                                <SettingsIcon sx={{ mr: 1 }} />
                                Advanced Settings
                            </Typography>
                            
                            <List>
                                <ListItem>
                                    <ListItemIcon>
                                        <TimelineIcon />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Auto-sync Variables"
                                        secondary="Automatically detect new table columns"
                                    />
                                    <ListItemSecondaryAction>
                                        <Switch />
                                    </ListItemSecondaryAction>
                                </ListItem>
                                
                                <ListItem>
                                    <ListItemIcon>
                                        <AnalyticsIcon />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Performance Monitoring"
                                        secondary="Track query performance metrics"
                                    />
                                    <ListItemSecondaryAction>
                                        <Switch defaultChecked />
                                    </ListItemSecondaryAction>
                                </ListItem>
                                
                                <ListItem>
                                    <ListItemIcon>
                                        <CheckCircleIcon />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Data Validation"
                                        secondary="Validate data types and constraints"
                                    />
                                    <ListItemSecondaryAction>
                                        <Switch defaultChecked />
                                    </ListItemSecondaryAction>
                                </ListItem>
                            </List>
                        </CardContent>
                    </StyledCard>
                </Grid>
            </Grid>
        </Box>
    );

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="lg"
            fullWidth
            PaperProps={{
                sx: { 
                    borderRadius: 3,
                    minHeight: '80vh',
                    maxHeight: '90vh'
                }
            }}
        >
            <GradientHeader>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2, width: 48, height: 48 }}>
                            {bucket ? <EditIcon /> : <AddIcon />}
                        </Avatar>
                        <Box>
                            <Typography variant="h5" fontWeight="bold">
                                {bucket ? 'Edit Data Bucket' : 'Create New Data Bucket'}
                            </Typography>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                {bucket ? `Modify ${bucket.name} configuration` : 'Set up a new data bucket for monitoring'}
                            </Typography>
                        </Box>
                    </Box>
                    {bucket && (
                        <StatusChip
                            label={formData.status.toUpperCase()}
                            status={formData.status}
                        />
                    )}
                </Box>
            </GradientHeader>
            
            <DialogContent sx={{ p: 0 }}>
                {isLoading && <LinearProgress />}
                
                <Box sx={{ p: 3 }}>
                    <Tabs
                        value={activeTab}
                        onChange={(e, newValue) => setActiveTab(newValue)}
                        sx={{ mb: 3 }}
                    >
                        <Tab label="Basic Info" />
                        <Tab label="Variables" />
                        <Tab label="Settings" />
                    </Tabs>
                    
                    {activeTab === 0 && renderBasicInfoTab()}
                    {activeTab === 1 && renderVariablesTab()}
                    {activeTab === 2 && renderSettingsTab()}
                </Box>
            </DialogContent>
            
            <DialogActions sx={{ p: 3, pt: 1, bgcolor: alpha(theme.palette.background.default, 0.5) }}>
                <Button 
                    onClick={onClose} 
                    startIcon={<CancelIcon />}
                    sx={{ borderRadius: 2 }}
                >
                    Cancel
                </Button>
                <Box sx={{ flexGrow: 1 }} />
                {bucket && (
                    <Button 
                        color="error" 
                        startIcon={<DeleteIcon />}
                        sx={{ borderRadius: 2, mr: 1 }}
                    >
                        Delete
                    </Button>
                )}
                <Button 
                    variant="contained" 
                    onClick={handleSave}
                    startIcon={<SaveIcon />}
                    disabled={isLoading}
                    sx={{ borderRadius: 2 }}
                >
                    {bucket ? 'Save Changes' : 'Create Bucket'}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default ModernBucketEditor;
