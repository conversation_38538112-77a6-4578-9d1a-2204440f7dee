package com.progize.tms.repository;

import com.progize.tms.repository.entity.DataType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DataTypeRepository extends JpaRepository<DataType, Long> {

  Optional<DataType> findByCode(String code);

  @Query("SELECT dt FROM DataType dt WHERE dt.updatedAt > :timestamp")
  List<DataType> findUpdatedSince(@Param("timestamp") LocalDateTime timestamp);
}
