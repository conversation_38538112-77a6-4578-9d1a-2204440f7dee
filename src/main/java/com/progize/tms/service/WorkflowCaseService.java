package com.progize.tms.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.repository.entity.WorkflowType;
import java.util.List;
import java.util.Map;
import org.flowable.task.api.Task;

public interface WorkflowCaseService {

  /** Start a new case with a Flowable process */
  WorkflowCase startCase(
      WorkflowType workflowType, String entityType, Long entityId, Map<String, Object> variables);

  /** Complete a task in the case */
  void completeTask(Long caseId, String taskId, Map<String, Object> variables);

  /** Get active tasks for a case */
  List<Task> getCaseTasks(Long caseId);

  /** Get case by ID */
  WorkflowCase getCase(Long caseId);

  /** Get case by process instance ID */
  WorkflowCase getCaseByProcessInstanceId(String processInstanceId);

  /**
   * Update case metadata
   *
   * @throws JsonProcessingException
   */
  WorkflowCase updateCaseMetadata(Long caseId, Map<String, Object> metadata)
      throws JsonProcessingException;

  /** Get cases by entity */
  List<WorkflowCase> getCasesByEntity(String entityType, Long entityId);

  /** Get cases by workflow type and status */
  List<WorkflowCase> getCasesByTypeAndStatus(WorkflowType workflowType, String status);
}
