package com.progize.tms.config;

import org.flowable.common.engine.impl.history.HistoryLevel;
import org.flowable.engine.FormService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
public class FlowableConfig {

  @Bean(name = "flowableDataSource")
  @ConfigurationProperties(prefix = "flowable.datasource")
  public javax.sql.DataSource flowableDataSource() {
    return DataSourceBuilder.create().type(com.zaxxer.hikari.HikariDataSource.class).build();
  }

  @Bean
  public EngineConfigurationConfigurer<SpringProcessEngineConfiguration>
      customProcessEngineConfigurer() {
    return engineConfiguration -> {
      engineConfiguration.setValidateFlowable5EntitiesEnabled(false);
      engineConfiguration.setHistoryLevel(HistoryLevel.FULL);
      // Enable async history for better performance
      engineConfiguration.setAsyncHistoryEnabled(true);
      engineConfiguration.setAsyncHistoryExecutorActivate(true);
    };
  }

  @Bean
  public SpringProcessEngineConfiguration springProcessEngineConfiguration(
      @Qualifier("flowableDataSource") javax.sql.DataSource flowableDataSource,
      PlatformTransactionManager transactionManager,
      ResourceLoader resourceLoader)
      throws Exception {
    SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
    config.setDataSource(flowableDataSource);
    config.setTransactionManager(transactionManager);
    config.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);

    // Load BPMN process definitions
    Resource[] resources =
        ResourcePatternUtils.getResourcePatternResolver(resourceLoader)
            .getResources("classpath:/processes/*.bpmn20.xml");
    config.setDeploymentResources(resources);

    return config;
  }

  @Bean
  public ProcessEngine processEngine(SpringProcessEngineConfiguration config) {
    return config.buildProcessEngine();
  }

  @Bean
  public RuntimeService runtimeService(ProcessEngine processEngine) {
    return processEngine.getRuntimeService();
  }

  @Bean
  public TaskService taskService(ProcessEngine processEngine) {
    return processEngine.getTaskService();
  }

  @Bean
  public HistoryService historyService(ProcessEngine processEngine) {
    return processEngine.getHistoryService();
  }

  /**
   * Exposes the Flowable FormService as a Spring bean. This service is used to access form
   * definitions and form data.
   *
   * @param processEngine The Flowable process engine
   * @return The FormService instance
   */
  @Bean
  public FormService formService(ProcessEngine processEngine) {
    return processEngine.getFormService();
  }
}
