import React from "react";
import { 
  Box, 
  Container, 
  Typography, 
  Paper,
  styled,
  useTheme 
} from '@mui/material';
import DescriptionIcon from '@mui/icons-material/Description';
import { RuleList } from "./RuleList";

// Styled components
const PageContainer = styled(Container)(({ theme }) => ({
  padding: theme.spacing(4),
  maxWidth: '100%',
}));

const Header = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

const ContentPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(0),
  borderRadius: theme.spacing(1),
  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  overflow: 'hidden',
}));

const TransactionRules = () => {
  const theme = useTheme();

  return (
    <PageContainer>
      <Header>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1 }}>
          <DescriptionIcon 
            sx={{ 
              color: theme.palette.primary.main, 
              fontSize: 28
            }} 
          />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 600,
              color: theme.palette.text.primary
            }}
          >
            Transaction Rules
          </Typography>
        </Box>
        <Typography 
          variant="body1" 
          color="textSecondary"
        >
          Configure and manage rule-based transaction monitoring policies. Rules determine which transactions are flagged for review based on conditions and thresholds.
        </Typography>
      </Header>

      <ContentPaper>
        <Box sx={{ p: 3 }}>
          <RuleList />
        </Box>
      </ContentPaper>
    </PageContainer>
  );
};

export default TransactionRules;
