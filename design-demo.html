<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SoniQue UI Design Concepts</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
  <style>
    html, body, #root {
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: 'Inter', sans-serif;
    }
  </style>
</head>
<body>
  <div id="root"></div>

  <!-- Production React -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

  <!-- Material UI -->
  <script crossorigin src="https://unpkg.com/@mui/material@5.13.0/umd/material-ui.production.min.js"></script>

  <!-- React Router -->
  <script crossorigin src="https://unpkg.com/react-router@6.11.1/dist/umd/react-router.production.min.js"></script>
  <script crossorigin src="https://unpkg.com/react-router-dom@6.11.1/dist/umd/react-router-dom.production.min.js"></script>

  <!-- Babel for JSX transpilation -->
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

  <script type="text/babel" src="design-demo-bundle.js"></script>
</body>
</html>
