import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Divider, 
  Button, 
  TextField, 
  MenuItem, 
  FormControl, 
  FormLabel, 
  FormControlLabel, 
  RadioGroup, 
  Radio, 
  Checkbox, 
  CircularProgress, 
  Paper, 
  Chip, 
  Alert, 
  AlertTitle, 
  Tabs,
  Tab,
  LinearProgress,
  Avatar,
  Badge,
  IconButton,
  Tooltip,
  Fade,
  Zoom,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  Stack
} from '@mui/material';
import { 
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
  ArrowBack as ArrowBackIcon,
  AttachFile as AttachFileIcon,
  Send as SendIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Flag as FlagIcon,
  AccountBalance as AccountBalanceIcon,
  Business as BusinessIcon,
  Public as PublicIcon,
  Security as SecurityIcon,
  Gavel as GavelIcon,
  Timeline as TimelineIcon,
  Bar<PERSON>hart as BarChartIcon,
  SmartToy as SmartToyIcon,
  Close as CloseIcon,
  Help as HelpIcon,
  MoreVert as MoreVertIcon,
  Escalator as EscalateIcon,
  Cancel as RejectIcon,
  CheckCircleOutline as ApproveIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import ComplianceWorkflowService from '../../services/ComplianceWorkflowService';


/**
 * Modern compliance task detail component with tabbed interface and AI investigator
 */
const ComplianceTaskDetail = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  
  // Core state
  const [task, setTask] = useState(null);
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [attachments, setAttachments] = useState([]);
  
  // UI state
  const [tabValue, setTabValue] = useState(0);
  const [aiChatOpen, setAiChatOpen] = useState(false);
  const [aiMessages, setAiMessages] = useState([
    { sender: 'ai', text: 'Hello! I can help investigate this case. What would you like to know?' }
  ]);
  const [aiInput, setAiInput] = useState('');
  const [confirmationDialog, setConfirmationDialog] = useState({
    open: false,
    action: null,
    title: '',
    message: ''
  });
  
  useEffect(() => {
    const fetchTaskDetails = async () => {
      try {
        setLoading(true);
        const taskDetails = await ComplianceWorkflowService.getTaskDetails(taskId);
        console.log('Task details:', taskDetails);
        setTask(taskDetails);
        
        // Initialize form data with default values from form properties
        const initialFormData = {};
        if (taskDetails.formProperties) {
          taskDetails.formProperties.forEach(prop => {
            // Don't set default values for comment fields to avoid showing previous comments
            if (prop.id.endsWith('Comments')) {
              initialFormData[prop.id] = '';
            } else {
              initialFormData[prop.id] = prop.defaultValue || '';
            }
          });
        }
        setFormData(initialFormData);
        
        setError(null);
      } catch (err) {
        console.error('Error fetching task details:', err);
        setError('Failed to load task details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTaskDetails();
  }, [taskId]);
  
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  const handleFileChange = (e) => {
    setAttachments(Array.from(e.target.files));
  };
  
  // Tab handling
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // AI Chat functions
  const toggleAiChat = () => {
    setAiChatOpen(!aiChatOpen);
  };
  
  const handleAiInputChange = (e) => {
    setAiInput(e.target.value);
  };
  
  const sendAiMessage = () => {
    if (!aiInput.trim()) return;
    
    // Add user message
    const userMessage = { sender: 'user', text: aiInput };
    setAiMessages(prev => [...prev, userMessage]);
    
    // Simulate AI response (in a real app, this would call an API)
    setTimeout(() => {
      const aiResponse = { 
        sender: 'ai', 
        text: `I've analyzed this case and found that the transaction of ${task?.variables?.fund_net_amount || 'unknown amount'} has a high risk score of ${task?.variables?.aml_risk_score_customer || '?'}/100. This requires additional verification.` 
      };
      setAiMessages(prev => [...prev, aiResponse]);
    }, 1000);
    
    setAiInput('');
  };
  
  // Action button handlers
  const handleActionButtonClick = (action) => {
    let title, message;
    
    switch(action) {
      case 'request_info':
        title = 'Request Additional Information';
        message = 'Are you sure you want to request additional information for this case?';
        break;
      case 'escalate':
        title = 'Escalate Case';
        message = 'Are you sure you want to escalate this case to a higher authority?';
        break;
      case 'reject':
        title = 'Reject Transaction';
        message = 'Are you sure you want to reject this transaction? This action cannot be undone.';
        break;
      case 'approve':
        title = 'Approve Transaction';
        message = 'Are you sure you want to approve this transaction?';
        break;
      default:
        return;
    }
    
    setConfirmationDialog({
      open: true,
      action,
      title,
      message
    });
  };
  
  const handleConfirmAction = async () => {
    const { action } = confirmationDialog;
    setConfirmationDialog(prev => ({ ...prev, open: false }));
    
    try {
      setSubmitting(true);
      setError(null);
      
      // Map action to form values
      let decision, comments;
      
      switch(action) {
        case 'request_info':
          decision = 'request_info';
          comments = formData.comments || 'Additional information requested';
          break;
        case 'escalate':
          decision = 'escalate';
          comments = formData.comments || 'Case escalated for further review';
          break;
        case 'reject':
          decision = 'negative';
          comments = formData.comments || 'Transaction rejected';
          break;
        case 'approve':
          decision = 'positive';
          comments = formData.comments || 'Transaction approved';
          break;
        default:
          throw new Error('Invalid action');
      }
      
      // Determine which API to call based on task definition key
      if (task.taskDefinitionKey === 'officerReviewTask') {
        await ComplianceWorkflowService.completeOfficerReview(
          taskId,
          decision,
          comments,
          attachments
        );
      } else if (task.taskDefinitionKey === 'managerReviewTask') {
        await ComplianceWorkflowService.completeManagerReview(
          taskId,
          decision === 'positive' ? 'approve' : (decision === 'negative' ? 'reject' : decision),
          comments,
          attachments
        );
      } else {
        // Generic task completion
        const formValues = {
          ...formData,
          decision,
          comments
        };
        
        await ComplianceWorkflowService.completeGenericTask(
          taskId,
          formValues,
          attachments
        );
      }
      
      setSubmitSuccess(true);
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate('/compliance/tasks');
      }, 1500);
      
    } catch (err) {
      console.error('Error submitting task:', err);
      setError('Failed to submit task. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };
  
  const handleCancelAction = () => {
    setConfirmationDialog(prev => ({ ...prev, open: false }));
  };
  
  // Format date to a readable string
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Render form field based on property type
  const renderFormField = (property) => {
    const { id, name, type, required, values } = property;
    
    switch (type?.toLowerCase()) {
      case 'enum':
        return (
          <FormControl fullWidth margin="normal" required={required}>
            <FormLabel>{name}</FormLabel>
            <RadioGroup
              name={id}
              value={formData[id] || ''}
              onChange={handleInputChange}
              row
            >
              {values && values.map((option) => (
                <FormControlLabel
                  key={option.id}
                  value={option.id}
                  control={<Radio />}
                  label={option.name}
                />
              ))}
            </RadioGroup>
          </FormControl>
        );
        
  // Helper function to get risk level color
  const getRiskLevelColor = (riskLevel) => {
    if (!riskLevel) return theme.palette.grey[500];
    const level = riskLevel.toString().toUpperCase();
    if (level.includes('HIGH')) return theme.palette.error.main;
    if (level.includes('MEDIUM')) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  // Helper function to get priority color
  const getPriorityColor = (priority) => {
    if (!priority) return theme.palette.grey[500];
    const level = priority.toString().toUpperCase();
    if (level.includes('HIGH')) return theme.palette.error.main;
    if (level.includes('MEDIUM')) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  // Helper function to get SLA status color
  const getSlaStatusColor = (dueDate) => {
    if (!dueDate) return theme.palette.grey[500];
    const now = new Date();
    const due = new Date(dueDate);
    const diffHours = (due - now) / (1000 * 60 * 60);
    
    if (diffHours < 0) return theme.palette.error.main; // Overdue
    if (diffHours < 24) return theme.palette.warning.main; // Due soon
    return theme.palette.success.main; // On track
  };

  // Render form field based on property type
  const renderFormField = (property) => {
    switch (property.type.toLowerCase()) {
      case 'string':
        return (
          <TextField
            fullWidth
            label={property.name}
            name={property.id}
            value={formData[property.id] || ''}
            onChange={handleInputChange}
            disabled={!property.writable || submitting}
            required={property.required}
            variant="outlined"
            margin="normal"
          />
        );
      case 'long':
      case 'integer':
      case 'double':
        return (
          <TextField
            fullWidth
            label={property.name}
            name={property.id}
            type="number"
            value={formData[property.id] || ''}
            onChange={handleInputChange}
            disabled={!property.writable || submitting}
            required={property.required}
            variant="outlined"
            margin="normal"
          />
        );
      case 'date':
        return (
          <TextField
            fullWidth
            label={property.name}
            name={property.id}
            type="date"
            value={formData[property.id] || ''}
            onChange={handleInputChange}
            disabled={!property.writable || submitting}
            required={property.required}
            variant="outlined"
            margin="normal"
            InputLabelProps={{ shrink: true }}
          />
        );
      case 'boolean':
        return (
          <FormControlLabel
            control={
              <Checkbox
                name={property.id}
                checked={Boolean(formData[property.id])}
                onChange={handleInputChange}
                disabled={!property.writable || submitting}
                color="primary"
              />
            }
            label={property.name}
          />
        );
      case 'enum':
        return (
          <TextField
            select
            fullWidth
            label={property.name}
            name={property.id}
            value={formData[property.id] || ''}
            onChange={handleInputChange}
            disabled={!property.writable || submitting}
            required={property.required}
            variant="outlined"
            margin="normal"
          >
            {property.values && property.values.map((option) => (
              <MenuItem key={option.id} value={option.id}>
                {option.name}
              </MenuItem>
            ))}
          </TextField>
        );
      default:
        return (
          <TextField
            fullWidth
            label={property.name}
            name={property.id}
            value={formData[property.id] || ''}
            onChange={handleInputChange}
            disabled={!property.writable || submitting}
            required={property.required}
            variant="outlined"
            margin="normal"
            multiline={property.id.includes('comment') || property.id.includes('Comment')}
            rows={property.id.includes('comment') || property.id.includes('Comment') ? 4 : 1}
          />
        );
    }
  };

  // Loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <AlertTitle>Error</AlertTitle>
          {error}
        </Alert>
        <Button
          variant="outlined"
          onClick={() => navigate('/compliance/tasks')}
          sx={{ mt: 2 }}
          startIcon={<ArrowBackIcon />}
        >
          Back to Tasks
        </Button>
      </Box>
    );
  }

  // Success state
  if (submitSuccess) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="success">
          <AlertTitle>Success</AlertTitle>
          Task completed successfully! Redirecting...
        </Alert>
      </Box>
    );
  }

  // No task found state
  if (!task) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          <AlertTitle>No Task Found</AlertTitle>
          The requested task could not be found or has been completed.
        </Alert>
        <Button
          variant="outlined"
          onClick={() => navigate('/compliance/tasks')}
          sx={{ mt: 2 }}
          startIcon={<ArrowBackIcon />}
        >
          Back to Tasks
        </Button>
      </Box>
    );
  }
  
  return (
    <Box sx={{ p: 3 }}>
      {/* Header with back button */}
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          mb: 3 
        }}

      >
        <Tooltip title="Back to Tasks">
          <IconButton 
            onClick={() => navigate('/compliance/tasks')}
            sx={{ mr: 2 }}
          >
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          {task.name}
        </Typography>
      </Box>
      
      <Grid container spacing={3}>
        {/* Task Information Card */}
        <Grid item xs={12} md={4}>
          <Card 
            sx={{ 
              borderRadius: 2,
              height: '100%',
              boxShadow: theme.shadows[3],
              animation: `${theme.transitions.create('all', {
                duration: theme.transitions.duration.standard,
                easing: theme.transitions.easing.easeOut
              })}`
            }}
          >
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ 
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}>
                <AssignmentIcon sx={{ mr: 1 }} /> Task Information
              </Typography>
              
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Task ID
                </Typography>
                <Typography variant="body1">
                  {task.id}
                </Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ 
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} /> Created
                </Typography>
                <Typography variant="body1">
                  {formatDate(task.createTime)}
                </Typography>
              </Box>
              
              {task.dueDate && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Due Date
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(task.dueDate)}
                  </Typography>
                </Box>
              )}
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ 
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <PersonIcon fontSize="small" sx={{ mr: 0.5 }} /> Assignee
                </Typography>
                <Typography variant="body1">
                  {task.assignee || 'Unassigned'}
                </Typography>
              </Box>
              
              {task.description && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ 
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <DescriptionIcon fontSize="small" sx={{ mr: 0.5 }} /> Description
                  </Typography>
                  <Paper 
                    variant="outlined" 
                    sx={{ 
                      p: 1.5, 
                      mt: 0.5, 
                      bgcolor: theme.palette.grey[50],
                      borderRadius: 1
                    }}
                  >
                    <Typography variant="body2">
                      {task.description}
                    </Typography>
                  </Paper>
                </Box>
              )}
              
              {/* Process variables if available */}
              {task.variables && Object.keys(task.variables).length > 0 && (
                <>
                  <Typography variant="subtitle1" sx={{ mt: 3, mb: 1 }}>
                    Process Variables
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  {Object.entries(task.variables).map(([key, value]) => (
                    <Box key={key} sx={{ mb: 1 }}>
                      <Typography variant="subtitle2" color="text.secondary">
                        {key}
                      </Typography>
                      <Typography variant="body2">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </Typography>
                    </Box>
                  ))}
                </>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        {/* Task Form Card */}
        <Grid item xs={12} md={8}>
          <Card 
            sx={{ 
              borderRadius: 2,
              boxShadow: theme.shadows[3],
              animation: `${theme.transitions.create('all', {
                duration: theme.transitions.duration.standard,
                easing: theme.transitions.easing.easeOut
              })}`
            }}
          >
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                Complete Task
              </Typography>
              
              <Divider sx={{ mb: 3 }} />
              
              <form onSubmit={handleSubmit}>
                {/* Render form fields based on form properties */}
                {task.formProperties && task.formProperties.length > 0 ? (
                  task.formProperties.map((property) => (
                    <Box key={property.id} sx={{ mb: 2 }}>
                      {renderFormField(property)}
                    </Box>
                  ))
                ) : (
                  <Alert severity="info" sx={{ mb: 3 }}>
                    This task doesn't have any form fields to complete.
                  </Alert>
                )}
                
                {/* File attachments */}
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Attachments (Optional)
                  </Typography>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<AttachFileIcon />}
                    sx={{ mb: 2 }}
                  >
                    Upload Files
                    <input
                      type="file"
                      hidden
                      multiple
                      onChange={handleFileChange}
                    />
                  </Button>
                  
                  {/* Display selected files */}
                  {attachments.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Selected Files:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {attachments.map((file, index) => (
                          <Chip
                            key={index}
                            label={file.name}
                            onDelete={() => {
                              const newAttachments = [...attachments];
                              newAttachments.splice(index, 1);
                              setAttachments(newAttachments);
                            }}
                            size="small"
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
                
                <Divider sx={{ my: 3 }} />
                
                {/* Submit button */}
                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    type="button"
                    variant="outlined"
                    onClick={() => navigate('/compliance/tasks')}
                    sx={{ mr: 2 }}
                    disabled={submitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={submitting}
                    startIcon={submitting ? <CircularProgress size={20} /> : <SendIcon />}
                  >
                    {submitting ? 'Submitting...' : 'Complete Task'}
                  </Button>
                </Box>
              </form>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ComplianceTaskDetail;
