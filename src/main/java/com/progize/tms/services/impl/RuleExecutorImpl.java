package com.progize.tms.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.RuleRepository;
import com.progize.tms.repository.TransactionRepository;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.repository.entity.Transaction;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.repository.entity.WorkflowType;
import com.progize.tms.service.WorkflowCaseService;
import com.progize.tms.services.DataBucketService;
import com.progize.tms.services.RuleEvaluationService;
import com.progize.tms.services.RuleExecutor;
import com.progize.tms.services.models.Condition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RuleExecutorImpl implements RuleExecutor {
  private final RuleRepository ruleRepository;
  private final TransactionRepository transactionRepository;
  private final RuleEvaluationService ruleEvaluationService;
  private final DataBucketService dataBucketService;
  private final ObjectMapper objectMapper;
  private final WorkflowCaseService workflowCaseService;

  public RuleExecutorImpl(
      RuleRepository ruleRepository,
      TransactionRepository transactionRepository,
      RuleEvaluationService ruleEvaluationService,
      DataBucketService dataBucketService,
      ObjectMapper objectMapper,
      WorkflowCaseService workflowCaseService) {
    this.ruleRepository = ruleRepository;
    this.transactionRepository = transactionRepository;
    this.ruleEvaluationService = ruleEvaluationService;
    this.dataBucketService = dataBucketService;
    this.objectMapper = objectMapper;
    this.workflowCaseService = workflowCaseService;
  }

  @Override
  public List<Transaction> processTransactions(List<Transaction> transactions) {
    List<Rule> activeRules = ruleRepository.findByActiveOrderByPriorityAsc(true);
    return transactions.stream()
        .map(transaction -> processTransaction(transaction, activeRules))
        .collect(Collectors.toList());
  }

  @Override
  public Transaction processTransaction(Transaction transaction) {
    List<Rule> activeRules = ruleRepository.findByActiveOrderByPriorityAsc(true);
    return processTransaction(transaction, activeRules);
  }

  private Transaction processTransaction(Transaction transaction, List<Rule> rules) {
    // Reset flag state before processing
    transaction.setFlagged(false);
    transaction.setFlagReason(null);

    // Convert transaction to map for rule evaluation
    Map<String, Object> transactionMap = convertTransactionToMap(transaction);

    // Track all matching rules for this transaction
    List<Rule> matchingRules = new ArrayList<>();

    // Apply rules in priority order
    for (Rule rule : rules) {
      try {
        // Extract customer ID from transaction
        String customerId = transaction.getCustomerId();

        // Parse the rule condition from JSON
        Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

        // Use the rule evaluation service to evaluate the rule
        boolean ruleTriggered =
            ruleEvaluationService.evaluateRule(
                condition,
                customerId,
                transactionMap,
                true, // Use real aggregations
                rule.getDataBucket() != null ? rule.getDataBucket().getId() : null);

        if (ruleTriggered) {
          transaction.setFlagged(true);

          // Add this rule to the list of matching rules
          matchingRules.add(rule);

          log.debug(
              "Transaction {} flagged by rule {}: {}",
              transaction.getId(),
              rule.getId(),
              rule.getName());
        }
      } catch (Exception e) {
        log.error(
            "Error evaluating rule {} against transaction {}: {}",
            rule.getId(),
            transaction.getId(),
            e.getMessage(),
            e);
      }
    }

    // If any rules matched, create workflow cases
    if (!matchingRules.isEmpty()) {
      // Set flag reason to the name of the first matching rule
      transaction.setFlagReason(matchingRules.get(0).getName());

      // Create workflow cases for each matching rule
      for (Rule rule : matchingRules) {
        try {
          createWorkflowCase(transaction, rule, transactionMap);
        } catch (Exception e) {
          log.error(
              "Error creating workflow case for transaction {} and rule {}: {}",
              transaction.getId(),
              rule.getId(),
              e.getMessage(),
              e);
        }
      }

      // Save the updated transaction
      transactionRepository.save(transaction);
    }

    return transaction;
  }

  @Override
  public List<Map<String, Object>> processBucketTransactions(
      Long bucketId, List<Map<String, Object>> transactions) {
    log.info(
        "Processing {} transactions in bucket {} against all rules", transactions.size(), bucketId);
    // Get all active rules for the specified bucket
    List<Rule> activeRules =
        ruleRepository.findByActiveAndDataBucket_IdOrderByPriorityAsc(true, bucketId);

    if (activeRules.isEmpty()) {
      log.info("No active rules found for bucket ID: {}", bucketId);
      return transactions;
    }

    // Process each transaction against all rules
    return transactions.stream()
        .map(transaction -> processSingleBucketTransaction(transaction, activeRules, bucketId))
        .collect(Collectors.toList());
  }

  /**
   * Process a single transaction against all applicable rules for a bucket.
   *
   * @param transaction The transaction data as a map
   * @param rules The rules to evaluate against the transaction
   * @param bucketId The bucket ID
   * @return The processed transaction with flagging information
   */
  private Map<String, Object> processSingleBucketTransaction(
      Map<String, Object> transaction, List<Rule> rules, Long bucketId) {
    // Convert transaction data to correct types based on bucket variables
    Map<String, Object> typedTransaction = convertToCorrectTypes(transaction, bucketId);

    // Track flagging status
    boolean flagged = false;
    StringBuilder flagReasonBuilder = new StringBuilder();
    List<Rule> matchingRules = new ArrayList<>();

    // Extract customer ID if available
    String customerId = null;
    if (typedTransaction.containsKey("customerId")) {
      customerId = String.valueOf(typedTransaction.get("customerId"));
    }

    // Apply each rule
    for (Rule rule : rules) {
      try {
        // Parse the rule condition from JSON
        Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

        // Use the rule evaluation service to evaluate the rule
        boolean ruleTriggered =
            ruleEvaluationService.evaluateRule(
                condition,
                customerId,
                typedTransaction,
                true, // Use real aggregations
                bucketId);

        if (ruleTriggered) {
          flagged = true;
          if (flagReasonBuilder.length() > 0) {
            flagReasonBuilder.append(" | ");
          }
          flagReasonBuilder.append(rule.getFlagReason());
          matchingRules.add(rule);
        }
      } catch (Exception e) {
        log.error("Error evaluating rule {} on transaction: {}", rule.getId(), e.getMessage());
      }
    }

    // Add flagged status to the result
    Map<String, Object> result = new HashMap<>(typedTransaction);
    result.put("flagged", flagged);
    if (flagged) {
      result.put("flagReason", flagReasonBuilder.toString());

      // Create or update cases for each matching rule
      for (Rule rule : matchingRules) {
        try {
          // Create workflow case for this transaction and rule
          createWorkflowCase(null, rule, typedTransaction);
        } catch (Exception e) {
          log.error("Error creating case for rule {}: {}", rule.getId(), e.getMessage());
        }
      }
    }

    return result;
  }

  /**
   * Creates a workflow case for a flagged transaction and rule.
   *
   * @param transaction The transaction entity (may be null for dynamic transactions)
   * @param rule The rule that flagged the transaction
   * @param transactionData The transaction data as a map
   * @return The created workflow case
   */
  /**
   * Creates a workflow case for a flagged transaction and rule.
   *
   * @param transaction The transaction entity (may be null for dynamic transactions)
   * @param rule The rule that flagged the transaction
   * @param transactionData The transaction data as a map
   * @return The created workflow case
   */
  private WorkflowCase createWorkflowCase(
      Transaction transaction, Rule rule, Map<String, Object> transactionData) {
    try {
      // Prepare variables for the workflow process
      Map<String, Object> processVariables = new HashMap<>();

      // Add customer ID if available
      String customerId = null;
      if (transaction != null) {
        customerId = transaction.getCustomerId();
      } else if (transactionData.containsKey("customerId")) {
        customerId = String.valueOf(transactionData.get("customerId"));
      }

      if (customerId != null) {
        processVariables.put("customerId", customerId);
      }

      // Add all transaction data as process variables
      // These will be available in the Flowable process
      if (transaction != null) {
        // If we have a Transaction entity, convert it to a map first
        processVariables.putAll(convertTransactionToMap(transaction));
      } else if (transactionData != null) {
        // Otherwise use the provided transaction data map
        processVariables.putAll(transactionData);
      }

      // Add rule information
      processVariables.put("ruleId", rule.getId());
      processVariables.put("ruleName", rule.getName());
      processVariables.put("ruleDescription", rule.getDescription());

      // Generate a unique case number
      String caseNumber = "CASE-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
      processVariables.put("caseNumber", caseNumber);

      // Determine entity ID from transaction or transaction data
      Long entityId = null;
      if (transaction != null) {
        entityId = transaction.getId();
      } else if (transactionData != null && transactionData.containsKey("id")) {
        Object idObj = transactionData.get("id");
        if (idObj != null) {
          entityId = Long.parseLong(idObj.toString());
        }
      }

      // Create the workflow case
      WorkflowCase workflowCase =
          workflowCaseService.startCase(
              WorkflowType.COMPLIANCE, // Use COMPLIANCE type for the compliance review process
              "TRANSACTION", // Entity type
              entityId, // Entity ID
              processVariables);

      // Update case with additional information
      try {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("ruleId", rule.getId());
        metadata.put("ruleName", rule.getName());
        metadata.put("flagReason", rule.getFlagReason());

        // Add transaction details to metadata
        if (transaction != null) {
          metadata.put("transactionId", transaction.getId());
          metadata.put("transactionType", transaction.getTransType());
        } else if (transactionData != null) {
          // Only include essential transaction data in metadata to avoid large objects
          Map<String, Object> essentialData = new HashMap<>();
          if (transactionData.containsKey("id")) essentialData.put("id", transactionData.get("id"));
          if (transactionData.containsKey("customerId"))
            essentialData.put("customerId", transactionData.get("customerId"));
          if (transactionData.containsKey("transType"))
            essentialData.put("transType", transactionData.get("transType"));
          if (transactionData.containsKey("amount"))
            essentialData.put("amount", transactionData.get("amount"));
          metadata.put("transactionDetails", essentialData);
        }

        workflowCaseService.updateCaseMetadata(workflowCase.getId(), metadata);

        log.info(
            "Created workflow case {} for rule {} on transaction {}",
            workflowCase.getId(),
            rule.getId(),
            entityId != null ? entityId : "unknown");

        return workflowCase;
      } catch (Exception metadataEx) {
        log.error("Error updating case metadata: {}", metadataEx.getMessage());
        return workflowCase; // Still return the case even if metadata update fails
      }
    } catch (Exception e) {
      log.error("Error creating case for rule {}: {}", rule.getId(), e.getMessage());
      return null;
    }
  }

  /**
   * Converts transaction data to the correct types based on bucket variable definitions.
   *
   * @param transactionData The raw transaction data
   * @param bucketId The bucket ID to get variable definitions from
   * @return Transaction data with correctly typed values
   */
  private Map<String, Object> convertToCorrectTypes(
      Map<String, Object> transactionData, Long bucketId) {
    Map<String, Object> result = new HashMap<>();

    // Get bucket variables
    List<DataBucketVariableEntity> bucketVariables = dataBucketService.getVariables(bucketId);

    // Convert each field based on its defined type
    for (Map.Entry<String, Object> entry : transactionData.entrySet()) {
      String key = entry.getKey();
      Object value = entry.getValue();

      // Find the variable definition for this field
      DataBucketVariableEntity varDef =
          bucketVariables.stream().filter(v -> v.getCode().equals(key)).findFirst().orElse(null);

      if (varDef == null) {
        // If no definition found, keep the value as is
        result.put(key, value);
        continue;
      }

      // Convert based on data type
      String dataType = varDef.getDataType().getCode();
      result.put(key, convertValueToType(value, dataType));
    }

    return result;
  }

  /**
   * Converts a value to the specified data type.
   *
   * @param value The value to convert
   * @param dataType The target data type
   * @return The converted value
   */
  private Object convertValueToType(Object value, String dataType) {
    if (value == null) {
      return null;
    }

    try {
      switch (dataType.toLowerCase()) {
        case "string":
          return value.toString();
        case "integer":
          if (value instanceof String) {
            return Integer.parseInt((String) value);
          } else if (value instanceof Number) {
            return ((Number) value).intValue();
          }
          return value;
        case "decimal":
        case "double":
          if (value instanceof String) {
            return Double.parseDouble((String) value);
          } else if (value instanceof Number) {
            return ((Number) value).doubleValue();
          }
          return value;
        case "boolean":
          if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
          } else if (value instanceof Boolean) {
            return value;
          }
          return value;
        case "date":
          if (value instanceof String) {
            try {
              SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
              return dateFormat.parse((String) value);
            } catch (Exception e) {
              log.error("Failed to parse date: {}", value, e);
              return value;
            }
          } else if (value instanceof Date) {
            return value;
          }
          return value;
        case "datetime":
          if (value instanceof String) {
            try {
              SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
              return dateFormat.parse((String) value);
            } catch (Exception e) {
              log.error("Failed to parse datetime: {}", value, e);
              return value;
            }
          } else if (value instanceof Date) {
            return value;
          }
          return value;
        default:
          // For unknown types, return as is
          return value;
      }
    } catch (Exception e) {
      log.error("Error converting value {} to type {}: {}", value, dataType, e.getMessage());
    }

    // Return original value if conversion failed
    return value;
  }

  /**
   * Generate a deduplication key for a rule and transaction data. This method creates a unique key
   * based on rule ID and transaction data to prevent duplicate case creation for the same rule and
   * transaction.
   *
   * <p>Note: This method is currently used for reference in future implementations of deduplication
   * logic when creating workflow cases.
   *
   * @param rule The rule to generate a key for
   * @param transactionData The transaction data
   * @return A deduplication key
   */
  private String generateDeduplicationKey(Rule rule, Map<String, Object> transactionData) {
    // Base key on rule ID
    StringBuilder keyBuilder = new StringBuilder("RULE_").append(rule.getId());

    // Add bucket ID if available
    if (rule.getDataBucket() != null) {
      keyBuilder.append("_BUCKET_").append(rule.getDataBucket().getId());
    }

    // Add relevant transaction fields for deduplication
    // This is a simplified approach - in a real system, you might want to
    // configure which fields are used for deduplication per rule
    if (transactionData.containsKey("customerId")) {
      keyBuilder.append("_CUSTOMER_").append(transactionData.get("customerId"));
    }
    if (transactionData.containsKey("accountId")) {
      keyBuilder.append("_ACCOUNT_").append(transactionData.get("accountId"));
    }

    return keyBuilder.toString();
  }

  private Map<String, Object> convertTransactionToMap(Transaction transaction) {
    Map<String, Object> map = new HashMap<>();
    map.put("id", transaction.getId());
    map.put("customerId", transaction.getCustomerId());
    map.put("fundId", transaction.getFundId());
    map.put("fundName", transaction.getFundName());
    map.put("fundShortName", transaction.getFundShortName());
    map.put("fundBankAccount", transaction.getFundBankAccount());
    map.put("transDecs", transaction.getTransDecs());
    map.put("transaction", transaction.getTransaction());
    map.put("portfolioId", transaction.getPortfolioId());
    map.put("customerName", transaction.getCustomerName());
    map.put("crmBankAccountNo", transaction.getCrmBankAccountNo());
    map.put("orderType", transaction.getOrderType());
    map.put("unitTypeCode", transaction.getUnitTypeCode());
    map.put("unitTypeName", transaction.getUnitTypeName());
    map.put("dealDate", transaction.getDealDate());
    map.put("month", transaction.getMonth());
    map.put("transType", transaction.getTransType());
    map.put("fundNetAmount", transaction.getFundNetAmount());
    map.put("amountPerUnit", transaction.getAmountPerUnit());
    map.put("nav", transaction.getNav());
    map.put("transactionUnit", transaction.getTransactionUnit());
    map.put("entryLoad", transaction.getEntryLoad());
    map.put("discountAmt", transaction.getDiscountAmt());
    map.put("grossLoad", transaction.getGrossLoad());
    map.put("netLoad", transaction.getNetLoad());
    map.put("exitLoad", transaction.getExitLoad());
    map.put("zakatAmt", transaction.getZakatAmt());
    map.put("cgt", transaction.getCgt());
    map.put("transCost", transaction.getTransCost());
    map.put("col22", transaction.getCol22());
    map.put("rmCode", transaction.getRmCode());
    map.put("rmName", transaction.getRmName());
    map.put("branch", transaction.getBranch());
    map.put("newExist", transaction.getNewExist());
    map.put("region", transaction.getRegion());
    map.put("fundCategory", transaction.getFundCategory());
    map.put("industryDesc", transaction.getIndustryDesc());
    map.put("secpSector", transaction.getSecpSector());
    map.put("city", transaction.getCity());
    map.put("channel", transaction.getChannel());
    map.put("tradeDateTime", transaction.getTradeDateTime());
    map.put("flagged", transaction.isFlagged());
    map.put("flagReason", transaction.getFlagReason());
    map.put("customerRiskLevel", transaction.getCustomerRiskLevel());
    map.put("kycStatus", transaction.getKycStatus());
    map.put("profileLastUpdated", transaction.getProfileLastUpdated());
    map.put("loginAttempts", transaction.getLoginAttempts());
    return map;
  }
}
