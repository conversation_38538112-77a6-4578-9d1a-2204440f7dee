package com.progize.tms.repository.entity;

import com.progize.tms.repository.entity.base.BaseEntity;
import com.progize.tms.repository.entity.enums.BucketType;
import jakarta.persistence.*;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "data_buckets")
@Getter
@Setter
public class DataBucket extends BaseEntity {
  @Column(name = "name", nullable = false)
  private String name;

  @Column(name = "description")
  private String description;

  @Column(name = "table_name", nullable = false)
  private String tableName;

  @Column(name = "bucket_type", nullable = false)
  private Integer bucketType = BucketType.RULE_BUCKET.getValue(); // Default to RULE_BUCKET

  @Transient
  public BucketType getBucketTypeEnum() {
    return BucketType.fromValue(this.bucketType);
  }

  public void setBucketTypeEnum(BucketType bucketType) {
    this.bucketType = bucketType.getValue();
  }

  @OneToMany(mappedBy = "dataBucket", cascade = CascadeType.ALL, orphanRemoval = true)
  private Set<DataBucketVariable> variables;
}
