package com.progize.tms.repository;

import com.progize.tms.repository.entity.Transaction;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ClickHouseTransactionRepository {
  private final JdbcTemplate jdbcTemplate;

  private static final String INSERT_SQL =
      """
      INSERT INTO transactions (
          id, fund_id, fund_name, fund_short_name, fund_bank_account,
          trans_decs, transaction_ref, customer_id, portfolio_id,
          customer_name, crm_bank_account_no, order_type, unit_type_code,
          unit_type_name, deal_date, month, trans_type, fund_net_amount,
          amount_per_unit, nav, transaction_unit, entry_load, discount_amt,
          gross_load, net_load, exit_load, zakat_amt, cgt, trans_cost,
          col22, rm_code, rm_name, branch, new_exist, region,
          fund_category, industry_desc, secp_sector, city, channel,
          trade_date_time, flagged, flag_reason, customer_risk_level,
          kyc_status, profile_last_updated, login_attempts
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
               ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
               ?, ?, ?, ?, ?, ?, ?, ?, ?)
      """;

  private static final String FIND_RECENT_SQL =
      "SELECT * FROM transactions ORDER BY deal_date DESC LIMIT ?";

  private static final String FIND_BY_FLAGGED_SQL = "SELECT * FROM transactions WHERE flagged = ?";

  private static final String FIND_BY_ACCOUNT_ID_SQL =
      "SELECT * FROM transactions WHERE customer_id = ?";

  private static final String SUM_AGGREGATION_SQL =
      "SELECT SUM(%s) FROM transactions WHERE %s = ? AND deal_date >= ? AND deal_date <= ?";

  private static final String COUNT_AGGREGATION_SQL =
      "SELECT COUNT(%s) FROM transactions WHERE %s = ? AND deal_date >= ? AND deal_date <= ?";

  private static final String AVG_AGGREGATION_SQL =
      "SELECT AVG(%s) FROM transactions WHERE %s = ? AND deal_date >= ? AND deal_date <= ?";

  private static final String MAX_AGGREGATION_SQL =
      "SELECT MAX(%s) FROM transactions WHERE %s = ? AND deal_date >= ? AND deal_date <= ?";

  private static final String MIN_AGGREGATION_SQL =
      "SELECT MIN(%s) FROM transactions WHERE %s = ? AND deal_date >= ? AND deal_date <= ?";

  public ClickHouseTransactionRepository(JdbcTemplate jdbcTemplate) {
    this.jdbcTemplate = jdbcTemplate;
  }

  public void save(Transaction transaction) {
    jdbcTemplate.update(
        INSERT_SQL,
        new PreparedStatementSetter() {
          @Override
          public void setValues(PreparedStatement ps) throws SQLException {
            setParameters(ps, transaction);
          }
        });
  }

  public List<Transaction> findRecent(int limit) {
    return jdbcTemplate.query(FIND_RECENT_SQL, (ps, rowNum) -> mapTransaction(ps), limit);
  }

  public List<Transaction> findByFlagged(boolean flagged) {
    return jdbcTemplate.query(
        FIND_BY_FLAGGED_SQL, (ps, rowNum) -> mapTransaction(ps), flagged ? 1 : 0);
  }

  public List<Transaction> findByAccountId(String accountId) {
    return jdbcTemplate.query(
        FIND_BY_ACCOUNT_ID_SQL, (ps, rowNum) -> mapTransaction(ps), accountId);
  }

  public BigDecimal calculateSum(
      String field, int periodDays, String entityField, String entityId) {
    return calculateSum(field, periodDays, 0, entityField, entityId);
  }

  public BigDecimal calculateSum(
      String field, int periodDays, int startOffset, String entityField, String entityId) {
    try {
      // Handle variable expressions in entityField
      String entityFieldToUse;
      if (entityField == null || entityField.trim().isEmpty()) {
        entityFieldToUse = "customer_id";
      } else if (entityField.startsWith("${") && entityField.endsWith("}")) {
        // Extract the variable name from ${variable_name}
        String variableName = entityField.substring(2, entityField.length() - 1);
        // Use the column name that corresponds to the variable
        entityFieldToUse = variableName;
        log.debug(
            "Using column name '{}' extracted from variable expression: {}",
            entityFieldToUse,
            entityField);
      } else {
        entityFieldToUse = entityField;
      }

      String sql = String.format(SUM_AGGREGATION_SQL, field, entityFieldToUse);

      // Calculate start and end dates based on period and startOffset
      LocalDateTime endDate = LocalDateTime.now().minusDays(startOffset);
      LocalDateTime startDate = endDate.minusDays(periodDays);

      // For ClickHouse, use properly formatted date strings instead of Timestamps
      String startDateStr = startDate.toLocalDate().toString(); // Format as YYYY-MM-DD
      String endDateStr = endDate.toLocalDate().toString(); // Format as YYYY-MM-DD
      log.debug("Using ClickHouse date formats: start={}, end={}", startDateStr, endDateStr);

      return jdbcTemplate.queryForObject(
          sql,
          (rs, rowNum) -> rs.getBigDecimal(1) != null ? rs.getBigDecimal(1) : BigDecimal.ZERO,
          entityId,
          startDateStr,
          endDateStr);
    } catch (Exception e) {
      log.error("Error calculating sum for field: {} and entity ID: {}", field, entityId, e);
      return BigDecimal.ZERO;
    }
  }

  public Long calculateCount(String field, int periodDays, String entityField, String entityId) {
    return calculateCount(field, periodDays, 0, entityField, entityId);
  }

  public Long calculateCount(
      String field, int periodDays, int startOffset, String entityField, String entityId) {
    try {
      String fieldToCount = (field == null || field.trim().isEmpty()) ? "*" : field;

      // Handle variable expressions in entityField
      String entityFieldToUse;
      if (entityField == null || entityField.trim().isEmpty()) {
        entityFieldToUse = "customer_id";
      } else if (entityField.startsWith("${") && entityField.endsWith("}")) {
        // Extract the variable name from ${variable_name}
        String variableName = entityField.substring(2, entityField.length() - 1);
        // Use the column name that corresponds to the variable
        entityFieldToUse = variableName;
        log.debug(
            "Using column name '{}' extracted from variable expression: {}",
            entityFieldToUse,
            entityField);
      } else {
        entityFieldToUse = entityField;
      }

      String sql = String.format(COUNT_AGGREGATION_SQL, fieldToCount, entityFieldToUse);

      // Calculate start and end dates based on period and startOffset
      LocalDateTime endDate = LocalDateTime.now().minusDays(startOffset);
      LocalDateTime startDate = endDate.minusDays(periodDays);

      // For ClickHouse, use properly formatted date strings instead of Timestamps
      String startDateStr = startDate.toLocalDate().toString(); // Format as YYYY-MM-DD
      String endDateStr = endDate.toLocalDate().toString(); // Format as YYYY-MM-DD
      log.debug("Using ClickHouse date formats: start={}, end={}", startDateStr, endDateStr);

      return jdbcTemplate.queryForObject(
          sql, (rs, rowNum) -> rs.getLong(1), entityId, startDateStr, endDateStr);
    } catch (Exception e) {
      log.error("Error calculating count for field: {} and entity ID: {}", field, entityId, e);
      return 0L;
    }
  }

  public BigDecimal calculateAverage(
      String field, int periodDays, String entityField, String entityId) {
    try {
      // Handle variable expressions in entityField
      String entityFieldToUse;
      if (entityField == null || entityField.trim().isEmpty()) {
        entityFieldToUse = "customer_id";
      } else if (entityField.startsWith("${") && entityField.endsWith("}")) {
        // Extract the variable name from ${variable_name}
        String variableName = entityField.substring(2, entityField.length() - 1);
        // Use the column name that corresponds to the variable
        entityFieldToUse = variableName;
        log.debug(
            "Using column name '{}' extracted from variable expression: {}",
            entityFieldToUse,
            entityField);
      } else {
        entityFieldToUse = entityField;
      }

      String sql = String.format(AVG_AGGREGATION_SQL, field, entityFieldToUse);
      LocalDateTime startDate = LocalDateTime.now().minusDays(periodDays);

      // For ClickHouse, use a properly formatted date string instead of Timestamp
      String dateStr = startDate.toLocalDate().toString(); // Format as YYYY-MM-DD
      log.debug("Using ClickHouse date format: {}", dateStr);

      return jdbcTemplate.queryForObject(
          sql,
          (rs, rowNum) -> rs.getBigDecimal(1) != null ? rs.getBigDecimal(1) : BigDecimal.ZERO,
          entityId,
          dateStr);
    } catch (Exception e) {
      log.error("Error calculating average for field: {} and entity ID: {}", field, entityId, e);
      return BigDecimal.ZERO;
    }
  }

  public BigDecimal calculateMax(
      String field, int periodDays, String entityField, String entityId) {
    try {
      // Handle variable expressions in entityField
      String entityFieldToUse;
      if (entityField == null || entityField.trim().isEmpty()) {
        entityFieldToUse = "customer_id";
      } else if (entityField.startsWith("${") && entityField.endsWith("}")) {
        // Extract the variable name from ${variable_name}
        String variableName = entityField.substring(2, entityField.length() - 1);
        // Use the column name that corresponds to the variable
        entityFieldToUse = variableName;
        log.debug(
            "Using column name '{}' extracted from variable expression: {}",
            entityFieldToUse,
            entityField);
      } else {
        entityFieldToUse = entityField;
      }

      String sql = String.format(MAX_AGGREGATION_SQL, field, entityFieldToUse);
      LocalDateTime startDate = LocalDateTime.now().minusDays(periodDays);

      // For ClickHouse, use a properly formatted date string instead of Timestamp
      String dateStr = startDate.toLocalDate().toString(); // Format as YYYY-MM-DD
      log.debug("Using ClickHouse date format: {}", dateStr);

      return jdbcTemplate.queryForObject(
          sql,
          (rs, rowNum) -> rs.getBigDecimal(1) != null ? rs.getBigDecimal(1) : BigDecimal.ZERO,
          entityId,
          dateStr);
    } catch (Exception e) {
      log.error("Error calculating maximum for field: {} and entity ID: {}", field, entityId, e);
      return BigDecimal.ZERO;
    }
  }

  public BigDecimal calculateMin(
      String field, int periodDays, String entityField, String entityId) {
    try {
      // Handle variable expressions in entityField
      String entityFieldToUse;
      if (entityField == null || entityField.trim().isEmpty()) {
        entityFieldToUse = "customer_id";
      } else if (entityField.startsWith("${") && entityField.endsWith("}")) {
        // Extract the variable name from ${variable_name}
        String variableName = entityField.substring(2, entityField.length() - 1);
        // Use the column name that corresponds to the variable
        entityFieldToUse = variableName;
        log.debug(
            "Using column name '{}' extracted from variable expression: {}",
            entityFieldToUse,
            entityField);
      } else {
        entityFieldToUse = entityField;
      }

      String sql = String.format(MIN_AGGREGATION_SQL, field, entityFieldToUse);
      LocalDateTime startDate = LocalDateTime.now().minusDays(periodDays);

      // For ClickHouse, use a properly formatted date string instead of Timestamp
      String dateStr = startDate.toLocalDate().toString(); // Format as YYYY-MM-DD
      log.debug("Using ClickHouse date format: {}", dateStr);

      return jdbcTemplate.queryForObject(
          sql,
          (rs, rowNum) -> rs.getBigDecimal(1) != null ? rs.getBigDecimal(1) : BigDecimal.ZERO,
          entityId,
          dateStr);
    } catch (Exception e) {
      log.error("Error calculating minimum for field: {} and entity ID: {}", field, entityId, e);
      return BigDecimal.ZERO;
    }
  }

  private void setParameters(PreparedStatement ps, Transaction t) throws SQLException {
    ps.setLong(1, t.getId());
    ps.setString(2, t.getFundId());
    ps.setString(3, t.getFundName());
    ps.setString(4, t.getFundShortName());
    ps.setString(5, t.getFundBankAccount());
    ps.setString(6, t.getTransDecs());
    ps.setString(7, t.getTransaction());
    ps.setString(8, t.getCustomerId());
    ps.setString(9, t.getPortfolioId());
    ps.setString(10, t.getCustomerName());
    ps.setString(11, t.getCrmBankAccountNo());
    ps.setString(12, t.getOrderType());
    ps.setString(13, t.getUnitTypeCode());
    ps.setString(14, t.getUnitTypeName());
    ps.setTimestamp(15, Timestamp.valueOf(t.getDealDate()));
    ps.setString(16, t.getMonth());
    ps.setString(17, t.getTransType());
    ps.setBigDecimal(18, t.getFundNetAmount());
    ps.setBigDecimal(19, t.getAmountPerUnit());
    ps.setBigDecimal(20, t.getNav());
    ps.setBigDecimal(21, t.getTransactionUnit());
    ps.setBigDecimal(22, t.getEntryLoad());
    ps.setBigDecimal(23, t.getDiscountAmt());
    ps.setBigDecimal(24, t.getGrossLoad());
    ps.setBigDecimal(25, t.getNetLoad());
    ps.setBigDecimal(26, t.getExitLoad());
    ps.setBigDecimal(27, t.getZakatAmt());
    ps.setBigDecimal(28, t.getCgt());
    ps.setBigDecimal(29, t.getTransCost());
    ps.setString(30, t.getCol22());
    ps.setString(31, t.getRmCode());
    ps.setString(32, t.getRmName());
    ps.setString(33, t.getBranch());
    ps.setString(34, t.getNewExist());
    ps.setString(35, t.getRegion());
    ps.setString(36, t.getFundCategory());
    ps.setString(37, t.getIndustryDesc());
    ps.setString(38, t.getSecpSector());
    ps.setString(39, t.getCity());
    ps.setString(40, t.getChannel());
    ps.setTimestamp(41, Timestamp.valueOf(t.getTradeDateTime()));
    ps.setInt(42, t.isFlagged() ? 1 : 0);
    ps.setString(43, t.getFlagReason());
    ps.setString(44, t.getCustomerRiskLevel());
    ps.setString(45, t.getKycStatus());
    ps.setTimestamp(
        46,
        t.getProfileLastUpdated() != null ? Timestamp.valueOf(t.getProfileLastUpdated()) : null);
    ps.setInt(47, t.getLoginAttempts());
  }

  private Transaction mapTransaction(ResultSet rs) throws SQLException {
    Transaction t = new Transaction();
    t.setId(rs.getLong("id"));
    t.setFundId(rs.getString("fund_id"));
    t.setFundName(rs.getString("fund_name"));
    t.setFundShortName(rs.getString("fund_short_name"));
    t.setFundBankAccount(rs.getString("fund_bank_account"));
    t.setTransDecs(rs.getString("trans_decs"));
    t.setTransaction(rs.getString("transaction_ref"));
    t.setCustomerId(rs.getString("customer_id"));
    t.setPortfolioId(rs.getString("portfolio_id"));
    t.setCustomerName(rs.getString("customer_name"));
    t.setCrmBankAccountNo(rs.getString("crm_bank_account_no"));
    t.setOrderType(rs.getString("order_type"));
    t.setUnitTypeCode(rs.getString("unit_type_code"));
    t.setUnitTypeName(rs.getString("unit_type_name"));
    t.setDealDate(rs.getTimestamp("deal_date").toLocalDateTime());
    t.setMonth(rs.getString("month"));
    t.setTransType(rs.getString("trans_type"));
    t.setFundNetAmount(rs.getBigDecimal("fund_net_amount"));
    t.setAmountPerUnit(rs.getBigDecimal("amount_per_unit"));
    t.setNav(rs.getBigDecimal("nav"));
    t.setTransactionUnit(rs.getBigDecimal("transaction_unit"));
    t.setEntryLoad(rs.getBigDecimal("entry_load"));
    t.setDiscountAmt(rs.getBigDecimal("discount_amt"));
    t.setGrossLoad(rs.getBigDecimal("gross_load"));
    t.setNetLoad(rs.getBigDecimal("net_load"));
    t.setExitLoad(rs.getBigDecimal("exit_load"));
    t.setZakatAmt(rs.getBigDecimal("zakat_amt"));
    t.setCgt(rs.getBigDecimal("cgt"));
    t.setTransCost(rs.getBigDecimal("trans_cost"));
    t.setCol22(rs.getString("col22"));
    t.setRmCode(rs.getString("rm_code"));
    t.setRmName(rs.getString("rm_name"));
    t.setBranch(rs.getString("branch"));
    t.setNewExist(rs.getString("new_exist"));
    t.setRegion(rs.getString("region"));
    t.setFundCategory(rs.getString("fund_category"));
    t.setIndustryDesc(rs.getString("industry_desc"));
    t.setSecpSector(rs.getString("secp_sector"));
    t.setCity(rs.getString("city"));
    t.setChannel(rs.getString("channel"));
    t.setTradeDateTime(rs.getTimestamp("trade_date_time").toLocalDateTime());
    t.setFlagged(rs.getInt("flagged") == 1);
    t.setFlagReason(rs.getString("flag_reason"));
    t.setCustomerRiskLevel(rs.getString("customer_risk_level"));
    t.setKycStatus(rs.getString("kyc_status"));
    Timestamp profileLastUpdated = rs.getTimestamp("profile_last_updated");
    if (profileLastUpdated != null) {
      t.setProfileLastUpdated(profileLastUpdated.toLocalDateTime());
    }
    t.setLoginAttempts(rs.getInt("login_attempts"));
    return t;
  }
}
