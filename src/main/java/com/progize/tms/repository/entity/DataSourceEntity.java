package com.progize.tms.repository.entity;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

import com.progize.tms.repository.entity.base.BaseEntity;
import com.progize.tms.repository.entity.enums.DataSourceType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * Entity representing a data source that can be used for lookup buckets. This could be a database
 * connection, REST API, etc.
 */
@Entity
@Table(name = "data_source")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Audited(targetAuditMode = NOT_AUDITED)
public class DataSourceEntity extends BaseEntity {

  @Column(name = "name", nullable = false, unique = true, length = 255)
  private String name;

  @Column(name = "description")
  private String description;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private DataSourceType type;

  @Column(name = "active")
  private Boolean active;

  @Column(name = "last_schema_discovery")
  private LocalDateTime lastSchemaDiscovery;
}
