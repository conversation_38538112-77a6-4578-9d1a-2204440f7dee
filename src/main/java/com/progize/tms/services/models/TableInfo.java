package com.progize.tms.services.models;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Model representing a database table with metadata. */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TableInfo {
  private String schemaName;
  private String tableName;
  private String engine;
  private long totalRows;
  private long totalBytes;
  private String description;

  // Computed property for display
  public String getFullName() {
    return schemaName + "." + tableName;
  }
}
