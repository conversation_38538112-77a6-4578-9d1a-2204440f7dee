import React from 'react';
import { Box } from '@mui/material';
import AceEditor from 'react-ace';
import 'ace-builds/src-noconflict/mode-json';
import 'ace-builds/src-noconflict/theme-github';
import 'ace-builds/src-noconflict/ext-language_tools';

/**
 * JSONEditor component for editing JSON data
 * Uses react-ace (Ace Editor) for a nice JSON editing experience with syntax highlighting
 */
const JSONEditor = ({ value, onChange, height = "200px", width = "100%" }) => {
  // Handle changes when the user edits the JSON content
  const handleChange = (newValue) => {
    try {
      // Parse to ensure it's valid JSON
      const parsedJson = JSON.parse(newValue);
      onChange(parsedJson);
    } catch (error) {
      // If it's not valid JSON, still update the editor content
      // but don't call the onChange callback with invalid JSON
      console.warn('Invalid JSON:', error);
    }
  };

  // Convert value to a pretty-printed JSON string
  const jsonString = typeof value === 'string' 
    ? value 
    : JSON.stringify(value, null, 2);

  return (
    <Box sx={{ width: '100%', border: '1px solid #e0e0e0', borderRadius: 1 }}>
      <AceEditor
        mode="json"
        theme="github"
        width={width}
        height={height}
        value={jsonString}
        onChange={handleChange}
        name="json-editor"
        editorProps={{ $blockScrolling: true }}
        setOptions={{
          useWorker: false, // Disable worker for better performance
          showPrintMargin: false,
          tabSize: 2,
          enableBasicAutocompletion: true,
          enableLiveAutocompletion: true,
          enableSnippets: false
        }}
      />
    </Box>
  );
};

export default JSONEditor;
