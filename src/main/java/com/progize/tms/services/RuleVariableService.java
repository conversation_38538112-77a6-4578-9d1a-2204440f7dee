package com.progize.tms.services;

import com.progize.tms.services.models.AggregateFunction;
import com.progize.tms.services.models.Operator;
import com.progize.tms.services.models.RuleVariable;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for handling rule variables, operators, and aggregate functions. This service provides
 * access to variables that can be used in rule conditions.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleVariableService {

  private final Map<String, RuleVariable> variablesMap = new ConcurrentHashMap<>();
  private final Map<String, Operator> operatorsMap = new ConcurrentHashMap<>();
  private final Map<String, AggregateFunction> aggregateFunctionsMap = new ConcurrentHashMap<>();

  private final OperatorRegistry operatorRegistry;
  private final RuleVariableRegistryDb ruleVariableRegistry;

  /**
   * Initialize variable definitions, operators, and aggregate functions. In a production system,
   * these might come from a database or configuration.
   */
  @PostConstruct
  public void init() {
    // Load variables from DB-backed registry to maintain backward compatibility
    initializeFromRegistry();
    initializeOperators();
    initializeAggregateFunctions();
  }

  /**
   * Get all rule variables.
   *
   * @return List of all rule variables
   */
  public List<RuleVariable> getAllVariables() {
    return new ArrayList<>(ruleVariableRegistry.getAllVariables());
  }

  /**
   * Get a rule variable by ID.
   *
   * @param id The ID of the variable
   * @return The rule variable, or null if not found
   */
  public RuleVariable getVariable(String id) {
    return ruleVariableRegistry.getVariableById(id);
  }

  /**
   * Get variables by category (table name in the new implementation).
   *
   * @param category The category/table name
   * @return List of variables in the category
   */
  public List<RuleVariable> getVariablesByCategory(String category) {
    return ruleVariableRegistry.getVariablesBySourceTable(category);
  }

  /**
   * Get all operators.
   *
   * @return List of all operators
   */
  public List<Operator> getAllOperators() {
    return new ArrayList<>(operatorsMap.values());
  }

  /**
   * Get operators applicable to the specified type.
   *
   * @param type The variable type (string, number, boolean, date, etc.)
   * @return List of applicable operators
   */
  public List<Operator> getOperatorsByType(String type) {
    return operatorsMap.values().stream()
        .filter(op -> op.getApplicableTypes().contains(type))
        .collect(Collectors.toList());
  }

  /**
   * Get all aggregate functions.
   *
   * @return List of all aggregate functions
   */
  public List<AggregateFunction> getAllAggregateFunctions() {
    return new ArrayList<>(aggregateFunctionsMap.values());
  }

  /**
   * Maps specific types to their general category for aggregation purposes. This helps maintain
   * compatibility when transitioning from generic to specific types.
   *
   * @param type The specific type to normalize
   * @return The normalized type for aggregation purposes
   */
  private String normalizeTypeForAggregation(String type) {
    switch (type) {
      case "bigdecimal":
      case "integer":
      case "int":
      case "long":
      case "double":
      case "float":
        return "number";
      default:
        return type;
    }
  }

  /**
   * Get aggregate functions applicable to the specified type.
   *
   * @param type The variable type (string, number, boolean, date, etc.)
   * @return List of applicable aggregate functions
   */
  public List<AggregateFunction> getAggregateFunctionsByType(String type) {
    String normalizedType = normalizeTypeForAggregation(type);
    return aggregateFunctionsMap.values().stream()
        .filter(
            func ->
                func.getApplicableTypes().contains(normalizedType)
                    || func.getApplicableTypes().contains(type))
        .collect(Collectors.toList());
  }

  /**
   * Initialize variables from the database registry. This replaces the old hardcoded implementation
   * while maintaining compatibility.
   */
  private void initializeFromRegistry() {
    // Clear any existing entries
    variablesMap.clear();

    // Load all variables from registry
    Collection<RuleVariable> allVariables = ruleVariableRegistry.getAllVariables();

    // Add to map for backward compatibility
    for (RuleVariable variable : allVariables) {
      variablesMap.put(variable.getId(), variable);
    }

    log.info("Loaded {} rule variables from registry", allVariables.size());
  }

  private void initializeOperators() {
    List<Operator> operators = new ArrayList<>();

    // General equality operators
    operators.add(
        Operator.builder()
            .id(OperatorRegistry.EQUALS)
            .name("Equals")
            .description("Checks if the variable equals the specified value")
            .symbol("==")
            .applicableTypes(
                Arrays.asList(
                    "string", "number", "bigdecimal", "integer", "date", "boolean", "double"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.NOT_EQUALS)
            .name("Not Equals")
            .description("Checks if the variable does not equal the specified value")
            .symbol("!=")
            .applicableTypes(
                Arrays.asList(
                    "string", "number", "bigdecimal", "integer", "date", "boolean", "double"))
            .build());

    // Comparison operators
    operators.add(
        Operator.builder()
            .id(OperatorRegistry.GREATER_THAN)
            .name("Greater Than")
            .description("Checks if the variable is greater than the specified value")
            .symbol(">")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer", "date", "double"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.LESS_THAN)
            .name("Less Than")
            .description("Checks if the variable is less than the specified value")
            .symbol("<")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer", "date", "double"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.GREATER_THAN_EQUALS)
            .name("Greater Than or Equal To")
            .description("Checks if the variable is greater than or equal to the specified value")
            .symbol(">=")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer", "date", "double"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.LESS_THAN_EQUALS)
            .name("Less Than or Equal To")
            .description("Checks if the variable is less than or equal to the specified value")
            .symbol("<=")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer", "date", "double"))
            .build());

    // String operators
    operators.add(
        Operator.builder()
            .id(OperatorRegistry.CONTAINS)
            .name("Contains")
            .description("Checks if the variable contains the specified substring")
            .symbol("contains")
            .applicableTypes(Arrays.asList("string"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.STARTS_WITH)
            .name("Starts With")
            .description("Checks if the variable starts with the specified substring")
            .symbol("startsWith")
            .applicableTypes(Arrays.asList("string"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.ENDS_WITH)
            .name("Ends With")
            .description("Checks if the variable ends with the specified substring")
            .symbol("endsWith")
            .applicableTypes(Arrays.asList("string"))
            .build());

    // Collection operators
    operators.add(
        Operator.builder()
            .id(OperatorRegistry.IN)
            .name("In")
            .description("Checks if the variable is in the specified list of values")
            .symbol("in")
            .applicableTypes(Arrays.asList("string", "number", "bigdecimal", "integer"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.NOT_IN)
            .name("Not In")
            .description("Checks if the variable is not in the specified list of values")
            .symbol("not in")
            .applicableTypes(Arrays.asList("string", "number", "bigdecimal", "integer"))
            .build());

    // Boolean operators
    operators.add(
        Operator.builder()
            .id(OperatorRegistry.IS_TRUE)
            .name("Is True")
            .description("Checks if the variable is true")
            .symbol("== true")
            .applicableTypes(Arrays.asList("boolean"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.IS_FALSE)
            .name("Is False")
            .description("Checks if the variable is false")
            .symbol("== false")
            .applicableTypes(Arrays.asList("boolean"))
            .build());

    // Null operators
    operators.add(
        Operator.builder()
            .id(OperatorRegistry.IS_NULL)
            .name("Is Null")
            .description("Checks if the variable is null")
            .symbol("== null")
            .applicableTypes(
                Arrays.asList("string", "number", "bigdecimal", "integer", "date", "boolean"))
            .build());

    operators.add(
        Operator.builder()
            .id(OperatorRegistry.IS_NOT_NULL)
            .name("Is Not Null")
            .description("Checks if the variable is not null")
            .symbol("!= null")
            .applicableTypes(
                Arrays.asList("string", "number", "bigdecimal", "integer", "date", "boolean"))
            .build());

    // Between operator
    operators.add(
        Operator.builder()
            .id(OperatorRegistry.BETWEEN)
            .name("Between")
            .description("Checks if the value is between the specified range")
            .symbol("between")
            .applicableTypes(Arrays.asList("date", "number", "bigdecimal", "integer"))
            .build());

    // Add all operators to the map for easy lookup
    for (Operator operator : operators) {
      operatorsMap.put(operator.getId(), operator);
    }
  }

  private void initializeAggregateFunctions() {
    List<AggregateFunction> functions = new ArrayList<>();

    functions.add(
        AggregateFunction.builder()
            .id("sum")
            .name("Sum")
            .description("Calculates the sum of the variable over a period")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer"))
            .build());

    functions.add(
        AggregateFunction.builder()
            .id("avg")
            .name("Average")
            .description("Calculates the average of the variable over a period")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer"))
            .build());

    functions.add(
        AggregateFunction.builder()
            .id("min")
            .name("Minimum")
            .description("Finds the minimum value of the variable over a period")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer", "date"))
            .build());

    functions.add(
        AggregateFunction.builder()
            .id("max")
            .name("Maximum")
            .description("Finds the maximum value of the variable over a period")
            .applicableTypes(Arrays.asList("number", "bigdecimal", "integer", "date"))
            .build());

    functions.add(
        AggregateFunction.builder()
            .id("count")
            .name("Count")
            .description("Counts the occurrences of transactions matching criteria over a period")
            .applicableTypes(
                Arrays.asList("string", "number", "bigdecimal", "integer", "boolean", "date"))
            .build());

    // Add all functions to the map for easy lookup
    for (AggregateFunction function : functions) {
      aggregateFunctionsMap.put(function.getId(), function);
    }
  }
}
