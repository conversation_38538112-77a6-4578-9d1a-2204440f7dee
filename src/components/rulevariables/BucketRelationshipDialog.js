import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Grid,
  alpha,
  useTheme
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { dataBucketService, bucketRelationshipService } from '../../services/api';

const BucketRelationshipDialog = ({ open, onClose, ruleBucketId, relationshipToEdit = null }) => {
  const [lookupBuckets, setLookupBuckets] = useState([]);
  const [selectedLookupBucket, setSelectedLookupBucket] = useState('');
  const [ruleBucketVariables, setRuleBucketVariables] = useState([]);
  const [lookupBucketVariables, setLookupBucketVariables] = useState([]);
  const [joinConditions, setJoinConditions] = useState([{ ruleBucketVariableId: '', lookupBucketVariableId: '' }]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const theme = useTheme();

  const isEditMode = !!relationshipToEdit;

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Load all lookup buckets
        const buckets = await dataBucketService.getAllBuckets();
        const lookupBucketsData = buckets.filter(bucket => bucket.bucketType === 'LOOKUP');
        setLookupBuckets(lookupBucketsData);
        
        // Load rule bucket variables
        const ruleVariables = await dataBucketService.getVariablesByBucketId(ruleBucketId);
        setRuleBucketVariables(ruleVariables);
        
        // If in edit mode, set initial values
        if (isEditMode) {
          setSelectedLookupBucket(relationshipToEdit.lookupBucketId);
          
          // Load lookup bucket variables
          const lookupVariables = await dataBucketService.getVariablesByBucketId(relationshipToEdit.lookupBucketId);
          setLookupBucketVariables(lookupVariables);
          
          // Set join conditions
          setJoinConditions(relationshipToEdit.joinConditions.map(condition => ({
            ruleBucketVariableId: condition.ruleBucketVariableId,
            lookupBucketVariableId: condition.lookupBucketVariableId
          })));
        }
        
        setError(null);
      } catch (err) {
        console.error('Error loading data for relationship dialog:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };
    
    if (open) {
      loadData();
    }
  }, [open, ruleBucketId, isEditMode, relationshipToEdit]);

  const handleLookupBucketChange = async (event) => {
    const lookupBucketId = event.target.value;
    setSelectedLookupBucket(lookupBucketId);
    
    // Reset join conditions
    setJoinConditions([{ ruleBucketVariableId: '', lookupBucketVariableId: '' }]);
    
    // Load variables for the selected lookup bucket
    try {
      const variables = await dataBucketService.getVariablesByBucketId(lookupBucketId);
      setLookupBucketVariables(variables);
    } catch (err) {
      console.error('Error loading lookup bucket variables:', err);
      setError('Failed to load lookup bucket variables');
    }
  };

  const handleAddJoinCondition = () => {
    setJoinConditions([...joinConditions, { ruleBucketVariableId: '', lookupBucketVariableId: '' }]);
  };

  const handleRemoveJoinCondition = (index) => {
    const newConditions = [...joinConditions];
    newConditions.splice(index, 1);
    setJoinConditions(newConditions);
  };

  const handleJoinConditionChange = (index, field, value) => {
    const newConditions = [...joinConditions];
    newConditions[index][field] = value;
    setJoinConditions(newConditions);
  };

  const handleSave = async () => {
    // Validate form
    if (!selectedLookupBucket) {
      setError('Please select a lookup bucket');
      return;
    }
    
    if (joinConditions.length === 0) {
      setError('At least one join condition is required');
      return;
    }
    
    for (const condition of joinConditions) {
      if (!condition.ruleBucketVariableId || !condition.lookupBucketVariableId) {
        setError('Please complete all join conditions');
        return;
      }
    }
    
    try {
      setSaving(true);
      
      const request = {
        ruleBucketId,
        lookupBucketId: selectedLookupBucket,
        joinConditions
      };
      
      if (isEditMode) {
        // For now, we'll delete and recreate since we don't have an update endpoint
        await bucketRelationshipService.deleteRelationship(relationshipToEdit.id);
        await bucketRelationshipService.createRelationship(request);
      } else {
        await bucketRelationshipService.createRelationship(request);
      }
      
      onClose(true); // Pass true to indicate successful save
    } catch (err) {
      console.error('Error saving relationship:', err);
      setError(`Failed to ${isEditMode ? 'update' : 'create'} relationship: ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={() => !saving && onClose(false)} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '10px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        }
      }}
    >
      <DialogTitle sx={{ 
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        pb: 2
      }}>
        {isEditMode ? 'Edit Lookup Relationship' : 'Add Lookup Relationship'}
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}
            
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Lookup Bucket</InputLabel>
              <Select
                value={selectedLookupBucket}
                onChange={handleLookupBucketChange}
                label="Lookup Bucket"
                disabled={isEditMode || saving}
              >
                {lookupBuckets.map((bucket) => (
                  <MenuItem key={bucket.id} value={bucket.id}>
                    {bucket.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <Typography variant="subtitle1" gutterBottom>
              Join Conditions
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Define how the rule bucket should be joined with the lookup bucket.
            </Typography>
            
            {joinConditions.map((condition, index) => (
              <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                <Grid item xs={5}>
                  <FormControl fullWidth>
                    <InputLabel>Rule Bucket Variable</InputLabel>
                    <Select
                      value={condition.ruleBucketVariableId}
                      onChange={(e) => handleJoinConditionChange(index, 'ruleBucketVariableId', e.target.value)}
                      label="Rule Bucket Variable"
                      disabled={saving}
                    >
                      {ruleBucketVariables.map((variable) => (
                        <MenuItem key={variable.id} value={variable.id}>
                          {variable.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={1} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1">=</Typography>
                </Grid>
                
                <Grid item xs={5}>
                  <FormControl fullWidth>
                    <InputLabel>Lookup Bucket Variable</InputLabel>
                    <Select
                      value={condition.lookupBucketVariableId}
                      onChange={(e) => handleJoinConditionChange(index, 'lookupBucketVariableId', e.target.value)}
                      label="Lookup Bucket Variable"
                      disabled={!selectedLookupBucket || saving}
                    >
                      {lookupBucketVariables.map((variable) => (
                        <MenuItem key={variable.id} value={variable.id}>
                          {variable.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={1} sx={{ display: 'flex', alignItems: 'center' }}>
                  {joinConditions.length > 1 && (
                    <IconButton
                      onClick={() => handleRemoveJoinCondition(index)}
                      disabled={saving}
                      size="small"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  )}
                </Grid>
              </Grid>
            ))}
            
            <Button
              startIcon={<AddIcon />}
              onClick={handleAddJoinCondition}
              disabled={!selectedLookupBucket || saving}
              sx={{ mt: 1 }}
              size="small"
              variant="outlined"
            >
              Add Join Condition
            </Button>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, py: 2, borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
        <Button 
          onClick={() => onClose(false)} 
          disabled={saving}
          sx={{
            borderRadius: '20px',
            px: 2
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || saving}
          startIcon={saving && <CircularProgress size={20} />}
          sx={{
            borderRadius: '20px',
            px: 2,
            boxShadow: '0 2px 8px 0 rgba(0,118,255,0.2)',
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 12px rgba(0,118,255,0.25)'
            }
          }}
        >
          {saving ? 'Saving...' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BucketRelationshipDialog;
