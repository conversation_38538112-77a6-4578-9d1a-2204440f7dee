package com.progize.tms.repository;

import com.progize.tms.repository.entity.Operator;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface OperatorRepository extends JpaRepository<Operator, Long> {

  Optional<Operator> findByCode(String code);

  @Query("SELECT o FROM Operator o WHERE o.updatedAt > :timestamp")
  List<Operator> findUpdatedSince(@Param("timestamp") LocalDateTime timestamp);
}
