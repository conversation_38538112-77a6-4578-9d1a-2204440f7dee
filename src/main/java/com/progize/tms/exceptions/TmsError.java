package com.progize.tms.exceptions;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public enum TmsError {
  USER_NOT_FOUND("User not found", HttpStatus.NOT_FOUND),
  EMAIL_ALREADY_EXISTS("Email already exists", HttpStatus.BAD_REQUEST),
  INVALID_REQUEST("Invalid request", HttpStatus.BAD_REQUEST),

  // Data source errors
  DATA_SOURCE_NOT_FOUND("Data source not found", HttpStatus.NOT_FOUND),
  DATABASE_SOURCE_NOT_FOUND("Database source not found", HttpStatus.NOT_FOUND),
  REST_API_SOURCE_NOT_FOUND("REST API source not found", HttpStatus.NOT_FOUND),
  DATA_SOURCE_INACTIVE("Data source is not active", HttpStatus.BAD_REQUEST),
  UNSUPPORTED_DATA_SOURCE_TYPE("Unsupported data source type", HttpStatus.BAD_REQUEST),
  CONNECTION_ERROR("Error connecting to data source", HttpStatus.INTERNAL_SERVER_ERROR),
  SCHEMA_DISCOVERY_ERROR("Error discovering schema", HttpStatus.INTERNAL_SERVER_ERROR);

  private final String message;
  private final HttpStatus status;

  TmsError(String message, HttpStatus status) {
    this.message = message;
    this.status = status;
  }
}
