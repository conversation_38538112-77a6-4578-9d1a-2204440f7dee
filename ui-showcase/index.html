<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SoniQue Rule UI Design Concepts</title>
  <!-- Add Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <!-- Add Inter font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    .header {
      margin-bottom: 40px;
    }
    h1 {
      font-weight: 600;
      color: #0c4e7a;
      margin: 0 0 10px 0;
    }
    .subtitle {
      color: #64748b;
      margin: 0;
    }
    .design-section {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
      margin-bottom: 40px;
      overflow: hidden;
    }
    .design-header {
      padding: 20px;
      background-color: #f1f5f9;
      border-bottom: 1px solid #e2e8f0;
    }
    .design-title {
      font-weight: 600;
      margin: 0;
      color: #0f172a;
      display: flex;
      align-items: center;
    }
    .design-title span {
      margin-right: 10px;
    }
    .design-content {
      padding: 20px;
    }
    .design-image {
      width: 100%;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    }
    .design-description {
      margin-top: 20px;
      color: #475569;
      line-height: 1.5;
    }
    .feature-list {
      margin-top: 15px;
      padding-left: 20px;
    }
    .feature-list li {
      margin-bottom: 8px;
      color: #334155;
    }
    .badge {
      display: inline-block;
      padding: 4px 10px;
      border-radius: 999px;
      font-size: 12px;
      font-weight: 500;
      background-color: #e0f2fe;
      color: #0c4e7a;
      margin-left: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>SoniQue Rule Listing UI Design Concepts</h1>
      <p class="subtitle">Visual explorations for enhancing the rule listing page in the SoniQue Transaction Management System</p>
    </div>

    <!-- Card Layout -->
    <div class="design-section">
      <div class="design-header">
        <h2 class="design-title">
          <span class="material-icons">dashboard</span>
          Card-Based Layout
          <span class="badge">Modern</span>
        </h2>
      </div>
      <div class="design-content">
        <img src="https://i.ibb.co/XbLyCJ9/card-layout.png" alt="Card-Based Layout" class="design-image">
        <div class="design-description">
          <p>A visual grid of rule cards that transform the traditional table into a more modern, card-based interface. Each card contains a summary of the rule with key information prominently displayed.</p>
          <ul class="feature-list">
            <li><strong>Visual Hierarchy:</strong> Important information like status and priority are highlighted with chips</li>
            <li><strong>Interactive Elements:</strong> Cards elevate on hover for an interactive feel</li>
            <li><strong>Responsive Design:</strong> Automatically adjusts to different screen sizes</li>
            <li><strong>Compact Information:</strong> Displays key data in a space-efficient format</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Enhanced Table -->
    <div class="design-section">
      <div class="design-header">
        <h2 class="design-title">
          <span class="material-icons">table_chart</span>
          Enhanced Table with Visual Hierarchy
          <span class="badge">Refined</span>
        </h2>
      </div>
      <div class="design-content">
        <img src="https://i.ibb.co/3dfVyh9/enhanced-table.png" alt="Enhanced Table" class="design-image">
        <div class="design-description">
          <p>An improved version of the traditional table view with better visual hierarchy, subtle hover states, and clearer distinction between elements. This approach maintains the familiar table format while enhancing usability and aesthetics.</p>
          <ul class="feature-list">
            <li><strong>Subtle Backgrounds:</strong> Headers and alternating rows with slight color differentiation</li>
            <li><strong>Interactive Rows:</strong> Rows highlight on hover for better user feedback</li>
            <li><strong>Clear Typography:</strong> Improved font weights and sizes for scannability</li>
            <li><strong>Consistent Spacing:</strong> Better cell padding for improved readability</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- List View -->
    <div class="design-section">
      <div class="design-header">
        <h2 class="design-title">
          <span class="material-icons">list</span>
          List View with Expandable Sections
          <span class="badge">Compact</span>
        </h2>
      </div>
      <div class="design-content">
        <img src="https://i.ibb.co/k1GgDtt/list-view.png" alt="List View" class="design-image">
        <div class="design-description">
          <p>A vertically compact list view that maximizes information density while maintaining readability. Each rule is displayed as a list item with an avatar indicating status, and the ability to expand for more details.</p>
          <ul class="feature-list">
            <li><strong>Avatar Indicators:</strong> Visual cues for rule status</li>
            <li><strong>Space Efficient:</strong> Displays more rules per screen</li>
            <li><strong>Scannable:</strong> Easy to quickly parse multiple rules</li>
            <li><strong>Secondary Information:</strong> Contextual details displayed with proper hierarchy</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Categorized Tables -->
    <div class="design-section">
      <div class="design-header">
        <h2 class="design-title">
          <span class="material-icons">tab</span>
          Categorized Tables with Tabs
          <span class="badge">Organized</span>
        </h2>
      </div>
      <div class="design-content">
        <img src="https://i.ibb.co/SX9SRh2/tabs-view.png" alt="Categorized Tables" class="design-image">
        <div class="design-description">
          <p>A tabbed interface that allows users to quickly filter between different categories of rules (All, Active, Inactive, High Priority). This approach combines the familiarity of tables with the organizational benefits of categorization.</p>
          <ul class="feature-list">
            <li><strong>Quick Filtering:</strong> Immediate access to specific rule categories</li>
            <li><strong>Contextual Headers:</strong> Color-coded headers with high contrast</li>
            <li><strong>Focused View:</strong> Reduces cognitive load by showing only relevant rules</li>
            <li><strong>Maintains Context:</strong> Users always know which category they're viewing</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Timeline View -->
    <div class="design-section">
      <div class="design-header">
        <h2 class="design-title">
          <span class="material-icons">timeline</span>
          Timeline View
          <span class="badge">Chronological</span>
        </h2>
      </div>
      <div class="design-content">
        <img src="https://i.ibb.co/HG0MThG/timeline-view.png" alt="Timeline View" class="design-image">
        <div class="design-description">
          <p>A chronological presentation of rules based on their last update time, creating a historical perspective on rule changes. This approach is especially useful for auditing and understanding the evolution of the rule set.</p>
          <ul class="feature-list">
            <li><strong>Temporal Context:</strong> Visualizes when rules were last modified</li>
            <li><strong>Visual Connection:</strong> Timeline connector shows relationships</li>
            <li><strong>Hierarchical Information:</strong> Clear organization of rule details</li>
            <li><strong>Card-Like Presentation:</strong> Each rule is presented in its own contained space</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Data Grid View -->
    <div class="design-section">
      <div class="design-header">
        <h2 class="design-title">
          <span class="material-icons">grid_on</span>
          Data Grid View
          <span class="badge">Powerful</span>
        </h2>
      </div>
      <div class="design-content">
        <img src="https://i.ibb.co/D5g2WH7/data-grid.png" alt="Data Grid View" class="design-image">
        <div class="design-description">
          <p>A feature-rich data grid with advanced capabilities like selection, sorting, filtering, and pagination. This approach is ideal for power users who need to manage large sets of rules with complex operations.</p>
          <ul class="feature-list">
            <li><strong>Advanced Controls:</strong> Checkboxes for bulk operations, export functionality</li>
            <li><strong>Pagination:</strong> Built-in navigation for large rule sets</li>
            <li><strong>Status Summary:</strong> Shows count and selection information</li>
            <li><strong>Consistent Header/Footer:</strong> Clear containment of the data area</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Kanban Board View -->
    <div class="design-section">
      <div class="design-header">
        <h2 class="design-title">
          <span class="material-icons">view_kanban</span>
          Kanban Board View
          <span class="badge">Visual</span>
        </h2>
      </div>
      <div class="design-content">
        <img src="https://i.ibb.co/n1Fq9Ww/kanban-view.png" alt="Kanban Board View" class="design-image">
        <div class="design-description">
          <p>A Kanban-style board that organizes rules by their status (Active vs. Inactive). This approach gives a clear visual separation between rule states and could potentially support drag-and-drop for changing status.</p>
          <ul class="feature-list">
            <li><strong>Status Columns:</strong> Clear visual separation of rule states</li>
            <li><strong>Color Coding:</strong> Subtle background colors to reinforce status</li>
            <li><strong>Compact Cards:</strong> Essential information in a space-efficient format</li>
            <li><strong>Visual Count:</strong> Immediate feedback on the distribution of rules</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
