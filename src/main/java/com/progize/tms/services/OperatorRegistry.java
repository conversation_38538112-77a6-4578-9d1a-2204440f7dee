package com.progize.tms.services;

import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Centralized registry for rule operators and their MVEL expressions. This allows consistent
 * operator usage across rule variables and expression building.
 *
 * <p>This implementation delegates to the database-backed OperatorRegistryDb.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperatorRegistry {
  // Standard operator constants
  public static final String EQUALS = "equals";
  public static final String NOT_EQUALS = "notEquals";
  public static final String GREATER_THAN = "greaterThan";
  public static final String LESS_THAN = "lessThan";
  public static final String GREATER_THAN_EQUALS = "greaterThanEquals";
  public static final String LESS_THAN_EQUALS = "lessThanEquals";
  public static final String BETWEEN = "between";
  public static final String CONTAINS = "contains";
  public static final String STARTS_WITH = "startsWith";
  public static final String ENDS_WITH = "endsWith";
  public static final String MATCHES = "matches";
  public static final String IS_EMPTY = "isEmpty";
  public static final String IS_NOT_EMPTY = "isNotEmpty";
  public static final String IN = "in";
  public static final String NOT_IN = "notIn";
  public static final String IS_TRUE = "isTrue";
  public static final String IS_FALSE = "isFalse";
  public static final String IS_NULL = "isNull";
  public static final String IS_NOT_NULL = "isNotNull";

  private final OperatorRegistryDb operatorRegistryDb;

  /** Get the MVEL expression for a given operator and field type */
  public String getOperatorExpression(String operatorId, String fieldType) {
    return operatorRegistryDb.getOperatorExpression(operatorId, fieldType);
  }

  /** Get the operator ID for a given MVEL expression (reverse lookup) */
  public String getOperatorIdForExpression(String expression) {
    return operatorRegistryDb.getOperatorIdForExpression(expression);
  }

  /** Get the default set of supported operators for a given field type */
  public List<String> getDefaultSupportedOperators(String fieldType) {
    List<String> operators = operatorRegistryDb.getDefaultSupportedOperators(fieldType);

    // If no operators found in the database for this data type, use the fallback logic
    if (operators.isEmpty()) {
      log.warn(
          "No operators found in database for data type: {}. Using fallback logic.", fieldType);
      return getFallbackSupportedOperators(fieldType);
    }

    return operators;
  }

  /** Fallback method to return operators for types not in the database */
  private List<String> getFallbackSupportedOperators(String fieldType) {
    switch (fieldType) {
      case "string":
        return Arrays.asList(
            EQUALS,
            NOT_EQUALS,
            CONTAINS,
            STARTS_WITH,
            ENDS_WITH,
            IS_EMPTY,
            IS_NOT_EMPTY,
            IS_NULL,
            IS_NOT_NULL);
      case "bigdecimal":
      case "number", "date":
        return Arrays.asList(
            EQUALS,
            NOT_EQUALS,
            GREATER_THAN,
            LESS_THAN,
            GREATER_THAN_EQUALS,
            LESS_THAN_EQUALS,
            BETWEEN,
            IS_NULL,
            IS_NOT_NULL);
      case "integer":
      case "int":
      case "long":
      case "double":
      case "float":
        return Arrays.asList(
            EQUALS,
            NOT_EQUALS,
            GREATER_THAN,
            LESS_THAN,
            GREATER_THAN_EQUALS,
            LESS_THAN_EQUALS,
            IS_NULL,
            IS_NOT_NULL);
      case "boolean":
        return Arrays.asList(IS_TRUE, IS_FALSE, IS_NULL, IS_NOT_NULL);
      default:
        return Arrays.asList(EQUALS, NOT_EQUALS, IS_NULL, IS_NOT_NULL);
    }
  }

  /** Check if a field type is a primitive numeric type */
  public boolean isPrimitiveNumeric(String fieldType) {
    return "integer".equals(fieldType)
        || "int".equals(fieldType)
        || "long".equals(fieldType)
        || "double".equals(fieldType)
        || "float".equals(fieldType);
  }
}
