package com.progize.tms.controllers.rules.req;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class RuleCondition {

  @NotNull(message = "Condition type is required")
  private ConditionType type;

  // For SIMPLE conditions
  private String field;
  private String operator;
  private Object value;
  private Boolean isExpression;

  // For LOGICAL conditions (AND, OR)
  private List<RuleCondition> conditions;

  // For AGGREGATE conditions
  private String function; // SUM, COUNT, AVG, etc.
  private String aggregateField; // Field to aggregate
  private Integer period; // Time period in days
  private Integer
      startOffset; // Days in the past to start the time window (0 means start from today)
  private Object threshold; // The value to compare against
  private String entityField; // Field to identify the entity
  // For filtered AGGREGATE conditions
  private RuleCondition
      filterCondition; // Optional condition to filter transactions before aggregation

  public enum ConditionType {
    SIMPLE,
    AND,
    OR,
    NOT,
    AGGREGATE,
    COMPARISON
  }

  // For COMPARISON conditions
  private RuleCondition left;
  private RuleCondition right;
  private Double multiplier;

  // Constructor for SIMPLE
  public RuleCondition(ConditionType type, String field, String operator, Object value) {
    this.type = type;
    this.field = field;
    this.operator = operator;
    this.value = value;
  }

  // Constructor for LOGICAL types (AND, OR, NOT)
  public RuleCondition(ConditionType type, List<RuleCondition> conditions) {
    this.type = type;
    this.conditions = conditions;
  }

  // Constructor for AGGREGATE
  public RuleCondition(
      ConditionType type,
      String function,
      String aggregateField,
      Integer period,
      Integer startOffset,
      String operator,
      Object threshold,
      String entityField) {
    this.type = type;
    this.function = function;
    this.aggregateField = aggregateField;
    this.period = period;
    this.startOffset = startOffset;
    this.operator = operator;
    this.threshold = threshold;
    this.entityField = entityField;
  }

  // Constructor for AGGREGATE without startOffset (for backward compatibility)
  public RuleCondition(
      ConditionType type,
      String function,
      String aggregateField,
      Integer period,
      String operator,
      Object threshold,
      String entityField) {
    this(type, function, aggregateField, period, 0, operator, threshold, entityField);
  }

  // Constructor for COMPARISON
  public RuleCondition(
      ConditionType type,
      RuleCondition left,
      String operator,
      RuleCondition right,
      Double multiplier) {
    this.type = type;
    this.left = left;
    this.operator = operator;
    this.right = right;
    this.multiplier = multiplier;
  }
}
