import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  CircularProgress,
  IconButton,
  Paper,
  Tooltip,
  useTheme,
  alpha,
  Alert,
  Snackbar,
  DialogContentText,
  styled
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import { permissionService } from '../../services/api';

// Styled components
const PermissionCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  transition: 'all 0.3s cubic-bezier(.25,.8,.25,1)',
  '&:hover': {
    boxShadow: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
    transform: 'translateY(-2px)'
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(5),
  padding: theme.spacing(1, 3),
  textTransform: 'none',
  fontWeight: 500,
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  transition: 'all 0.2s ease',
  '&:hover': {
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    transform: 'translateY(-1px)'
  }
}));

const PermissionManagement = () => {
  const theme = useTheme();
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentPermission, setCurrentPermission] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch permissions on component mount
  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const data = await permissionService.getAllPermissions();
      setPermissions(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load permissions',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Reset form data
  const resetFormData = () => {
    setFormData({
      name: '',
      description: ''
    });
  };

  // Open create modal
  const openCreateModal = () => {
    resetFormData();
    setShowCreateModal(true);
  };

  // Open edit modal
  const openEditModal = (permission) => {
    setCurrentPermission(permission);
    setFormData({
      name: permission.name,
      description: permission.description || ''
    });
    setShowEditModal(true);
  };

  // Open delete modal
  const openDeleteModal = (permission) => {
    setCurrentPermission(permission);
    setShowDeleteModal(true);
  };

  // Create permission
  const createPermission = async () => {
    try {
      setLoading(true);
      const response = await permissionService.createPermission(formData);
      setPermissions([...permissions, response]);
      setShowCreateModal(false);
      resetFormData();
      setSnackbar({
        open: true,
        message: 'Permission created successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error creating permission:', error);
      setSnackbar({
        open: true,
        message: 'Failed to create permission',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Update permission
  const updatePermission = async () => {
    try {
      setLoading(true);
      const response = await permissionService.updatePermission(currentPermission.id, formData);
      setPermissions(permissions.map(permission => 
        permission.id === currentPermission.id ? response : permission
      ));
      setShowEditModal(false);
      resetFormData();
      setSnackbar({
        open: true,
        message: 'Permission updated successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error updating permission:', error);
      setSnackbar({
        open: true,
        message: 'Failed to update permission',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Delete permission
  const deletePermission = async () => {
    try {
      setLoading(true);
      await permissionService.deletePermission(currentPermission.id);
      setPermissions(permissions.filter(permission => permission.id !== currentPermission.id));
      setShowDeleteModal(false);
      setSnackbar({
        open: true,
        message: 'Permission deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error deleting permission:', error);
      setSnackbar({
        open: true,
        message: 'Failed to delete permission',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  return (
    <Container fluid>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 3 
        }}>
          <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
            Permission Management
          </Typography>
          <ActionButton
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={openCreateModal}
          >
            Create New Permission
          </ActionButton>
        </Box>
        
        {loading && !permissions.length ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {permissions.length > 0 ? (
              <Box>
                {permissions.map(permission => (
                  <PermissionCard key={permission.id}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <VpnKeyIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {permission.name}
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                            {permission.description || 'No description provided'}
                          </Typography>
                        </Box>
                        <Box>
                          <Tooltip title="Edit Permission">
                            <IconButton 
                              onClick={() => openEditModal(permission)}
                              sx={{ 
                                color: theme.palette.primary.main,
                                '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.1) }
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Permission">
                            <IconButton 
                              onClick={() => openDeleteModal(permission)}
                              sx={{ 
                                color: theme.palette.error.main,
                                '&:hover': { backgroundColor: alpha(theme.palette.error.main, 0.1) }
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </CardContent>
                  </PermissionCard>
                ))}
              </Box>
            ) : (
              <Paper 
                sx={{ 
                  p: 4, 
                  borderRadius: 2,
                  textAlign: 'center',
                  bgcolor: alpha(theme.palette.background.paper, 0.6),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <Box sx={{ mb: 2 }}>
                  <VpnKeyIcon sx={{ fontSize: 48, color: theme.palette.text.disabled }} />
                </Box>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 500 }}>
                  No permissions found
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                  Create a new permission to get started
                </Typography>
                <ActionButton
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={openCreateModal}
                >
                  Create New Permission
                </ActionButton>
              </Paper>
            )}
          </>
        )}
      </Box>

      {/* Create Permission Modal */}
      <Dialog 
        open={showCreateModal} 
        onClose={() => setShowCreateModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
          py: 2
        }}>
          Create New Permission
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" sx={{ '& .MuiTextField-root': { my: 1 } }}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter permission name"
              required
              margin="normal"
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter permission description"
              multiline
              rows={2}
              margin="normal"
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ 
          px: 3, 
          py: 2,
          borderTop: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
        }}>
          <Button 
            onClick={() => setShowCreateModal(false)} 
            color="inherit"
          >
            Cancel
          </Button>
          <Button 
            onClick={createPermission}
            variant="contained" 
            color="primary"
            disabled={!formData.name.trim()}
            startIcon={<AddIcon />}
          >
            Create Permission
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Permission Modal */}
      <Dialog 
        open={showEditModal} 
        onClose={() => setShowEditModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
          py: 2
        }}>
          Edit Permission
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" sx={{ '& .MuiTextField-root': { my: 1 } }}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter permission name"
              required
              margin="normal"
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter permission description"
              multiline
              rows={2}
              margin="normal"
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ 
          px: 3, 
          py: 2,
          borderTop: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
        }}>
          <Button 
            onClick={() => setShowEditModal(false)} 
            color="inherit"
          >
            Cancel
          </Button>
          <Button 
            onClick={updatePermission}
            variant="contained" 
            color="primary"
            disabled={!formData.name.trim()}
            startIcon={<EditIcon />}
          >
            Update Permission
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Permission Modal */}
      <Dialog
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxWidth: '400px',
            backgroundImage: 'linear-gradient(to bottom, rgba(255,255,255,0.95), rgba(255,255,255,0.98))',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
          }
        }}
      >
        <DialogTitle sx={{
          fontWeight: 600,
          fontSize: '1.1rem',
          pb: 1,
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          color: theme.palette.error.main,
          display: 'flex',
          alignItems: 'center',
        }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Confirm Deletion
        </DialogTitle>
        <DialogContent sx={{ pt: 2, mt: 1 }}>
          <DialogContentText variant="body2" sx={{ color: theme.palette.text.primary }}>
            Are you sure you want to delete the permission <Box component="span" sx={{ fontWeight: 600 }}>"{currentPermission?.name}"</Box>? This action cannot be undone and may affect roles that use this permission.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={() => setShowDeleteModal(false)}
            variant="outlined"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
            size="small"
          >
            Cancel
          </Button>
          <Button
            onClick={deletePermission}
            color="error"
            variant="contained"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              backgroundColor: theme.palette.error.main,
              '&:hover': {
                backgroundColor: theme.palette.error.dark,
                boxShadow: '0 4px 12px rgba(211, 47, 47, 0.3)',
              }
            }}
            size="small"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar 
        open={snackbar.open} 
        autoHideDuration={6000} 
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity} 
          sx={{ width: '100%' }}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default PermissionManagement;
