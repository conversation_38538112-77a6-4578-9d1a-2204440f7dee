package com.progize.tms.controllers.ruletesting;

import com.progize.tms.controllers.ruletesting.req.RuleTestReq;
import com.progize.tms.controllers.ruletesting.res.RuleTestResultRes;
import com.progize.tms.payload.ApiResponse;
import com.progize.tms.services.RuleTestingService;
import com.progize.tms.services.models.RuleTestingBO;
import com.progize.tms.services.models.RuleTestingResultBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for rule testing functionality. Following TMS architectural principles, this
 * controller handles HTTP concerns and uses a transformer to convert between REST DTOs and service
 * layer business objects.
 */
@RestController
@RequestMapping("/api/rule-testing")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*") // Enable CORS for the Rule endpoints to work with the UI
public class RuleTestingController {

  private final RuleTestingService ruleTestingService;
  private final RuleTestingTransformer transformer;

  /**
   * Test a rule against sample data.
   *
   * @param request The rule test request containing rule definition and sample data
   * @return The test results with analysis and debug information
   */
  @PostMapping
  public ResponseEntity<ApiResponse<RuleTestResultRes>> testRule(@RequestBody RuleTestReq request) {
    log.info("Testing rule with sample data");

    // Transform request DTO to business object
    RuleTestingBO testingBO = transformer.toBusinessObject(request);

    // No need to hardcode useRealAggregations as it's set by the UI now
    // and properly transformed in the transformer

    // Call service layer
    RuleTestingResultBO resultBO = ruleTestingService.testRule(testingBO);

    // Transform result back to response DTO
    RuleTestResultRes response = transformer.toResponseDTO(resultBO);

    log.info("Rule test completed, result: {}", resultBO.isPassed());

    // Return response
    return ResponseEntity.ok(
        new ApiResponse<>(true, "Rule evaluation completed successfully", response));
  }
}
