package com.progize.tms.controllers.datasources.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Base request class for creating a data source. */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateDataSourceRequest {
  @NotBlank(message = "Name is required")
  private String name;

  private String description;

  @NotNull(message = "Type is required")
  private String type;
}
