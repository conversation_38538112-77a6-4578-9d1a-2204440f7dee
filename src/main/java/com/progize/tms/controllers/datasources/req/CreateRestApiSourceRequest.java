package com.progize.tms.controllers.datasources.req;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/** Request class for creating a REST API data source. */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreateRestApiSourceRequest extends CreateDataSourceRequest {
  @NotBlank(message = "Base URL is required")
  private String baseUrl;

  @NotBlank(message = "Authentication type is required")
  private String authenticationType;

  private String username;

  private String password;

  private String apiKeyName;

  private String apiKeyValue;

  private String apiKeyLocation;

  private String oauthTokenUrl;

  private String oauthClientId;

  private String oauthClientSecret;

  private String oauthScope;

  private String headers;

  private Integer requestTimeoutMs;
}
