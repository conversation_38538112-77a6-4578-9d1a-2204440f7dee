import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  CircularProgress,
  Alert,
  Tooltip
} from '@mui/material';
import {
  InsertDriveFile as FileIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  Description as TextIcon,
  Code as CodeIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import CaseService from '../../services/CaseService';

/**
 * Component for displaying attachments on a case
 */
const CaseAttachments = ({ caseId, refreshKey }) => {
  const [attachments, setAttachments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch attachments when the component mounts or refreshKey changes
  useEffect(() => {
    const fetchAttachments = async () => {
      setLoading(true);
      try {
        const data = await CaseService.getAttachments(caseId);
        setAttachments(data);
        setError(null);
      } catch (err) {
        setError('Failed to fetch attachments. Please try again later.');
        setAttachments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAttachments();
  }, [caseId, refreshKey]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format file size for display
  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown';
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // Get appropriate icon based on file type
  const getFileIcon = (fileType) => {
    if (!fileType) return <FileIcon />;
    
    if (fileType.includes('pdf')) {
      return <PdfIcon color="error" />;
    } else if (fileType.includes('image')) {
      return <ImageIcon color="primary" />;
    } else if (fileType.includes('text')) {
      return <TextIcon color="info" />;
    } else if (fileType.includes('json') || fileType.includes('xml') || fileType.includes('html')) {
      return <CodeIcon color="secondary" />;
    } else {
      return <FileIcon />;
    }
  };

  // Handle file download
  const handleDownload = (attachment) => {
    // In a real app, this would download the file from the server
    // For now, we'll just show an alert
    alert(`Downloading file: ${attachment.fileName}`);
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  // Render empty state
  if (attachments.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">No attachments yet.</Typography>
      </Paper>
    );
  }

  // Render attachments
  return (
    <Paper>
      <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
        {attachments.map((attachment, index) => (
          <React.Fragment key={attachment.id}>
            <ListItem>
              <ListItemIcon>
                {getFileIcon(attachment.fileType)}
              </ListItemIcon>
              <ListItemText
                primary={attachment.fileName}
                secondary={
                  <>
                    <Typography component="span" variant="body2" color="text.secondary">
                      {formatFileSize(attachment.fileSize)} • Uploaded by {attachment.uploadedBy} • {formatDate(attachment.uploadedAt)}
                    </Typography>
                  </>
                }
              />
              <ListItemSecondaryAction>
                <Tooltip title="Download">
                  <IconButton edge="end" onClick={() => handleDownload(attachment)}>
                    <DownloadIcon />
                  </IconButton>
                </Tooltip>
              </ListItemSecondaryAction>
            </ListItem>
            {index < attachments.length - 1 && <Divider />}
          </React.Fragment>
        ))}
      </List>
    </Paper>
  );
};

export default CaseAttachments;
