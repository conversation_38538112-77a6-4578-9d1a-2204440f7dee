package com.progize.tms.controllers.rulevariables;

import com.progize.tms.controllers.rulevariables.res.ColumnInfoResponse;
import com.progize.tms.controllers.rulevariables.res.RuleVariableResponse;
import com.progize.tms.controllers.rulevariables.res.RuleVariableSourceTableResponse;
import com.progize.tms.controllers.rulevariables.res.TableInfoResponse;
import com.progize.tms.repository.entity.RuleVariableEntity;
import com.progize.tms.repository.entity.RuleVariableSourceTable;
import com.progize.tms.services.models.ColumnInfo;
import com.progize.tms.services.models.RuleVariable;
import com.progize.tms.services.models.TableInfo;
import org.springframework.stereotype.Component;

/**
 * Transformer for converting between rule variable entities, models, and DTOs. Maintains separation
 * between REST layer DTOs and service/data layer objects.
 */
@Component
public class RuleRegistryVariableTransformer {

  /**
   * Convert a rule variable model to a REST response DTO.
   *
   * @param model The rule variable model
   * @return The rule variable response DTO
   */
  public RuleVariableResponse toResponse(RuleVariable model) {
    RuleVariableResponse response = new RuleVariableResponse();
    response.setId(model.getId());
    response.setName(model.getName());
    response.setDescription(model.getDescription());
    response.setType(model.getType());
    response.setCategory(model.getCategory());
    response.setAggregatable(model.isAggregatable());
    response.setSupportedOperators(model.getSupportedOperators());
    response.setColumnName(model.getColumnName());
    response.setTableName(model.getTableName());
    response.setMetadata(model.getMetadata());
    return response;
  }

  /**
   * Convert a rule variable entity to a REST response DTO.
   *
   * @param entity The rule variable entity
   * @return The rule variable response DTO
   */
  public RuleVariableResponse toRuleVariableResponse(RuleVariableEntity entity) {
    RuleVariableResponse response = new RuleVariableResponse();
    response.setId(entity.getCode());
    response.setEntityId(entity.getId());
    response.setName(entity.getDisplayName());
    response.setDescription(entity.getDescription());
    response.setType(entity.getDataType().getCode());
    response.setCategory(entity.getSourceTable().getTableName());
    response.setAggregatable(entity.isAggregatable());
    response.setColumnName(entity.getColumnName());
    response.setTableName(entity.getSourceTable().getTableName());
    response.setMetadata(entity.getMetadata());
    // Note: supportedOperators is populated from the service layer with
    // getDefaultSupportedOperators
    return response;
  }

  /**
   * Convert a source table entity to a REST response DTO.
   *
   * @param entity The source table entity
   * @return The source table response DTO
   */
  public RuleVariableSourceTableResponse toSourceTableResponse(RuleVariableSourceTable entity) {
    RuleVariableSourceTableResponse response = new RuleVariableSourceTableResponse();
    response.setId(entity.getId());
    response.setTableName(entity.getTableName());
    response.setDisplayName(entity.getDisplayName());
    response.setSchemaName(entity.getSchemaName());
    response.setDescription(entity.getDescription());
    response.setConnectionName(entity.getConnectionName());
    response.setMetadata(entity.getMetadata());
    return response;
  }

  /**
   * Convert a table info model to a REST response DTO.
   *
   * @param model The table info model
   * @return The table info response DTO
   */
  public TableInfoResponse toTableInfoResponse(TableInfo model) {
    TableInfoResponse response = new TableInfoResponse();
    response.setSchemaName(model.getSchemaName());
    response.setTableName(model.getTableName());
    response.setEngine(model.getEngine());
    response.setTotalRows(model.getTotalRows());
    response.setTotalBytes(model.getTotalBytes());
    response.setDescription(model.getDescription());
    response.setFullName(model.getFullName());
    return response;
  }

  /**
   * Convert a column info model to a REST response DTO.
   *
   * @param model The column info model
   * @return The column info response DTO
   */
  public ColumnInfoResponse toColumnInfoResponse(ColumnInfo model) {
    ColumnInfoResponse response = new ColumnInfoResponse();
    response.setName(model.getName());
    response.setType(model.getDataType());
    response.setPosition(model.getPosition());
    response.setMappedDataType(model.getMappedDataType());
    response.setDescription(model.getDescription());
    response.setSuggestedDisplayName(model.generateDisplayName());
    return response;
  }
}
