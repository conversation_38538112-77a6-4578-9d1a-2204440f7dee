import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Breadcrumbs,
  Link,
  CircularProgress,
  Alert,
  useTheme
} from '@mui/material';
import DataSourceForm from '../../components/datasources/DataSourceForm';
import dataSourceService from '../../services/dataSourceService';

/**
 * Page component for editing an existing data source
 */
const DataSourceEditPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const [dataSource, setDataSource] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load data source details on component mount
  useEffect(() => {
    const loadDataSource = async () => {
      try {
        setLoading(true);
        const data = await dataSourceService.getDataSourceById(id);
        setDataSource(data);
        setError(null);
      } catch (err) {
        console.error('Error loading data source:', err);
        setError('Failed to load data source details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadDataSource();
    }
  }, [id]);

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ bgcolor: theme.palette.mode === 'dark' ? 'transparent' : '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
      <DataSourceForm
        mode="edit"
        initialData={dataSource}
        dataSourceId={parseInt(id)}
      />
    </Box>
  );
};

export default DataSourceEditPage;
