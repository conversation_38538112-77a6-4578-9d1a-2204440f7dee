import React, { useState, useCallback, useRef, useMemo } from 'react';
import {
  ReactFlow,
  ReactFlowProvider,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  useReactFlow,
  MarkerType,
  Panel
} from 'reactflow';
import 'reactflow/dist/style.css';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Divider,
  Container,
  Grid,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Drawer,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Tooltip,
  alpha,
  useTheme
} from '@mui/material';
import {
  Add as AddIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  ArrowForward as ArrowForwardIcon,
  Settings as SettingsIcon,
  Timeline as TimelineIcon,
  List as ListIcon,
  People as PeopleIcon
} from '@mui/icons-material';

// Import custom components
import StepNode from './StepNode';
import TransitionEdge from './TransitionEdge';
import StepConfigPanel from './StepConfigPanel';
import PicklistEditor from './PicklistEditor';
import RoleSelector from './RoleSelector';
import WorkflowDesignerService from '../../services/WorkflowDesignerService';

const WorkflowDesigner = () => {
  const theme = useTheme();
  const reactFlowWrapper = useRef(null);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedEdge, setSelectedEdge] = useState(null);
  const [configPanelOpen, setConfigPanelOpen] = useState(false);
  const [workflowId, setWorkflowId] = useState(null);
  const [workflowName, setWorkflowName] = useState('New Workflow');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [availableRoles, setAvailableRoles] = useState([]);
  const [availablePicklists, setAvailablePicklists] = useState([]);
  
  // Define node types
  const nodeTypes = useMemo(() => ({ stepNode: StepNode }), []);
  
  // Define edge types
  const edgeTypes = useMemo(() => ({ transition: TransitionEdge }), []);
  
  // Connection line style
  const connectionLineStyle = { stroke: theme.palette.primary.main, strokeWidth: 2 };
  
  // Default viewport
  const defaultViewport = { x: 0, y: 0, zoom: 1.5 };
  
  // Load available roles and picklists
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        const roles = await WorkflowDesignerService.getRoles();
        setAvailableRoles(roles);
        
        const picklists = await WorkflowDesignerService.getPicklists();
        setAvailablePicklists(picklists);
      } catch (error) {
        console.error('Error loading workflow data:', error);
      }
    };
    
    fetchData();
  }, []);
  
  // Handle node click
  const onNodeClick = useCallback((event, node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
    setConfigPanelOpen(true);
    setActiveTab(0);
  }, []);
  
  // Handle edge click
  const onEdgeClick = useCallback((event, edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
    setConfigPanelOpen(true);
    setActiveTab(0);
  }, []);
  
  // Handle connection
  const onConnect = useCallback((params) => {
    // Create a new edge with a default name
    const newEdge = {
      ...params,
      type: 'transition',
      animated: true,
      data: {
        name: 'New Transition',
        actionName: 'ACTION_NAME'
      },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
        color: theme.palette.primary.main
      }
    };
    
    setEdges((eds) => addEdge(newEdge, eds));
  }, [theme.palette.primary.main]);
  
  // Update node
  const updateNode = (nodeId, data) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              ...data
            }
          };
        }
        return node;
      })
    );
  };
  
  // Update edge
  const updateEdge = (edgeId, data) => {
    setEdges((eds) =>
      eds.map((edge) => {
        if (edge.id === edgeId) {
          return {
            ...edge,
            data: {
              ...edge.data,
              ...data
            }
          };
        }
        return edge;
      })
    );
  };
  
  // Load workflow
  const loadWorkflow = async (id) => {
    try {
      const workflow = await WorkflowDesignerService.getWorkflowById(id);
      setWorkflowId(workflow.id);
      setWorkflowName(workflow.name);
      setWorkflowDescription(workflow.description);
      
      // Convert workflow steps to nodes
      const workflowNodes = workflow.steps.map(step => ({
        id: step.id.toString(),
        type: 'stepNode',
        position: step.position || { x: 100, y: 100 },
        data: {
          label: step.name,
          description: step.description,
          statusLabel: step.statusLabel,
          stepOrder: step.stepOrder,
          eligibleRoles: step.eligibleRoles,
          picklist: step.picklist,
          requiresAttachment: step.requiresAttachment,
          requiresComments: step.requiresComments
        }
      }));
      
      // Convert workflow transitions to edges
      const workflowEdges = workflow.transitions.map(transition => ({
        id: transition.id.toString(),
        source: transition.fromStep.id.toString(),
        target: transition.toStep.id.toString(),
        type: 'transition',
        data: {
          name: transition.name,
          description: transition.description,
          actionName: transition.actionName
        },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 20,
          height: 20,
          color: theme.palette.primary.main
        }
      }));
      
      setNodes(workflowNodes);
      setEdges(workflowEdges);
    } catch (error) {
      console.error('Error loading workflow:', error);
    }
  };
  
  // Save workflow
  const saveWorkflow = async () => {
    try {
      const workflowData = {
        id: workflowId,
        name: workflowName,
        description: workflowDescription,
        steps: nodes.map(node => ({
          id: node.id,
          name: node.data.label,
          description: node.data.description,
          statusLabel: node.data.statusLabel,
          stepOrder: node.data.stepOrder,
          position: node.position,
          eligibleRoles: node.data.eligibleRoles,
          picklist: node.data.picklist,
          requiresAttachment: node.data.requiresAttachment,
          requiresComments: node.data.requiresComments
        })),
        transitions: edges.map(edge => ({
          id: edge.id,
          name: edge.data?.name || 'Transition',
          description: edge.data?.description || '',
          actionName: edge.data?.actionName || 'ACTION',
          fromStep: { id: edge.source },
          toStep: { id: edge.target }
        }))
      };
      
      const savedWorkflow = await WorkflowDesignerService.saveWorkflow(workflowData);
      setWorkflowId(savedWorkflow.id);
      setSaveDialogOpen(false);
    } catch (error) {
      console.error('Error saving workflow:', error);
    }
  };
  
  // Add new step
  const addNewStep = () => {
    const newId = `step-${Date.now()}`;
    const newNode = {
      id: newId,
      type: 'stepNode',
      position: {
        x: 100 + Math.random() * 200,
        y: 100 + Math.random() * 200
      },
      data: {
        label: 'New Step',
        description: 'Step description',
        statusLabel: 'NEW',
        stepOrder: nodes.length + 1,
        eligibleRoles: [],
        requiresAttachment: false,
        requiresComments: false
      }
    };
    
    setNodes(nds => [...nds, newNode]);
    setSelectedNode(newNode);
    setConfigPanelOpen(true);
  };
  
  // Delete selected node
  const deleteSelectedNode = () => {
    if (selectedNode) {
      // Remove all connected edges
      const connectedEdges = edges.filter(
        edge => edge.source === selectedNode.id || edge.target === selectedNode.id
      );
      
      setEdges(eds => eds.filter(
        edge => edge.source !== selectedNode.id && edge.target !== selectedNode.id
      ));
      
      // Remove the node
      setNodes(nds => nds.filter(node => node.id !== selectedNode.id));
      
      setSelectedNode(null);
      setConfigPanelOpen(false);
    }
  };
  
  // Delete selected edge
  const deleteSelectedEdge = () => {
    if (selectedEdge) {
      setEdges(eds => eds.filter(edge => edge.id !== selectedEdge.id));
      setSelectedEdge(null);
      setConfigPanelOpen(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  return (
    <ReactFlowProvider>
      <Box sx={{ display: 'flex', height: 'calc(100vh - 64px)' }}>
        <Box sx={{ flexGrow: 1, height: '100%' }} ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onEdgeClick={onEdgeClick}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            onInit={setReactFlowInstance}
            connectionLineStyle={connectionLineStyle}
            defaultViewport={defaultViewport}
            connectionMode="loose"
            snapToGrid={true}
            snapGrid={[10, 10]}
            fitView
            attributionPosition="bottom-right"
          >
            <Controls />
            <Background color="#aaa" gap={16} />
            <Panel position="top-left">
              <Paper sx={{ p: 2, mb: 2, borderRadius: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {workflowName}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {workflowDescription || 'No description'}
                </Typography>
              </Paper>
            </Panel>
            <Panel position="top-right">
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={addNewStep}
                >
                  Add Step
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<SaveIcon />}
                  onClick={() => setSaveDialogOpen(true)}
                >
                  Save Workflow
                </Button>
              </Box>
            </Panel>
          </ReactFlow>
        </Box>
        
        {/* Configuration Panel */}
        <Drawer
          anchor="right"
          open={configPanelOpen}
          onClose={() => setConfigPanelOpen(false)}
          variant="persistent"
          sx={{
            width: 350,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: 350,
              boxSizing: 'border-box',
              height: 'calc(100% - 64px)',
              top: 64
            }
          }}
        >
          <Box sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {selectedNode ? 'Step Configuration' : selectedEdge ? 'Transition Configuration' : 'Configuration'}
              </Typography>
              <IconButton onClick={() => setConfigPanelOpen(false)}>
                <CloseIcon />
              </IconButton>
            </Box>
            
            {selectedNode && (
              <>
                <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
                  <Tab label="Properties" />
                  <Tab label="Picklist" />
                  <Tab label="Roles" />
                </Tabs>
                
                {activeTab === 0 && (
                  <StepConfigPanel 
                    node={selectedNode} 
                    updateNode={updateNode} 
                    deleteNode={deleteSelectedNode} 
                  />
                )}
                
                {activeTab === 1 && (
                  <PicklistEditor 
                    node={selectedNode} 
                    updateNode={updateNode} 
                    picklists={availablePicklists}
                  />
                )}
                
                {activeTab === 2 && (
                  <RoleSelector 
                    node={selectedNode} 
                    roles={availableRoles} 
                    updateNode={updateNode} 
                  />
                )}
              </>
            )}
            
            {selectedEdge && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Transition Properties
                </Typography>
                
                <TextField
                  label="Name"
                  fullWidth
                  margin="normal"
                  size="small"
                  value={selectedEdge.data?.name || ''}
                  onChange={(e) => updateEdge(selectedEdge.id, { name: e.target.value })}
                />
                
                <TextField
                  label="Action Name"
                  fullWidth
                  margin="normal"
                  size="small"
                  value={selectedEdge.data?.actionName || ''}
                  onChange={(e) => updateEdge(selectedEdge.id, { actionName: e.target.value })}
                />
                
                <TextField
                  label="Description"
                  fullWidth
                  margin="normal"
                  size="small"
                  multiline
                  rows={3}
                  value={selectedEdge.data?.description || ''}
                  onChange={(e) => updateEdge(selectedEdge.id, { description: e.target.value })}
                />
                
                <Button
                  variant="contained"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={deleteSelectedEdge}
                  sx={{ mt: 2 }}
                >
                  Delete Transition
                </Button>
              </Box>
            )}
          </Box>
        </Drawer>
        
        {/* Save Dialog */}
        <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)}>
          <DialogTitle>Save Workflow</DialogTitle>
          <DialogContent>
            <TextField
              label="Workflow Name"
              fullWidth
              margin="normal"
              value={workflowName}
              onChange={(e) => setWorkflowName(e.target.value)}
            />
            <TextField
              label="Description"
              fullWidth
              margin="normal"
              multiline
              rows={3}
              value={workflowDescription}
              onChange={(e) => setWorkflowDescription(e.target.value)}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
            <Button onClick={saveWorkflow} variant="contained" color="primary">
              Save
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ReactFlowProvider>
  );
};

export default WorkflowDesigner;
