import React from 'react';
import { Box, Container, Typography, Breadcrumbs, Link } from '@mui/material';
import { Home as HomeIcon, Assignment as AssignmentIcon } from '@mui/icons-material';
import { Routes, Route, Link as RouterLink } from 'react-router-dom';
import ComplianceTaskList from '../components/compliance/ComplianceTaskList';
import ComplianceTaskDetail from '../components/compliance/ComplianceTaskDetail';

/**
 * Page component for compliance tasks section
 */
const ComplianceTasksPage = () => {
  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Breadcrumbs navigation */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
          <Link
            component={RouterLink}
            to="/"
            sx={{
              display: 'flex',
              alignItems: 'center',
              color: 'text.secondary',
              textDecoration: 'none',
              '&:hover': { textDecoration: 'underline' }
            }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="small" />
            Home
          </Link>
          <Typography
            sx={{
              display: 'flex',
              alignItems: 'center',
              color: 'text.primary'
            }}
          >
            <AssignmentIcon sx={{ mr: 0.5 }} fontSize="small" />
            Compliance Tasks
          </Typography>
        </Breadcrumbs>

        {/* Routes for task list and task details */}
        <Routes>
          <Route path="/" element={<ComplianceTaskList />} />
          <Route path="/:taskId" element={<ComplianceTaskDetail />} />
        </Routes>
      </Box>
    </Container>
  );
};

export default ComplianceTasksPage;
