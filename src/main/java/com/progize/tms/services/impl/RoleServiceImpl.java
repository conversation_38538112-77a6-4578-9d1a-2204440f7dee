package com.progize.tms.services.impl;

import com.progize.tms.repository.PermissionRepository;
import com.progize.tms.repository.RoleRepository;
import com.progize.tms.repository.entity.Permission;
import com.progize.tms.repository.entity.Role;
import com.progize.tms.services.RoleService;
import jakarta.persistence.EntityNotFoundException;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RoleServiceImpl implements RoleService {

  private final RoleRepository roleRepository;
  private final PermissionRepository permissionRepository;

  @Autowired
  public RoleServiceImpl(RoleRepository roleRepository, PermissionRepository permissionRepository) {
    this.roleRepository = roleRepository;
    this.permissionRepository = permissionRepository;
  }

  @Override
  public List<Role> findAll() {
    return roleRepository.findAll();
  }

  @Override
  public Optional<Role> findById(Long id) {
    return roleRepository.findById(id);
  }

  @Override
  public Optional<Role> findByName(String name) {
    return roleRepository.findByName(name);
  }

  @Override
  @Transactional
  public Role save(Role role) {
    return roleRepository.save(role);
  }

  @Override
  @Transactional
  public void deleteById(Long id) {
    roleRepository.deleteById(id);
  }

  @Override
  public boolean existsById(Long id) {
    return roleRepository.existsById(id);
  }

  @Override
  public boolean existsByName(String name) {
    return roleRepository.existsByName(name);
  }

  @Override
  @Transactional
  public Role assignPermissions(Long roleId, Set<Long> permissionIds) {
    Role role =
        roleRepository
            .findById(roleId)
            .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + roleId));

    Set<Permission> permissionsToAdd = findPermissionsByIds(permissionIds);
    role.getPermissions().addAll(permissionsToAdd);

    return roleRepository.save(role);
  }

  @Override
  @Transactional
  public Role removePermissions(Long roleId, Set<Long> permissionIds) {
    Role role =
        roleRepository
            .findById(roleId)
            .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + roleId));

    Set<Permission> permissionsToRemove =
        role.getPermissions().stream()
            .filter(permission -> permissionIds.contains(permission.getId()))
            .collect(Collectors.toSet());

    role.getPermissions().removeAll(permissionsToRemove);

    return roleRepository.save(role);
  }

  @Override
  @Transactional
  public Role setPermissions(Long roleId, Set<Long> permissionIds) {
    Role role =
        roleRepository
            .findById(roleId)
            .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + roleId));

    Set<Permission> permissions = findPermissionsByIds(permissionIds);
    role.setPermissions(permissions);

    return roleRepository.save(role);
  }

  private Set<Permission> findPermissionsByIds(Set<Long> permissionIds) {
    Set<Permission> permissions = new HashSet<>();
    for (Long permissionId : permissionIds) {
      Permission permission =
          permissionRepository
              .findById(permissionId)
              .orElseThrow(
                  () ->
                      new EntityNotFoundException("Permission not found with id: " + permissionId));
      permissions.add(permission);
    }
    return permissions;
  }
}
