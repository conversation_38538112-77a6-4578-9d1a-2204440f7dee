package com.progize.tms.controllers.datasources.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/** Response class for database data source information. */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DatabaseSourceResponse extends DataSourceResponse {
  private String databaseType;
  private String connectionUrl;
  private String driverClass;
  private String username;
  // Password is not included in the response for security reasons
  private String schemaName;
}
