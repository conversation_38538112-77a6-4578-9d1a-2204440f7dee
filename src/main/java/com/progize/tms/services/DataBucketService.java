package com.progize.tms.services;

import com.progize.tms.controllers.databuckets.req.RegisterDataBucketVariableRequest;
import com.progize.tms.repository.BucketRelationshipRepository;
import com.progize.tms.repository.DataBucketRepository;
import com.progize.tms.repository.DataBucketVariableRepository;
import com.progize.tms.repository.DataTypeRepository;
import com.progize.tms.repository.DataTypeSupportedOperatorRepository;
import com.progize.tms.repository.entity.BucketRelationshipEntity;
import com.progize.tms.repository.entity.DataBucketEntity;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.repository.entity.DataType;
import com.progize.tms.repository.entity.enums.BucketType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataBucketService {
  private final DataBucketRepository bucketRepo;
  private final DataBucketVariableRepository varRepo;
  private final DataTypeRepository typeRepo;
  private final DataTypeSupportedOperatorRepository dataTypeSupportedOperatorRepository;
  private final BucketRelationshipRepository relationshipRepo;

  public List<DataBucketEntity> getAllBuckets() {
    List<DataBucketEntity> buckets = bucketRepo.findAll();

    // Eagerly fetch variables for each bucket
    for (DataBucketEntity bucket : buckets) {
      List<DataBucketVariableEntity> variables = varRepo.findByDataBucketId(bucket.getId());
      bucket.setVariables(variables);
    }

    return buckets;
  }

  public DataBucketEntity getBucketById(Long bucketId) {
    return bucketRepo
        .findById(bucketId)
        .orElseThrow(() -> new IllegalArgumentException("Bucket not found: " + bucketId));
  }

  public DataBucketEntity createBucket(String name, String description) {
    DataBucketEntity bucket = new DataBucketEntity();
    bucket.setName(name);
    bucket.setDescription(description);
    bucket.setBucketType(BucketType.SYSTEM);
    return bucketRepo.save(bucket);
  }

  public DataBucketEntity createBucket(DataBucketEntity bucket) {
    // Ensure required fields are set
    if (bucket.getBucketType() == null) {
      bucket.setBucketType(BucketType.SYSTEM);
    }

    // Resolve data types for variables if present
    if (bucket.getVariables() != null && !bucket.getVariables().isEmpty()) {
      bucket
          .getVariables()
          .forEach(
              variable -> {
                if (variable.getDataType() != null && variable.getDataType().getCode() != null) {
                  String dataTypeCode = normalizeDataTypeCode(variable.getDataType().getCode());
                  DataType dataType =
                      typeRepo
                          .findByCode(dataTypeCode)
                          .orElseThrow(
                              () ->
                                  new IllegalArgumentException(
                                      "Data type not found: " + dataTypeCode));
                  variable.setDataType(dataType);
                }
              });
    }
    return bucketRepo.save(bucket);
  }

  public DataBucketEntity updateBucket(DataBucketEntity bucket) {
    // Verify bucket exists
    DataBucketEntity existingBucket = getBucketById(bucket.getId());

    // Update fields
    existingBucket.setName(bucket.getName());
    existingBucket.setDescription(bucket.getDescription());
    existingBucket.setTableName(bucket.getTableName());

    if (bucket.getBucketType() != null) {
      existingBucket.setBucketType(bucket.getBucketType());
    }

    return bucketRepo.save(existingBucket);
  }

  // Keep both method names for backward compatibility
  public List<DataBucketVariableEntity> getVariables(Long bucketId) {
    return getVariablesByBucketId(bucketId);
  }

  public List<DataBucketVariableEntity> getVariablesByBucketId(Long bucketId) {
    return varRepo.findByDataBucketId(bucketId);
  }

  /**
   * Get variables for a bucket, including variables from related lookup buckets. This is used for
   * rule definition to include enrichment variables.
   *
   * @param bucketId The ID of the bucket
   * @param includeEnrichment Whether to include variables from related lookup buckets
   * @return List of variables, including enrichment variables if requested
   */
  public List<DataBucketVariableEntity> getVariablesWithEnrichment(
      Long bucketId, boolean includeEnrichment) {
    // Get the bucket's own variables
    List<DataBucketVariableEntity> variables = varRepo.findByDataBucketId(bucketId);

    // If enrichment is not requested or the bucket doesn't exist, return just the bucket's
    // variables
    if (!includeEnrichment) {
      return variables;
    }

    // Get the bucket to check its type
    DataBucketEntity bucket;
    try {
      bucket = getBucketById(bucketId);
    } catch (IllegalArgumentException e) {
      log.warn("Bucket not found: {}", bucketId);
      return variables;
    }

    // Only rule buckets can have enrichment relationships
    if (bucket.getBucketType() != BucketType.RULE_BUCKET) {
      return variables;
    }

    // Get relationships for this rule bucket
    List<BucketRelationshipEntity> relationships = relationshipRepo.findByRuleBucketId(bucketId);
    if (relationships.isEmpty()) {
      return variables;
    }

    // Create a list to hold all variables including enrichment
    List<DataBucketVariableEntity> allVariables = new ArrayList<>(variables);

    // Add variables from each related lookup bucket
    for (BucketRelationshipEntity relationship : relationships) {
      Long lookupBucketId = relationship.getLookupBucket().getId();
      List<DataBucketVariableEntity> lookupVariables = varRepo.findByDataBucketId(lookupBucketId);

      // Add a source bucket marker to each lookup variable
      for (DataBucketVariableEntity lookupVar : lookupVariables) {
        // Create a copy of the variable to avoid modifying the original
        DataBucketVariableEntity enrichmentVar = new DataBucketVariableEntity();
        enrichmentVar.setId(lookupVar.getId());
        enrichmentVar.setName(lookupVar.getName());
        enrichmentVar.setCode(lookupVar.getCode());
        enrichmentVar.setDescription(
            lookupVar.getDescription()
                + " (from "
                + relationship.getLookupBucket().getName()
                + ")");
        enrichmentVar.setTableName(lookupVar.getTableName());
        enrichmentVar.setColumnName(lookupVar.getColumnName());
        enrichmentVar.setDataType(lookupVar.getDataType());
        enrichmentVar.setAggregatable(lookupVar.isAggregatable());
        enrichmentVar.setIsUnique(lookupVar.getIsUnique());
        enrichmentVar.setDataBucket(lookupVar.getDataBucket());

        // Add to the combined list
        allVariables.add(enrichmentVar);
      }
    }

    return allVariables;
  }

  /**
   * Get variables for a bucket along with their supported operators.
   *
   * @param bucketId The ID of the bucket
   * @return Map containing variables and their supported operators
   */
  public Map<DataBucketVariableEntity, List<String>> getVariablesWithSupportedOperators(
      Long bucketId) {
    List<DataBucketVariableEntity> variables = varRepo.findByDataBucketId(bucketId);
    Map<DataBucketVariableEntity, List<String>> result = new HashMap<>();

    for (DataBucketVariableEntity variable : variables) {
      DataType dataType = variable.getDataType();
      if (dataType != null) {
        // Use the correct method to find supported operators
        String dataTypeCode = dataType.getCode();
        List<String> operatorCodes =
            dataTypeSupportedOperatorRepository.findOperatorCodesByDataTypeCode(dataTypeCode);
        result.put(variable, operatorCodes);
      } else {
        result.put(variable, List.of());
      }
    }

    return result;
  }

  /**
   * Get variables for a bucket along with their supported operators, including variables from
   * related lookup buckets. This is used for rule validation to include enrichment variables.
   *
   * @param bucketId The ID of the bucket
   * @return Map containing variables and their supported operators
   */
  public Map<DataBucketVariableEntity, List<String>>
      getVariablesWithSupportedOperatorsAndEnrichment(Long bucketId) {
    // Get all variables including enrichment
    List<DataBucketVariableEntity> allVariables = getVariablesWithEnrichment(bucketId, true);
    Map<DataBucketVariableEntity, List<String>> result = new HashMap<>();

    for (DataBucketVariableEntity variable : allVariables) {
      DataType dataType = variable.getDataType();
      if (dataType != null) {
        // Use the correct method to find supported operators
        String dataTypeCode = dataType.getCode();
        List<String> operatorCodes =
            dataTypeSupportedOperatorRepository.findOperatorCodesByDataTypeCode(dataTypeCode);
        result.put(variable, operatorCodes);
      } else {
        result.put(variable, List.of());
      }
    }

    return result;
  }

  @Transactional
  public List<DataBucketVariableEntity> registerVariables(
      Long bucketId, List<RegisterDataBucketVariableRequest> reqs) {
    DataBucketEntity bucket =
        bucketRepo
            .findById(bucketId)
            .orElseThrow(() -> new IllegalArgumentException("Bucket not found: " + bucketId));
    List<DataBucketVariableEntity> entities =
        reqs.stream()
            .map(
                req -> {
                  DataBucketVariableEntity var = new DataBucketVariableEntity();
                  var.setDataBucket(bucket);
                  var.setCode(req.getCode());
                  var.setName(req.getName());
                  var.setDescription(req.getDescription());
                  var.setTableName(req.getTableName());
                  var.setColumnName(req.getColumnName());
                  String dataTypeCode = normalizeDataTypeCode(req.getDataTypeCode());
                  DataType dt =
                      typeRepo
                          .findByCode(dataTypeCode)
                          .orElseThrow(
                              () ->
                                  new IllegalArgumentException(
                                      "Data type not found: " + dataTypeCode));
                  var.setDataType(dt);
                  var.setAggregatable(req.isAggregatable());
                  return var;
                })
            .collect(Collectors.toList());
    return varRepo.saveAll(entities);
  }

  public void deleteVariable(Long varId) {
    varRepo.deleteById(varId);
  }

  public void deleteVariable(Long bucketId, Long variableId) {
    // Verify variable exists and belongs to the bucket
    DataBucketVariableEntity variable =
        varRepo
            .findById(variableId)
            .orElseThrow(() -> new IllegalArgumentException("Variable not found: " + variableId));

    if (!variable.getDataBucket().getId().equals(bucketId)) {
      throw new IllegalArgumentException("Variable does not belong to the specified bucket");
    }

    varRepo.deleteById(variableId);
  }

  public void deleteBucket(Long bucketId) {
    bucketRepo.deleteById(bucketId);
  }

  public DataBucketVariableEntity createVariable(Long bucketId, DataBucketVariableEntity variable) {
    DataBucketEntity bucket = getBucketById(bucketId);
    variable.setDataBucket(bucket);

    // Normalize data type code if present
    if (variable.getDataType() != null && variable.getDataType().getCode() != null) {
      String dataTypeCode = normalizeDataTypeCode(variable.getDataType().getCode());
      DataType dataType =
          typeRepo
              .findByCode(dataTypeCode)
              .orElseThrow(
                  () -> new IllegalArgumentException("Data type not found: " + dataTypeCode));
      variable.setDataType(dataType);
    }

    return varRepo.save(variable);
  }

  public DataBucketVariableEntity updateVariable(Long bucketId, DataBucketVariableEntity variable) {
    // Verify bucket and variable exist
    DataBucketEntity bucket = getBucketById(bucketId);
    DataBucketVariableEntity existingVariable =
        varRepo
            .findById(variable.getId())
            .orElseThrow(
                () -> new IllegalArgumentException("Variable not found: " + variable.getId()));

    // Ensure variable belongs to the specified bucket
    if (!existingVariable.getDataBucket().getId().equals(bucketId)) {
      throw new IllegalArgumentException("Variable does not belong to the specified bucket");
    }

    // Update fields
    existingVariable.setName(variable.getName());
    existingVariable.setCode(variable.getCode());
    existingVariable.setDescription(variable.getDescription());

    if (variable.getDataType() != null) {
      // Normalize data type code
      String dataTypeCode = normalizeDataTypeCode(variable.getDataType().getCode());
      DataType dataType =
          typeRepo
              .findByCode(dataTypeCode)
              .orElseThrow(
                  () -> new IllegalArgumentException("Data type not found: " + dataTypeCode));
      existingVariable.setDataType(dataType);
    }

    return varRepo.save(existingVariable);
  }

  public DataBucketVariableEntity createVariable(
      Long bucketId, RegisterDataBucketVariableRequest request) {
    DataBucketEntity bucket = getBucketById(bucketId);

    DataBucketVariableEntity variable = new DataBucketVariableEntity();
    variable.setDataBucket(bucket);
    variable.setCode(request.getCode());
    variable.setName(request.getName());
    variable.setDescription(request.getDescription());
    variable.setTableName(request.getTableName());
    variable.setColumnName(request.getColumnName());

    String dataTypeCode = normalizeDataTypeCode(request.getDataTypeCode());
    DataType dataType =
        typeRepo
            .findByCode(dataTypeCode)
            .orElseThrow(
                () -> new IllegalArgumentException("Data type not found: " + dataTypeCode));

    variable.setDataType(dataType);
    variable.setAggregatable(request.isAggregatable());

    return varRepo.save(variable);
  }

  /**
   * Normalize data type codes from UI to match database values. This handles cases where UI sends
   * different format of data type codes.
   */
  private String normalizeDataTypeCode(String code) {
    if (code == null) {
      return null;
    }

    // Convert to lowercase for case-insensitive comparison
    String lowerCode = code.toLowerCase();

    // Map common SQL data types to our database data type codes
    switch (lowerCode) {
      case "varchar":
      case "char":
      case "text":
        return "string";
      case "int":
      case "smallint":
      case "tinyint":
        return "integer";
      case "bigint":
        return "long";
      case "decimal":
      case "numeric":
        return "bigdecimal";
      case "float":
      case "real":
        return "float";
      case "double":
        return "double";
      case "date":
      case "timestamp":
        return "date";
      case "boolean":
        return "boolean";
      default:
        // If it's already a valid code, return as is
        return lowerCode;
    }
  }
}
