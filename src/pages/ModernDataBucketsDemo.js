import React, { useState } from 'react';
import {
    Box,
    Container,
    <PERSON>po<PERSON>,
    <PERSON>ton,
    Card,
    CardContent,
    Grid,
    Chip,
    Avatar,
    useTheme,
    alpha,
    Fab,
    Tooltip
} from '@mui/material';
import {
    Rocket as RocketIcon,
    AutoAwesome as AutoAwesomeIcon,
    Speed as SpeedIcon,
    Security as SecurityIcon,
    Analytics as AnalyticsIcon,
    DataObject as DataObjectIcon,
    Settings as SettingsIcon,
    Visibility as VisibilityIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import ModernDataBucketsManager from '../components/databuckets/ModernDataBucketsManager';
import ModernVariableConfigurator from '../components/databuckets/ModernVariableConfigurator';
import ModernBucketEditor from '../components/databuckets/ModernBucketEditor';

const HeroSection = styled(Box)(({ theme }) => ({
    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    borderRadius: 24,
    padding: theme.spacing(6),
    color: 'white',
    position: 'relative',
    overflow: 'hidden',
    marginBottom: theme.spacing(4),
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
    }
}));

const FeatureCard = styled(Card)(({ theme }) => ({
    borderRadius: 16,
    height: '100%',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    '&:hover': {
        transform: 'translateY(-8px)',
        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
        borderColor: theme.palette.primary.main,
    }
}));

const DemoCard = styled(Card)(({ theme }) => ({
    borderRadius: 20,
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
    border: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    '&:hover': {
        transform: 'scale(1.02)',
        boxShadow: theme.shadows[8],
        borderColor: theme.palette.primary.main,
    }
}));

const ModernDataBucketsDemo = () => {
    const theme = useTheme();
    const [activeDemo, setActiveDemo] = useState(null);
    const [configuratorOpen, setConfiguratorOpen] = useState(false);
    const [editorOpen, setEditorOpen] = useState(false);
    const [selectedBucket, setSelectedBucket] = useState(null);

    // Mock data
    const mockVariables = [
        { id: 1, name: 'customer_id', dataType: 'integer', description: 'Unique customer identifier' },
        { id: 2, name: 'transaction_id', dataType: 'string', description: 'Transaction reference number' },
        { id: 3, name: 'amount', dataType: 'decimal', description: 'Transaction amount' },
        { id: 4, name: 'transaction_date', dataType: 'date', description: 'Date of transaction' },
        { id: 5, name: 'currency', dataType: 'string', description: 'Transaction currency' },
        { id: 6, name: 'status', dataType: 'string', description: 'Transaction status' }
    ];

    const mockDataSources = [
        { id: 1, name: 'PostgreSQL Main', type: 'PostgreSQL' },
        { id: 2, name: 'MySQL HR', type: 'MySQL' },
        { id: 3, name: 'Oracle Finance', type: 'Oracle' }
    ];

    const features = [
        {
            icon: <AutoAwesomeIcon />,
            title: 'Modern Design',
            description: 'Beautiful, intuitive interface with smooth animations and professional styling',
            color: 'primary'
        },
        {
            icon: <SpeedIcon />,
            title: 'Fast & Responsive',
            description: 'Optimized performance with responsive design that works on all devices',
            color: 'success'
        },
        {
            icon: <SecurityIcon />,
            title: 'Secure Configuration',
            description: 'Advanced security settings and access controls for enterprise environments',
            color: 'warning'
        },
        {
            icon: <AnalyticsIcon />,
            title: 'Smart Analytics',
            description: 'Built-in analytics and monitoring for data bucket performance and usage',
            color: 'info'
        }
    ];

    const demos = [
        {
            id: 'manager',
            title: 'Data Bucket Manager',
            description: 'Complete bucket management with modern card-based interface',
            icon: <DataObjectIcon />,
            color: 'primary',
            component: 'manager'
        },
        {
            id: 'configurator',
            title: 'Variable Configurator',
            description: 'Step-by-step wizard for configuring variable properties',
            icon: <SettingsIcon />,
            color: 'secondary',
            component: 'configurator'
        },
        {
            id: 'editor',
            title: 'Bucket Editor',
            description: 'Advanced bucket editing with tabbed interface',
            icon: <VisibilityIcon />,
            color: 'success',
            component: 'editor'
        }
    ];

    const handleDemoClick = (demo) => {
        switch (demo.component) {
            case 'manager':
                setActiveDemo('manager');
                break;
            case 'configurator':
                setConfiguratorOpen(true);
                break;
            case 'editor':
                setEditorOpen(true);
                break;
            default:
                break;
        }
    };

    const handleSaveConfiguration = (configuredVariables) => {
        console.log('Configured variables:', configuredVariables);
        // Handle save logic here
    };

    const handleSaveBucket = (bucketData) => {
        console.log('Bucket data:', bucketData);
        // Handle save logic here
    };

    if (activeDemo === 'manager') {
        return <ModernDataBucketsManager />;
    }

    return (
        <Container maxWidth="xl" sx={{ py: 4 }}>
            {/* Hero Section */}
            <HeroSection>
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2, width: 64, height: 64 }}>
                            <RocketIcon sx={{ fontSize: 32 }} />
                        </Avatar>
                        <Box>
                            <Typography variant="h3" fontWeight="bold" sx={{ mb: 1 }}>
                                Modern Data Bucket UI
                            </Typography>
                            <Typography variant="h6" sx={{ opacity: 0.9 }}>
                                Professional, sexy, and intuitive interface for data bucket management
                            </Typography>
                        </Box>
                    </Box>
                    
                    <Typography variant="body1" sx={{ mb: 4, opacity: 0.9, maxWidth: 600 }}>
                        Experience the next generation of data bucket management with our completely redesigned interface. 
                        Features modern design principles, smooth animations, and enterprise-grade functionality.
                    </Typography>
                    
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        <Chip 
                            label="Material-UI 5" 
                            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white', fontWeight: 600 }} 
                        />
                        <Chip 
                            label="Responsive Design" 
                            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white', fontWeight: 600 }} 
                        />
                        <Chip 
                            label="Modern Animations" 
                            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white', fontWeight: 600 }} 
                        />
                        <Chip 
                            label="Enterprise Ready" 
                            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white', fontWeight: 600 }} 
                        />
                    </Box>
                </Box>
            </HeroSection>

            {/* Features Section */}
            <Box sx={{ mb: 6 }}>
                <Typography variant="h4" fontWeight="bold" sx={{ mb: 1, textAlign: 'center' }}>
                    Key Features
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4, textAlign: 'center' }}>
                    Built with modern design principles and enterprise requirements in mind
                </Typography>
                
                <Grid container spacing={3}>
                    {features.map((feature, index) => (
                        <Grid item xs={12} sm={6} md={3} key={index}>
                            <FeatureCard>
                                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                                    <Avatar sx={{ 
                                        bgcolor: `${feature.color}.main`, 
                                        mx: 'auto', 
                                        mb: 2,
                                        width: 56,
                                        height: 56
                                    }}>
                                        {feature.icon}
                                    </Avatar>
                                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                                        {feature.title}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {feature.description}
                                    </Typography>
                                </CardContent>
                            </FeatureCard>
                        </Grid>
                    ))}
                </Grid>
            </Box>

            {/* Demo Section */}
            <Box sx={{ mb: 6 }}>
                <Typography variant="h4" fontWeight="bold" sx={{ mb: 1, textAlign: 'center' }}>
                    Interactive Demos
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4, textAlign: 'center' }}>
                    Click on any demo to experience the new interface components
                </Typography>
                
                <Grid container spacing={4}>
                    {demos.map((demo) => (
                        <Grid item xs={12} md={4} key={demo.id}>
                            <DemoCard onClick={() => handleDemoClick(demo)}>
                                <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                    <Avatar sx={{ 
                                        bgcolor: `${demo.color}.main`, 
                                        mx: 'auto', 
                                        mb: 3,
                                        width: 72,
                                        height: 72
                                    }}>
                                        {demo.icon}
                                    </Avatar>
                                    <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
                                        {demo.title}
                                    </Typography>
                                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                                        {demo.description}
                                    </Typography>
                                    <Button 
                                        variant="contained" 
                                        color={demo.color}
                                        sx={{ borderRadius: 3, px: 4 }}
                                    >
                                        Try Demo
                                    </Button>
                                </CardContent>
                            </DemoCard>
                        </Grid>
                    ))}
                </Grid>
            </Box>

            {/* Implementation Notes */}
            <Card sx={{ borderRadius: 3, bgcolor: alpha(theme.palette.info.main, 0.05), border: `1px solid ${alpha(theme.palette.info.main, 0.2)}` }}>
                <CardContent sx={{ p: 4 }}>
                    <Typography variant="h5" fontWeight="bold" sx={{ mb: 2, color: 'info.main' }}>
                        Implementation Notes
                    </Typography>
                    <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                            <Typography variant="h6" fontWeight="medium" sx={{ mb: 1 }}>
                                Design Principles
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                • Card-based layout with clean shadows and spacing<br/>
                                • Gradient headers and modern color schemes<br/>
                                • Smooth animations and hover effects<br/>
                                • Responsive design for all screen sizes
                            </Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Typography variant="h6" fontWeight="medium" sx={{ mb: 1 }}>
                                Technical Features
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                • Material-UI 5 with styled components<br/>
                                • TypeScript support ready<br/>
                                • Modular component architecture<br/>
                                • Enterprise-grade accessibility
                            </Typography>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>

            {/* Floating Action Button */}
            <Tooltip title="Back to Original Interface">
                <Fab
                    color="primary"
                    sx={{
                        position: 'fixed',
                        bottom: 24,
                        right: 24,
                        borderRadius: 3,
                    }}
                    onClick={() => window.history.back()}
                >
                    <RocketIcon />
                </Fab>
            </Tooltip>

            {/* Dialogs */}
            <ModernVariableConfigurator
                open={configuratorOpen}
                onClose={() => setConfiguratorOpen(false)}
                variables={mockVariables}
                onSave={handleSaveConfiguration}
            />

            <ModernBucketEditor
                open={editorOpen}
                onClose={() => setEditorOpen(false)}
                bucket={selectedBucket}
                dataSources={mockDataSources}
                onSave={handleSaveBucket}
            />
        </Container>
    );
};

export default ModernDataBucketsDemo;
