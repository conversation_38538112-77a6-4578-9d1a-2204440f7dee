import React, { useState, useRef, useEffect } from 'react';
import {
  TextField,
  Popper,
  Paper,
  ListItem,
  ListItemText,
  CircularProgress,
  Box,
  Typography,
  Chip,
  Divider,
  alpha,
  useTheme
} from '@mui/material';
import { fetchRuleVariables } from '../../services/variableService';
import CodeIcon from '@mui/icons-material/Code';
import FunctionsIcon from '@mui/icons-material/Functions';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';

/**
 * ExpressionInput Component
 *
 * A specialized text input that provides autocomplete suggestions
 * for variables when typing ${...}
 */
const ExpressionInput = ({
  value,
  onChange,
  label = "Value",
  fullWidth = true,
  size = "small",
  type = "text",
  fieldType, // New prop to filter variable suggestions by type
  disabled = false,
  placeholder,
  ...props
}) => {
  const [inputValue, setInputValue] = useState(value === undefined ? '' : value);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [variables, setVariables] = useState([]);
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const inputRef = useRef(null);
  const theme = useTheme(); // Use the theme here in the parent component

  // Load variables from the API
  useEffect(() => {
    const loadVariables = async () => {
      setLoading(true);
      try {
        const data = await fetchRuleVariables();
        console.log('Variables loaded for autocomplete:', data);
        // Transform the API response to the format we need
        const transformedData = Array.isArray(data) ? data.map(v => ({
          id: v.id || v.name, // Use name as fallback for id
          name: v.name,
          type: v.type || v.dataTypeCode, // Support both type and dataTypeCode
          description: v.description || v.name,
          category: v.category,
          isEnriched: v.isEnriched || false,
          sourceBucketName: v.sourceBucketName
        })) : [];
        console.log('Transformed variables for autocomplete:', transformedData);
        setVariables(transformedData);
      } catch (error) {
        console.error("Failed to load variables:", error);
      } finally {
        setLoading(false);
      }
    };

    loadVariables();
  }, []);

  // Update local input value when prop changes
  useEffect(() => {
    setInputValue(value === undefined ? '' : value);
  }, [value]);

  // Detect if we should show variable suggestions
  const checkForVariableExpression = (text, position) => {
    // Find the last opening ${ before the cursor
    const beforeCursor = text.substring(0, position);
    const lastOpenBrace = beforeCursor.lastIndexOf('${');

    // If we found ${, and there's no closing } between it and the cursor
    if (lastOpenBrace !== -1) {
      const closingBrace = beforeCursor.indexOf('}', lastOpenBrace);
      if (closingBrace === -1) {
        // Extract the variable name being typed
        const varNameBeingTyped = beforeCursor.substring(lastOpenBrace + 2);
        return {
          show: true,
          varPrefix: varNameBeingTyped
        };
      }
    }

    return { show: false };
  };

  const handleChange = (e) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Check if we should show variable suggestions
    const curPos = e.target.selectionStart;
    setCursorPosition(curPos);

    const { show, varPrefix } = checkForVariableExpression(newValue, curPos);
    setShowSuggestions(show);

    if (show && !anchorEl) {
      setAnchorEl(e.target);
    } else if (!show && anchorEl) {
      setAnchorEl(null);
    }

    // Notify parent component
    onChange(e);
  };

  const handleVariableSelect = (variable) => {
    // Find the last opening ${ before the cursor
    const beforeCursor = inputValue.substring(0, cursorPosition);
    const lastOpenBrace = beforeCursor.lastIndexOf('${');

    if (lastOpenBrace !== -1) {
      // Use the variable name instead of ID
      const variableIdentifier = variable.name || (variable.id ? String(variable.id) : 'unknown');

      if (!variableIdentifier) {
        console.error('Variable has no id or name:', variable);
        return;
      }

      // Replace what's being typed with the selected variable
      const newValue =
        inputValue.substring(0, lastOpenBrace + 2) +
        String(variableIdentifier) +
        '}' +
        inputValue.substring(cursorPosition);

      console.log('Inserting variable:', variableIdentifier);
      console.log('New value:', newValue);

      // Update input and notify parent
      setInputValue(newValue);
      setShowSuggestions(false);
      setAnchorEl(null);

      // Create a synthetic event for the parent handler
      const syntheticEvent = {
        target: {
          value: newValue,
          name: props.name
        }
      };
      onChange(syntheticEvent);
    }
  };

  // Filter variables based on what the user has typed
  const getFilteredVariables = () => {
    if (!showSuggestions) return [];

    const beforeCursor = inputValue.substring(0, cursorPosition);
    const lastOpenBrace = beforeCursor.lastIndexOf('${');
    const varNameBeingTyped = beforeCursor.substring(lastOpenBrace + 2).toLowerCase();

    // Filter by type if fieldType is provided
    let filteredByType = variables;
    if (fieldType) {
      // Convert fieldType to lowercase for case-insensitive comparison
      const fieldTypeLower = fieldType.toLowerCase();

      filteredByType = variables.filter(variable => {
        // If variable has no type, include it
        if (!variable.type) return true;

        // Convert variable type to lowercase
        const varTypeLower = variable.type.toLowerCase();

        // Map fieldType from UI to variable type in API
        return (
          // Exact match
          (varTypeLower === fieldTypeLower) ||
          // Number types
          (fieldTypeLower === 'number' && (
            varTypeLower === 'bigdecimal' ||
            varTypeLower === 'long' ||
            varTypeLower === 'integer' ||
            varTypeLower === 'int' ||
            varTypeLower === 'double' ||
            varTypeLower === 'float' ||
            varTypeLower.includes('number')
          )) ||
          // String types
          (fieldTypeLower === 'string' && (
            varTypeLower === 'string' ||
            varTypeLower === 'text' ||
            varTypeLower === 'varchar' ||
            varTypeLower === 'char'
          )) ||
          // Boolean types
          (fieldTypeLower === 'boolean' && (
            varTypeLower === 'boolean' ||
            varTypeLower === 'bool'
          )) ||
          // Date types
          (fieldTypeLower === 'date' && (
            varTypeLower === 'date' ||
            varTypeLower === 'datetime' ||
            varTypeLower === 'timestamp'
          ))
        );
      });

      console.log(`Filtered variables by type '${fieldType}':`, filteredByType);
    }

    // Then filter by search text
    return filteredByType.filter(variable => {
      // Safely convert id to string and lowercase
      const idStr = variable.id ? String(variable.id).toLowerCase() : '';
      // Safely convert name to string and lowercase
      const nameStr = variable.name ? String(variable.name).toLowerCase() : '';

      return idStr.includes(varNameBeingTyped) || nameStr.includes(varNameBeingTyped);
    });
  };

  // Custom Popper for suggestions
  const SuggestionsPopper = ({ anchorEl, variables }) => {
    if (!anchorEl || !showSuggestions) return null;

    const filteredVars = getFilteredVariables();

    // Helper function to get icon based on variable type
    const getVariableIcon = (type) => {
      if (!type) return <CodeIcon fontSize="small" />;

      const lcType = type.toLowerCase();
      if (lcType.includes('bigdecimal') || lcType.includes('integer') ||
          lcType.includes('long') || lcType.includes('double') || lcType === 'number') {
        return <FunctionsIcon fontSize="small" color="primary" />;
      } else if (lcType === 'string') {
        return <TextFieldsIcon fontSize="small" color="success" />;
      } else if (lcType === 'boolean') {
        return <ToggleOnIcon fontSize="small" color="warning" />;
      } else if (lcType === 'date') {
        return <CalendarTodayIcon fontSize="small" color="info" />;
      }
      return <CodeIcon fontSize="small" />;
    };

    // Helper function to get chip color based on variable type
    const getTypeChipColor = (type) => {
      if (!type) return 'default';

      const lcType = type.toLowerCase();
      if (lcType.includes('bigdecimal') || lcType.includes('integer') ||
          lcType.includes('long') || lcType.includes('double') || lcType === 'number') {
        return 'primary';
      } else if (lcType === 'string') {
        return 'success';
      } else if (lcType === 'boolean') {
        return 'warning';
      } else if (lcType === 'date') {
        return 'info';
      }
      return 'default';
    };

    return (
      <Popper
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        placement="bottom-start"
        modifiers={[
          { name: 'offset', options: { offset: [0, 2] } },
        ]}
        style={{ zIndex: 1500, width: anchorEl?.clientWidth || 300 }}
      >
        <Paper
          elevation={6}
          sx={{
            width: '100%',
            maxHeight: 320,
            overflow: 'auto',
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: '8px',
            backgroundColor: theme.palette.background.paper
          }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: 2 }}>
              <CircularProgress size={24} sx={{ mr: 2 }} />
              <Typography variant="body2">Loading variables...</Typography>
            </Box>
          ) : filteredVars.length > 0 ? (
            <>
              <Box sx={{ px: 2, py: 1.5, backgroundColor: alpha(theme.palette.primary.main, 0.08) }}>
                <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 600 }}>
                  {fieldType ? `${fieldType.toUpperCase()} VARIABLES` : 'VARIABLES'}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Insert variables with ${'{variableName}'}
                </Typography>
              </Box>
              <Divider />
              {filteredVars.map((variable, index) => (
                <React.Fragment key={variable.name || variable.id || index}>
                  <ListItem
                    button
                    onClick={() => handleVariableSelect(variable)}
                    sx={{
                      py: 1.5,
                      px: 2,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.05)
                      },
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <Box sx={{ mr: 1.5, display: 'flex', alignItems: 'center', color: theme.palette.text.secondary }}>
                        {getVariableIcon(variable.type)}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600, color: theme.palette.text.primary, fontFamily: 'monospace' }}>
                            ${'{'}
                            <Box component="span" sx={{ color: theme.palette.primary.main }}>{variable.name || String(variable.id)}</Box>
                            {'}'}
                          </Typography>
                          <Chip
                            label={variable.type || 'unknown'}
                            size="small"
                            color={getTypeChipColor(variable.type)}
                            sx={{
                              height: '20px',
                              '& .MuiChip-label': {
                                px: 1,
                                fontSize: '0.65rem',
                                fontWeight: 'bold'
                              }
                            }}
                            variant="outlined"
                          />
                        </Box>
                        {variable.description && variable.description !== variable.name && (
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            {variable.description}
                          </Typography>
                        )}
                        {variable.isEnriched && (
                          <Typography variant="caption" color="success.main" sx={{ display: 'block', fontStyle: 'italic', mt: 0.5 }}>
                            From {variable.sourceBucketName || 'lookup bucket'}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </ListItem>
                  {index < filteredVars.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </>
          ) : (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                {fieldType ? `No matching ${fieldType} variables found` : "No matching variables found"}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                {variables.length === 0 ?
                  "No variables available. Make sure a data bucket is selected." :
                  "Try a different search term"}
              </Typography>
              {variables.length === 0 && (
                <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1, fontStyle: 'italic' }}>
                  Variables are loaded from the selected data bucket
                </Typography>
              )}
            </Box>
          )}
        </Paper>
      </Popper>
    );
  };

  return (
    <>
      <TextField
        ref={inputRef}
        label={label}
        variant="outlined"
        fullWidth={fullWidth}
        size={size}
        type={type}
        disabled={disabled}
        value={inputValue}
        onChange={handleChange}
        placeholder={placeholder || "Enter value or ${variable} expression"}
        InputProps={{
          autoComplete: 'off' // Disable browser's native autocomplete
        }}
        {...props}
      />

      <SuggestionsPopper anchorEl={anchorEl} variables={variables} />
    </>
  );
};

export default ExpressionInput;