package com.progize.tms.repository;

import com.progize.tms.repository.entity.OperatorExpression;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface OperatorExpressionRepository extends JpaRepository<OperatorExpression, Long> {

  @Query("SELECT oe FROM OperatorExpression oe WHERE oe.dataType.id = :dataTypeId")
  List<OperatorExpression> findByDataTypeId(@Param("dataTypeId") Long dataTypeId);

  @Query("SELECT oe FROM OperatorExpression oe WHERE oe.dataType.code = :dataTypeCode")
  List<OperatorExpression> findByDataTypeCode(@Param("dataTypeCode") String dataTypeCode);

  @Query("SELECT oe FROM OperatorExpression oe WHERE oe.dataType IS NULL")
  List<OperatorExpression> findGenericExpressions();

  @Query(
      "SELECT oe FROM OperatorExpression oe WHERE oe.operator.id = :operatorId AND oe.dataType.id = :dataTypeId")
  Optional<OperatorExpression> findByOperatorIdAndDataTypeId(
      @Param("operatorId") Long operatorId, @Param("dataTypeId") Long dataTypeId);

  @Query(
      "SELECT oe FROM OperatorExpression oe WHERE oe.operator.code = :operatorCode AND oe.dataType.code = :dataTypeCode")
  Optional<OperatorExpression> findByOperatorCodeAndDataTypeCode(
      @Param("operatorCode") String operatorCode, @Param("dataTypeCode") String dataTypeCode);

  @Query(
      "SELECT oe FROM OperatorExpression oe WHERE oe.operator.id = :operatorId AND oe.dataType IS NULL")
  Optional<OperatorExpression> findGenericByOperatorId(@Param("operatorId") Long operatorId);

  @Query(
      "SELECT oe FROM OperatorExpression oe WHERE oe.operator.code = :operatorCode AND oe.dataType IS NULL")
  Optional<OperatorExpression> findGenericByOperatorCode(
      @Param("operatorCode") String operatorCode);
}
