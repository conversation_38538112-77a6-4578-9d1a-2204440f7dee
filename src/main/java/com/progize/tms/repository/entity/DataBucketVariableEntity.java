package com.progize.tms.repository.entity;

import com.progize.tms.repository.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "data_bucket_variable")
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class DataBucketVariableEntity extends BaseEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_bucket_id", nullable = false)
  private DataBucketEntity dataBucket;

  @Column(name = "code", nullable = false, length = 100)
  private String code;

  @Column(name = "name", nullable = false, length = 255)
  private String name;

  @Column(name = "description")
  private String description;

  @Column(name = "table_name", nullable = false, length = 255)
  private String tableName;

  @Column(name = "column_name", nullable = false, length = 255)
  private String columnName;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "data_type_id", nullable = false)
  private DataType dataType;

  @Column(name = "aggregatable", nullable = false)
  private boolean aggregatable;

  @Column(name = "is_unique", nullable = false)
  private Boolean isUnique = false;
}
