package com.progize.tms.controllers.roles.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRoleRequest {

  @NotBlank(message = "Role name is required")
  @Size(min = 3, max = 50, message = "Role name must be between 3 and 50 characters")
  private String name;

  @NotBlank(message = "Role description is required")
  @Size(max = 255, message = "Role description must be less than 255 characters")
  private String description;
}
