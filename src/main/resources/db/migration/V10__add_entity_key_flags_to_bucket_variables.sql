-- Add primary and secondary entity key flags to data_bucket_variable table
ALTER TABLE data_bucket_variable 
ADD COLUMN is_primary_entity_key BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN is_secondary_entity_key BOOLEAN NOT NULL DEFAULT FALSE;

-- Add audit table columns
ALTER TABLE data_bucket_variable_aud 
ADD COLUMN is_primary_entity_key BOOLEAN DEFAULT FALSE,
ADD COLUMN is_secondary_entity_key BOOLEAN DEFAULT FALSE;

-- Add indexes for performance on entity key queries
CREATE INDEX idx_data_bucket_variable_primary_key ON data_bucket_variable(data_bucket_id, is_primary_entity_key) WHERE is_primary_entity_key = TRUE;
CREATE INDEX idx_data_bucket_variable_secondary_key ON data_bucket_variable(data_bucket_id, is_secondary_entity_key) WHERE is_secondary_entity_key = TRUE;
