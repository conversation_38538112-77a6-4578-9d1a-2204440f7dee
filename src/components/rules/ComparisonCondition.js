import React from 'react';
import {
  Box,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Tooltip,
  useTheme
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import InfoIcon from '@mui/icons-material/Info';
import SimpleCondition from './SimpleCondition';
import AggregateCondition from './AggregateCondition';
import AggregateInComparisonCondition from './AggregateInComparisonCondition';

const ComparisonCondition = ({
  condition,
  onChange,
  onRemove,
  fields = [],
  operators = [],
  aggregateFunctions = [],
  isEditable = true
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Get comparison operators (>, <, >=, <=, ==, !=)
  const comparisonOperators = operators.filter(op =>
    ['>', '<', '>=', '<=', '==', '!='].includes(op.symbol)
  );

  // Helper function to get condition type
  const getConditionType = (cond) => {
    return cond?.type || 'SIMPLE';
  };

  // Handle changes to the left condition
  const handleLeftConditionChange = (updatedLeftCondition) => {
    onChange({
      ...condition,
      left: updatedLeftCondition
    });
  };

  // Handle changes to the right condition
  const handleRightConditionChange = (updatedRightCondition) => {
    onChange({
      ...condition,
      right: updatedRightCondition
    });
  };

  // Handle operator change
  const handleOperatorChange = (event) => {
    onChange({
      ...condition,
      operator: event.target.value
    });
  };

  // Handle multiplier change
  const handleMultiplierChange = (event) => {
    const value = parseFloat(event.target.value);
    onChange({
      ...condition,
      multiplier: isNaN(value) ? null : value
    });
  };

  // Initialize left and right conditions if they don't exist
  const leftCondition = condition.left || {
    id: `left_${Date.now()}`,
    type: 'AGGREGATE',
    function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : 'AVG',
    field: fields.length > 0 ? fields[0].name : '',
    fieldId: fields.length > 0 ? fields[0].id : '',
    period: 7,
    operator: '>',
    threshold: 0
  };

  const rightCondition = condition.right || {
    id: `right_${Date.now()}`,
    type: 'AGGREGATE',
    function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : 'AVG',
    field: fields.length > 0 ? fields[0].name : '',
    fieldId: fields.length > 0 ? fields[0].id : '',
    period: 7,
    operator: '>',
    threshold: 0
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        position: 'relative',
        borderLeft: '4px solid #9C27B0', // Purple for comparison
        backgroundColor: theme.palette.background.paper,
        opacity: isEditable ? 1 : 0.8
      }}
    >
      <Box sx={{ position: 'absolute', top: 8, right: 8, display: 'flex' }}>
        <IconButton
          size="small"
          color="error"
          onClick={onRemove}
          disabled={!isEditable}
        >
          <DeleteIcon />
        </IconButton>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          Comparison Condition
        </Typography>
        <Tooltip title="Compare two conditions, such as comparing aggregations across different time windows">
          <IconButton size="small" color="info">
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={2}>
        {/* Left Condition */}
        <Grid item xs={12}>
          <Typography variant="subtitle2" gutterBottom>
            Left Side
          </Typography>
          <Box sx={{ pl: 2, borderLeft: '1px dashed rgba(0, 0, 0, 0.12)' }}>
            {getConditionType(leftCondition) === 'SIMPLE' && (
              <SimpleCondition
                condition={leftCondition}
                onChange={handleLeftConditionChange}
                onRemove={() => {}} // Can't remove left condition
                fields={fields}
                operators={operators}
                isEditable={isEditable}
              />
            )}
            {getConditionType(leftCondition) === 'AGGREGATE' && (
              <AggregateInComparisonCondition
                condition={leftCondition}
                onChange={handleLeftConditionChange}
                fields={fields}
                aggregateFunctions={aggregateFunctions}
                isEditable={isEditable}
              />
            )}
          </Box>
        </Grid>

        {/* Operator */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth variant="outlined" size="small">
            <InputLabel id="comparison-operator-label">Operator</InputLabel>
            <Select
              labelId="comparison-operator-label"
              value={condition.operator || '>'}
              onChange={handleOperatorChange}
              label="Operator"
              disabled={!isEditable}
            >
              {comparisonOperators.map((op) => (
                <MenuItem key={op.id} value={op.symbol}>
                  {op.displayName} ({op.symbol})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Multiplier */}
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Multiplier (optional)"
            type="number"
            variant="outlined"
            size="small"
            value={condition.multiplier || ''}
            onChange={handleMultiplierChange}
            disabled={!isEditable}
            helperText="Apply a multiplier to the right side (e.g., 2 for '2x higher')"
          />
        </Grid>

        {/* Right Condition */}
        <Grid item xs={12}>
          <Typography variant="subtitle2" gutterBottom>
            Right Side
          </Typography>
          <Box sx={{ pl: 2, borderLeft: '1px dashed rgba(0, 0, 0, 0.12)' }}>
            {getConditionType(rightCondition) === 'SIMPLE' && (
              <SimpleCondition
                condition={rightCondition}
                onChange={handleRightConditionChange}
                onRemove={() => {}} // Can't remove right condition
                fields={fields}
                operators={operators}
                isEditable={isEditable}
              />
            )}
            {getConditionType(rightCondition) === 'AGGREGATE' && (
              <AggregateInComparisonCondition
                condition={rightCondition}
                onChange={handleRightConditionChange}
                fields={fields}
                aggregateFunctions={aggregateFunctions}
                isEditable={isEditable}
              />
            )}
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default ComparisonCondition;
