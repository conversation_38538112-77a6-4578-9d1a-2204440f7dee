package com.progize.tms.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.RuleRepository;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.services.models.AggregateCondition;
import com.progize.tms.services.models.AggregationRequirement;
import com.progize.tms.services.models.CompositeCondition;
import com.progize.tms.services.models.Condition;
import jakarta.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Service responsible for analyzing rules to extract and register aggregation requirements. This
 * registry maintains a list of all unique aggregations needed by the active rules in the system.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AggregationRegistryService {

  private final RuleRepository ruleRepository;
  private final ObjectMapper objectMapper;

  // Registry of aggregation requirements by entity type
  private final Map<String, Set<AggregationRequirement>> aggregationRegistry =
      new ConcurrentHashMap<>();

  /** Initialize the registry at application startup. */
  @PostConstruct
  public void initialize() {
    refreshAggregationRegistry();
  }

  /**
   * Periodically refresh the registry to capture new or updated rules. Runs every 5 minutes by
   * default.
   */
  @Scheduled(fixedRateString = "${tms.aggregation.registry.refresh-rate:300000}")
  public void refreshAggregationRegistry() {
    log.info("Refreshing aggregation registry");

    // Clear the current registry
    aggregationRegistry.clear();

    // Get all active rules
    List<Rule> activeRules = ruleRepository.findByActiveOrderByPriorityAsc(true);

    // Process each rule to extract aggregation requirements
    activeRules.forEach(this::processRule);

    log.info(
        "Aggregation registry refreshed. Found {} unique entity types with aggregation requirements",
        aggregationRegistry.size());
  }

  /**
   * Registers a new aggregation requirement in the registry.
   *
   * @param requirement The aggregation requirement to register
   */
  public void registerAggregation(AggregationRequirement requirement) {
    if (requirement == null || requirement.getEntityField() == null) {
      return;
    }

    String entityType = requirement.getEntityField();
    aggregationRegistry
        .computeIfAbsent(entityType, k -> ConcurrentHashMap.newKeySet())
        .add(requirement);

    log.debug(
        "Registered aggregation: {} for entity type: {}",
        requirement.getAggregationKey(),
        entityType);
  }

  /**
   * Gets all aggregation requirements for a specific entity type.
   *
   * @param entityType The entity type to get aggregations for
   * @return Set of aggregation requirements for the entity type
   */
  public Set<AggregationRequirement> getAggregationsForEntity(String entityType) {
    return aggregationRegistry.getOrDefault(entityType, Collections.emptySet());
  }

  /**
   * Gets all unique aggregation requirements across all entity types.
   *
   * @return Set of all unique aggregation requirements
   */
  public Set<AggregationRequirement> getAllAggregationRequirements() {
    Set<AggregationRequirement> allRequirements = new HashSet<>();
    aggregationRegistry.values().forEach(allRequirements::addAll);
    return allRequirements;
  }

  /**
   * Process a rule to extract and register its aggregation requirements.
   *
   * @param rule The rule to process
   */
  private void processRule(Rule rule) {
    if (rule.getRuleJson() == null || rule.getRuleJson().isEmpty()) {
      log.debug("Rule {} has no JSON condition", rule.getId());
      return;
    }

    try {
      // Parse the rule JSON to extract conditions
      Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

      // Extract and register aggregation requirements
      Set<AggregationRequirement> requirements = extractAggregationRequirements(condition);
      requirements.forEach(this::registerAggregation);

      log.debug(
          "Processed rule: {}. Found {} aggregation requirements",
          rule.getId(),
          requirements.size());
    } catch (JsonProcessingException e) {
      log.error("Failed to parse rule JSON for rule: {}", rule.getId(), e);
    }
  }

  /**
   * Recursively extracts aggregation requirements from a condition.
   *
   * @param condition The condition to analyze
   * @return Set of extracted aggregation requirements
   */
  public Set<AggregationRequirement> extractAggregationRequirements(Condition condition) {
    Set<AggregationRequirement> requirements = new HashSet<>();

    if (condition == null) {
      return requirements;
    }

    // Check if this is an aggregate condition
    if ("AGGREGATE".equals(condition.getType())) {
      AggregateCondition aggCondition = (AggregateCondition) condition;

      // Create the appropriate requirement with or without filter condition
      AggregationRequirement req;

      // Get the startOffset (default to 0 if not set)
      Integer startOffset =
          aggCondition.getStartOffset() != null ? aggCondition.getStartOffset() : 0;

      if (aggCondition.getFilterCondition() != null) {
        req =
            new AggregationRequirement(
                aggCondition.getFunction(),
                aggCondition.getField(),
                aggCondition.getPeriod(),
                startOffset,
                aggCondition.getEntityField(),
                aggCondition.getFilterCondition());

        log.debug(
            "Adding filtered aggregation: {} with filter on field: {}, period: {}, startOffset: {}",
            aggCondition.getFunction(),
            aggCondition.getFilterCondition().getField(),
            aggCondition.getPeriod(),
            startOffset);
      } else {
        req =
            new AggregationRequirement(
                aggCondition.getFunction(),
                aggCondition.getField(),
                aggCondition.getPeriod(),
                startOffset,
                aggCondition.getEntityField());

        log.debug(
            "Adding aggregation: {}, period: {}, startOffset: {}",
            aggCondition.getFunction(),
            aggCondition.getPeriod(),
            startOffset);
      }

      requirements.add(req);
    }
    // Check if this is a comparison condition
    else if ("COMPARISON".equals(condition.getType())) {
      log.debug("Processing COMPARISON condition");
      com.progize.tms.services.models.ComparisonCondition comparisonCondition =
          (com.progize.tms.services.models.ComparisonCondition) condition;

      // Extract requirements from left and right conditions
      if (comparisonCondition.getLeft() != null) {
        requirements.addAll(extractAggregationRequirements(comparisonCondition.getLeft()));
      }

      if (comparisonCondition.getRight() != null) {
        requirements.addAll(extractAggregationRequirements(comparisonCondition.getRight()));
      }
    }
    // Check if this is a composite condition (AND, OR, NOT)
    else if (condition instanceof CompositeCondition compCondition) {
      if (compCondition.getConditions() != null) {
        for (Condition childCondition : compCondition.getConditions()) {
          requirements.addAll(extractAggregationRequirements(childCondition));
        }
      }
    }

    return requirements;
  }
}
