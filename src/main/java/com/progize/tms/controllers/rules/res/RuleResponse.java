package com.progize.tms.controllers.rules.res;

import com.progize.tms.controllers.rules.req.RuleCondition;
import java.time.Instant;
import lombok.Data;

@Data
public class RuleResponse {
  private Long id;
  private String name;
  private String description;
  private String expression; // The compiled MVEL expression
  private String flagReason;
  private boolean active;
  private int priority;
  private Long dataBucketId; // The associated data bucket ID
  private Instant createdAt;
  private Instant updatedAt;
  private String createdBy;
  private String updatedBy;
  private String ruleJson;
  private RuleCondition condition; // Add deserialized condition
}
