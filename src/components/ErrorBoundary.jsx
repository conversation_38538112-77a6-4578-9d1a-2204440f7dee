import React from 'react';
import { useRouteError } from 'react-router-dom';
import { Box, Typography, Button } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

export default function ErrorBoundary() {
  const error = useRouteError();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        textAlign: 'center',
        p: 3
      }}
    >
      <ErrorOutlineIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
      <Typography variant="h4" gutterBottom>
        {error.status === 404 ? 'Page Not Found' : 'Oops! Something went wrong'}
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        {error.status === 404
          ? "The page you're looking for doesn't exist."
          : 'An unexpected error has occurred.'}
      </Typography>
      <Button
        variant="contained"
        color="primary"
        href="/"
      >
        Go to Homepage
      </Button>
    </Box>
  );
}
