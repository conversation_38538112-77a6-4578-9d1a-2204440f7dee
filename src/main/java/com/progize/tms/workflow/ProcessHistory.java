package com.progize.tms.workflow;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class ProcessHistory {
  private String processInstanceId;
  private String processDefinitionName;
  private Date startTime;
  private Date endTime;
  private String status;
  private List<ActivityInstance> activities = new ArrayList<>();

  @Data
  public static class ActivityInstance {
    private String activityId;
    private String activityName;
    private String activityType;
    private Date startTime;
    private Date endTime;
    private String assignee;
    private Map<String, Object> variables;
  }
}
