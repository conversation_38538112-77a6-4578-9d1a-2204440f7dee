package com.progize.tms.repository;

import com.progize.tms.repository.entity.CaseComment;
import com.progize.tms.repository.entity.WorkflowCase;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Repository for case comments. */
@Repository
public interface CaseCommentRepository extends JpaRepository<CaseComment, Long> {

  /**
   * Find all comments for a specific workflow case.
   *
   * @param workflowCase the workflow case
   * @return list of comments
   */
  List<CaseComment> findByWorkflowCaseOrderByCreatedAtDesc(WorkflowCase workflowCase);

  /**
   * Find all comments for a specific task.
   *
   * @param taskId the task ID
   * @return list of comments
   */
  List<CaseComment> findByTaskIdOrderByCreatedAtDesc(String taskId);

  /**
   * Find all comments for a specific process instance.
   *
   * @param processInstanceId the process instance ID
   * @return list of comments
   */
  List<CaseComment> findByProcessInstanceIdOrderByCreatedAtDesc(String processInstanceId);
}
