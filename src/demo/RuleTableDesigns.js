import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>ton,
  Card,
  Chip,
  Container,
  Divider,
  Grid,
  IconButton,
  ListItem,
  ListItemText,
  ListItemIcon,
  MenuItem,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Typography,
  List,
  Avatar,
  ButtonGroup,
  alpha,
  styled
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import RuleIcon from '@mui/icons-material/Gavel';
import PauseIcon from '@mui/icons-material/Pause';
import DoneIcon from '@mui/icons-material/Done';
import Filter1Icon from '@mui/icons-material/Filter1';
import Filter2Icon from '@mui/icons-material/Filter2';
import Filter3Icon from '@mui/icons-material/Filter3';
import Filter4Icon from '@mui/icons-material/Filter4';
import Filter5Icon from '@mui/icons-material/Filter5';
import Filter6Icon from '@mui/icons-material/Filter6';
import Filter7Icon from '@mui/icons-material/Filter7';

// Sample data for visualization
const sampleRules = [
  {
    id: 1,
    name: 'High Value Transaction Alert',
    description: 'Detect transactions above a certain threshold',
    active: true,
    priority: 1,
    updatedAt: new Date('2025-04-01').toISOString()
  },
  {
    id: 2,
    name: 'Suspicious Frequency Pattern',
    description: 'Multiple transactions in short time period',
    active: true,
    priority: 2,
    updatedAt: new Date('2025-04-05').toISOString()
  },
  {
    id: 3,
    name: 'New Account Large Transfer',
    description: 'Large transfers on new accounts',
    active: false,
    priority: 1,
    updatedAt: new Date('2025-04-10').toISOString()
  },
  {
    id: 4,
    name: 'International Transfer Limit',
    description: 'Enforce limits on international transfers',
    active: true,
    priority: 3,
    updatedAt: new Date('2025-04-12').toISOString()
  },
  {
    id: 5,
    name: 'Repeated Failed Authentication',
    description: 'Detect multiple failed login attempts',
    active: false,
    priority: 2,
    updatedAt: new Date('2025-04-15').toISOString()
  }
];

const designOptions = [
  { id: 1, name: 'Card-Based Layout', icon: <Filter1Icon /> },
  { id: 2, name: 'Enhanced Table with Visual Hierarchy', icon: <Filter2Icon /> },
  { id: 3, name: 'List View with Expandable Sections', icon: <Filter3Icon /> },
  { id: 4, name: 'Categorized Tables with Tabs', icon: <Filter4Icon /> },
  { id: 5, name: 'Timeline View', icon: <Filter5Icon /> },
  { id: 6, name: 'Data Grid View', icon: <Filter6Icon /> },
  { id: 7, name: 'Kanban Board View', icon: <Filter7Icon /> }
];

const RuleTableDesigns = () => {
  const [selectedDesign, setSelectedDesign] = useState(1);
  const [tabValue, setTabValue] = useState('all');

  const handleDesignChange = (event) => {
    setSelectedDesign(event.target.value);
  };
  
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const renderDesign = () => {
    switch (selectedDesign) {
      case 1:
        return <CardBasedLayout rules={sampleRules} />;
      case 2:
        return <EnhancedTable rules={sampleRules} />;
      case 3:
        return <ListViewWithSections rules={sampleRules} />;
      case 4:
        return <CategorizedTables rules={sampleRules} tabValue={tabValue} handleTabChange={handleTabChange} />;
      case 5:
        return <TimelineView rules={sampleRules} />;
      case 6:
        return <DataGridView rules={sampleRules} />;
      case 7:
        return <KanbanBoardView rules={sampleRules} />;
      default:
        return <CardBasedLayout rules={sampleRules} />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ mb: 1 }}>
          Rule Table Design Concepts
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Visualizing different UI approaches for the Transaction Management System rule listing page
        </Typography>
        
        <Box sx={{ maxWidth: 500, mb: 4 }}>
          <Select
            value={selectedDesign}
            onChange={handleDesignChange}
            fullWidth
            displayEmpty
            sx={{ mb: 2 }}
            renderValue={(selected) => {
              const design = designOptions.find(d => d.id === selected);
              return (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {design?.icon}
                  <Typography variant="subtitle1" sx={{ ml: 1 }}>
                    {design?.name || "Select a design"}
                  </Typography>
                </Box>
              );
            }}
          >
            {designOptions.map((design) => (
              <MenuItem key={design.id} value={design.id}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {design.icon}
                  <Typography variant="subtitle1" sx={{ ml: 1 }}>
                    {design.name}
                  </Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </Box>
      </Box>
      
      {renderDesign()}
    </Container>
  );
};

// 1. Card-Based Layout
const CardBasedLayout = ({ rules }) => {
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>Card-Based Layout</Typography>
      <Grid container spacing={3}>
        {rules.map(rule => (
          <Grid item xs={12} sm={6} md={4} key={rule.id}>
            <Card elevation={2} sx={{ 
              p: 2, 
              height: '100%',
              transition: 'all 0.2s',
              '&:hover': { transform: 'translateY(-4px)', boxShadow: 6 }
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Chip size="small" 
                  color={rule.active ? "success" : "default"} 
                  label={rule.active ? "Active" : "Inactive"} 
                />
                <Box>
                  <IconButton size="small"><EditIcon fontSize="small" /></IconButton>
                  <IconButton size="small"><DeleteIcon fontSize="small" /></IconButton>
                </Box>
              </Box>
              <Typography variant="h6" noWrap sx={{ mb: 0.5 }}>{rule.name}</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1.5 }}>{rule.description}</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 'auto' }}>
                <Chip 
                  size="small" 
                  label={`Priority: ${rule.priority}`} 
                  variant="outlined" 
                  sx={{ height: 20 }}
                />
                <Typography variant="caption">
                  {new Date(rule.updatedAt).toLocaleDateString()}
                </Typography>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

// 2. Enhanced Table with Visual Hierarchy
const EnhancedTable = ({ rules }) => {
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>Enhanced Table with Visual Hierarchy</Typography>
      <TableContainer component={Paper} elevation={0} sx={{ 
        border: '1px solid', 
        borderColor: 'divider',
        borderRadius: 2,
        overflow: 'hidden'
      }}>
        <Table>
          <TableHead sx={{ bgcolor: theme => alpha(theme.palette.primary.main, 0.04) }}>
            <TableRow>
              <TableCell sx={{ fontWeight: 600, py: 2 }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2 }}>Name</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2 }}>Description</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2 }}>Priority</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2 }} align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rules.map((rule, index) => (
              <TableRow 
                key={rule.id} 
                sx={{ 
                  '&:nth-of-type(odd)': { bgcolor: theme => alpha(theme.palette.primary.main, 0.01) },
                  '&:hover': { bgcolor: theme => alpha(theme.palette.primary.main, 0.04) },
                  transition: 'background-color 0.2s'
                }}
              >
                <TableCell>
                  <Chip 
                    size="small" 
                    color={rule.active ? "success" : "default"} 
                    label={rule.active ? "Active" : "Inactive"}
                    sx={{ minWidth: 75, justifyContent: 'center' }}
                  />
                </TableCell>
                <TableCell sx={{ fontWeight: 500 }}>{rule.name}</TableCell>
                <TableCell>{rule.description}</TableCell>
                <TableCell>
                  <Chip 
                    size="small"
                    color={rule.priority === 1 ? "error" : rule.priority === 2 ? "warning" : "info"}
                    label={`P${rule.priority}`}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell align="right">
                  <ButtonGroup size="small" variant="outlined">
                    <Button startIcon={<EditIcon />}>Edit</Button>
                    <Button color="error" startIcon={<DeleteIcon />}>Delete</Button>
                  </ButtonGroup>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

// 3. List View with Expandable Sections
const ListViewWithSections = ({ rules }) => {
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>List View with Expandable Sections</Typography>
      <List sx={{ 
        bgcolor: 'background.paper', 
        borderRadius: 2,
        border: '1px solid', 
        borderColor: 'divider',
        overflow: 'hidden' 
      }}>
        {rules.map((rule) => (
          <React.Fragment key={rule.id}>
            <ListItem
              secondaryAction={
                <Box>
                  <IconButton edge="end" size="small"><EditIcon fontSize="small" /></IconButton>
                  <IconButton edge="end" size="small"><DeleteIcon fontSize="small" /></IconButton>
                </Box>
              }
              sx={{ 
                py: 1.5,
                px: 2, 
                '&:hover': { backgroundColor: theme => alpha(theme.palette.primary.main, 0.04) },
                transition: 'background-color 0.2s'
              }}
            >
              <ListItemIcon>
                <Avatar sx={{ 
                  width: 40, 
                  height: 40, 
                  bgcolor: rule.active ? 'success.light' : 'action.disabledBackground'
                }}>
                  <RuleIcon fontSize="small" />
                </Avatar>
              </ListItemIcon>
              <ListItemText 
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="subtitle1" component="span">{rule.name}</Typography>
                    <Chip 
                      size="small" 
                      label={`Priority: ${rule.priority}`} 
                      sx={{ ml: 1, height: 20 }} 
                      variant="outlined"
                      color={rule.priority === 1 ? "error" : rule.priority === 2 ? "warning" : "info"}
                    />
                  </Box>
                }
                secondary={
                  <Box sx={{ mt: 0.5 }}>
                    <Typography variant="body2" color="text.secondary">
                      {rule.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Last updated: {new Date(rule.updatedAt).toLocaleDateString()}
                    </Typography>
                  </Box>
                } 
              />
            </ListItem>
            <Divider variant="inset" component="li" />
          </React.Fragment>
        ))}
      </List>
    </Box>
  );
};

// 4. Categorized Tables with Tabs
const CategorizedTables = ({ rules, tabValue, handleTabChange }) => {
  const getFilteredRules = () => {
    switch (tabValue) {
      case 'active':
        return rules.filter(rule => rule.active);
      case 'inactive':
        return rules.filter(rule => !rule.active);
      case 'high':
        return rules.filter(rule => rule.priority === 1);
      default:
        return rules;
    }
  };
  
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>Categorized Tables with Tabs</Typography>
      <Box sx={{ width: '100%' }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange} 
          sx={{ mb: 2 }}
          variant="scrollable" 
          scrollButtons="auto"
        >
          <Tab label="All Rules" value="all" />
          <Tab label="Active" value="active" />
          <Tab label="Inactive" value="inactive" />
          <Tab label="High Priority" value="high" />
        </Tabs>
        
        <TableContainer component={Paper} elevation={2} sx={{ borderRadius: 2 }}>
          <Table>
            <TableHead sx={{ bgcolor: 'primary.light' }}>
              <TableRow>
                <TableCell sx={{ fontWeight: 600, color: 'white' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 600, color: 'white' }}>Name</TableCell>
                <TableCell sx={{ fontWeight: 600, color: 'white' }}>Description</TableCell>
                <TableCell sx={{ fontWeight: 600, color: 'white' }}>Priority</TableCell>
                <TableCell sx={{ fontWeight: 600, color: 'white' }} align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {getFilteredRules().map((rule) => (
                <TableRow 
                  key={rule.id} 
                  hover
                >
                  <TableCell>
                    <Chip 
                      size="small" 
                      color={rule.active ? "success" : "default"} 
                      label={rule.active ? "Active" : "Inactive"}
                    />
                  </TableCell>
                  <TableCell>{rule.name}</TableCell>
                  <TableCell>{rule.description}</TableCell>
                  <TableCell>{rule.priority}</TableCell>
                  <TableCell align="right">
                    <IconButton size="small" color="primary"><EditIcon fontSize="small" /></IconButton>
                    <IconButton size="small" color="error"><DeleteIcon fontSize="small" /></IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
};

// 5. Timeline View
const TimelineView = ({ rules }) => {
  const TimelineItem = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(2),
    position: 'relative',
    '&:before': {
      content: '""',
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: -20,
      width: 2,
      backgroundColor: theme.palette.divider,
    },
    '&:after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      left: -25,
      width: 10,
      height: 10,
      borderRadius: '50%',
      backgroundColor: theme.palette.primary.main,
      transform: 'translateY(-50%)',
    }
  }));
  
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>Timeline View</Typography>
      <Box sx={{ ml: 4, position: 'relative' }}>
        <Stack spacing={3}>
          {rules.map(rule => (
            <TimelineItem key={rule.id}>
              <Box sx={{ mb: 1 }}>
                <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 'medium' }}>
                  Last updated: {new Date(rule.updatedAt).toLocaleDateString()}
                </Typography>
                <Chip 
                  size="small" 
                  color={rule.active ? "success" : "default"} 
                  label={rule.active ? "Active" : "Inactive"}
                  sx={{ ml: 1 }}
                />
              </Box>
              <Typography variant="h6" component="div">{rule.name}</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1.5 }}>
                {rule.description}
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Chip 
                  size="small"
                  color={rule.priority === 1 ? "error" : rule.priority === 2 ? "warning" : "info"}
                  label={`Priority: ${rule.priority}`}
                  variant="outlined"
                />
                <Box>
                  <IconButton size="small" color="primary"><EditIcon fontSize="small" /></IconButton>
                  <IconButton size="small" color="error"><DeleteIcon fontSize="small" /></IconButton>
                </Box>
              </Box>
            </TimelineItem>
          ))}
        </Stack>
      </Box>
    </Box>
  );
};

// 6. Data Grid View (Simplified version since we can't use MUI-X here)
const DataGridView = ({ rules }) => {
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>Data Grid View</Typography>
      <Paper 
        elevation={0} 
        sx={{ 
          border: '1px solid', 
          borderColor: 'divider',
          borderRadius: 2,
          overflow: 'hidden'
        }}
      >
        <Box sx={{ 
          p: 2, 
          display: 'flex', 
          justifyContent: 'space-between', 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          backgroundColor: theme => alpha(theme.palette.primary.main, 0.03)
        }}>
          <Typography variant="subtitle1" fontWeight="medium">Rules Data Grid</Typography>
          <Box>
            <Button size="small" variant="outlined" sx={{ mr: 1 }}>Export</Button>
            <Button size="small" variant="contained">Add Rule</Button>
          </Box>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <input type="checkbox" />
                </TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Name</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Priority</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Last Updated</TableCell>
                <TableCell sx={{ fontWeight: 600 }} align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rules.map((rule) => (
                <TableRow 
                  key={rule.id} 
                  hover
                  sx={{ '&:hover': { cursor: 'pointer' } }}
                >
                  <TableCell padding="checkbox">
                    <input type="checkbox" />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      size="small" 
                      color={rule.active ? "success" : "default"} 
                      label={rule.active ? "Active" : "Inactive"}
                    />
                  </TableCell>
                  <TableCell>{rule.name}</TableCell>
                  <TableCell>{rule.description}</TableCell>
                  <TableCell>{rule.priority}</TableCell>
                  <TableCell>{new Date(rule.updatedAt).toLocaleDateString()}</TableCell>
                  <TableCell align="right">
                    <IconButton size="small" color="primary"><EditIcon fontSize="small" /></IconButton>
                    <IconButton size="small" color="error"><DeleteIcon fontSize="small" /></IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Box sx={{ 
          p: 1.5, 
          display: 'flex', 
          justifyContent: 'space-between',
          borderTop: '1px solid', 
          borderColor: 'divider',
          backgroundColor: theme => alpha(theme.palette.primary.main, 0.01)
        }}>
          <Typography variant="body2">
            {rules.length} rules
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" sx={{ mr: 2 }}>
              1-{rules.length} of {rules.length}
            </Typography>
            <Button disabled>Previous</Button>
            <Button>Next</Button>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

// 7. Kanban Board View
const KanbanBoardView = ({ rules }) => {  
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>Kanban Board View</Typography>
      <Box sx={{ display: 'flex', gap: 2, overflowX: 'auto', pb: 2 }}>
        {/* Active Rules Column */}
        <Paper sx={{ minWidth: 300, maxWidth: 350, p: 2, bgcolor: alpha('#4caf50', 0.05) }}>
          <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <DoneIcon sx={{ mr: 1, color: 'success.main' }} />
            Active Rules ({rules.filter(rule => rule.active).length})
          </Typography>
          <Stack spacing={1.5}>
            {rules.filter(rule => rule.active).map(rule => (
              <Card key={rule.id} elevation={1} sx={{ p: 1.5 }}>
                <Typography variant="subtitle2">{rule.name}</Typography>
                <Typography variant="body2" color="text.secondary" noWrap sx={{ mb: 1 }}>
                  {rule.description}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Chip 
                    size="small"
                    label={`P${rule.priority}`}
                    color={rule.priority === 1 ? "error" : rule.priority === 2 ? "warning" : "info"}
                    sx={{ height: 20 }}
                  />
                  <Box>
                    <IconButton size="small"><EditIcon fontSize="small" /></IconButton>
                    <IconButton size="small"><DeleteIcon fontSize="small" /></IconButton>
                  </Box>
                </Box>
              </Card>
            ))}
          </Stack>
        </Paper>
        
        {/* Inactive Rules Column */}
        <Paper sx={{ minWidth: 300, maxWidth: 350, p: 2, bgcolor: alpha('#9e9e9e', 0.05) }}>
          <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <PauseIcon sx={{ mr: 1, color: 'text.secondary' }} />
            Inactive Rules ({rules.filter(rule => !rule.active).length})
          </Typography>
          <Stack spacing={1.5}>
            {rules.filter(rule => !rule.active).map(rule => (
              <Card key={rule.id} elevation={1} sx={{ p: 1.5 }}>
                <Typography variant="subtitle2">{rule.name}</Typography>
                <Typography variant="body2" color="text.secondary" noWrap sx={{ mb: 1 }}>
                  {rule.description}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Chip 
                    size="small"
                    label={`P${rule.priority}`}
                    color={rule.priority === 1 ? "error" : rule.priority === 2 ? "warning" : "info"}
                    sx={{ height: 20 }}
                  />
                  <Box>
                    <IconButton size="small"><EditIcon fontSize="small" /></IconButton>
                    <IconButton size="small"><DeleteIcon fontSize="small" /></IconButton>
                  </Box>
                </Box>
              </Card>
            ))}
          </Stack>
        </Paper>
      </Box>
    </Box>
  );
};

export default RuleTableDesigns;
