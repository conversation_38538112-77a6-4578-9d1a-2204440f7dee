package com.progize.tms.repository;

import com.progize.tms.repository.entity.RuleVariableSourceTable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/** Repository for accessing rule variable source tables. */
public interface RuleVariableSourceTableRepository
    extends JpaRepository<RuleVariableSourceTable, Long> {

  /** Find a source table by its name, schema, and connection. */
  Optional<RuleVariableSourceTable> findByTableNameAndSchemaNameAndConnectionName(
      String tableName, String schemaName, String connectionName);

  /** Find a source table by its name only (using default schema and connection). */
  Optional<RuleVariableSourceTable> findByTableName(String tableName);

  /** Find source tables updated since a given timestamp. */
  @Query("SELECT t FROM RuleVariableSourceTable t WHERE t.updatedAt > :since")
  List<RuleVariableSourceTable> findUpdatedSince(@Param("since") LocalDateTime since);
}
