-- Main case table
CREATE TABLE workflow_cases (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    case_number VARCHAR(50) NOT NULL UNIQUE,
    workflow_type VARCHAR(50) NOT NULL,  -- FINANCE, COMPLIANCE, RULE_TRIGGERED, etc.
    status VARCHAR(50) NOT NULL,
    final_outcome VARCHAR(50),           -- Final resolution (FALSE_POSITIVE, SAR_FILED, etc.)
    process_instance_id VARCHAR(64),      -- Flowable process instance ID
    rule_id BIGINT,                      -- Reference to the rule that triggered this case
    priority INTEGER,
    due_date TIMESTAMP,
    entity_type VARCHAR(50),              -- TRANSACTION, CUSTOMER, ACCOUNT, etc.
    entity_id BIGINT,                     -- Can be NULL for aggregate/system-wide cases
    customer_id BIGINT,                   -- Customer associated with the case
    summary JSON,                         -- Denormalized data for quick access
    metadata JSON,                        -- Searchable metadata
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGIN<PERSON>,
    updated_by BIGIN<PERSON>,
    CONSTRAINT fk_workflow_cases_created_by <PERSON>OR<PERSON><PERSON><PERSON>EY (created_by) REFERENCES users(id),
    CONSTRAINT fk_workflow_cases_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Case history for audit trail
CREATE TABLE case_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    case_id BIGINT NOT NULL,
    event_type VARCHAR(50) NOT NULL,      -- TASK_COMPLETED, ATTACHMENT_ADDED, etc.
    event_time TIMESTAMP NOT NULL,
    actor VARCHAR(50) NOT NULL,
    task_id VARCHAR(64),                  -- Flowable task ID
    task_name VARCHAR(255),
    event_details JSON,                   -- Event-specific data
    old_values JSON,                      -- Previous state
    new_values JSON,                      -- New state
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_case_history_case FOREIGN KEY (case_id) REFERENCES workflow_cases(id)
);

-- Case attachments
CREATE TABLE case_attachments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    case_id BIGINT NOT NULL,
    process_instance_id VARCHAR(64),
    task_id VARCHAR(64),
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    storage_path VARCHAR(512) NOT NULL,    -- Path in cloud storage
    storage_provider VARCHAR(50) NOT NULL,  -- S3/AZURE/GCS
    metadata JSON,                         -- File metadata (size, content type, etc.)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    CONSTRAINT fk_case_attachments_case FOREIGN KEY (case_id) REFERENCES workflow_cases(id),
    CONSTRAINT fk_case_attachments_created_by FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Case comments
CREATE TABLE case_comments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    case_id BIGINT NOT NULL,
    process_instance_id VARCHAR(64),
    task_id VARCHAR(64),
    comment_text TEXT NOT NULL,
    comment_type VARCHAR(50),             -- SYSTEM, USER, DECISION
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    CONSTRAINT fk_case_comments_case FOREIGN KEY (case_id) REFERENCES workflow_cases(id),
    CONSTRAINT fk_case_comments_created_by FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Audit tables
CREATE TABLE workflow_cases_aud (
    id BIGINT NOT NULL,
    rev INTEGER NOT NULL,
    revtype TINYINT,
    case_number VARCHAR(50),
    workflow_type VARCHAR(50),
    status VARCHAR(50),
    process_instance_id VARCHAR(64),
    priority INTEGER,
    due_date TIMESTAMP,
    entity_type VARCHAR(50),
    entity_id BIGINT,
    summary JSON,
    metadata JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_workflow_cases_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);

-- Indexes
CREATE INDEX idx_workflow_cases_workflow_type ON workflow_cases(workflow_type);
CREATE INDEX idx_workflow_cases_status ON workflow_cases(status);
CREATE INDEX idx_workflow_cases_entity ON workflow_cases(entity_type, entity_id);
CREATE INDEX idx_workflow_cases_process ON workflow_cases(process_instance_id);
CREATE INDEX idx_case_history_case_id ON case_history(case_id);
CREATE INDEX idx_case_history_event_type ON case_history(event_type);
CREATE INDEX idx_case_attachments_case_id ON case_attachments(case_id);
CREATE INDEX idx_case_comments_case_id ON case_comments(case_id);
