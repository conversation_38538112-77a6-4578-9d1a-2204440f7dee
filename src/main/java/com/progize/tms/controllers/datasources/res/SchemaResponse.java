package com.progize.tms.controllers.datasources.res;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Response class for schema information. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchemaResponse {
  private String entityName;
  private String entityType;
  private List<SchemaFieldResponse> fields;
  private LocalDateTime discoveredAt;

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class SchemaFieldResponse {
    private String name;
    private String type;
    private boolean nullable;
    private String description;
  }
}
