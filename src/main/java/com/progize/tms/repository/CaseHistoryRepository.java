package com.progize.tms.repository;

import com.progize.tms.repository.entity.CaseHistory;
import com.progize.tms.repository.entity.WorkflowCase;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Repository for case history. */
@Repository
public interface CaseHistoryRepository extends JpaRepository<CaseHistory, Long> {

  /**
   * Find all history entries for a specific workflow case.
   *
   * @param workflowCase the workflow case
   * @return list of history entries
   */
  List<CaseHistory> findByWorkflowCaseOrderByCreatedAtDesc(WorkflowCase workflowCase);

  /**
   * Find all history entries for a specific task.
   *
   * @param taskId the task ID
   * @return list of history entries
   */
  List<CaseHistory> findByTaskIdOrderByCreatedAtDesc(String taskId);

  /**
   * Find all history entries for a specific process instance.
   *
   * @param processInstanceId the process instance ID
   * @return list of history entries
   */
  List<CaseHistory> findByProcessInstanceIdOrderByCreatedAtDesc(String processInstanceId);
}
