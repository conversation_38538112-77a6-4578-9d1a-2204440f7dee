package com.progize.tms.controllers.databuckets;

import com.progize.tms.controllers.databuckets.req.CreateDataBucketRequest;
import com.progize.tms.controllers.databuckets.req.CreateDataBucketVariableRequest;
import com.progize.tms.controllers.databuckets.req.CreateLookupBucketRequest;
import com.progize.tms.controllers.databuckets.req.CreateLookupBucketRequest.LookupBucketVariableRequest;
import com.progize.tms.controllers.databuckets.res.DataBucketResponse;
import com.progize.tms.controllers.databuckets.res.DataBucketVariableResponse;
import com.progize.tms.controllers.databuckets.res.LookupBucketResponse;
import com.progize.tms.controllers.databuckets.res.LookupBucketResponse.LookupBucketVariableResponse;
import com.progize.tms.controllers.variables.res.RuleVariableResponse;
import com.progize.tms.repository.entity.DataBucketEntity;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DataType;
import com.progize.tms.repository.entity.enums.BucketType;
import com.progize.tms.service.DataSourceService;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/** Transformer for converting between Data Bucket entities and DTOs. */
@Component
@RequiredArgsConstructor
public class DataBucketTransformer {

  private final DataSourceService dataSourceService;

  /** Convert a DataBucketEntity to a DataBucketResponse. */
  public DataBucketResponse toDataBucketResponse(DataBucketEntity entity) {
    if (entity == null) {
      return null;
    }

    DataBucketResponse response = new DataBucketResponse();
    response.setId(entity.getId());
    response.setName(entity.getName());
    response.setDescription(entity.getDescription());
    response.setBucketType(entity.getBucketType().name());
    response.setTableName(entity.getTableName());
    response.setCreatedAt(entity.getCreatedAt());

    // Include data source ID if available
    if (entity.getDataSource() != null) {
      response.setDataSourceId(entity.getDataSource().getId());
      response.setDataSourceName(entity.getDataSource().getName());
    }

    // Include variables if they exist
    if (entity.getVariables() != null && !entity.getVariables().isEmpty()) {
      response.setVariables(toDataBucketVariableResponseList(entity.getVariables()));
    }

    return response;
  }

  /** Convert a list of DataBucketEntity to a list of DataBucketResponse. */
  public List<DataBucketResponse> toDataBucketResponseList(List<DataBucketEntity> entities) {
    if (entities == null) {
      return null;
    }

    return entities.stream().map(this::toDataBucketResponse).collect(Collectors.toList());
  }

  /** Convert a DataBucketVariableEntity to a DataBucketVariableResponse. */
  public DataBucketVariableResponse toDataBucketVariableResponse(DataBucketVariableEntity entity) {
    if (entity == null) {
      return null;
    }

    DataBucketVariableResponse response = new DataBucketVariableResponse();
    response.setId(entity.getId());
    response.setCode(entity.getCode());
    response.setName(entity.getName());
    response.setDescription(entity.getDescription());
    response.setDataTypeCode(entity.getDataType().getCode());
    response.setIsUnique(entity.getIsUnique());
    response.setAggregatable(entity.isAggregatable());
    response.setTableName(entity.getTableName());
    response.setColumnName(entity.getColumnName());

    // Set the data bucket ID
    if (entity.getDataBucket() != null) {
      response.setDataBucketId(entity.getDataBucket().getId());

      // By default, the source is the same as the data bucket
      response.setSourceBucketId(entity.getDataBucket().getId());
      response.setSourceBucketName(entity.getDataBucket().getName());
      response.setEnriched(false);
    }

    return response;
  }

  /**
   * Convert a DataBucketVariableEntity to a DataBucketVariableResponse with source information.
   * This is used for enriched variables from lookup buckets.
   */
  public DataBucketVariableResponse toEnrichedDataBucketVariableResponse(
      DataBucketVariableEntity entity, Long targetBucketId, String targetBucketName) {
    if (entity == null) {
      return null;
    }

    DataBucketVariableResponse response = toDataBucketVariableResponse(entity);

    // Override the data bucket ID with the target bucket ID
    response.setDataBucketId(targetBucketId);

    // Set source information to indicate this is an enriched variable
    if (entity.getDataBucket() != null) {
      response.setSourceBucketId(entity.getDataBucket().getId());
      response.setSourceBucketName(entity.getDataBucket().getName());
    }
    response.setEnriched(true);

    return response;
  }

  /** Convert a list of DataBucketVariableEntity to a list of DataBucketVariableResponse. */
  public List<DataBucketVariableResponse> toDataBucketVariableResponseList(
      List<DataBucketVariableEntity> entities) {
    if (entities == null) {
      return null;
    }

    return entities.stream().map(this::toDataBucketVariableResponse).collect(Collectors.toList());
  }

  /** Convert a list of DataBucketVariableEntity to a list of RuleVariableResponse. */
  public List<RuleVariableResponse> toRuleVariableResponseList(
      List<DataBucketVariableEntity> entities) {
    if (entities == null) {
      return null;
    }

    return entities.stream().map(this::toRuleVariableResponse).collect(Collectors.toList());
  }

  /**
   * Convert a map of DataBucketVariableEntity and their supported operators to a list of
   * RuleVariableResponse.
   *
   * @param entitiesWithOperators Map of entities and their supported operators
   * @return List of RuleVariableResponse objects
   */
  public List<RuleVariableResponse> toRuleVariableResponseList(
      Map<DataBucketVariableEntity, List<String>> entitiesWithOperators) {
    if (entitiesWithOperators == null || entitiesWithOperators.isEmpty()) {
      return Collections.emptyList();
    }

    return entitiesWithOperators.entrySet().stream()
        .map(entry -> toRuleVariableResponse(entry.getKey(), entry.getValue()))
        .collect(Collectors.toList());
  }

  /** Convert a DataBucketVariableEntity to a RuleVariableResponse. */
  public RuleVariableResponse toRuleVariableResponse(DataBucketVariableEntity entity) {
    if (entity == null) {
      return null;
    }

    // Map the data type code to the expected type format in RuleVariableResponse
    String type = mapDataTypeToVariableType(entity.getDataType().getCode());

    // Create a new RuleVariableResponse with the mapped fields
    return RuleVariableResponse.builder()
        .id(entity.getCode())
        .name(entity.getName())
        .description(entity.getDescription())
        .type(type)
        .category(entity.getTableName())
        .aggregatable(entity.isAggregatable())
        .supportedOperators(Collections.emptyList()) // Default empty list
        .dataTypeCode(entity.getDataType().getCode()) // Add dataTypeCode for backward compatibility
        .build();
  }

  /**
   * Convert a DataBucketVariableEntity to a RuleVariableResponse with the provided supported
   * operators.
   *
   * @param entity The entity to convert
   * @param supportedOperators The list of supported operators for this variable
   * @return A RuleVariableResponse with the supported operators
   */
  public RuleVariableResponse toRuleVariableResponse(
      DataBucketVariableEntity entity, List<String> supportedOperators) {
    if (entity == null) {
      return null;
    }

    // Map the data type code to the expected type format in RuleVariableResponse
    String type = mapDataTypeToVariableType(entity.getDataType().getCode());

    // Create a new RuleVariableResponse with the mapped fields
    return RuleVariableResponse.builder()
        .id(entity.getCode())
        .name(entity.getName())
        .description(entity.getDescription())
        .type(type)
        .category(entity.getTableName())
        .aggregatable(entity.isAggregatable())
        .supportedOperators(
            supportedOperators != null ? supportedOperators : Collections.emptyList())
        .dataTypeCode(entity.getDataType().getCode()) // Add dataTypeCode for backward compatibility
        .build();
  }

  /** Map data type code to variable type format used in RuleVariableResponse. */
  private String mapDataTypeToVariableType(String dataTypeCode) {
    switch (dataTypeCode) {
      case "VARCHAR":
      case "CHAR":
      case "TEXT":
        return "string";
      case "INT":
      case "SMALLINT":
      case "TINYINT":
        return "integer";
      case "BIGINT":
        return "bigint";
      case "DECIMAL":
      case "NUMERIC":
        return "bigdecimal";
      case "FLOAT":
      case "DOUBLE":
      case "REAL":
        return "double";
      case "DATE":
      case "TIMESTAMP":
        return "date";
      case "BOOLEAN":
        return "boolean";
      default:
        return "string"; // Default to string for unknown types
    }
  }

  /** Convert a DataBucketEntity to a LookupBucketResponse. */
  public LookupBucketResponse toLookupBucketResponse(DataBucketEntity entity) {
    if (entity == null) {
      return null;
    }

    LookupBucketResponse response = new LookupBucketResponse();
    response.setId(entity.getId());
    response.setName(entity.getName());
    response.setDescription(entity.getDescription());
    response.setTableName(entity.getTableName());
    response.setBucketType(entity.getBucketType().name());

    // Include data source ID if available
    if (entity.getDataSource() != null) {
      response.setDataSourceId(entity.getDataSource().getId());
      response.setDataSourceName(entity.getDataSource().getName());
    }

    if (entity.getVariables() != null) {
      response.setVariables(
          entity.getVariables().stream()
              .map(this::toLookupBucketVariableResponse)
              .collect(Collectors.toList()));
    }

    return response;
  }

  /** Convert a DataBucketVariableEntity to a LookupBucketVariableResponse. */
  public LookupBucketVariableResponse toLookupBucketVariableResponse(
      DataBucketVariableEntity entity) {
    if (entity == null) {
      return null;
    }

    LookupBucketVariableResponse response = new LookupBucketVariableResponse();
    response.setId(entity.getId());
    response.setCode(entity.getCode());
    response.setName(entity.getName());
    response.setDescription(entity.getDescription());
    response.setDataTypeCode(entity.getDataType().getCode());
    response.setIsUnique(entity.getIsUnique());

    return response;
  }

  /** Convert a CreateDataBucketRequest to a DataBucketEntity. */
  public DataBucketEntity toDataBucketEntity(CreateDataBucketRequest request) {
    if (request == null) {
      return null;
    }

    DataBucketEntity entity = new DataBucketEntity();
    entity.setName(request.getName());
    entity.setDescription(request.getDescription());
    entity.setTableName(request.getTableName());
    entity.setBucketType(
        BucketType.valueOf(request.getBucketType() != null ? request.getBucketType() : "SYSTEM"));

    // Set data source if ID is provided
    if (request.getDataSourceId() != null) {
      DataSourceEntity dataSource =
          dataSourceService
              .getDataSourceById(request.getDataSourceId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Data source not found: " + request.getDataSourceId()));
      entity.setDataSource(dataSource);
    }

    return entity;
  }

  /** Convert a CreateLookupBucketRequest to a DataBucketEntity. */
  public DataBucketEntity toDataBucketEntity(CreateLookupBucketRequest request) {
    if (request == null) {
      return null;
    }

    DataBucketEntity entity = new DataBucketEntity();
    entity.setName(request.getName());
    entity.setDescription(request.getDescription());
    entity.setTableName(request.getTableName());
    entity.setBucketType(
        BucketType.valueOf(request.getBucketType() != null ? request.getBucketType() : "LOOKUP"));

    // Set data source if ID is provided
    if (request.getDataSourceId() != null) {
      DataSourceEntity dataSource =
          dataSourceService
              .getDataSourceById(request.getDataSourceId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Data source not found: " + request.getDataSourceId()));
      entity.setDataSource(dataSource);
    }

    if (request.getVariables() != null) {
      entity.setVariables(
          request.getVariables().stream()
              .map(varReq -> toDataBucketVariableEntity(varReq, entity))
              .collect(Collectors.toList()));
    }

    return entity;
  }

  /** Convert a LookupBucketVariableRequest to a DataBucketVariableEntity. */
  private DataBucketVariableEntity toDataBucketVariableEntity(
      LookupBucketVariableRequest request, DataBucketEntity bucket) {
    if (request == null) {
      return null;
    }

    DataBucketVariableEntity entity = new DataBucketVariableEntity();
    entity.setCode(request.getCode());
    entity.setName(request.getName());
    entity.setDescription(request.getDescription());
    entity.setIsUnique(request.getIsUnique());
    entity.setDataBucket(bucket);
    entity.setColumnName(request.getColumnName());

    // Set table name from bucket if available
    if (bucket != null && bucket.getTableName() != null) {
      entity.setTableName(bucket.getTableName());
    }

    // Create a new DataType with just the code (it will be resolved by the service)
    DataType dataType = new DataType();
    dataType.setCode(request.getDataTypeCode());
    entity.setDataType(dataType);

    return entity;
  }

  /** Convert a CreateDataBucketVariableRequest to a DataBucketVariableEntity. */
  public DataBucketVariableEntity toDataBucketVariableEntity(
      CreateDataBucketVariableRequest request) {
    if (request == null) {
      return null;
    }

    DataBucketVariableEntity entity = new DataBucketVariableEntity();
    entity.setCode(request.getCode());
    entity.setName(request.getName());
    entity.setDescription(request.getDescription());
    entity.setTableName(request.getTableName());
    entity.setColumnName(request.getColumnName());
    entity.setAggregatable(request.isAggregatable());
    entity.setIsUnique(request.getIsUnique());

    // Create a new DataType with just the code (it will be resolved by the service)
    DataType dataType = new DataType();
    dataType.setCode(request.getDataTypeCode());
    entity.setDataType(dataType);

    return entity;
  }
}
