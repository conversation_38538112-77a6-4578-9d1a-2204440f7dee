package com.progize.tms.controllers.datasources.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/** Response class for REST API data source information. */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RestApiSourceResponse extends DataSourceResponse {
  private String baseUrl;
  private String authenticationType;
  private String username;
  // Password and secrets are not included in the response for security reasons
  private String apiKeyName;
  private String apiKeyLocation;
  private String oauthTokenUrl;
  private String oauthClientId;
  private String oauthScope;
  private Integer requestTimeoutMs;
}
