package com.progize.tms.repository;

import com.progize.tms.repository.entity.DataBucketVariableEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Repository for DataBucketVariableEntity. */
@Repository
public interface DataBucketVariableRepository
    extends JpaRepository<DataBucketVariableEntity, Long> {
  List<DataBucketVariableEntity> findByDataBucketId(Long bucketId);
}
