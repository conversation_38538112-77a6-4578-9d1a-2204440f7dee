import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Button,
  TextField,
  MenuItem,
  FormControl,
  FormLabel,
  FormControlLabel,
  RadioGroup,
  Radio,
  Checkbox,
  CircularProgress,
  Paper,
  Chip,
  Alert,
  AlertTitle,
  Tabs,
  Tab,
  LinearProgress,
  Avatar,
  Badge,
  IconButton,
  Tooltip,
  Fade,
  Zoom,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  Stack,
  Slide
} from '@mui/material';
import {
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
  ArrowBack as ArrowBackIcon,
  AttachFile as AttachFileIcon,
  Send as SendIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Flag as FlagIcon,
  AccountBalance as AccountBalanceIcon,
  Business as BusinessIcon,
  Public as PublicIcon,
  Security as SecurityIcon,
  Gavel as GavelIcon,
  Timeline as TimelineIcon,
  Bar<PERSON>hart as BarChartIcon,
  SmartToy as SmartToyIcon,
  Close as CloseIcon,
  Help as HelpIcon,
  MoreVert as MoreVertIcon,
  TrendingUp as EscalateIcon,
  Cancel as RejectIcon,
  CheckCircleOutline as ApproveIcon,
  Info as InfoIcon,
  MonetizationOn as MoneyIcon,
  LocationOn as LocationIcon,
  Assessment as RiskIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import ComplianceWorkflowService from '../../services/ComplianceWorkflowService';


/**
 * Modern compliance task detail component with tabbed interface and AI investigator
 */
const ComplianceTaskDetail = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();

  // Sample task data based on your variables structure
  const sampleTaskData = {
    id: taskId,
    name: "High Risk Transaction Review",
    variables: {
      deal_date: "2025-06-04T07:39:13.207Z",
      nav: 1000.5,
      officerComments: "adsfasfsdafasdfas",
      trade_date_time: "2025-06-04T07:39:13.207Z",
      aml_risk_score_channel: 100,
      is_pep: "Sample is_pep",
      fund_id: 100,
      fund_name: "Sample fund_name",
      ruleDescription: "High Transaction Amount with High Risk",
      fund_net_amount: 1000.5,
      flagReason: null,
      managerDecision: "return_to_officer",
      source_of_fund: "Sample source_of_fund",
      caseId: 3,
      aml_risk_profile: "HIGH",
      aml_risk_score_geography: 100,
      ruleName: "High Transaction Amount with High Risk",
      aml_risk_score_customer: 100,
      portfolio_id: 100,
      managerComments: "This should be returned to the officer, more information needed.",
      fund_category: "Sample fund_category",
      officerDecision: "positive",
      ruleId: 1
    }
  };

  // Core state
  const [task, setTask] = useState(null);
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [attachments, setAttachments] = useState([]);

  // UI state
  const [tabValue, setTabValue] = useState(0);
  const [aiChatOpen, setAiChatOpen] = useState(false);
  const [aiMessages, setAiMessages] = useState([
    { sender: 'ai', text: 'Hello! I can help investigate this case. What would you like to know?' }
  ]);
  const [aiInput, setAiInput] = useState('');
  const [confirmationDialog, setConfirmationDialog] = useState({
    open: false,
    action: null,
    title: '',
    message: ''
  });
  
  useEffect(() => {
    const fetchTaskDetails = async () => {
      try {
        setLoading(true);
        // For now, use sample data. Later this will be replaced with actual API call
        // const taskDetails = await ComplianceWorkflowService.getTaskDetails(taskId);
        setTask(sampleTaskData);
        setError(null);
      } catch (err) {
        console.error('Error fetching task details:', err);
        setError('Failed to load task details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTaskDetails();
  }, [taskId]);
  
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  const handleFileChange = (e) => {
    setAttachments(Array.from(e.target.files));
  };
  
  // Tab handling
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // AI Chat functions
  const toggleAiChat = () => {
    setAiChatOpen(!aiChatOpen);
  };
  
  const handleAiInputChange = (e) => {
    setAiInput(e.target.value);
  };
  
  const sendAiMessage = () => {
    if (!aiInput.trim()) return;

    // Add user message
    const userMessage = {
      sender: 'user',
      text: aiInput,
      timestamp: new Date()
    };
    setAiMessages(prev => [...prev, userMessage]);

    // Simulate AI response (in a real app, this would call an API)
    setTimeout(() => {
      const aiResponse = {
        sender: 'ai',
        text: `I've analyzed this case and found that the transaction of $${task?.variables?.fund_net_amount?.toLocaleString() || 'unknown amount'} has a high risk score of ${task?.variables?.aml_risk_score_customer || '?'}/100. This requires additional verification based on the rule "${task?.variables?.ruleName}".`,
        timestamp: new Date()
      };
      setAiMessages(prev => [...prev, aiResponse]);
    }, 1000);

    setAiInput('');
  };
  
  // Action button handlers
  const handleActionButtonClick = (action) => {
    let title, message;
    
    switch(action) {
      case 'request_info':
        title = 'Request Additional Information';
        message = 'Are you sure you want to request additional information for this case?';
        break;
      case 'escalate':
        title = 'Escalate Case';
        message = 'Are you sure you want to escalate this case to a higher authority?';
        break;
      case 'reject':
        title = 'Reject Transaction';
        message = 'Are you sure you want to reject this transaction? This action cannot be undone.';
        break;
      case 'approve':
        title = 'Approve Transaction';
        message = 'Are you sure you want to approve this transaction?';
        break;
      default:
        return;
    }
    
    setConfirmationDialog({
      open: true,
      action,
      title,
      message
    });
  };
  
  const handleConfirmAction = async () => {
    const { action } = confirmationDialog;
    setConfirmationDialog(prev => ({ ...prev, open: false }));

    try {
      setSubmitting(true);
      setError(null);

      // Simulate API call for now
      console.log(`Performing action: ${action} on task ${taskId}`);

      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSubmitSuccess(true);

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/compliance/tasks');
      }, 1500);

    } catch (err) {
      console.error('Error submitting task:', err);
      setError('Failed to submit task. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };
  
  const handleCancelAction = () => {
    setConfirmationDialog(prev => ({ ...prev, open: false }));
  };
  

  
  // Helper functions
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getRiskLevelColor = (riskLevel) => {
    if (!riskLevel) return theme.palette.grey[500];
    const level = riskLevel.toString().toUpperCase();
    if (level.includes('HIGH')) return theme.palette.error.main;
    if (level.includes('MEDIUM')) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getPriorityColor = (priority) => {
    if (!priority) return theme.palette.grey[500];
    const level = priority.toString().toUpperCase();
    if (level.includes('HIGH')) return theme.palette.error.main;
    if (level.includes('MEDIUM')) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getSlaStatusColor = (dueDate) => {
    if (!dueDate) return theme.palette.grey[500];
    const now = new Date();
    const due = new Date(dueDate);
    const diffHours = (due - now) / (1000 * 60 * 60);

    if (diffHours < 0) return theme.palette.error.main; // Overdue
    if (diffHours < 24) return theme.palette.warning.main; // Due soon
    return theme.palette.success.main; // On track
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendAiMessage();
    }
  };




  // Loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <AlertTitle>Error</AlertTitle>
          {error}
        </Alert>
        <Button
          variant="outlined"
          onClick={() => navigate('/compliance/tasks')}
          sx={{ mt: 2 }}
          startIcon={<ArrowBackIcon />}
        >
          Back to Tasks
        </Button>
      </Box>
    );
  }

  // Success state
  if (submitSuccess) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="success">
          <AlertTitle>Success</AlertTitle>
          Task completed successfully! Redirecting...
        </Alert>
      </Box>
    );
  }

  // No task found state
  if (!task) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          <AlertTitle>No Task Found</AlertTitle>
          The requested task could not be found or has been completed.
        </Alert>
        <Button
          variant="outlined"
          onClick={() => navigate('/compliance/tasks')}
          sx={{ mt: 2 }}
          startIcon={<ArrowBackIcon />}
        >
          Back to Tasks
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
      {/* Header with back button */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Tooltip title="Back to Tasks">
          <IconButton
            onClick={() => navigate('/compliance/tasks')}
            sx={{ mr: 2 }}
          >
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Compliance Task Detail
        </Typography>
      </Box>

      {/* Summary Header */}
      <Paper
        elevation={0}
        sx={{
          mb: 2,
          p: 2,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 500, fontSize: '1.1rem' }}>
                Transaction #{task?.variables?.portfolio_id || 'N/A'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box
                  sx={{
                    width: 6,
                    height: 6,
                    borderRadius: '50%',
                    bgcolor: theme.palette.error.main
                  }}
                />
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                  HIGH PRIORITY
                </Typography>
              </Box>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5, fontSize: '0.85rem' }}>
              {task?.variables?.ruleName || 'N/A'} • {task?.variables?.fund_name || 'N/A'}
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' }, gap: 1 }}>
              <Box
                sx={{
                  px: 1.5,
                  py: 0.5,
                  bgcolor: theme.palette.warning.light + '20',
                  color: theme.palette.warning.dark,
                  borderRadius: 0.5,
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: 0.5
                }}
              >
                Under Review
              </Box>
              <Box
                sx={{
                  px: 1.5,
                  py: 0.5,
                  bgcolor: theme.palette.success.light + '20',
                  color: theme.palette.success.dark,
                  borderRadius: 0.5,
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: 0.5
                }}
              >
                2d SLA
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Content Tabs */}
      <Paper
        elevation={0}
        sx={{
          mb: 2,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1
        }}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              minHeight: 40,
              '& .MuiTab-root': {
                minHeight: 40,
                fontSize: '0.875rem',
                fontWeight: 500,
                textTransform: 'none',
                color: theme.palette.text.secondary,
                '&.Mui-selected': {
                  color: theme.palette.primary.main,
                  fontWeight: 600
                }
              },
              '& .MuiTabs-indicator': {
                height: 2
              }
            }}
          >
            <Tab label="Transaction" />
            <Tab label="Rule Details" />
            <Tab label="Customer" />
          </Tabs>
        </Box>

        {/* Transaction Info Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 1.5, bgcolor: theme.palette.grey[50], borderRadius: 1 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    AMOUNT
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1.25rem', mt: 0.5 }}>
                    {formatCurrency(task?.variables?.fund_net_amount)}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 1.5, bgcolor: theme.palette.grey[50], borderRadius: 1 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    CHANNEL
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {task?.variables?.fund_category || 'N/A'}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 1.5, bgcolor: theme.palette.grey[50], borderRadius: 1 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    GEOGRAPHY RISK
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {task?.variables?.aml_risk_score_geography || 'N/A'}/100
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ p: 1.5, bgcolor: theme.palette.grey[50], borderRadius: 1 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    RISK SCORE
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                    <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
                      {task?.variables?.aml_risk_score_customer || 0}/100
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={task?.variables?.aml_risk_score_customer || 0}
                      sx={{
                        flexGrow: 1,
                        height: 4,
                        borderRadius: 2,
                        backgroundColor: theme.palette.grey[300],
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: getRiskLevelColor(task?.variables?.aml_risk_score_customer),
                          borderRadius: 2
                        }
                      }}
                    />
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}
        {/* Rule Info Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    RULE NAME
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {task?.variables?.ruleName || 'N/A'}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    TRIGGER CONDITION
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    Amount &gt; $50,000
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    RULE VERSION
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    v1.0
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    DESCRIPTION
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {task?.variables?.ruleDescription || 'N/A'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Customer Info Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    CUSTOMER NAME
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {task?.variables?.fund_name || 'N/A'}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    NATIONALITY
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    Not Available
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    RISK LEVEL
                  </Typography>
                  <Box sx={{ mt: 0.5 }}>
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.25,
                        bgcolor: task?.variables?.aml_risk_profile === 'HIGH' ? theme.palette.error.light + '20' : theme.palette.warning.light + '20',
                        color: task?.variables?.aml_risk_profile === 'HIGH' ? theme.palette.error.dark : theme.palette.warning.dark,
                        borderRadius: 0.5,
                        fontSize: '0.75rem',
                        fontWeight: 600
                      }}
                    >
                      {task?.variables?.aml_risk_profile || 'UNKNOWN'}
                    </Box>
                  </Box>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    KYC STATUS
                  </Typography>
                  <Box sx={{ mt: 0.5 }}>
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.25,
                        bgcolor: theme.palette.success.light + '20',
                        color: theme.palette.success.dark,
                        borderRadius: 0.5,
                        fontSize: '0.75rem',
                        fontWeight: 600
                      }}
                    >
                      VERIFIED
                    </Box>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                    PEP STATUS
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '0.9rem', mt: 0.5 }}>
                    {task?.variables?.is_pep || 'Not Available'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>
      {/* Actions */}
      <Paper
        elevation={0}
        sx={{
          mb: 2,
          p: 2,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
            Actions
          </Typography>
          <Button
            variant="text"
            size="small"
            startIcon={<SmartToyIcon />}
            onClick={toggleAiChat}
            sx={{
              fontSize: '0.8rem',
              fontWeight: 500,
              textTransform: 'none',
              color: theme.palette.primary.main
            }}
          >
            AI Investigate
          </Button>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => handleActionButtonClick('request_info')}
            disabled={submitting}
            sx={{
              fontSize: '0.8rem',
              fontWeight: 500,
              textTransform: 'none',
              borderColor: theme.palette.grey[300],
              color: theme.palette.text.primary,
              '&:hover': {
                borderColor: theme.palette.grey[400],
                bgcolor: theme.palette.grey[50]
              }
            }}
          >
            Request Info
          </Button>

          <Button
            variant="outlined"
            size="small"
            onClick={() => handleActionButtonClick('escalate')}
            disabled={submitting}
            sx={{
              fontSize: '0.8rem',
              fontWeight: 500,
              textTransform: 'none',
              borderColor: theme.palette.warning.light,
              color: theme.palette.warning.dark,
              '&:hover': {
                borderColor: theme.palette.warning.main,
                bgcolor: theme.palette.warning.light + '10'
              }
            }}
          >
            Escalate
          </Button>

          <Button
            variant="outlined"
            size="small"
            onClick={() => handleActionButtonClick('reject')}
            disabled={submitting}
            sx={{
              fontSize: '0.8rem',
              fontWeight: 500,
              textTransform: 'none',
              borderColor: theme.palette.error.light,
              color: theme.palette.error.dark,
              '&:hover': {
                borderColor: theme.palette.error.main,
                bgcolor: theme.palette.error.light + '10'
              }
            }}
          >
            Reject
          </Button>

          <Button
            variant="contained"
            size="small"
            onClick={() => handleActionButtonClick('approve')}
            disabled={submitting}
            sx={{
              fontSize: '0.8rem',
              fontWeight: 500,
              textTransform: 'none',
              bgcolor: theme.palette.success.main,
              '&:hover': {
                bgcolor: theme.palette.success.dark
              }
            }}
          >
            Approve
          </Button>
        </Box>
      </Paper>

      {/* AI Chat Interface */}
      <Slide direction="up" in={aiChatOpen} mountOnEnter unmountOnExit>
        <Paper
          elevation={8}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            width: 350,
            height: 450,
            borderRadius: 2,
            display: 'flex',
            flexDirection: 'column',
            zIndex: 1300,
            overflow: 'hidden',
            border: `1px solid ${theme.palette.divider}`
          }}
        >
          {/* Chat Header */}
          <Box
            sx={{
              p: 1.5,
              bgcolor: theme.palette.grey[50],
              borderBottom: `1px solid ${theme.palette.divider}`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SmartToyIcon sx={{ fontSize: '1.2rem', color: theme.palette.primary.main }} />
              <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
                AI Assistant
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={toggleAiChat}
              sx={{
                color: theme.palette.text.secondary,
                '&:hover': { bgcolor: theme.palette.grey[200] }
              }}
            >
              <CloseIcon sx={{ fontSize: '1rem' }} />
            </IconButton>
          </Box>

          {/* Chat Messages */}
          <Box
            sx={{
              flexGrow: 1,
              p: 1.5,
              overflowY: 'auto',
              backgroundColor: 'white'
            }}
          >
            {aiMessages.map((message, index) => (
              <Box
                key={index}
                sx={{
                  mb: 1.5,
                  display: 'flex',
                  justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'
                }}
              >
                <Box
                  sx={{
                    p: 1,
                    maxWidth: '85%',
                    backgroundColor: message.sender === 'user'
                      ? theme.palette.primary.main
                      : theme.palette.grey[100],
                    color: message.sender === 'user' ? 'white' : theme.palette.text.primary,
                    borderRadius: 1.5,
                    fontSize: '0.8rem'
                  }}
                >
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', lineHeight: 1.4 }}>
                    {message.text}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>

          {/* Chat Input */}
          <Box sx={{ p: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Ask about this case..."
                value={aiInput}
                onChange={handleAiInputChange}
                onKeyDown={handleKeyPress}
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    fontSize: '0.8rem',
                    '& fieldset': {
                      borderColor: theme.palette.grey[300]
                    }
                  }
                }}
              />
              <IconButton
                size="small"
                color="primary"
                onClick={sendAiMessage}
                disabled={!aiInput.trim()}
                sx={{
                  bgcolor: theme.palette.primary.main,
                  color: 'white',
                  '&:hover': { bgcolor: theme.palette.primary.dark },
                  '&:disabled': { bgcolor: theme.palette.grey[300] }
                }}
              >
                <SendIcon sx={{ fontSize: '1rem' }} />
              </IconButton>
            </Box>
          </Box>
        </Paper>
      </Slide>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmationDialog.open}
        onClose={handleCancelAction}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ fontWeight: 600 }}>
          {confirmationDialog.title}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {confirmationDialog.message}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCancelAction} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmAction}
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Processing...' : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ComplianceTaskDetail;
