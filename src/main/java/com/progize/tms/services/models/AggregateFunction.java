package com.progize.tms.services.models;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents an aggregate function that can be applied to rule variables. Examples include: sum,
 * average, count, min, max, etc.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregateFunction {
  private String id;
  private String name;
  private String description;
  private List<String> applicableTypes; // The variable types this function can be applied to
}
