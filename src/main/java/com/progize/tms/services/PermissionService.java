package com.progize.tms.services;

import com.progize.tms.repository.entity.Permission;
import java.util.List;
import java.util.Optional;

public interface PermissionService {
  List<Permission> findAll();

  Optional<Permission> findById(Long id);

  Optional<Permission> findByName(String name);

  Permission save(Permission permission);

  void deleteById(Long id);

  boolean existsById(Long id);

  boolean existsByName(String name);
}
