package com.progize.tms.controllers.permissions.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePermissionRequest {

  @NotBlank(message = "Permission name is required")
  @Size(min = 3, max = 50, message = "Permission name must be between 3 and 50 characters")
  private String name;

  @NotBlank(message = "Permission description is required")
  @Size(max = 255, message = "Permission description must be less than 255 characters")
  private String description;
}
