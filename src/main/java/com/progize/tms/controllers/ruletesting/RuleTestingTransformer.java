package com.progize.tms.controllers.ruletesting;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.progize.tms.controllers.rules.req.RuleCondition;
import com.progize.tms.controllers.ruletesting.req.RuleTestReq;
import com.progize.tms.controllers.ruletesting.res.RuleTestResultRes;
import com.progize.tms.services.models.AggregateCondition;
import com.progize.tms.services.models.ComparisonCondition;
import com.progize.tms.services.models.CompositeCondition;
import com.progize.tms.services.models.Condition;
import com.progize.tms.services.models.RuleTestingBO;
import com.progize.tms.services.models.RuleTestingResultBO;
import com.progize.tms.services.models.SimpleCondition;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * Transformer for converting between rule testing DTOs and business objects. Following TMS
 * architectural principles, transformers are responsible for converting between the REST layer and
 * service layer.
 */
@Component
public class RuleTestingTransformer {

  private final ObjectMapper objectMapper;

  public RuleTestingTransformer() {
    // Initialize ObjectMapper with Java 8 date/time module
    this.objectMapper = new ObjectMapper();
    this.objectMapper.registerModule(new JavaTimeModule());
  }

  /** Convert request DTO to business object. */
  public RuleTestingBO toBusinessObject(RuleTestReq request) {
    RuleTestingBO bo = new RuleTestingBO();

    // Convert the RuleCondition (DTO) to Condition (business object)
    if (request.getRuleDefinition() != null) {
      Condition condition = convertToCondition(request.getRuleDefinition());
      bo.setRuleCondition(condition);
    }

    // Set the sample data directly from the map
    if (request.getSampleData() != null) {
      bo.setSampleData(request.getSampleData());
    }

    // Set the bucket ID for context
    bo.setBucketId(request.getBucketId());

    RuleTestingBO.TestOptions options = new RuleTestingBO.TestOptions();
    if (request.getOptions() != null) {
      options.setValidateSyntax(request.getOptions().isValidateSyntax());
      options.setValidateTypes(request.getOptions().isValidateTypes());
      options.setCheckPerformance(request.getOptions().isCheckPerformance());
      options.setIncludeDebugInfo(request.getOptions().isIncludeDebugInfo());
      options.setUseRealAggregations(request.getOptions().isUseRealAggregations());
    } else {
      // Default options
      options.setValidateSyntax(true);
      options.setValidateTypes(true);
      options.setCheckPerformance(true);
      options.setIncludeDebugInfo(true);
      options.setUseRealAggregations(false); // Default to mock aggregations for safety
    }
    bo.setOptions(options);

    return bo;
  }

  /** Convert from REST layer RuleCondition to service layer Condition */
  private Condition convertToCondition(RuleCondition ruleCondition) {
    if (ruleCondition == null) {
      return null;
    }

    String type = ruleCondition.getType().name();

    // Handle different condition types
    if ("SIMPLE".equals(type)) {
      SimpleCondition condition = new SimpleCondition();
      condition.setType("SIMPLE");
      condition.setField(ruleCondition.getField());
      condition.setOperator(ruleCondition.getOperator());
      condition.setValue(ruleCondition.getValue());
      condition.setIsExpression(ruleCondition.getIsExpression());
      return condition;

    } else if ("AGGREGATE".equals(type)) {
      AggregateCondition condition = new AggregateCondition();
      condition.setType("AGGREGATE");
      condition.setFunction(ruleCondition.getFunction());
      condition.setField(ruleCondition.getAggregateField());
      condition.setPeriod(ruleCondition.getPeriod());
      condition.setStartOffset(
          ruleCondition.getStartOffset() != null ? ruleCondition.getStartOffset() : 0);
      condition.setOperator(ruleCondition.getOperator());
      condition.setThreshold(ruleCondition.getThreshold());
      condition.setEntityField(ruleCondition.getEntityField());
      condition.setIsExpression(ruleCondition.getIsExpression());

      // Convert filter condition if present
      if (ruleCondition.getFilterCondition() != null) {
        SimpleCondition filterCondition =
            (SimpleCondition) convertToCondition(ruleCondition.getFilterCondition());
        condition.setFilterCondition(filterCondition);
      }

      return condition;

    } else if ("AND".equals(type) || "OR".equals(type) || "NOT".equals(type)) {
      CompositeCondition condition = new CompositeCondition();
      condition.setType(type);

      // Convert nested conditions
      if (ruleCondition.getConditions() != null) {
        List<Condition> conditions = new ArrayList<>();
        for (RuleCondition nestedCondition : ruleCondition.getConditions()) {
          conditions.add(convertToCondition(nestedCondition));
        }
        condition.setConditions(conditions);
      }

      return condition;
    } else if ("COMPARISON".equals(type)) {
      ComparisonCondition condition = new ComparisonCondition();
      condition.setType("COMPARISON");
      condition.setOperator(ruleCondition.getOperator());
      condition.setMultiplier(ruleCondition.getMultiplier());

      // Convert left and right conditions
      if (ruleCondition.getLeft() != null) {
        condition.setLeft(convertToCondition(ruleCondition.getLeft()));
      }

      if (ruleCondition.getRight() != null) {
        condition.setRight(convertToCondition(ruleCondition.getRight()));
      }

      return condition;
    }

    // Default fallback
    return null;
  }

  /** Convert business object to response DTO. */
  public RuleTestResultRes toResponseDTO(RuleTestingResultBO result) {
    RuleTestResultRes response = new RuleTestResultRes();
    response.setPassed(result.isPassed());
    response.setGeneratedExpression(result.getGeneratedExpression());
    response.setEvaluationTime(result.getEvaluationTime());
    response.setExplanation(result.getExplanation());

    // Map analysis
    if (result.getAnalysis() != null) {
      RuleTestResultRes.AnalysisResult analysis = new RuleTestResultRes.AnalysisResult();
      analysis.setSyntaxValid(result.getAnalysis().isSyntaxValid());

      // Map type compatibility
      if (result.getAnalysis().getTypeCompatibility() != null) {
        analysis.setTypeCompatibility(
            result.getAnalysis().getTypeCompatibility().stream()
                .map(
                    tc -> {
                      RuleTestResultRes.TypeCompatibility typeCompat =
                          new RuleTestResultRes.TypeCompatibility();
                      typeCompat.setField(tc.getField());
                      typeCompat.setDeclaredType(tc.getDeclaredType());
                      typeCompat.setOperatorId(tc.getOperatorId());
                      typeCompat.setCompatible(tc.isCompatible());
                      typeCompat.setReason(tc.getReason());
                      return typeCompat;
                    })
                .collect(Collectors.toList()));
      }

      // Map performance analysis
      if (result.getAnalysis().getPerformance() != null) {
        RuleTestResultRes.PerformanceAnalysis perfAnalysis =
            new RuleTestResultRes.PerformanceAnalysis();
        perfAnalysis.setComplexity(result.getAnalysis().getPerformance().getComplexity());
        perfAnalysis.setRecommendations(result.getAnalysis().getPerformance().getRecommendations());
        analysis.setPerformance(perfAnalysis);
      }

      response.setAnalysis(analysis);
    }

    // Map issues
    if (result.getIssues() != null) {
      response.setIssues(
          result.getIssues().stream()
              .map(
                  issue -> {
                    RuleTestResultRes.Issue issueRes = new RuleTestResultRes.Issue();
                    issueRes.setType(issue.getType());
                    issueRes.setCategory(issue.getCategory());
                    issueRes.setMessage(issue.getMessage());
                    issueRes.setLocation(issue.getLocation());
                    issueRes.setSuggestion(issue.getSuggestion());
                    return issueRes;
                  })
              .collect(Collectors.toList()));
    }

    // Map debug info if present
    if (result.getDebugInfo() != null) {
      RuleTestResultRes.DebugInfo debugInfo = new RuleTestResultRes.DebugInfo();
      debugInfo.setContext(result.getDebugInfo().getContext());

      if (result.getDebugInfo().getEvaluationTrace() != null) {
        debugInfo.setEvaluationTrace(
            result.getDebugInfo().getEvaluationTrace().stream()
                .map(
                    step -> {
                      RuleTestResultRes.EvaluationStep stepRes =
                          new RuleTestResultRes.EvaluationStep();
                      stepRes.setStep(step.getStep());
                      stepRes.setExpression(step.getExpression());
                      stepRes.setValue(step.getValue());
                      return stepRes;
                    })
                .collect(Collectors.toList()));
      }

      response.setDebugInfo(debugInfo);
    }

    return response;
  }
}
