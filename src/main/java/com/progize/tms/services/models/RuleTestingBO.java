package com.progize.tms.services.models;

import java.util.Map;
import lombok.Data;

/**
 * Business object for rule testing. Following TMS architectural principles, this object is used in
 * the service layer instead of passing DTOs directly.
 */
@Data
public class RuleTestingBO {
  private Condition ruleCondition;
  private Map<String, Object> sampleData;
  private Long bucketId;
  private TestOptions options;

  @Data
  public static class TestOptions {
    private boolean validateSyntax;
    private boolean validateTypes;
    private boolean checkPerformance;
    private boolean includeDebugInfo;
    private boolean useRealAggregations;
  }
}
