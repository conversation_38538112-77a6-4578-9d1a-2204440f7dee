package com.progize.tms.controllers.variables;

import com.progize.tms.controllers.variables.res.AggregateFunctionResponse;
import com.progize.tms.controllers.variables.res.OperatorResponse;
import com.progize.tms.controllers.variables.res.RuleVariableResponse;
import com.progize.tms.payload.ApiResponse;
import com.progize.tms.services.RuleVariableService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for managing rule variables, operators, and aggregate functions. These endpoints
 * provide the necessary metadata for building rule conditions in the UI.
 */
@RestController
@RequestMapping("/api/rule-variables")
@RequiredArgsConstructor
@CrossOrigin(origins = "*") // Enable CORS for the Rule Variables endpoints to work with the UI
public class RuleVariableController {

  private final RuleVariableService ruleVariableService;
  private final RuleVariableTransformer transformer;

  /**
   * Get all rule variables available for creating rules.
   *
   * @return List of all rule variables
   */
  @GetMapping
  public ResponseEntity<ApiResponse<List<RuleVariableResponse>>> getAllVariables() {
    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Rule variables retrieved successfully",
            transformer.toResponseList(ruleVariableService.getAllVariables())));
  }

  /**
   * Get rule variables by category (e.g., transaction, customer, account).
   *
   * @param category The category to filter by
   * @return List of rule variables in the specified category
   */
  @GetMapping("/category/{category}")
  public ResponseEntity<ApiResponse<List<RuleVariableResponse>>> getVariablesByCategory(
      @PathVariable String category) {
    List<RuleVariableResponse> variables =
        transformer.toResponseList(
            ruleVariableService.getAllVariables().stream()
                .filter(v -> v.getCategory().equals(category))
                .toList());

    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Rule variables for category '" + category + "' retrieved successfully",
            variables));
  }

  /**
   * Get a specific rule variable by ID.
   *
   * @param id The ID of the variable
   * @return The rule variable
   */
  @GetMapping("/{id}")
  public ResponseEntity<ApiResponse<RuleVariableResponse>> getVariable(@PathVariable String id) {
    RuleVariableResponse variable = transformer.toResponse(ruleVariableService.getVariable(id));
    if (variable == null) {
      return ResponseEntity.notFound().build();
    }

    return ResponseEntity.ok(
        new ApiResponse<>(true, "Rule variable retrieved successfully", variable));
  }

  /**
   * Get all operators available for rule conditions.
   *
   * @return List of all operators
   */
  @GetMapping("/operators")
  public ResponseEntity<ApiResponse<List<OperatorResponse>>> getAllOperators() {
    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Operators retrieved successfully",
            transformer.toOperatorResponseList(ruleVariableService.getAllOperators())));
  }

  /**
   * Get operators applicable to a specific variable type.
   *
   * @param type The variable type (string, number, boolean, date, etc.)
   * @return List of applicable operators
   */
  @GetMapping("/operators/type/{type}")
  public ResponseEntity<ApiResponse<List<OperatorResponse>>> getOperatorsByType(
      @PathVariable String type) {
    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Operators for type '" + type + "' retrieved successfully",
            transformer.toOperatorResponseList(ruleVariableService.getOperatorsByType(type))));
  }

  /**
   * Get all aggregate functions available for rule conditions.
   *
   * @return List of all aggregate functions
   */
  @GetMapping("/aggregate-functions")
  public ResponseEntity<ApiResponse<List<AggregateFunctionResponse>>> getAllAggregateFunctions() {
    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Aggregate functions retrieved successfully",
            transformer.toAggregateFunctionResponseList(
                ruleVariableService.getAllAggregateFunctions())));
  }

  /**
   * Get aggregate functions applicable to a specific variable type.
   *
   * @param type The variable type (string, number, boolean, date, etc.)
   * @return List of applicable aggregate functions
   */
  @GetMapping("/aggregate-functions/type/{type}")
  public ResponseEntity<ApiResponse<List<AggregateFunctionResponse>>> getAggregateFunctionsByType(
      @PathVariable String type) {
    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Aggregate functions for type '" + type + "' retrieved successfully",
            transformer.toAggregateFunctionResponseList(
                ruleVariableService.getAggregateFunctionsByType(type))));
  }

  /**
   * Get a comprehensive metadata object with all rule variables, operators, and aggregate
   * functions. This is a convenience endpoint for the UI to fetch all necessary data in one
   * request.
   *
   * @return Combined metadata for rule configuration
   */
  @GetMapping("/metadata")
  public ResponseEntity<ApiResponse<RuleMetadata>> getRuleMetadata() {
    RuleMetadata metadata =
        new RuleMetadata(
            transformer.toResponseList(ruleVariableService.getAllVariables()),
            transformer.toOperatorResponseList(ruleVariableService.getAllOperators()),
            transformer.toAggregateFunctionResponseList(
                ruleVariableService.getAllAggregateFunctions()));

    return ResponseEntity.ok(
        new ApiResponse<>(true, "Rule metadata retrieved successfully", metadata));
  }

  /** Inner class to hold all metadata for rule configuration. */
  @lombok.Data
  @lombok.AllArgsConstructor
  private static class RuleMetadata {
    private List<RuleVariableResponse> variables;
    private List<OperatorResponse> operators;
    private List<AggregateFunctionResponse> aggregateFunctions;
  }
}
