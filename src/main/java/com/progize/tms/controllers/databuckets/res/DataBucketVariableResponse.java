package com.progize.tms.controllers.databuckets.res;

import java.time.Instant;
import java.util.Map;
import lombok.Data;

/** Response DTO for data bucket variable information. */
@Data
public class DataBucketVariableResponse {
  private Long id;
  private Long dataBucketId;
  private String code;
  private String name;
  private String description;
  private String tableName;
  private String columnName;
  private String dataTypeCode;
  private String dataTypeName;
  private boolean aggregatable;
  private Boolean isUnique;
  private Boolean isPrimaryEntityKey;
  private Boolean isSecondaryEntityKey;
  private Boolean isDateDimension;
  private Map<String, Object> metadata;
  private Instant createdAt;

  // Source information for enriched variables
  private String sourceBucketName;
  private Long sourceBucketId;
  private boolean isEnriched;
}
