/**
 * Rule Variables Service
 *
 * Provides methods to interact with the rule variables API
 */
import api from './api';

/**
 * Fetches all available rule variables
 * @returns {Promise<Array>} Array of variable objects
 */
export const fetchRuleVariables = async () => {
  try {
    const activeBucketId = localStorage.getItem('activeBucketId');
    if (!activeBucketId) {
      console.log('No active bucket selected');
      return []; // No active bucket, return empty array
    }
    
    console.log('Fetching variables for active bucket:', activeBucketId);
    const response = await api.get(`/api/data-buckets/${activeBucketId}/variables/enriched`);
    return response.data?.data || [];
  } catch (error) {
    console.error('Error fetching variables:', error);
    return [];
  }
};

/**
 * Fetches a specific rule variable by ID
 * @param {string} id Variable ID
 * @returns {Promise<Object>} Variable object
 */
export const fetchVariableById = async (id) => {
  try {
    const activeBucketId = localStorage.getItem('activeBucketId');
    if (!activeBucketId) {
      throw new Error('No active bucket selected');
    }
    
    const response = await api.get(`/api/data-buckets/${activeBucketId}/variables/${id}`);
    return response.data?.data || null;
  } catch (error) {
    console.error(`Error fetching variable ${id}:`, error);
    throw error;
  }
};