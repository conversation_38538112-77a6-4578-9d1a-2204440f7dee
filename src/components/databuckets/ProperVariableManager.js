import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON>po<PERSON>,
    Button,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Switch,
    Chip,
    Alert,
    Breadcrumbs,
    Link,
    CircularProgress,
    Card,
    CardContent,
    Grid,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    FormControlLabel,
    Divider
} from '@mui/material';
import {
    ArrowBack as ArrowBackIcon,
    Add as AddIcon,
    Save as SaveIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Key as KeyIcon,
    Timeline as TimelineIcon,
    Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { dataBucketService } from '../../services/api';

const ProperVariableManager = ({ bucket, onBack }) => {
    const [variables, setVariables] = useState([]);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);
    const [editingVariable, setEditingVariable] = useState(null);
    const [configDialogOpen, setConfigDialogOpen] = useState(false);

    useEffect(() => {
        if (bucket?.id) {
            loadVariables();
        }
    }, [bucket?.id]);

    const loadVariables = async () => {
        try {
            setLoading(true);
            setError(null);
            const variablesData = await dataBucketService.getVariablesByBucketId(bucket.id);
            setVariables(Array.isArray(variablesData) ? variablesData : []);
        } catch (err) {
            console.error('Error loading variables:', err);
            setError('Failed to load variables');
            setVariables([]);
        } finally {
            setLoading(false);
        }
    };

    const handlePropertyChange = (variableId, property, value) => {
        setVariables(prev => prev.map(variable => {
            if (variable.id === variableId) {
                const updated = { ...variable, [property]: value };
                
                // Handle exclusive properties
                if (property === 'isPrimaryEntityKey' && value) {
                    // Unset primary key for all other variables
                    setVariables(prevVars => prevVars.map(v => 
                        v.id !== variableId ? { 
                            ...v, 
                            isPrimaryEntityKey: false,
                            isSecondaryEntityKey: false 
                        } : v
                    ));
                    updated.isSecondaryEntityKey = false;
                    updated.isDateDimension = false;
                } else if (property === 'isSecondaryEntityKey' && value) {
                    // Unset secondary key for all other variables
                    setVariables(prevVars => prevVars.map(v => 
                        v.id !== variableId ? { 
                            ...v, 
                            isSecondaryEntityKey: false,
                            isPrimaryEntityKey: false 
                        } : v
                    ));
                    updated.isPrimaryEntityKey = false;
                    updated.isDateDimension = false;
                } else if (property === 'isDateDimension' && value) {
                    // Unset date dimension for all other variables
                    setVariables(prevVars => prevVars.map(v => 
                        v.id !== variableId ? { ...v, isDateDimension: false } : v
                    ));
                }
                
                return updated;
            }
            return variable;
        }));
    };

    const handleSaveChanges = async () => {
        try {
            setSaving(true);
            setError(null);
            
            // Update each variable
            const updatePromises = variables.map(variable => 
                dataBucketService.updateVariable(bucket.id, variable.id, {
                    isUnique: variable.isUnique,
                    aggregatable: variable.aggregatable,
                    isPrimaryEntityKey: variable.isPrimaryEntityKey,
                    isSecondaryEntityKey: variable.isSecondaryEntityKey,
                    isDateDimension: variable.isDateDimension
                })
            );
            
            await Promise.all(updatePromises);
            setSuccess('Variables updated successfully');
            
            // Reload variables to get fresh data
            await loadVariables();
        } catch (err) {
            console.error('Error saving variables:', err);
            setError('Failed to save variable changes');
        } finally {
            setSaving(false);
        }
    };

    const handleDeleteVariable = async (variableId) => {
        try {
            await dataBucketService.deleteVariable(bucket.id, variableId);
            setVariables(prev => prev.filter(v => v.id !== variableId));
            setSuccess('Variable deleted successfully');
        } catch (err) {
            console.error('Error deleting variable:', err);
            setError('Failed to delete variable');
        }
    };

    const getEntityKeyType = (variable) => {
        if (variable.isPrimaryEntityKey) return { label: 'Primary Key', color: 'primary', icon: <KeyIcon /> };
        if (variable.isSecondaryEntityKey) return { label: 'Secondary Key', color: 'secondary', icon: <KeyIcon /> };
        if (variable.isDateDimension) return { label: 'Date Dimension', color: 'success', icon: <TimelineIcon /> };
        return null;
    };

    const openConfigDialog = (variable) => {
        setEditingVariable(variable);
        setConfigDialogOpen(true);
    };

    const closeConfigDialog = () => {
        setEditingVariable(null);
        setConfigDialogOpen(false);
    };

    const renderConfigDialog = () => (
        <Dialog
            open={configDialogOpen}
            onClose={closeConfigDialog}
            maxWidth="md"
            fullWidth
        >
            <DialogTitle>
                Configure Variable: {editingVariable?.name}
            </DialogTitle>
            <DialogContent>
                {editingVariable && (
                    <Grid container spacing={3} sx={{ mt: 1 }}>
                        <Grid item xs={12} md={6}>
                            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                                Basic Properties
                            </Typography>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={editingVariable.isUnique || false}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ ...prev, isUnique: e.target.checked }));
                                                handlePropertyChange(editingVariable.id, 'isUnique', e.target.checked);
                                            }}
                                        />
                                    }
                                    label="Unique Values"
                                />
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={editingVariable.aggregatable || false}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ ...prev, aggregatable: e.target.checked }));
                                                handlePropertyChange(editingVariable.id, 'aggregatable', e.target.checked);
                                            }}
                                            color="info"
                                        />
                                    }
                                    label="Aggregatable (SUM, AVG, COUNT)"
                                />
                            </Box>
                        </Grid>
                        
                        <Grid item xs={12} md={6}>
                            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                                Entity Key Configuration
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                                Only one variable can be assigned to each entity key type
                            </Typography>
                            
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={editingVariable.isPrimaryEntityKey || false}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ 
                                                    ...prev, 
                                                    isPrimaryEntityKey: e.target.checked,
                                                    isSecondaryEntityKey: false,
                                                    isDateDimension: false
                                                }));
                                                handlePropertyChange(editingVariable.id, 'isPrimaryEntityKey', e.target.checked);
                                            }}
                                            color="primary"
                                        />
                                    }
                                    label="Primary Entity Key"
                                />
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={editingVariable.isSecondaryEntityKey || false}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ 
                                                    ...prev, 
                                                    isSecondaryEntityKey: e.target.checked,
                                                    isPrimaryEntityKey: false,
                                                    isDateDimension: false
                                                }));
                                                handlePropertyChange(editingVariable.id, 'isSecondaryEntityKey', e.target.checked);
                                            }}
                                            color="secondary"
                                        />
                                    }
                                    label="Secondary Entity Key"
                                />
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={editingVariable.isDateDimension || false}
                                            onChange={(e) => {
                                                setEditingVariable(prev => ({ 
                                                    ...prev, 
                                                    isDateDimension: e.target.checked,
                                                    isPrimaryEntityKey: false,
                                                    isSecondaryEntityKey: false
                                                }));
                                                handlePropertyChange(editingVariable.id, 'isDateDimension', e.target.checked);
                                            }}
                                            color="success"
                                        />
                                    }
                                    label="Date Dimension"
                                />
                            </Box>
                        </Grid>
                    </Grid>
                )}
            </DialogContent>
            <DialogActions>
                <Button onClick={closeConfigDialog}>
                    Close
                </Button>
                <Button variant="contained" onClick={closeConfigDialog}>
                    Apply Changes
                </Button>
            </DialogActions>
        </Dialog>
    );

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                <CircularProgress />
                <Typography sx={{ ml: 2 }}>Loading variables...</Typography>
            </Box>
        );
    }

    return (
        <Box>
            <Breadcrumbs sx={{ mb: 3 }}>
                <Link color="inherit" href="#" onClick={onBack} sx={{ textDecoration: 'none' }}>
                    Data Buckets
                </Link>
                <Typography color="text.primary">{bucket?.name} Variables</Typography>
            </Breadcrumbs>

            <Box sx={{ mb: 4 }}>
                <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
                    {bucket?.name} Variables
                </Typography>
                <Typography variant="body1" color="text.secondary">
                    Configure variable properties and entity keys for workflow case management
                </Typography>
            </Box>

            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                    {success}
                </Alert>
            )}

            <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                    <strong>Entity Keys:</strong> Only one variable can be assigned as Primary Key, Secondary Key, or Date Dimension. 
                    These are used for workflow case management and data aggregation.
                </Typography>
            </Alert>

            {/* Summary Cards */}
            <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="primary">
                                {variables.length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Total Variables
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="primary">
                                {variables.filter(v => v.isPrimaryEntityKey).length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Primary Key
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="secondary">
                                {variables.filter(v => v.isSecondaryEntityKey).length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Secondary Key
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                    <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="h6" color="success.main">
                                {variables.filter(v => v.isDateDimension).length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Date Dimension
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            <Card>
                <CardContent sx={{ p: 0 }}>
                    <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="h6" fontWeight="600">
                                Variables ({variables.length})
                            </Typography>
                            <Button variant="contained" startIcon={<AddIcon />}>
                                Add Variables
                            </Button>
                        </Box>
                    </Box>
                    
                    <TableContainer>
                        <Table>
                            <TableHead>
                                <TableRow sx={{ bgcolor: 'grey.50' }}>
                                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }}>Data Type</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Unique</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Aggregatable</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }}>Entity Key</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {variables.map((variable) => {
                                    const entityKey = getEntityKeyType(variable);
                                    
                                    return (
                                        <TableRow key={variable.id} hover>
                                            <TableCell>
                                                <Box>
                                                    <Typography variant="subtitle2" fontWeight="medium">
                                                        {variable.name}
                                                    </Typography>
                                                    <Typography variant="caption" color="text.secondary">
                                                        {variable.description || 'No description'}
                                                    </Typography>
                                                </Box>
                                            </TableCell>
                                            
                                            <TableCell>
                                                <Chip
                                                    label={variable.dataTypeCode || 'string'}
                                                    size="small"
                                                    variant="outlined"
                                                />
                                            </TableCell>
                                            
                                            <TableCell align="center">
                                                <Switch
                                                    checked={variable.isUnique || false}
                                                    onChange={(e) => handlePropertyChange(variable.id, 'isUnique', e.target.checked)}
                                                    size="small"
                                                />
                                            </TableCell>
                                            
                                            <TableCell align="center">
                                                <Switch
                                                    checked={variable.aggregatable || false}
                                                    onChange={(e) => handlePropertyChange(variable.id, 'aggregatable', e.target.checked)}
                                                    size="small"
                                                    color="info"
                                                />
                                            </TableCell>
                                            
                                            <TableCell>
                                                {entityKey ? (
                                                    <Chip
                                                        label={entityKey.label}
                                                        color={entityKey.color}
                                                        size="small"
                                                        icon={entityKey.icon}
                                                    />
                                                ) : (
                                                    <Typography variant="caption" color="text.secondary">
                                                        None
                                                    </Typography>
                                                )}
                                            </TableCell>
                                            
                                            <TableCell align="center">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => openConfigDialog(variable)}
                                                >
                                                    <EditIcon />
                                                </IconButton>
                                                <IconButton
                                                    size="small"
                                                    color="error"
                                                    onClick={() => handleDeleteVariable(variable.id)}
                                                >
                                                    <DeleteIcon />
                                                </IconButton>
                                            </TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </CardContent>
            </Card>

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                    variant="outlined"
                    startIcon={<ArrowBackIcon />}
                    onClick={onBack}
                >
                    Back to Buckets
                </Button>
                <Button
                    variant="contained"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveChanges}
                    disabled={saving}
                >
                    {saving ? 'Saving...' : 'Save All Changes'}
                </Button>
            </Box>

            {renderConfigDialog()}
        </Box>
    );
};

export default ProperVariableManager;
