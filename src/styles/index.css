body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #1e2836;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.rule-builder-container {
  background-color: #fff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.condition-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  position: relative;
  transition: all 0.2s ease;
}

.condition-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.condition-card.simple {
  border-left: 4px solid #0959a1;
}

.condition-card.aggregate {
  border-left: 4px solid #f47d15;
}

.condition-card.composite {
  border-left: 4px solid #2e7d32;
}

.condition-actions {
  position: absolute;
  top: 12px;
  right: 12px;
}

.draggable-condition {
  cursor: move;
}

.drop-zone {
  border: 2px dashed #e0e0e0;
  border-radius: 12px;
  padding: 24px;
  margin: 12px 0;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  transition: all 0.2s ease;
}

.drop-zone.active {
  border-color: #0959a1;
  background-color: rgba(9, 89, 161, 0.05);
}

.rule-expression {
  background-color: #f5f8fa;
  padding: 20px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  overflow-x: auto;
  margin: 16px 0;
  border-left: 4px solid #0959a1;
}

.field-selector,
.operator-selector,
.aggregate-function-selector,
.period-selector,
.entity-field-selector,
.threshold-input {
  width: 100%;
  margin-bottom: 16px;
}

.rule-test-panel {
  margin-top: 24px;
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #0959a1;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.rule-result {
  margin-top: 16px;
  padding: 16px;
  border-radius: 8px;
  font-weight: 500;
  border-left: 4px solid;
}

.rule-result.pass {
  background-color: rgba(46, 125, 50, 0.05);
  color: #2e7d32;
  border-color: #2e7d32;
}

.rule-result.fail {
  background-color: rgba(211, 47, 47, 0.05);
  color: #d32f2f;
  border-color: #d32f2f;
}
