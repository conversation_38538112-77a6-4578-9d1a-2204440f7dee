import React, { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { 
  AppBar, 
  Toolbar, 
  Button, 
  Box, 
  IconButton,
  useTheme,
  useMediaQuery,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Avatar
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import LogoutIcon from '@mui/icons-material/Logout';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import logo from '../assets/logo.svg';
import { useSpring, animated } from 'react-spring';
import { useThemeContext } from '../contexts/ThemeContext';
import AuthService from '../services/AuthService';

const Header = ({ onLogout }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { isDarkMode, toggleTheme } = useThemeContext();
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const user = AuthService.getCurrentUser();
  
  const logoAnimation = useSpring({
    from: { opacity: 0, transform: 'translateY(-20px)' },
    to: { opacity: 1, transform: 'translateY(0)' },
    config: { tension: 100, friction: 10 },
  });
  
  const buttonAnimation = useSpring({
    from: { opacity: 0, transform: 'scale(0.8)' },
    to: { opacity: 1, transform: 'scale(1)' },
    delay: 150,
    config: { tension: 200, friction: 12 },
  });

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleMenuClose();
    AuthService.logout()
      .then(() => {
        // Notify parent component about logout
        if (onLogout) {
          onLogout();
        }
        navigate('/login');
      })
      .catch(error => {
        console.error('Logout error:', error);
        // Force logout even if API call fails
        localStorage.removeItem('user');
        // Notify parent component about logout
        if (onLogout) {
          onLogout();
        }
        navigate('/login');
      });
  };

  return (
    <AppBar 
      position="static" 
      elevation={0}
      sx={{ 
        background: theme.palette.mode === 'dark' 
          ? 'linear-gradient(90deg, rgba(22, 40, 54, 0.95) 0%, rgba(13, 25, 33, 0.9) 100%)'
          : 'linear-gradient(90deg, rgba(12, 78, 122, 0.85) 0%, rgba(12, 78, 122, 0.7) 50%, rgba(25, 118, 210, 0.75) 100%)',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        borderBottom: theme.palette.mode === 'dark'
          ? '1px solid rgba(255, 255, 255, 0.05)'
          : '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: 0,
      }}
    >
      <Toolbar disableGutters sx={{ py: 0.5, px: 2 }}>
        <animated.div style={logoAnimation}>
          <Box 
            component="img"
            sx={{ 
              height: 28,
              display: { xs: 'block', sm: 'block' },
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
            }}
            alt="Progize Logo"
            src={logo}
          />
        </animated.div>
        
        <Box sx={{ flexGrow: 1 }} />
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <animated.div style={buttonAnimation}>
            <Button 
              component={RouterLink} 
              to="/rules/new" 
              variant="contained" 
              color="secondary" 
              startIcon={!isMobile ? <AddIcon fontSize="small" /> : null}
              sx={{ 
                py: 0.75, 
                px: isMobile ? 1.5 : 2,
                color: theme.palette.mode === 'dark' ? '#0d1921' : '#0c4e7a',
                fontWeight: 600,
                fontSize: '0.875rem',
                boxShadow: theme.palette.mode === 'dark'
                  ? '0 4px 10px rgba(105, 240, 174, 0.3)'
                  : '0 4px 10px rgba(123, 220, 181, 0.4)',
                transition: 'all 0.2s ease',
                mr: 2,
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 6px 12px rgba(105, 240, 174, 0.4)'
                    : '0 6px 12px rgba(123, 220, 181, 0.5)',
                }
              }}
            >
              {isMobile ? <AddIcon fontSize="small" /> : 'New Rule'}
            </Button>
          </animated.div>
          
          <Tooltip title={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}>
            <IconButton 
              onClick={toggleTheme}
              color="inherit"
              size="small"
              sx={{ 
                color: 'white', 
                mx: 1,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
            >
              {isDarkMode ? <Brightness7Icon fontSize="small" /> : <Brightness4Icon fontSize="small" />}
            </IconButton>
          </Tooltip>
          
          <IconButton 
            size="small" 
            sx={{ 
              color: 'white', 
              mx: 1,
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)'
              }
            }}
          >
            <NotificationsIcon fontSize="small" />
          </IconButton>
          
          <Tooltip title="Account">
            <IconButton 
              size="small" 
              onClick={handleProfileMenuOpen}
              aria-controls={open ? 'profile-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              sx={{ 
                color: 'white', 
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
            >
              {user?.firstName ? (
                <Avatar 
                  sx={{ 
                    width: 24, 
                    height: 24, 
                    fontSize: '0.875rem',
                    bgcolor: theme.palette.primary.dark 
                  }}
                >
                  {user.firstName.charAt(0)}{user.lastName?.charAt(0)}
                </Avatar>
              ) : (
                <AccountCircleIcon fontSize="small" />
              )}
            </IconButton>
          </Tooltip>
          
          <Menu
            id="profile-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            MenuListProps={{
              'aria-labelledby': 'profile-button',
            }}
            PaperProps={{
              elevation: 3,
              sx: {
                mt: 1.5,
                minWidth: 180,
                borderRadius: 2,
                overflow: 'visible',
                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.15))',
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              },
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            {user && (
              <Box sx={{ px: 2, py: 1.5 }}>
                <Box sx={{ fontWeight: 'bold' }}>{user.firstName} {user.lastName}</Box>
                <Box sx={{ fontSize: '0.8rem', color: 'text.secondary' }}>{user.username}</Box>
              </Box>
            )}
            <Divider />
            <MenuItem onClick={handleMenuClose}>
              <ListItemIcon>
                <PersonIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Profile</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleMenuClose}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Settings</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Logout</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
