package com.progize.tms.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.WorkflowCaseRepository;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.repository.entity.WorkflowType;
import com.progize.tms.service.WorkflowCaseService;
import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowCaseServiceImpl implements WorkflowCaseService {

  private final RuntimeService runtimeService;
  private final TaskService taskService;
  private final WorkflowCaseRepository caseRepository;
  private final ObjectMapper objectMapper;

  @Override
  @Transactional
  public WorkflowCase startCase(
      WorkflowType workflowType, String entityType, Long entityId, Map<String, Object> variables) {

    // Extract additional fields from variables if present
    Long customerId = null;
    Long ruleId = null;

    if (variables.containsKey("customerId")) {
      Object customerIdObj = variables.get("customerId");
      if (customerIdObj != null) {
        if (customerIdObj instanceof Long) {
          customerId = (Long) customerIdObj;
        } else if (customerIdObj instanceof String) {
          try {
            customerId = Long.parseLong((String) customerIdObj);
          } catch (NumberFormatException e) {
            log.warn("Invalid customerId format: {}", customerIdObj);
          }
        } else if (customerIdObj instanceof Number) {
          customerId = ((Number) customerIdObj).longValue();
        }
      }
    }

    if (variables.containsKey("ruleId")) {
      Object ruleIdObj = variables.get("ruleId");
      if (ruleIdObj != null) {
        if (ruleIdObj instanceof Long) {
          ruleId = (Long) ruleIdObj;
        } else if (ruleIdObj instanceof String) {
          try {
            ruleId = Long.parseLong((String) ruleIdObj);
          } catch (NumberFormatException e) {
            log.warn("Invalid ruleId format: {}", ruleIdObj);
          }
        } else if (ruleIdObj instanceof Number) {
          ruleId = ((Number) ruleIdObj).longValue();
        }
      }
    }

    // Create case
    WorkflowCase workflowCase =
        WorkflowCase.builder()
            .caseNumber(generateCaseNumber())
            .workflowType(workflowType)
            .status("CREATED")
            .entityType(entityType)
            .entityId(entityId)
            .customerId(customerId)
            .ruleId(ruleId)
            .build();

    workflowCase = caseRepository.save(workflowCase);

    // Add case ID to process variables
    variables.put("caseId", workflowCase.getId());

    // Start Flowable process
    String processInstanceId =
        runtimeService
            .startProcessInstanceByKey(
                workflowType.getProcessDefinitionKey(), workflowCase.getId().toString(), variables)
            .getProcessInstanceId();

    // Update case with process instance ID
    workflowCase.setProcessInstanceId(processInstanceId);
    return caseRepository.save(workflowCase);
  }

  @Override
  @Transactional
  public void completeTask(Long caseId, String taskId, Map<String, Object> variables) {
    WorkflowCase workflowCase = getCase(caseId);

    // Verify task belongs to this case
    Task task =
        taskService
            .createTaskQuery()
            .taskId(taskId)
            .processInstanceId(workflowCase.getProcessInstanceId())
            .singleResult();

    if (task == null) {
      throw new IllegalArgumentException("Task not found or not associated with case");
    }

    // Complete task
    taskService.complete(taskId, variables);

    // Update case status based on current activity
    updateCaseStatus(workflowCase);
  }

  @Override
  public List<Task> getCaseTasks(Long caseId) {
    WorkflowCase workflowCase = getCase(caseId);

    return taskService
        .createTaskQuery()
        .processInstanceId(workflowCase.getProcessInstanceId())
        .orderByTaskCreateTime()
        .asc()
        .list();
  }

  @Override
  public WorkflowCase getCase(Long caseId) {
    return caseRepository
        .findById(caseId)
        .orElseThrow(() -> new EntityNotFoundException("Case not found: " + caseId));
  }

  @Override
  public WorkflowCase getCaseByProcessInstanceId(String processInstanceId) {
    return caseRepository
        .findByProcessInstanceId(processInstanceId)
        .orElseThrow(
            () -> new EntityNotFoundException("Case not found for process: " + processInstanceId));
  }

  @Override
  @Transactional
  public WorkflowCase updateCaseMetadata(Long caseId, Map<String, Object> metadata)
      throws JsonProcessingException {
    WorkflowCase workflowCase = getCase(caseId);
    workflowCase.setMetadata(objectMapper.writeValueAsString(metadata));
    return caseRepository.save(workflowCase);
  }

  @Override
  public List<WorkflowCase> getCasesByEntity(String entityType, Long entityId) {
    return caseRepository.findByEntityTypeAndEntityId(entityType, entityId);
  }

  @Override
  public List<WorkflowCase> getCasesByTypeAndStatus(WorkflowType workflowType, String status) {
    return caseRepository.findByStatusAndWorkflowType(status, workflowType);
  }

  private String generateCaseNumber() {
    return UUID.randomUUID().toString().substring(0, 8).toUpperCase();
  }

  private void updateCaseStatus(WorkflowCase workflowCase) {
    // Get current activity from Flowable
    String currentActivity =
        runtimeService
            .createExecutionQuery()
            .processInstanceId(workflowCase.getProcessInstanceId())
            .activityId(null)
            .singleResult()
            .getActivityId();

    // Map activity to status (implement your mapping logic)
    String newStatus = mapActivityToStatus(currentActivity);
    workflowCase.setStatus(newStatus);

    caseRepository.save(workflowCase);
  }

  private String mapActivityToStatus(String activityId) {
    // Implement your activity to status mapping
    // This could be configuration-driven
    return "IN_PROGRESS";
  }
}
