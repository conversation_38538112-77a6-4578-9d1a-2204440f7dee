import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON>po<PERSON>,
    Button,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Chip,
    IconButton,
    TextField,
    Grid,
    Card,
    CardContent,
    Divider,
    Stack,
    Alert,
    <PERSON>readcrumbs,
    Link,
    useTheme
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as ViewIcon,
    Storage as StorageIcon,
    DataObject as DataObjectIcon,
    Search as SearchIcon,
    FilterList as FilterIcon,
    Refresh as RefreshIcon
} from '@mui/icons-material';
import CleanVariableManager from './CleanVariableManager';

const CleanDataBucketManager = () => {
    const theme = useTheme();
    const [buckets, setBuckets] = useState([]);
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [view, setView] = useState('list'); // 'list', 'create', 'edit', 'variables'
    const [searchTerm, setSearchTerm] = useState('');

    // Mock data - replace with real API calls
    const mockBuckets = [
        {
            id: 1,
            name: 'Customer Transactions',
            description: 'High-value customer transaction monitoring',
            type: 'RULE_BUCKET',
            dataSource: 'PostgreSQL Main',
            tableName: 'customer_transactions',
            variableCount: 12,
            status: 'Active',
            lastUpdated: '2024-01-15'
        },
        {
            id: 2,
            name: 'Employee Directory',
            description: 'Employee information lookup table',
            type: 'LOOKUP',
            dataSource: 'MySQL HR',
            tableName: 'employees',
            variableCount: 8,
            status: 'Active',
            lastUpdated: '2024-01-10'
        },
        {
            id: 3,
            name: 'Product Catalog',
            description: 'Product information and pricing',
            type: 'LOOKUP',
            dataSource: 'Oracle Finance',
            tableName: 'products',
            variableCount: 15,
            status: 'Inactive',
            lastUpdated: '2024-01-05'
        }
    ];

    useEffect(() => {
        setBuckets(mockBuckets);
    }, []);

    const getStatusColor = (status) => {
        return status === 'Active' ? 'success' : 'default';
    };

    const getTypeColor = (type) => {
        return type === 'RULE_BUCKET' ? 'primary' : 'secondary';
    };

    const filteredBuckets = buckets.filter(bucket =>
        bucket.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        bucket.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const renderBreadcrumbs = () => (
        <Breadcrumbs sx={{ mb: 3 }}>
            <Link 
                color="inherit" 
                href="#" 
                onClick={() => setView('list')}
                sx={{ textDecoration: 'none' }}
            >
                Data Buckets
            </Link>
            {view === 'create' && <Typography color="text.primary">Create New</Typography>}
            {view === 'edit' && selectedBucket && <Typography color="text.primary">Edit {selectedBucket.name}</Typography>}
            {view === 'variables' && selectedBucket && <Typography color="text.primary">{selectedBucket.name} Variables</Typography>}
        </Breadcrumbs>
    );

    const renderHeader = () => (
        <Box sx={{ mb: 4 }}>
            <Typography variant="h4" sx={{ mb: 1, fontWeight: 600 }}>
                Data Bucket Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
                Manage your data buckets and configure variables for monitoring rules
            </Typography>
        </Box>
    );

    const renderStats = () => (
        <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
                <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                        <StorageIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                        <Typography variant="h4" fontWeight="bold">
                            {buckets.length}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Total Buckets
                        </Typography>
                    </CardContent>
                </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                        <DataObjectIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                        <Typography variant="h4" fontWeight="bold">
                            {buckets.reduce((sum, bucket) => sum + bucket.variableCount, 0)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Total Variables
                        </Typography>
                    </CardContent>
                </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" fontWeight="bold" color="success.main">
                            {buckets.filter(b => b.status === 'Active').length}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Active Buckets
                        </Typography>
                    </CardContent>
                </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" fontWeight="bold" color="info.main">
                            {new Set(buckets.map(b => b.dataSource)).size}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Data Sources
                        </Typography>
                    </CardContent>
                </Card>
            </Grid>
        </Grid>
    );

    const renderToolbar = () => (
        <Box sx={{ mb: 3 }}>
            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                <Stack direction="row" spacing={2} alignItems="center" sx={{ flexGrow: 1 }}>
                    <TextField
                        placeholder="Search buckets..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        size="small"
                        sx={{ minWidth: 300 }}
                        InputProps={{
                            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        }}
                    />
                    <IconButton>
                        <FilterIcon />
                    </IconButton>
                    <IconButton>
                        <RefreshIcon />
                    </IconButton>
                </Stack>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setView('create')}
                    size="large"
                >
                    Create Bucket
                </Button>
            </Stack>
        </Box>
    );

    const renderBucketsList = () => (
        <TableContainer component={Paper} sx={{ boxShadow: 1 }}>
            <Table>
                <TableHead>
                    <TableRow sx={{ bgcolor: 'grey.50' }}>
                        <TableCell sx={{ fontWeight: 'bold' }}>Name</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Type</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Data Source</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Variables</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Last Updated</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }} align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {filteredBuckets.map((bucket) => (
                        <TableRow key={bucket.id} hover>
                            <TableCell>
                                <Box>
                                    <Typography variant="subtitle2" fontWeight="medium">
                                        {bucket.name}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {bucket.description}
                                    </Typography>
                                </Box>
                            </TableCell>
                            <TableCell>
                                <Chip
                                    label={bucket.type.replace('_', ' ')}
                                    color={getTypeColor(bucket.type)}
                                    size="small"
                                />
                            </TableCell>
                            <TableCell>
                                <Typography variant="body2">
                                    {bucket.dataSource}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    {bucket.tableName}
                                </Typography>
                            </TableCell>
                            <TableCell>
                                <Typography variant="body2" fontWeight="medium">
                                    {bucket.variableCount}
                                </Typography>
                            </TableCell>
                            <TableCell>
                                <Chip
                                    label={bucket.status}
                                    color={getStatusColor(bucket.status)}
                                    size="small"
                                />
                            </TableCell>
                            <TableCell>
                                <Typography variant="body2">
                                    {bucket.lastUpdated}
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Stack direction="row" spacing={1} justifyContent="center">
                                    <IconButton
                                        size="small"
                                        onClick={() => {
                                            setSelectedBucket(bucket);
                                            setView('variables');
                                        }}
                                    >
                                        <ViewIcon />
                                    </IconButton>
                                    <IconButton
                                        size="small"
                                        onClick={() => {
                                            setSelectedBucket(bucket);
                                            setView('edit');
                                        }}
                                    >
                                        <EditIcon />
                                    </IconButton>
                                    <IconButton size="small" color="error">
                                        <DeleteIcon />
                                    </IconButton>
                                </Stack>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );

    const renderCreateForm = () => (
        <Card>
            <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
                    Create New Data Bucket
                </Typography>
                
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Bucket Name"
                            placeholder="e.g., Customer Transactions"
                            sx={{ mb: 3 }}
                        />
                        <TextField
                            fullWidth
                            label="Description"
                            placeholder="Brief description of the bucket purpose"
                            multiline
                            rows={3}
                            sx={{ mb: 3 }}
                        />
                        <TextField
                            fullWidth
                            select
                            label="Bucket Type"
                            sx={{ mb: 3 }}
                            SelectProps={{ native: true }}
                        >
                            <option value="">Select type...</option>
                            <option value="RULE_BUCKET">Rule Bucket</option>
                            <option value="LOOKUP">Lookup Bucket</option>
                        </TextField>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            select
                            label="Data Source"
                            sx={{ mb: 3 }}
                            SelectProps={{ native: true }}
                        >
                            <option value="">Select data source...</option>
                            <option value="postgres">PostgreSQL Main</option>
                            <option value="mysql">MySQL HR</option>
                            <option value="oracle">Oracle Finance</option>
                        </TextField>
                        <TextField
                            fullWidth
                            label="Table Name"
                            placeholder="e.g., customer_transactions"
                            sx={{ mb: 3 }}
                        />
                        
                        <Alert severity="info" sx={{ mb: 3 }}>
                            After creating the bucket, you'll be able to select and configure variables from your chosen table.
                        </Alert>
                    </Grid>
                </Grid>
                
                <Divider sx={{ my: 3 }} />
                
                <Stack direction="row" spacing={2} justifyContent="flex-end">
                    <Button
                        variant="outlined"
                        onClick={() => setView('list')}
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                    >
                        Create Bucket
                    </Button>
                </Stack>
            </CardContent>
        </Card>
    );

    const renderVariablesView = () => (
        <CleanVariableManager
            bucket={selectedBucket}
            onBack={() => setView('list')}
            onSave={() => {
                // Handle save logic here
                console.log('Saving variables...');
                setView('list');
            }}
        />
    );

    return (
        <Box sx={{ p: 3, maxWidth: '100%', mx: 'auto' }}>
            {renderHeader()}
            {renderBreadcrumbs()}
            
            {view === 'list' && (
                <>
                    {renderStats()}
                    {renderToolbar()}
                    {renderBucketsList()}
                </>
            )}
            
            {view === 'create' && renderCreateForm()}
            {view === 'variables' && renderVariablesView()}
            {view === 'edit' && (
                <Typography variant="h6">Edit form will be implemented here</Typography>
            )}
        </Box>
    );
};

export default CleanDataBucketManager;
