package com.progize.tms.repository;

import com.progize.tms.repository.entity.BucketRelationshipEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BucketRelationshipRepository
    extends JpaRepository<BucketRelationshipEntity, Long> {
  List<BucketRelationshipEntity> findByRuleBucketId(Long ruleBucketId);

  Optional<BucketRelationshipEntity> findByRuleBucketIdAndLookupBucketId(
      Long ruleBucketId, Long lookupBucketId);
}
