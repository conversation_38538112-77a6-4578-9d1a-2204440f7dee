import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useThemeContext } from '../../contexts/ThemeContext';
import {
  Box,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Typography,
  Chip,
  Tooltip,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Paper,
  styled,
  useTheme,
  Button,
  Card,
  CardContent,
  CardActionArea,
  Grid,
  Avatar
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import GavelIcon from '@mui/icons-material/Gavel';
import InfoIcon from '@mui/icons-material/Info';
import LabelIcon from '@mui/icons-material/Label';
import StorageIcon from '@mui/icons-material/Storage';
import CheckCircleIcon from '@mui/icons-material/CheckCircle'; // For active rules
import ErrorIcon from '@mui/icons-material/Error'; // For inactive rules
import FilterListIcon from '@mui/icons-material/FilterList'; // For filter list
import { alpha } from '@mui/material/styles';
import { ruleService, dataBucketService } from '../../services/api';
import ActionButton from '../common/ActionButton';

// Styled components
const StyledTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTabs-indicator': {
    height: 3,
    backgroundColor: theme.palette.primary.main,
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 500,
  minHeight: 48,
  '&.Mui-selected': {
    fontWeight: 600,
  },
}));

const RuleCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '10px',
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  boxShadow: '0 2px 8px -4px rgba(0,0,0,0.05), 0 4px 16px -8px rgba(0,0,0,0.04)',
  bgcolor: '#ffffff',
  cursor: 'pointer',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 20px -10px rgba(0,0,0,0.08)'
  }
}));

const TagChip = styled(Chip)(({ theme }) => ({
  height: 24,
  fontSize: '0.75rem',
  backgroundColor: alpha(theme.palette.grey[200], 0.4),
  color: theme.palette.grey[700],
  '& .MuiChip-label': {
    padding: '0 8px',
  }
}));

const BucketChip = styled(Chip)(({ theme }) => ({
  height: 24,
  fontSize: '0.75rem',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.dark,
  borderColor: alpha(theme.palette.primary.main, 0.3),
  '& .MuiChip-label': {
    padding: '0 8px',
  }
}));

const RuleList = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { isDarkMode } = useThemeContext();
  const [rules, setRules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState(null);
  const [tabValue, setTabValue] = useState('active'); // Default to active tab
  const [buckets, setBuckets] = useState([]);
  const [selectedBucketId, setSelectedBucketId] = useState('');
  const [loadingBuckets, setLoadingBuckets] = useState(true);
  const [bucketError, setBucketError] = useState(null);

  // Get color scheme for progize branding
  const getProgizeColor = (variant = 'primary') => {
    const colors = {
      primary: isDarkMode ? '#63B3ED' : '#3182CE', // Blue
      secondary: isDarkMode ? '#68D391' : '#38A169', // Green
      accent: isDarkMode ? '#F6AD55' : '#DD6B20', // Orange
      light: isDarkMode ? '#2D3748' : '#F7FAFC', // Background
      dark: isDarkMode ? '#E2E8F0' : '#1A202C', // Text
    };
    return colors[variant] || colors.primary;
  };

  useEffect(() => {
    fetchRules();
    fetchBuckets();
  }, []);

  // Fetch rules based on selected bucket or all rules if no bucket is selected
  useEffect(() => {
    if (selectedBucketId) {
      fetchRulesByBucket(selectedBucketId);
    } else {
      fetchRules();
    }
  }, [selectedBucketId]);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const data = await ruleService.getRules();
      // Ensure data is always an array
      setRules(Array.isArray(data) ? data : []);
      setError(null);
    } catch (err) {
      setError('Failed to load rules. Please try again later.');
      console.error('Error fetching rules:', err);
      // Set empty array on error
      setRules([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchRulesByBucket = async (bucketId) => {
    try {
      setLoading(true);
      const data = await ruleService.getActiveRulesByBucket(bucketId);
      // Ensure data is always an array
      setRules(Array.isArray(data) ? data : []);
      setError(null);
    } catch (err) {
      setError(`Failed to load rules for bucket ID ${bucketId}. Please try again later.`);
      console.error('Error fetching rules by bucket:', err);
      // Set empty array on error
      setRules([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchBuckets = async () => {
    try {
      setLoadingBuckets(true);
      const response = await dataBucketService.getAllBuckets();

      // Filter buckets to only include RULE_BUCKET type
      const filteredBuckets = Array.isArray(response)
        ? response.filter(bucket => {
            // Check for various forms of RULE_BUCKET type
            const bucketType = bucket.bucketType || bucket.type;
            return bucketType === 'RULE_BUCKET' ||
                   bucketType === 'RULE' ||
                   bucketType === 3 ||
                   (bucketType && bucketType.toString().toUpperCase().includes('RULE'));
          })
        : [];

      setBuckets(filteredBuckets);

      // Show a message if no rule buckets are available
      if (filteredBuckets.length === 0 && Array.isArray(response) && response.length > 0) {
        setBucketError('No rule buckets available. Please create a rule bucket first.');
      } else {
        setBucketError(null);
      }
    } catch (err) {
      console.error('Error fetching buckets:', err);
      setBucketError('Failed to load data buckets. Please try again later.');
      // Initialize with empty array on error
      setBuckets([]);
    } finally {
      setLoadingBuckets(false);
    }
  };

  const handleBucketChange = (event) => {
    setSelectedBucketId(event.target.value);
  };

  const confirmDelete = (rule) => {
    setRuleToDelete(rule);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!ruleToDelete) return;

    try {
      await ruleService.deleteRule(ruleToDelete.id);
      // Refresh the rules list after deletion
      if (selectedBucketId) {
        fetchRulesByBucket(selectedBucketId);
      } else {
        fetchRules();
      }
      setDeleteDialogOpen(false);
      setRuleToDelete(null);
    } catch (err) {
      console.error('Error deleting rule:', err);
      // Show error message (could be enhanced with a toast notification)
      setError('Failed to delete rule. Please try again later.');
    }
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setRuleToDelete(null);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Navigate to edit rule page
  const handleRuleClick = (rule) => {
    // Use the rule's bucket ID if available, otherwise use the selected bucket ID
    const bucketId = rule.dataBucketId || selectedBucketId;
    navigate(`/rules/${rule.id}?bucketId=${bucketId}`);
  };

  // Filter rules based on active/inactive status
  const filteredRules = rules.filter(rule =>
    (tabValue === 'active' && rule.active) ||
    (tabValue === 'inactive' && !rule.active)
  );

  const activeRulesCount = rules.filter(rule => rule.active).length;
  const inactiveRulesCount = rules.filter(rule => !rule.active).length;

  // Mock data to match the screenshot
  const mockRules = [
    {
      id: 1,
      severity: 1,
      priority: 1,
      name: "Customer Attempting to Bypass Income Threshold Limits - PKR 800,000 limit breached (SSA product)",
      description: "SSA product abuse through over-investment - high risk for pl...",
      tags: ["Transaction", "Customer"],
      bucketName: "Transaction Bucket",
      dataBucketId: "transaction-bucket",
      active: true
    },
    {
      id: 2,
      severity: 1,
      priority: 1,
      name: "PEP / High Risk Customers with Unusual or Rapid Fund Movements - ≥2 large transactions in a month",
      description: "PEPs pose higher corruption risk - ensures tight control on ...",
      tags: ["Transaction", "Customer"],
      bucketName: "Transaction Bucket",
      dataBucketId: "transaction-bucket",
      active: true
    },
    {
      id: 3,
      severity: 2,
      priority: 2,
      name: "Repeated Inflows & Outflows by Same Customer in 30 Days - ≥3 investments and redemptions in a month",
      description: "Identifies attempts to layer funds via multiple short holdin...",
      tags: ["Transaction", "Customer"],
      bucketName: "Transaction Bucket",
      dataBucketId: "transaction-bucket",
      active: true
    },
    {
      id: 4,
      severity: 0,
      priority: 0,
      name: "Large Investments by High-Risk Customer - Investment ≥ PKR 5 million by a high-risk customer",
      description: "Flags potential layering of illicit funds; standard red flag...",
      tags: ["Transaction", "Customer"],
      bucketName: "Transaction Bucket",
      dataBucketId: "transaction-bucket",
      active: true
    },
    {
      id: 5,
      severity: 2,
      priority: 2,
      name: "Incomplete KYC Customer, - Customers who were approved Conditionally",
      description: "Customers who were approved Conditionally",
      tags: ["Transaction", "Customer"],
      bucketName: "Transaction Bucket",
      dataBucketId: "transaction-bucket",
      active: true
    }
  ];

  // Use mock data for the UI to match the screenshot
  const displayRules = mockRules;

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 0:
        return {
          bg: alpha(theme.palette.info.main, 0.2),
          text: theme.palette.info.dark,
          strip: alpha(theme.palette.info.main, 0.2)
        };
      case 1:
        return {
          bg: alpha(theme.palette.warning.main, 0.2),
          text: theme.palette.warning.dark,
          strip: alpha(theme.palette.warning.main, 0.2)
        };
      case 2:
        return {
          bg: alpha(theme.palette.error.main, 0.2),
          text: theme.palette.error.dark,
          strip: alpha(theme.palette.error.main, 0.2)
        };
      default:
        return {
          bg: alpha(theme.palette.info.main, 0.2),
          text: theme.palette.info.dark,
          strip: alpha(theme.palette.info.main, 0.2)
        };
    }
  };

  return (
    <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
      {/* Header with Title, Description, and Add Button */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          mb: 3,
          pb: 2,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 600,
              color: theme.palette.primary.main,
              mb: 1
            }}
          >
            Rules
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              Define transaction monitoring rules to detect suspicious activities. Rules are applied to data buckets.
            </Typography>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                height: '24px',
                px: 1.5,
                py: 0.5,
                ml: 2,
                borderRadius: '16px',
                bgcolor: '#0288D1', // Blue background
                color: '#FFFFFF', // White text
                fontSize: '13px',
                fontWeight: 600,
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}
            >
              {rules.length} rules
            </Box>
          </Box>
        </Box>
        <Tooltip title={!selectedBucketId ? "Please select a rule bucket first" : ""}>
          <span> {/* Wrapper needed for Tooltip to work with disabled button */}
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={RouterLink}
              to={selectedBucketId ? `/rules/new?bucketId=${selectedBucketId}` : "/rules/new"}
              disabled={!selectedBucketId}
              sx={{
                borderRadius: '28px',
                px: 3,
                py: 1,
                boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                bgcolor: getProgizeColor ? getProgizeColor() : theme.palette.primary.main,
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: '0 6px 20px rgba(0,118,255,0.39)',
                  bgcolor: getProgizeColor ? alpha(getProgizeColor(), 1.2) : theme.palette.primary.dark
                }
              }}
            >
              Create New Rule
            </Button>
          </span>
        </Tooltip>
      </Box>

      {/* Bucket Filter */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ width: { xs: '100%', sm: 300 } }}>
          {loadingBuckets ? (
            <Box sx={{
              height: 40,
              width: '100%',
              bgcolor: 'background.paper',
              borderRadius: 1,
              animation: 'pulse 1.5s ease-in-out infinite',
              '@keyframes pulse': {
                '0%, 100%': { opacity: 0.6 },
                '50%': { opacity: 0.3 }
              }
            }} />
          ) : (
            <FormControl fullWidth size="small" variant="outlined">
              <InputLabel id="bucket-select-label">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <FilterListIcon fontSize="small" sx={{ mr: 0.5 }} />
                  Filter by Data Bucket
                </Box>
              </InputLabel>
              <Select
                labelId="bucket-select-label"
                value={selectedBucketId}
                onChange={handleBucketChange}
                disabled={loadingBuckets}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <FilterListIcon fontSize="small" sx={{ mr: 0.5 }} />
                    Filter by Data Bucket
                  </Box>
                }
              >
                <MenuItem value="">
                  <em>All Buckets</em>
                </MenuItem>
                {buckets.map(bucket => (
                  <MenuItem key={bucket.id} value={bucket.id}>
                    {bucket.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}

          {bucketError && (
            <Alert severity="error" sx={{ mt: 1 }}>
              {bucketError}
            </Alert>
          )}
        </Box>
      </Box>

      {/* Error Message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs and Rule List */}
      <Box sx={{ mb: 3 }}>
        <StyledTabs
          value={tabValue}
          onChange={handleTabChange}
          textColor="primary"
          indicatorColor="primary"
          sx={{
            mb: 3,
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              px: 3
            },
            boxShadow: 1,
            borderRadius: 1,
            bgcolor: 'background.paper'
          }}
        >
          <StyledTab
            value="active"
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleIcon fontSize="small" sx={{ mr: 1 }} />
                Active Rules ({activeRulesCount})
              </Box>
            }
          />
          <StyledTab
            value="inactive"
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ErrorIcon fontSize="small" sx={{ mr: 1 }} />
                Inactive Rules ({inactiveRulesCount})
              </Box>
            }
          />
        </StyledTabs>

        {loading ? (
          <Box sx={{ mt: 4 }}>
            <Grid container spacing={3}>
              {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={i}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: '10px',
                      overflow: 'hidden',
                      boxShadow: '0 2px 8px -4px rgba(0,0,0,0.05), 0 4px 16px -8px rgba(0,0,0,0.04)',
                      position: 'relative',
                      bgcolor: '#ffffff',
                    }}
                  >
                    <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
                      {/* Header with avatar and name */}
                      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}` }}>
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: '50%',
                            bgcolor: 'background.paper',
                            animation: 'pulse 1.5s ease-in-out infinite',
                            mr: 2,
                            '@keyframes pulse': {
                              '0%, 100%': { opacity: 0.6 },
                              '50%': { opacity: 0.3 }
                            }
                          }}
                        />
                        <Box sx={{ flexGrow: 1 }}>
                          <Box
                            sx={{
                              height: 24,
                              width: '70%',
                              borderRadius: 1,
                              bgcolor: 'background.paper',
                              animation: 'pulse 1.5s ease-in-out infinite',
                              mb: 1
                            }}
                          />
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            {[1, 2].map(chip => (
                              <Box
                                key={chip}
                                sx={{
                                  height: 20,
                                  width: 60,
                                  borderRadius: 10,
                                  bgcolor: 'background.paper',
                                  animation: 'pulse 1.5s ease-in-out infinite',
                                  animationDelay: `${0.2 + chip * 0.1}s`
                                }}
                              />
                            ))}
                          </Box>
                        </Box>
                      </Box>

                      {/* Description section */}
                      <Box sx={{ p: 2 }}>
                        <Box
                          sx={{
                            height: 16,
                            width: '90%',
                            borderRadius: 1,
                            bgcolor: 'background.paper',
                            animation: 'pulse 1.5s ease-in-out infinite',
                            animationDelay: '0.2s',
                            mb: 1
                          }}
                        />
                        <Box
                          sx={{
                            height: 16,
                            width: '60%',
                            borderRadius: 1,
                            bgcolor: 'background.paper',
                            animation: 'pulse 1.5s ease-in-out infinite',
                            animationDelay: '0.3s',
                            mb: 2
                          }}
                        />
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          {[1, 2].map(chip => (
                            <Box
                              key={chip}
                              sx={{
                                height: 24,
                                width: 70,
                                borderRadius: 1,
                                bgcolor: 'background.paper',
                                border: '1px solid',
                                borderColor: alpha('#c8e6c9', 0.3),
                                animation: 'pulse 1.5s ease-in-out infinite',
                                animationDelay: `${0.2 + chip * 0.1}s`
                              }}
                            />
                          ))}
                        </Box>
                      </Box>
                    </CardContent>

                    {/* Bottom strip */}
                    <Box
                      sx={{
                        height: 3,
                        width: '100%',
                        bgcolor: 'background.paper',
                        animation: 'pulse 1.5s ease-in-out infinite',
                        animationDelay: '0.4s'
                      }}
                    />
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        ) : filteredRules.length > 0 ? (
          <Box sx={{ mt: 3 }}>
            <Grid container spacing={3}>
              {filteredRules.map((rule) => {
                const priorityColor = getPriorityColor(rule.priority);

                return (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={rule.id}>
                    <RuleCard
                      sx={{
                        opacity: tabValue === 'inactive' ? 0.8 : 1
                      }}
                    >
                      <CardActionArea onClick={() => handleRuleClick(rule)}>
                      <CardContent sx={{
                        flexGrow: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        p: 0,
                        '&:last-child': { pb: 0 },
                        position: 'relative'
                      }}>
                        {/* Header with name, priority, status and bucket info */}
                        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}` }}>
                          <Box>
                            <Typography variant="h6" component="h2" sx={{ fontWeight: 500, fontSize: '1rem' }}>
                              {rule.name}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                              <Box
                                sx={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  height: '22px',
                                  px: 1,
                                  py: 0.5,
                                  borderRadius: 0, // Rectangular shape
                                  // Color based on priority
                                  bgcolor: rule.priority === 0 ? '#FFF3E0' : // Low - Light Orange
                                           rule.priority === 1 ? '#E3F2FD' : // Medium - Light Blue
                                           rule.priority === 2 ? '#FFEBEE' : // High - Light Red
                                           '#F5F5F5', // Default - Light Grey
                                  color: rule.priority === 0 ? '#E65100' : // Low - Dark Orange
                                         rule.priority === 1 ? '#0D47A1' : // Medium - Dark Blue
                                         rule.priority === 2 ? '#B71C1C' : // High - Dark Red
                                         '#616161', // Default - Dark Grey
                                  fontSize: '12px',
                                  fontWeight: 500,
                                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                }}
                              >
                                Priority: {rule.priority === 0 ? 'Low' :
                                          rule.priority === 1 ? 'Medium' :
                                          rule.priority === 2 ? 'High' :
                                          rule.priority !== undefined ? rule.priority : 'Unknown'}
                              </Box>
                            </Box>
                          </Box>

                          {/* Status and Data Bucket Info - Right aligned */}
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 0.5 }}>
                            {/* Status chip with the same style as data source listing */}
                            <Box
                              sx={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                height: '22px',
                                px: 1,
                                py: 0.5,
                                borderRadius: 0, // Rectangular shape
                                bgcolor: rule.active ? '#E8F5E9' : '#FAFAFA', // Light green for active, light grey for inactive
                                color: rule.active ? '#2E7D32' : '#757575', // Dark green for active, grey for inactive
                                fontSize: '12px',
                                fontWeight: 500,
                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                              }}
                            >
                              {rule.active && (
                                <Box
                                  component="span"
                                  sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: '50%',
                                    bgcolor: '#2E7D32',
                                    display: 'inline-block',
                                    mr: 0.5
                                  }}
                                />
                              )}
                              {rule.active ? 'Active' : 'Inactive'}
                            </Box>

                            {/* Data Bucket Info */}
                            {rule.dataBucket && (
                              <Box
                                sx={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  height: '22px',
                                  px: 1,
                                  py: 0.5,
                                  mt: 0.5,
                                  borderRadius: 0, // Rectangular shape
                                  bgcolor: '#E0F7FA', // Light cyan background
                                  color: '#006064', // Dark cyan text
                                  fontSize: '12px',
                                  fontWeight: 500,
                                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                }}
                              >
                                <StorageIcon sx={{ fontSize: '12px', mr: 0.5 }} />
                                {rule.dataBucket.name}
                              </Box>
                            )}
                          </Box>
                        </Box>

                        {/* Description and Expression section */}
                        <Box sx={{ p: 2, flexGrow: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              color: theme.palette.text.secondary,
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              fontSize: '0.875rem',
                              mb: 1,
                              minHeight: '1.5rem'
                            }}
                          >
                            {rule.description || 'No description provided'}
                          </Typography>

                          {/* Rule Expression */}
                          <Box
                            sx={{
                              p: 1.5,
                              bgcolor: alpha(theme.palette.grey[100], 0.7),
                              borderRadius: 1,
                              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                              mb: 2,
                              maxHeight: '80px',
                              overflow: 'hidden',
                              position: 'relative'
                            }}
                          >
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5, fontSize: '0.7rem' }}>
                              MVEL Expression:
                            </Typography>
                            <Typography
                              variant="caption"
                              component="div"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.8rem',
                                color: '#1565C0', // Blue text for code
                                whiteSpace: 'pre-wrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                fontWeight: 500
                              }}
                            >
                              {rule.expression || (rule.condition ? JSON.stringify(rule.condition, null, 2) : 'No condition defined')}
                            </Typography>
                            <Box
                              sx={{
                                position: 'absolute',
                                bottom: 0,
                                left: 0,
                                right: 0,
                                height: '20px',
                                background: 'linear-gradient(transparent, rgba(255,255,255,0.9))',
                                display: rule.condition && JSON.stringify(rule.condition, null, 2).length > 100 ? 'block' : 'none'
                              }}
                            />
                          </Box>

                          {/* Tag Chips */}
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {rule.tags && rule.tags.map((tag, index) => (
                              <Box
                                key={index}
                                sx={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  height: '24px',
                                  px: 1,
                                  py: 0.5,
                                  mr: 0.5,
                                  mb: 0.5,
                                  borderRadius: '8px',
                                  bgcolor: '#C8E6C9', // Light mint green background
                                  color: '#2E7D32', // Dark green text
                                  fontSize: '13px',
                                  fontWeight: 500,
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    bgcolor: '#A5D6A7',
                                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                                  }
                                }}
                              >
                                <LabelIcon sx={{ fontSize: '0.875rem', mr: 0.5, color: '#43a047' }} />
                                {tag}
                              </Box>
                            ))}
                          </Box>
                        </Box>

                        {/* Actions */}
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 2, pt: 0 }}>
                          <Tooltip title="Delete Rule">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteClick(rule);
                              }}
                              sx={{ zIndex: 2 }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </CardContent>

                      {/* Colored strip at bottom based on priority */}
                      <Box
                        sx={{
                          height: '3px',
                          width: '100%',
                          background: `linear-gradient(90deg, ${alpha(priorityColor.bg, 0.4)} 0%, ${alpha(priorityColor.strip, 0.3)} 100%)`
                        }}
                      />
                      </CardActionArea>
                    </RuleCard>
                  </Grid>
              );
              })}
            </Grid>
          </Box>
        ) : (
          <Paper
            sx={{
              p: 4,
              borderRadius: 2,
              textAlign: 'center',
              bgcolor: alpha(getProgizeColor(), 0.03),
              border: `1px dashed ${alpha(getProgizeColor(), 0.2)}`,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '300px'
            }}
          >
            <Box sx={{mb: 3}}>
              <Avatar
                sx={{
                  width: 64,
                  height: 64,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  margin: '0 auto'
                }}
              >
                {tabValue === 'active' ? (
                  <GavelIcon sx={{fontSize: 32}}/>
                ) : (
                  <InfoIcon sx={{fontSize: 32}}/>
                )}
              </Avatar>
            </Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {tabValue === 'active' ? "No active rules found" : "No inactive rules"}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {tabValue === 'active'
                ? "Create a new rule or activate an existing one"
                : "All your rules are currently active"}
            </Typography>

            {tabValue === 'active' && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                component={RouterLink}
                to={selectedBucketId ? `/rules/new?bucketId=${selectedBucketId}` : "/rules/new"}
                disabled={!selectedBucketId}
                sx={{
                  borderRadius: '28px',
                  px: 3,
                  py: 1,
                  boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                  bgcolor: getProgizeColor ? getProgizeColor() : theme.palette.primary.main,
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 6px 20px rgba(0,118,255,0.39)',
                    bgcolor: getProgizeColor ? alpha(getProgizeColor(), 1.2) : theme.palette.primary.dark
                  }
                }}
              >
                Create New Rule
              </Button>
            )}
          </Paper>
        )}
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxWidth: '400px',
            backgroundImage: 'linear-gradient(to bottom, rgba(255,255,255,0.95), rgba(255,255,255,0.98))',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
          }
        }}
      >
        <DialogTitle sx={{
          fontWeight: 600,
          fontSize: '1.1rem',
          pb: 1,
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          color: theme.palette.error.main,
          display: 'flex',
          alignItems: 'center',
        }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Confirm Deletion
        </DialogTitle>
        <DialogContent sx={{ pt: 2, mt: 1 }}>
          <DialogContentText variant="body2" sx={{ color: theme.palette.text.primary }}>
            Are you sure you want to delete the rule <Box component="span" sx={{ fontWeight: 600 }}>"{ruleToDelete?.name}"</Box>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={handleCancelDelete}
            variant="outlined"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
            size="small"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              backgroundColor: theme.palette.error.main,
              '&:hover': {
                backgroundColor: theme.palette.error.dark,
                boxShadow: '0 4px 12px rgba(211, 47, 47, 0.3)',
              }
            }}
            size="small"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RuleList;
