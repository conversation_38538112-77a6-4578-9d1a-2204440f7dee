package com.progize.tms.controllers.rules;

import com.progize.tms.controllers.rules.req.CreateRuleRequest;
import com.progize.tms.controllers.rules.res.RuleResponse;
import com.progize.tms.controllers.rules.res.ValidationResponse;
import com.progize.tms.payload.ApiResponse;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.services.AggregationRegistryService;
import com.progize.tms.services.RuleConfigService;
import com.progize.tms.services.RuleExecutionService;
import com.progize.tms.services.RuleValidationService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for managing rule configurations. Provides endpoints for creating, updating,
 * validating, and managing rules.
 */
@RestController
@RequestMapping("/api/rules")
@RequiredArgsConstructor
@CrossOrigin(origins = "*") // Enable CORS for the Rule endpoints to work with the UI
@Slf4j
public class RuleConfigController {

  private final RuleConfigService ruleConfigService;
  private final RuleTransformer ruleTransformer;
  private final RuleValidationService ruleValidationService;
  private final AggregationRegistryService aggregationRegistryService;
  private final RuleExecutionService ruleExecutionService;

  /**
   * Creates a new rule after validating its conditions.
   *
   * @param request The rule creation request
   * @return The created rule response
   */
  @PostMapping
  public ResponseEntity<?> createRule(@RequestBody @Valid CreateRuleRequest request) {
    Rule rule = ruleTransformer.toEntity(request);

    RuleValidationService.ValidationResult validationResult =
        ruleValidationService.validateRule(rule);
    if (!validationResult.isValid()) {
      return ResponseEntity.badRequest()
          .body(ruleTransformer.toValidationResponse(validationResult));
    }

    Rule savedRule = ruleConfigService.createRule(rule);
    return ResponseEntity.ok(
        new ApiResponse<>(
            true, "Rule created successfully", ruleTransformer.toResponse(savedRule)));
  }

  /**
   * Updates an existing rule after validating its conditions.
   *
   * @param id The ID of the rule to update
   * @param request The rule update request
   * @return The updated rule response
   */
  @PutMapping("/{id}")
  public ResponseEntity<?> updateRule(
      @PathVariable String id, @RequestBody @Valid CreateRuleRequest request) {
    Rule existingRule = ruleConfigService.getRule(id);
    if (existingRule == null) {
      return ResponseEntity.notFound().build();
    }

    Rule updatedRule = ruleTransformer.toEntity(request);
    updatedRule.setId(existingRule.getId());

    // Validate the rule before saving it
    RuleValidationService.ValidationResult validationResult =
        ruleValidationService.validateRule(updatedRule);
    if (!validationResult.isValid()) {
      return ResponseEntity.badRequest()
          .body(ruleTransformer.toValidationResponse(validationResult));
    }

    Rule savedRule = ruleConfigService.updateRule(id, updatedRule);
    return ResponseEntity.ok(
        new ApiResponse<>(
            true, "Rule updated successfully", ruleTransformer.toResponse(savedRule)));
  }

  /**
   * Validates a rule without saving it.
   *
   * @param rule The rule to validate
   * @return Validation result
   */
  @PostMapping("/validate")
  public ResponseEntity<ValidationResponse> validateRule(
      @RequestBody @Valid CreateRuleRequest rule) {
    try {
      // Convert the request to a Rule entity
      Rule ruleEntity = ruleTransformer.toEntity(rule);

      // Get the bucket ID from the request
      Long bucketId = rule.getDataBucketId();

      // Validate the rule against the bucket-specific variables if a bucket ID is provided
      RuleValidationService.ValidationResult validationResult =
          bucketId != null
              ? ruleValidationService.validateRule(ruleEntity, bucketId)
              : ruleValidationService.validateRule(ruleEntity);

      if (validationResult.isValid()) {
        return ResponseEntity.ok(new ValidationResponse(true, null));
      } else {
        return ResponseEntity.ok(new ValidationResponse(false, validationResult.getErrors()));
      }
    } catch (Exception e) {
      log.error("Error validating rule", e);
      return ResponseEntity.ok(
          new ValidationResponse(false, List.of("Error validating rule: " + e.getMessage())));
    }
  }

  @GetMapping("/{id}")
  public ResponseEntity<ApiResponse<RuleResponse>> getRule(@PathVariable String id) {
    Rule rule = ruleConfigService.getRule(id);
    if (rule == null) {
      return ResponseEntity.notFound().build();
    }
    return ResponseEntity.ok(
        new ApiResponse<>(true, "Rule retrieved successfully", ruleTransformer.toResponse(rule)));
  }

  @GetMapping
  public ResponseEntity<ApiResponse<List<RuleResponse>>> getAllRules() {
    List<Rule> rules = ruleConfigService.getAllRules();
    List<RuleResponse> ruleResponses = ruleTransformer.toResponseList(rules);
    return ResponseEntity.ok(
        new ApiResponse<>(true, "Rules retrieved successfully", ruleResponses));
  }

  @GetMapping("/active")
  public ResponseEntity<ApiResponse<List<RuleResponse>>> getActiveRules() {
    List<Rule> rules = ruleConfigService.getActiveRules();
    List<RuleResponse> ruleResponses = ruleTransformer.toResponseList(rules);
    return ResponseEntity.ok(
        new ApiResponse<>(true, "Active rules retrieved successfully", ruleResponses));
  }

  /**
   * Get active rules for a specific data bucket.
   *
   * @param bucketId The ID of the data bucket
   * @return List of active rules for the specified bucket
   */
  @GetMapping("/active/bucket/{bucketId}")
  public ResponseEntity<ApiResponse<List<RuleResponse>>> getActiveRulesByBucket(
      @PathVariable Long bucketId) {
    List<Rule> rules = ruleConfigService.getActiveRulesByBucket(bucketId);
    List<RuleResponse> ruleResponses = ruleTransformer.toResponseList(rules);
    return ResponseEntity.ok(
        new ApiResponse<>(true, "Active rules for bucket retrieved successfully", ruleResponses));
  }

  @PutMapping("/{id}/activate")
  public ResponseEntity<ApiResponse<RuleResponse>> activateRule(@PathVariable String id) {
    Rule rule = ruleConfigService.activateRule(id);
    return ResponseEntity.ok(
        new ApiResponse<>(true, "Rule activated successfully", ruleTransformer.toResponse(rule)));
  }

  @PutMapping("/{id}/deactivate")
  public ResponseEntity<ApiResponse<RuleResponse>> deactivateRule(@PathVariable String id) {
    Rule rule = ruleConfigService.deactivateRule(id);
    return ResponseEntity.ok(
        new ApiResponse<>(true, "Rule deactivated successfully", ruleTransformer.toResponse(rule)));
  }
}
