package com.progize.tms.controllers.rules.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreateRuleRequest {
  @NotBlank(message = "Name is required")
  private String name;

  private String description;

  private String expression;

  private String flagReason;

  @NotNull(message = "Active status is required")
  private boolean active;

  private int priority;

  @NotNull(message = "Data bucket ID is required")
  private Long dataBucketId;

  @NotNull(message = "Condition is required")
  private RuleCondition condition;
}
