import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  useTheme
} from '@mui/material';
import { Delete as DeleteIcon } from '@mui/icons-material';

const StepConfigPanel = ({ node, updateNode, deleteNode }) => {
  const theme = useTheme();
  
  // Local state for form values
  const [label, setLabel] = useState(node.data.label || '');
  const [description, setDescription] = useState(node.data.description || '');
  const [statusLabel, setStatusLabel] = useState(node.data.statusLabel || '');
  const [stepOrder, setStepOrder] = useState(node.data.stepOrder || 1);
  const [requiresAttachment, setRequiresAttachment] = useState(node.data.requiresAttachment || false);
  const [requiresComments, setRequiresComments] = useState(node.data.requiresComments || false);
  
  // Handle form submission
  const handleSubmit = () => {
    updateNode(node.id, {
      label,
      description,
      statusLabel,
      stepOrder,
      requiresAttachment,
      requiresComments
    });
  };
  
  return (
    <Box>
      <TextField
        label="Step Name"
        fullWidth
        margin="normal"
        value={label}
        onChange={(e) => setLabel(e.target.value)}
      />
      
      <TextField
        label="Description"
        fullWidth
        multiline
        rows={3}
        margin="normal"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
      />
      
      <TextField
        label="Status Label"
        fullWidth
        margin="normal"
        value={statusLabel}
        onChange={(e) => setStatusLabel(e.target.value)}
        helperText="This will be displayed as the case status when in this step"
      />
      
      <TextField
        label="Step Order"
        type="number"
        fullWidth
        margin="normal"
        value={stepOrder}
        onChange={(e) => setStepOrder(parseInt(e.target.value, 10))}
        InputProps={{ inputProps: { min: 1 } }}
      />
      
      <Box sx={{ mt: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={requiresAttachment}
              onChange={(e) => setRequiresAttachment(e.target.checked)}
              color="primary"
            />
          }
          label="Requires Attachment"
        />
      </Box>
      
      <Box sx={{ mt: 1, mb: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={requiresComments}
              onChange={(e) => setRequiresComments(e.target.checked)}
              color="primary"
            />
          }
          label="Requires Comments"
        />
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          fullWidth
        >
          Apply Changes
        </Button>
        
        <Button
          variant="contained"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={deleteNode}
        >
          Delete
        </Button>
      </Box>
    </Box>
  );
};

export default StepConfigPanel;
