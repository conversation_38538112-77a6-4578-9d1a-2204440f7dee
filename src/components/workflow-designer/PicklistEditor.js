import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>ield,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Chip,
  useTheme
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon
} from '@mui/icons-material';
import WorkflowDesignerService from '../../services/WorkflowDesignerService';

const PicklistEditor = ({ node, picklists, updateNode }) => {
  const theme = useTheme();
  
  // Local state
  const [selectedPicklist, setSelectedPicklist] = useState(node.data.picklist || null);
  const [availablePicklists, setAvailablePicklists] = useState(picklists || []);
  const [picklistDialogO<PERSON>, setPicklistDialogOpen] = useState(false);
  const [optionDialogOpen, setOptionDialogOpen] = useState(false);
  const [newPicklistName, setNewPicklistName] = useState('');
  const [newPicklistDescription, setNewPicklistDescription] = useState('');
  const [editingOption, setEditingOption] = useState(null);
  const [optionValue, setOptionValue] = useState('');
  const [optionLabel, setOptionLabel] = useState('');
  const [optionDescription, setOptionDescription] = useState('');
  const [optionIsDefault, setOptionIsDefault] = useState(false);
  const [displayOrder, setDisplayOrder] = useState(1);
  
  // Fetch available picklists
  useEffect(() => {
    const fetchPicklists = async () => {
      try {
        const fetchedPicklists = await WorkflowDesignerService.getPicklists();
        setAvailablePicklists(fetchedPicklists);
      } catch (error) {
        console.error('Error fetching picklists:', error);
      }
    };
    
    fetchPicklists();
  }, []);
  
  // Handle picklist selection
  const handlePicklistChange = (event) => {
    const picklistId = event.target.value;
    if (picklistId === 'new') {
      setPicklistDialogOpen(true);
    } else {
      const picklist = availablePicklists.find(p => p.id === picklistId);
      setSelectedPicklist(picklist);
      updateNode(node.id, { picklist });
    }
  };
  
  // Create new picklist
  const handleCreatePicklist = async () => {
    try {
      const newPicklist = {
        name: newPicklistName,
        description: newPicklistDescription,
        options: []
      };
      
      const createdPicklist = await WorkflowDesignerService.createPicklist(newPicklist);
      setAvailablePicklists([...availablePicklists, createdPicklist]);
      setSelectedPicklist(createdPicklist);
      updateNode(node.id, { picklist: createdPicklist });
      
      // Reset form
      setNewPicklistName('');
      setNewPicklistDescription('');
      setPicklistDialogOpen(false);
    } catch (error) {
      console.error('Error creating picklist:', error);
    }
  };
  
  // Open option dialog
  const handleAddOption = () => {
    setEditingOption(null);
    setOptionValue('');
    setOptionLabel('');
    setOptionDescription('');
    setOptionIsDefault(false);
    setDisplayOrder(selectedPicklist?.options?.length + 1 || 1);
    setOptionDialogOpen(true);
  };
  
  // Edit option
  const handleEditOption = (option) => {
    setEditingOption(option);
    setOptionValue(option.optionValue);
    setOptionLabel(option.optionLabel);
    setOptionDescription(option.description || '');
    setOptionIsDefault(option.isDefault || false);
    setDisplayOrder(option.displayOrder || 1);
    setOptionDialogOpen(true);
  };
  
  // Save option
  const handleSaveOption = async () => {
    try {
      const optionData = {
        id: editingOption?.id,
        optionValue,
        optionLabel,
        description: optionDescription,
        displayOrder,
        isDefault: optionIsDefault,
        picklistId: selectedPicklist.id
      };
      
      let updatedPicklist;
      
      if (editingOption) {
        // Update existing option
        await WorkflowDesignerService.updatePicklistOption(optionData);
        updatedPicklist = {
          ...selectedPicklist,
          options: selectedPicklist.options.map(opt => 
            opt.id === editingOption.id ? optionData : opt
          )
        };
      } else {
        // Create new option
        const createdOption = await WorkflowDesignerService.createPicklistOption(optionData);
        updatedPicklist = {
          ...selectedPicklist,
          options: [...(selectedPicklist.options || []), createdOption]
        };
      }
      
      setSelectedPicklist(updatedPicklist);
      updateNode(node.id, { picklist: updatedPicklist });
      setOptionDialogOpen(false);
    } catch (error) {
      console.error('Error saving picklist option:', error);
    }
  };
  
  // Delete option
  const handleDeleteOption = async (optionId) => {
    try {
      await WorkflowDesignerService.deletePicklistOption(optionId);
      const updatedPicklist = {
        ...selectedPicklist,
        options: selectedPicklist.options.filter(opt => opt.id !== optionId)
      };
      setSelectedPicklist(updatedPicklist);
      updateNode(node.id, { picklist: updatedPicklist });
    } catch (error) {
      console.error('Error deleting picklist option:', error);
    }
  };
  
  // Move option up/down
  const handleMoveOption = async (option, direction) => {
    const currentIndex = selectedPicklist.options.findIndex(opt => opt.id === option.id);
    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    if (newIndex < 0 || newIndex >= selectedPicklist.options.length) {
      return;
    }
    
    const newOptions = [...selectedPicklist.options];
    const temp = newOptions[currentIndex];
    newOptions[currentIndex] = newOptions[newIndex];
    newOptions[newIndex] = temp;
    
    // Update display order
    const updatedOptions = newOptions.map((opt, index) => ({
      ...opt,
      displayOrder: index + 1
    }));
    
    try {
      // Update all options with new display orders
      await Promise.all(updatedOptions.map(opt => 
        WorkflowDesignerService.updatePicklistOption(opt)
      ));
      
      const updatedPicklist = {
        ...selectedPicklist,
        options: updatedOptions
      };
      
      setSelectedPicklist(updatedPicklist);
      updateNode(node.id, { picklist: updatedPicklist });
    } catch (error) {
      console.error('Error updating option order:', error);
    }
  };
  
  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom>
        Picklist Configuration
      </Typography>
      
      <FormControl fullWidth margin="normal">
        <InputLabel id="picklist-select-label">Picklist</InputLabel>
        <Select
          labelId="picklist-select-label"
          value={selectedPicklist?.id || ''}
          onChange={handlePicklistChange}
          label="Picklist"
        >
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {availablePicklists.map(picklist => (
            <MenuItem key={picklist.id} value={picklist.id}>
              {picklist.name}
            </MenuItem>
          ))}
          <MenuItem value="new">
            <em>Create New Picklist...</em>
          </MenuItem>
        </Select>
      </FormControl>
      
      {selectedPicklist && (
        <>
          <Box sx={{ mt: 2, mb: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              {selectedPicklist.name}
            </Typography>
            {selectedPicklist.description && (
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {selectedPicklist.description}
              </Typography>
            )}
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle2">
              Options
            </Typography>
            <Button
              startIcon={<AddIcon />}
              onClick={handleAddOption}
              size="small"
            >
              Add Option
            </Button>
          </Box>
          
          <List>
            {selectedPicklist.options && selectedPicklist.options.length > 0 ? (
              selectedPicklist.options
                .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0))
                .map(option => (
                  <ListItem key={option.id} divider>
                    <ListItemText
                      primary={option.optionLabel}
                      secondary={
                        <>
                          {option.optionValue}
                          {option.isDefault && (
                            <Chip
                              label="Default"
                              size="small"
                              color="primary"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleMoveOption(option, 'up')}
                        disabled={selectedPicklist.options.indexOf(option) === 0}
                      >
                        <ArrowUpwardIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        edge="end"
                        onClick={() => handleMoveOption(option, 'down')}
                        disabled={selectedPicklist.options.indexOf(option) === selectedPicklist.options.length - 1}
                      >
                        <ArrowDownwardIcon fontSize="small" />
                      </IconButton>
                      <IconButton edge="end" onClick={() => handleEditOption(option)}>
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton edge="end" onClick={() => handleDeleteOption(option.id)}>
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))
            ) : (
              <ListItem>
                <ListItemText primary="No options defined" />
              </ListItem>
            )}
          </List>
        </>
      )}
      
      {/* New Picklist Dialog */}
      <Dialog open={picklistDialogOpen} onClose={() => setPicklistDialogOpen(false)}>
        <DialogTitle>Create New Picklist</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Picklist Name"
            fullWidth
            value={newPicklistName}
            onChange={(e) => setNewPicklistName(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={3}
            value={newPicklistDescription}
            onChange={(e) => setNewPicklistDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPicklistDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreatePicklist} 
            variant="contained" 
            color="primary"
            disabled={!newPicklistName}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Option Dialog */}
      <Dialog open={optionDialogOpen} onClose={() => setOptionDialogOpen(false)}>
        <DialogTitle>
          {editingOption ? 'Edit Option' : 'Add Option'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Option Value"
            fullWidth
            value={optionValue}
            onChange={(e) => setOptionValue(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Option Label"
            fullWidth
            value={optionLabel}
            onChange={(e) => setOptionLabel(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={2}
            value={optionDescription}
            onChange={(e) => setOptionDescription(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Display Order"
            type="number"
            fullWidth
            value={displayOrder}
            onChange={(e) => setDisplayOrder(parseInt(e.target.value, 10))}
            InputProps={{ inputProps: { min: 1 } }}
          />
          <FormControlLabel
            control={
              <Switch
                checked={optionIsDefault}
                onChange={(e) => setOptionIsDefault(e.target.checked)}
                color="primary"
              />
            }
            label="Default Option"
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOptionDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleSaveOption} 
            variant="contained" 
            color="primary"
            disabled={!optionValue || !optionLabel}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PicklistEditor;
