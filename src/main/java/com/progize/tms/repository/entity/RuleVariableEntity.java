package com.progize.tms.repository.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * Entity representing a rule variable that can be used in rule conditions. Each rule variable is
 * linked to a specific column in a source table.
 */
@Entity
@Table(name = "rule_variables")
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class RuleVariableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "code", nullable = false, unique = true, length = 50)
  private String code;

  @Column(name = "display_name", nullable = false, length = 100)
  private String displayName;

  @Column(length = 500)
  private String description;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "source_table_id", nullable = false)
  private RuleVariableSourceTable sourceTable;

  @Column(name = "column_name", nullable = false)
  private String columnName;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "data_type_id", nullable = false)
  private DataType dataType;

  @Column(name = "aggregatable", nullable = false)
  private boolean aggregatable;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "operator_overrides", columnDefinition = "json")
  private Map<String, List<String>> operatorOverrides;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "json")
  private Map<String, Object> metadata;

  @CreatedDate
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @LastModifiedDate
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;
}
