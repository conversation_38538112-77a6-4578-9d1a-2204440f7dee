-- Create bucket_relationship table
CREATE TABLE bucket_relationship (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_bucket_id BIGINT NOT NULL,
    lookup_bucket_id BIGINT NOT NULL,
    join_conditions JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGIN<PERSON>,
    updated_by BIGIN<PERSON>,
    CONSTRAINT fk_bucket_relationship_rule_bucket FOREIGN KEY (rule_bucket_id) REFERENCES data_bucket(id) ON DELETE CASCADE,
    CONSTRAINT fk_bucket_relationship_lookup_bucket FOREIGN KEY (lookup_bucket_id) REFERENCES data_bucket(id) ON DELETE CASCADE,
    CONSTRAINT uk_bucket_relationship_buckets UNIQUE (rule_bucket_id, lookup_bucket_id),
    CONSTRAINT fk_bucket_relationship_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_bucket_relationship_updated_by F<PERSON><PERSON><PERSON><PERSON> KEY (updated_by) REFERENCES users(id)
);

-- Add audit table for bucket_relationship
CREATE TABLE bucket_relationship_aud (
    id BIGINT NOT NULL,
    rev INTEGER NOT NULL,
    revtype TINYINT,
    rule_bucket_id BIGINT,
    lookup_bucket_id BIGINT,
    join_conditions JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_bucket_relationship_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);
