import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  Divider,
  CircularProgress,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  IconButton,
  TextField,
  Alert,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Badge,
  Container,
  alpha,
  useTheme,
  styled
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  AttachFile as AttachFileIcon,
  Comment as CommentIcon,
  Receipt as ReceiptIcon,
  History as HistoryIcon,
  Add as AddIcon,
  Close as CloseIcon,
  Send as SendIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  FilterList as FilterListIcon,
  ArrowForward as ArrowForwardIcon,
  PriorityHigh as PriorityHighIcon,
  Timeline as WorkflowIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import CaseService from '../../services/CaseService';
import WorkflowService from '../../services/WorkflowService';
import CaseComments from './CaseComments';
import CaseAttachments from './CaseAttachments';
import CaseTransactions from './CaseTransactions';

// Status color mapping
const statusColors = {
  OPEN: 'error',
  IN_PROGRESS: 'warning',
  UNDER_REVIEW: 'info',
  CLOSED: 'success',
  PENDING: 'default'
};

// Priority color mapping
const priorityColors = {
  HIGH: 'error',
  MEDIUM: 'warning',
  LOW: 'info'
};

// Styled components
const StyledTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTabs-indicator': {
    height: 3,
    backgroundColor: theme.palette.primary.main,
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 500,
  minHeight: 48,
  '&.Mui-selected': {
    fontWeight: 600,
  },
}));

const StatusChip = styled(Chip)(({ theme, color }) => ({
  height: 28,
  fontSize: '0.85rem',
  backgroundColor: alpha(theme.palette[color]?.main || theme.palette.grey[500], 0.1),
  color: theme.palette[color]?.dark || theme.palette.grey[700],
  borderColor: alpha(theme.palette[color]?.main || theme.palette.grey[500], 0.3),
  '& .MuiChip-label': {
    padding: '0 8px',
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: 20,
  textTransform: 'none',
  fontWeight: 500,
  boxShadow: 'none',
  '&:hover': {
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
  }
}));

// Styled component for file input
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

/**
 * Component for displaying detailed case information and actions
 */
const CaseDetail = () => {
  const { caseId } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const [caseData, setCaseData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [comment, setComment] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [closeDialogOpen, setCloseDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [closeReason, setCloseReason] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);
  const [workflowInstance, setWorkflowInstance] = useState(null);
  const [availableActions, setAvailableActions] = useState([]);
  const [loadingWorkflow, setLoadingWorkflow] = useState(false);
  const [picklistOptions, setPicklistOptions] = useState([]);
  const [selectedPicklistOption, setSelectedPicklistOption] = useState('');
  const [picklistDialogOpen, setPicklistDialogOpen] = useState(false);
  const [currentPicklist, setCurrentPicklist] = useState(null);
  
  // Mock user ID - in a real app, this would come from authentication
  const userId = 'admin';

  // Fetch case data on component mount and when refreshKey changes
  useEffect(() => {
    const fetchCaseData = async () => {
      setLoading(true);
      try {
        const data = await CaseService.getCaseById(caseId);
        setCaseData(data);
        setError(null);
        // Set initial status for dialog
        setNewStatus(data.status);
        
        // Fetch workflow instance data if available
        if (data.workflowInstanceId) {
          await fetchWorkflowData(data.workflowInstanceId);
        } else {
          // Try to fetch workflow instances by entity
          await fetchWorkflowInstancesByEntity('CASE', data.id);
        }
      } catch (err) {
        console.error('Error fetching case details:', err);
        setError('Failed to fetch case details. Please try again later.');
        setCaseData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseData();
  }, [caseId, refreshKey]);
  
  // Fetch workflow data
  const fetchWorkflowData = async (instanceId) => {
    setLoadingWorkflow(true);
    try {
      console.log(`Fetching workflow data for instance ID: ${instanceId}`);
      const instance = await WorkflowService.getWorkflowInstanceById(instanceId);
      console.log('Fetched workflow instance:', instance);
      setWorkflowInstance(instance);
      
      // Always fetch picklist options regardless of whether the current step has a picklist
      console.log('Fetching picklist options for workflow instance');
      const picklistOptions = await WorkflowService.getPicklistOptions(instanceId);
      console.log('Fetched picklist options:', picklistOptions);
      
      if (picklistOptions && picklistOptions.length > 0) {
        setAvailableActions(picklistOptions);
      } else {
        console.warn('No picklist options found, using empty array');
        setAvailableActions([]);
      }
    } catch (err) {
      console.error('Error fetching workflow data:', err);
      setError('Failed to fetch workflow data. Please try again.');
    } finally {
      setLoadingWorkflow(false);
    }
  };
  
  // Fetch workflow instances by entity
  const fetchWorkflowInstancesByEntity = async (entityType, entityId) => {
    setLoadingWorkflow(true);
    try {
      const instances = await WorkflowService.getWorkflowInstancesByEntity(entityType, entityId);
      if (instances && instances.length > 0) {
        setWorkflowInstance(instances[0]);
        
        // Fetch available actions
        const actions = await WorkflowService.getAvailableActions(instances[0].id);
        setAvailableActions(actions);
      }
    } catch (err) {
      console.error('Error fetching workflow instances by entity:', err);
    } finally {
      setLoadingWorkflow(false);
    }
  };

  // Handle executing a workflow action
  const handleExecuteAction = async (action) => {
    if (!workflowInstance) return;
    
    setSubmitting(true);
    try {
      console.log(`Executing action on workflow ${workflowInstance.id}`, action);
      
      // If no toStepId is provided, open the dialog to select a step
      if (!action.toStepId) {
        console.warn('Action has no toStepId, opening picklist dialog', action);
        await handlePicklistDialogOpen(action);
        setSubmitting(false);
        return;
      }
      
      // Execute the action with the target step
      console.log('Executing workflow transition with:', {
        instanceId: workflowInstance.id,
        toStepId: action.toStepId,
        actionName: action.name || "User Action",
        picklistOptionId: action.id
      });
      
      await WorkflowService.executeAction(workflowInstance.id, {
        toStepId: action.toStepId,
        actionName: action.name || "User Action",
        currentUserId: "admin",
        comments: comment || "",
        picklistOptionId: action.id // Use the action ID as the picklist option ID if available
      });
      
      // Refresh the case data
      setRefreshKey(prevKey => prevKey + 1);
    } catch (err) {
      console.error('Error executing action:', err);
      setError('Failed to execute action. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };
  
  // Handle opening picklist dialog
  const handlePicklistDialogOpen = async (action) => {
    if (!workflowInstance) return;
    
    setCurrentPicklist(action);
    setSubmitting(true);
    try {
      // Get picklist options for the current step
      const options = await WorkflowService.getPicklistOptions(workflowInstance.id);
      console.log('Picklist options for dialog:', options);
      
      if (options && options.length > 0) {
        setPicklistOptions(options);
        setSelectedPicklistOption('');
        setPicklistDialogOpen(true);
      } else {
        setError('No options available for this workflow step.');
      }
    } catch (err) {
      console.error('Error fetching picklist options:', err);
      setError('Failed to fetch options. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle picklist option change
  const handlePicklistOptionChange = (event) => {
    setSelectedPicklistOption(event.target.value);
  };
  
  // Handle picklist dialog close
  const handlePicklistDialogClose = () => {
    setPicklistDialogOpen(false);
    setCurrentPicklist(null);
  };
  
  // Handle picklist option submit
  const handlePicklistSubmit = async () => {
    if (!workflowInstance || !selectedPicklistOption) return;
    
    setSubmitting(true);
    try {
      // Find the selected option to get the toStepId
      const selectedOption = picklistOptions.find(option => option.id === selectedPicklistOption);
      console.log('Selected option:', selectedOption);
      
      if (!selectedOption) {
        throw new Error('Invalid selection. No option found.');
      }
      
      // Execute the workflow transition
      await WorkflowService.executeAction(workflowInstance.id, {
        toStepId: selectedOption.toStepId,
        actionName: selectedOption.name || "User Action",
        currentUserId: "admin",
        comments: comment || "",
        picklistOptionId: selectedOption.id
      });
      
      setPicklistDialogOpen(false);
      setComment('');
      
      // Refresh the case data
      setRefreshKey(prevKey => prevKey + 1);
    } catch (err) {
      console.error('Error executing transition:', err);
      setError('Failed to execute transition. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle comment change
  const handleCommentChange = (event) => {
    setComment(event.target.value);
  };

  // Handle comment submission
  const handleSubmitComment = async () => {
    if (!comment.trim()) return;
    
    setSubmitting(true);
    try {
      await CaseService.addComment(caseId, userId, comment);
      setComment('');
      // Refresh the case data to show the new comment
      setRefreshKey(prevKey => prevKey + 1);
    } catch (err) {
      setError('Failed to add comment. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle file selection
  const handleFileChange = (event) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile) return;
    
    setSubmitting(true);
    try {
      // Simulate upload progress
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) clearInterval(interval);
          return Math.min(prev + 10, 90);
        });
      }, 300);
      
      await CaseService.addAttachment(caseId, userId, selectedFile);
      clearInterval(interval);
      setUploadProgress(100);
      setSelectedFile(null);
      // Refresh the case data to show the new attachment
      setRefreshKey(prevKey => prevKey + 1);
    } catch (err) {
      setError('Failed to upload attachment. Please try again.');
    } finally {
      setSubmitting(false);
      setUploadProgress(0);
    }
  };

  // Handle status change dialog
  const handleStatusDialogOpen = () => {
    setStatusDialogOpen(true);
  };

  const handleStatusDialogClose = () => {
    setStatusDialogOpen(false);
  };

  const handleStatusChange = (event) => {
    setNewStatus(event.target.value);
  };

  const handleStatusSubmit = async () => {
    setSubmitting(true);
    try {
      await CaseService.updateCaseStatus(caseId, newStatus, userId);
      setStatusDialogOpen(false);
      // Refresh the case data to show the new status
      setRefreshKey(prevKey => prevKey + 1);
    } catch (err) {
      setError('Failed to update status. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle case closure dialog
  const handleCloseDialogOpen = () => {
    setCloseDialogOpen(true);
  };

  const handleCloseDialogClose = () => {
    setCloseDialogOpen(false);
  };

  const handleCloseReasonChange = (event) => {
    setCloseReason(event.target.value);
  };

  const handleCloseCase = async () => {
    if (!closeReason.trim()) return;
    
    setSubmitting(true);
    try {
      await CaseService.closeCase(caseId, userId, closeReason);
      setCloseDialogOpen(false);
      // Refresh the case data to show the closed status
      setRefreshKey(prevKey => prevKey + 1);
    } catch (err) {
      setError('Failed to close case. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Handle back button click
  const handleBack = () => {
    navigate(-1);
  };

  // Get relative time for display
  const getRelativeTime = (dateString) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffDay > 0) {
      return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else if (diffHour > 0) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffMin > 0) {
      return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header with back button */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton 
          onClick={handleBack}
          sx={{ mr: 1, bgcolor: alpha(theme.palette.primary.main, 0.1) }}
        >
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600, flexGrow: 1 }}>
          Case Details
        </Typography>
        
        {/* Action buttons */}
        {!loading && caseData && (
          <Box sx={{ display: 'flex', gap: 2 }}>
            {caseData.status !== 'CLOSED' && (
              <>
                <ActionButton
                  variant="outlined"
                  color="primary"
                  onClick={handleStatusDialogOpen}
                  startIcon={<AssignmentIcon />}
                >
                  Change Status
                </ActionButton>
                <ActionButton
                  variant="contained"
                  color="error"
                  onClick={handleCloseDialogOpen}
                  startIcon={<CloseIcon />}
                >
                  Close Case
                </ActionButton>
              </>
            )}
          </Box>
        )}
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : caseData ? (
        <>
          {/* Case details card */}
          <Card sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
            <Box sx={{ 
              height: 8, 
              bgcolor: caseData.priority && priorityColors[caseData.priority] ? 
                theme.palette[priorityColors[caseData.priority]].main : 
                theme.palette.grey[500]
            }} />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                        {caseData.title}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip 
                          label={`Case #${caseData.id}`} 
                          size="small" 
                          variant="outlined" 
                          color="default"
                        />
                        <Chip 
                          label={caseData.status} 
                          size="small" 
                          variant="outlined" 
                          color={statusColors[caseData.status] || 'default'}
                        />
                        <Chip 
                          label={caseData.priority || 'MEDIUM'} 
                          size="small" 
                          variant="outlined" 
                          color={priorityColors[caseData.priority] || 'default'}
                          icon={<PriorityHighIcon fontSize="small" />}
                        />
                        {workflowInstance && workflowInstance.currentStepName && (
                          <Chip 
                            label={workflowInstance.currentStepName}
                            size="small" 
                            variant="outlined" 
                            color="info"
                            icon={<WorkflowIcon fontSize="small" />}
                          />
                        )}
                      </Box>
                    </Box>
                    <Box>
                      <Button 
                        variant="outlined" 
                        color="primary" 
                        size="small"
                        startIcon={<ArrowBackIcon />}
                        onClick={handleBack}
                        sx={{ borderRadius: 2 }}
                      >
                        Back to List
                      </Button>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, borderRadius: 2, bgcolor: alpha(theme.palette.background.default, 0.5) }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                      Case Information
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">Status:</Typography>
                        <StatusChip
                          label={caseData.status}
                          color={statusColors[caseData.status] || 'default'}
                          size="small"
                        />
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">Priority:</Typography>
                        <StatusChip
                          label={caseData.priority}
                          color={priorityColors[caseData.priority] || 'default'}
                          size="small"
                        />
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">Case ID:</Typography>
                        <Typography variant="body2">{caseData.id}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">Rule:</Typography>
                        <Typography variant="body2">{caseData.ruleName || 'N/A'}</Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, borderRadius: 2, bgcolor: alpha(theme.palette.background.default, 0.5) }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                      Timeline
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">Created:</Typography>
                        <Typography variant="body2">{getRelativeTime(caseData.createdAt)}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">Last Updated:</Typography>
                        <Typography variant="body2">{getRelativeTime(caseData.lastUpdated)}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">Created By:</Typography>
                        <Typography variant="body2">{caseData.createdBy || 'System'}</Typography>
                      </Box>
                      {caseData.status === 'CLOSED' && (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Closed By:</Typography>
                          <Typography variant="body2">{caseData.closedBy || 'N/A'}</Typography>
                        </Box>
                      )}
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
              
              {/* Workflow information */}
              {workflowInstance && (
                <Card sx={{ mt: 3, borderRadius: 2 }}>
                  <CardHeader 
                    title="Workflow Information" 
                    titleTypographyProps={{ variant: 'h6', fontWeight: 600 }}
                    avatar={<WorkflowIcon color="primary" />}
                  />
                  <Divider />
                  <CardContent>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom>Current Step</Typography>
                        {loadingWorkflow ? (
                          <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                            <CircularProgress size={24} />
                          </Box>
                        ) : (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip 
                              label={workflowInstance.currentStepName || 'Unknown'} 
                              color="info"
                              variant="filled"
                            />
                            {workflowInstance.currentStepDescription && (
                              <Typography variant="body2" color="text.secondary">
                                {workflowInstance.currentStepDescription}
                              </Typography>
                            )}
                          </Box>
                        )}
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom>Available Actions</Typography>
                        {loadingWorkflow ? (
                          <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                            <CircularProgress size={24} />
                          </Box>
                        ) : (
                          <Box>
                            {/* Always show Take Action button if there are available actions */}
                            {availableActions && availableActions.length > 0 && (
                              <Box sx={{ mb: 2 }}>
                                <ActionButton
                                  variant="contained"
                                  size="medium"
                                  color="primary"
                                  onClick={() => handlePicklistDialogOpen({
                                    id: 'take-action',
                                    name: "Take Action",
                                    description: "Select an option from the picklist"
                                  })}
                                  startIcon={<FilterListIcon />}
                                  disabled={submitting}
                                  fullWidth
                                >
                                  Take Action
                                </ActionButton>
                              </Box>
                            )}
                            
                            {/* Show available actions if any */}
                            {availableActions && availableActions.length > 0 ? (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                {availableActions.map(action => (
                                  <ActionButton
                                    key={action.id}
                                    variant="outlined"
                                    size="small"
                                    color="primary"
                                    onClick={() => handleExecuteAction(action)}
                                    startIcon={<ArrowForwardIcon />}
                                    disabled={submitting}
                                  >
                                    {action.name}
                                  </ActionButton>
                                ))}
                              </Box>
                            ) : (
                              <Typography variant="body2" color="text.secondary">No actions available</Typography>
                            )}
                          </Box>
                        )}
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>

          {/* Tabs for different sections */}
          <Box sx={{ mb: 3, borderBottom: 1, borderColor: 'divider' }}>
            <StyledTabs value={tabValue} onChange={handleTabChange} aria-label="case details tabs">
              <StyledTab label="Comments" id="tab-0" />
              <StyledTab label="Attachments" id="tab-1" />
              <StyledTab label="Transactions" id="tab-2" />
            </StyledTabs>
          </Box>

          {/* Tab panels */}
          <Box sx={{ mb: 3 }}>
            {/* Comments tab */}
            {tabValue === 0 && (
              <Box>
                {caseData.status !== 'CLOSED' && (
                  <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                      Add Comment
                    </Typography>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      placeholder="Type your comment here..."
                      value={comment}
                      onChange={handleCommentChange}
                      variant="outlined"
                      disabled={submitting || caseData.status === 'CLOSED'}
                      sx={{ mb: 2 }}
                    />
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <ActionButton
                        variant="contained"
                        color="primary"
                        endIcon={<SendIcon />}
                        onClick={handleSubmitComment}
                        disabled={!comment.trim() || submitting || caseData.status === 'CLOSED'}
                      >
                        {submitting ? 'Submitting...' : 'Add Comment'}
                      </ActionButton>
                    </Box>
                  </Paper>
                )}
                <CaseComments caseId={caseId} refreshKey={refreshKey} />
              </Box>
            )}

            {/* Attachments tab */}
            {tabValue === 1 && (
              <Box>
                {caseData.status !== 'CLOSED' && (
                  <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                      Upload Attachment
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <ActionButton
                        component="label"
                        variant="outlined"
                        startIcon={<AttachFileIcon />}
                        disabled={submitting}
                      >
                        Select File
                        <VisuallyHiddenInput type="file" onChange={handleFileChange} />
                      </ActionButton>
                      {selectedFile && (
                        <>
                          <Typography variant="body2" sx={{ flexGrow: 1 }}>
                            {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
                          </Typography>
                          <ActionButton
                            variant="contained"
                            color="primary"
                            endIcon={<CloudUploadIcon />}
                            onClick={handleFileUpload}
                            disabled={submitting}
                          >
                            {submitting ? `Uploading... ${uploadProgress}%` : 'Upload'}
                          </ActionButton>
                        </>
                      )}
                    </Box>
                  </Paper>
                )}
                <CaseAttachments caseId={caseId} refreshKey={refreshKey} />
              </Box>
            )}

            {/* Transactions tab */}
            {tabValue === 2 && (
              <CaseTransactions caseId={caseId} refreshKey={refreshKey} />
            )}
          </Box>
        </>
      ) : (
        <Paper 
          sx={{ 
            p: 5, 
            textAlign: 'center', 
            borderRadius: 2,
            bgcolor: alpha(theme.palette.background.paper, 0.6),
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Typography variant="h6">
            Case not found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            The requested case could not be found or you don't have permission to view it.
          </Typography>
          <ActionButton
            variant="contained"
            color="primary"
            onClick={handleBack}
            sx={{ mt: 3 }}
          >
            Go Back
          </ActionButton>
        </Paper>
      )}

      {/* Status change dialog */}
      <Dialog 
        open={statusDialogOpen} 
        onClose={handleStatusDialogClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxWidth: '400px',
          }
        }}
      >
        <DialogTitle sx={{ fontWeight: 600 }}>Change Case Status</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Update the status of this case. This will be visible to all team members.
          </DialogContentText>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel id="status-select-label">Status</InputLabel>
            <Select
              labelId="status-select-label"
              value={newStatus}
              label="Status"
              onChange={handleStatusChange}
            >
              <MenuItem value="OPEN">Open</MenuItem>
              <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
              <MenuItem value="UNDER_REVIEW">Under Review</MenuItem>
              <MenuItem value="PENDING">Pending</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleStatusDialogClose}>Cancel</Button>
          <ActionButton 
            onClick={handleStatusSubmit} 
            disabled={submitting || newStatus === caseData?.status}
            variant="contained"
            color="primary"
          >
            {submitting ? 'Updating...' : 'Update Status'}
          </ActionButton>
        </DialogActions>
      </Dialog>

      {/* Close case dialog */}
      <Dialog 
        open={closeDialogOpen} 
        onClose={handleCloseDialogClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxWidth: '400px',
          }
        }}
      >
        <DialogTitle sx={{ fontWeight: 600, color: theme.palette.error.main }}>Close Case</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to close this case? This action cannot be undone.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="close-reason"
            label="Reason for closing"
            type="text"
            fullWidth
            multiline
            rows={3}
            value={closeReason}
            onChange={handleCloseReasonChange}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCloseDialogClose}>Cancel</Button>
          <ActionButton 
            onClick={handleCloseCase} 
            color="error" 
            disabled={submitting || !closeReason.trim()}
            variant="contained"
          >
            {submitting ? 'Closing...' : 'Close Case'}
          </ActionButton>
        </DialogActions>
      </Dialog>

      {/* Picklist Dialog */}
      <Dialog 
        open={picklistDialogOpen} 
        onClose={handlePicklistDialogClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxWidth: '500px',
          }
        }}
      >
        <DialogTitle sx={{ fontWeight: 600 }}>
          {currentPicklist?.name || 'Select an Action'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Please select one of the following options to proceed with this case.
          </DialogContentText>
          
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel id="picklist-option-label">Action</InputLabel>
            <Select
              labelId="picklist-option-label"
              id="picklist-option"
              value={selectedPicklistOption}
              onChange={handlePicklistOptionChange}
              label="Action"
            >
              {picklistOptions.map(option => (
                <MenuItem key={option.id} value={option.id}>
                  {option.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {/* Show description of selected option if available */}
          {selectedPicklistOption && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Description
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {picklistOptions.find(o => o.id === selectedPicklistOption)?.description || 
                 'Take this action to move the case to the next step.'}
              </Typography>
            </Box>
          )}
          
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Comments"
            placeholder="Add any comments about this action..."
            value={comment}
            onChange={handleCommentChange}
            variant="outlined"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePicklistDialogClose} color="inherit">
            Cancel
          </Button>
          <Button 
            onClick={handlePicklistSubmit} 
            color="primary" 
            variant="contained"
            disabled={!selectedPicklistOption || submitting}
          >
            {submitting ? 'Submitting...' : 'Submit'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default CaseDetail;
