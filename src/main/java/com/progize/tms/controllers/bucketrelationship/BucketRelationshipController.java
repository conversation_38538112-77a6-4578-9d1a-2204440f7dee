package com.progize.tms.controllers.bucketrelationship;

import com.progize.tms.controllers.bucketrelationship.req.CreateBucketRelationshipRequest;
import com.progize.tms.controllers.bucketrelationship.res.BucketRelationshipResponse;
import com.progize.tms.repository.entity.BucketRelationshipEntity;
import com.progize.tms.services.BucketRelationshipService;
import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/bucket-relationships")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class BucketRelationshipController {
  private final BucketRelationshipService relationshipService;
  private final BucketRelationshipTransformer transformer;

  @PostMapping
  public ResponseEntity<BucketRelationshipResponse> createRelationship(
      @Valid @RequestBody CreateBucketRelationshipRequest request) {
    BucketRelationshipEntity relationship =
        relationshipService.createRelationship(
            request.getRuleBucketId(),
            request.getLookupBucketId(),
            transformer.toJoinConditions(request.getJoinConditions()));
    return ResponseEntity.ok(transformer.toResponse(relationship));
  }

  @GetMapping("/rule-bucket/{ruleBucketId}")
  public ResponseEntity<List<BucketRelationshipResponse>> getRelationshipsForRuleBucket(
      @PathVariable Long ruleBucketId) {
    List<BucketRelationshipEntity> relationships =
        relationshipService.getRelationshipsForRuleBucket(ruleBucketId);
    return ResponseEntity.ok(
        relationships.stream().map(transformer::toResponse).collect(Collectors.toList()));
  }

  @DeleteMapping("/{relationshipId}")
  public ResponseEntity<Void> deleteRelationship(@PathVariable Long relationshipId) {
    relationshipService.deleteRelationship(relationshipId);
    return ResponseEntity.noContent().build();
  }
}
