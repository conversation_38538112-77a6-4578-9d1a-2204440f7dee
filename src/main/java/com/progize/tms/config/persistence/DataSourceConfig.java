package com.progize.tms.config.persistence;

import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class DataSourceConfig {

  @Primary
  @Bean(name = "mysqlDataSource")
  @ConfigurationProperties(prefix = "spring.datasource.mysql")
  public DataSource mysqlDataSource() {
    return DataSourceBuilder.create().build();
  }

  @Bean(name = "clickhouseDataSource")
  @ConfigurationProperties(prefix = "spring.datasource.clickhouse")
  public DataSource clickhouseDataSource() {
    return DataSourceBuilder.create().build();
  }

  @Bean(name = "clickhouseJdbcTemplate")
  public JdbcTemplate clickhouseJdbcTemplate(
      @Qualifier("clickhouseDataSource") DataSource dataSource) {
    return new JdbcTemplate(dataSource);
  }
}
