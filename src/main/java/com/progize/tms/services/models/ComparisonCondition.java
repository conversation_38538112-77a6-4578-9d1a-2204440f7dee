package com.progize.tms.services.models;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Represents a comparison between two conditions. This allows for temporal comparisons like "if the
 * average transaction amount in the last 7 days is 2x higher than the 7 days before that", but can
 * also be used for other types of comparisons.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ComparisonCondition extends Condition {
  private Condition left;
  private String operator;
  private Condition right;
  private Double multiplier;

  public ComparisonCondition() {
    this.setType("COMPARISON");
  }
}
