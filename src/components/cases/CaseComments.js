import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import { Person as PersonIcon } from '@mui/icons-material';
import CaseService from '../../services/CaseService';

/**
 * Component for displaying comments on a case
 */
const CaseComments = ({ caseId, refreshKey }) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch comments when the component mounts or refreshKey changes
  useEffect(() => {
    const fetchComments = async () => {
      setLoading(true);
      try {
        const data = await CaseService.getComments(caseId);
        setComments(data);
        setError(null);
      } catch (err) {
        setError('Failed to fetch comments. Please try again later.');
        setComments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchComments();
  }, [caseId, refreshKey]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  // Render empty state
  if (comments.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">No comments yet.</Typography>
      </Paper>
    );
  }

  // Render comments
  return (
    <Paper>
      <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
        {comments.map((comment, index) => (
          <React.Fragment key={comment.id}>
            <ListItem alignItems="flex-start">
              <ListItemAvatar>
                <Avatar>
                  <PersonIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography component="span" variant="subtitle1">
                      {comment.userId}
                    </Typography>
                    <Typography component="span" variant="body2" color="text.secondary">
                      {formatDate(comment.createdAt)}
                    </Typography>
                  </Box>
                }
                secondary={
                  <Typography
                    component="span"
                    variant="body1"
                    color="text.primary"
                    sx={{ display: 'block', mt: 1 }}
                  >
                    {comment.commentText}
                  </Typography>
                }
              />
            </ListItem>
            {index < comments.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))}
      </List>
    </Paper>
  );
};

export default CaseComments;
