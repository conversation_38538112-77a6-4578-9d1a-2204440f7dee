package com.progize.tms.controllers.databuckets.res;

import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/** Response DTO for a data bucket. */
@Getter
@Setter
public class DataBucketResponse {
  private Long id;
  private String name;
  private String description;
  private String bucketType;
  private String tableName;
  private LocalDateTime createdAt;
  private Long dataSourceId;
  private String dataSourceName;
  private List<DataBucketVariableResponse> variables;
}
