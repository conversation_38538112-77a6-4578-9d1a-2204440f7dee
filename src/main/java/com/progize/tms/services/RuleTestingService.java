package com.progize.tms.services;

import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.services.models.Condition;
import com.progize.tms.services.models.RuleTestingBO;
import com.progize.tms.services.models.RuleTestingResultBO;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** Service for testing rules with sample data. */
@Service
@RequiredArgsConstructor
@Slf4j
public class RuleTestingService {

  private final RuleExpressionBuilder ruleExpressionBuilder;
  private final OperatorRegistry operatorRegistry;
  private final RuleEvaluationService ruleEvaluationService;
  private final DataBucketService dataBucketService;

  /**
   * Test a rule with the provided sample data.
   *
   * @param testingBO The business object containing the rule and sample data
   * @return The test result
   */
  public RuleTestingResultBO testRule(RuleTestingBO testingBO) {
    RuleTestingResultBO result = new RuleTestingResultBO();
    List<RuleTestingResultBO.Issue> issues = new ArrayList<>();

    try {
      // Generate the MVEL expression
      long startTime = System.nanoTime();
      String mvelExpression =
          ruleExpressionBuilder.buildMvelExpression(
              testingBO.getRuleCondition(), testingBO.getBucketId());
      result.setGeneratedExpression(mvelExpression);

      // Use the sample data map directly as the context
      final Map<String, Object> context = new HashMap<>();

      // Convert sample data to appropriate types based on bucket variables if available
      if (testingBO.getBucketId() != null && testingBO.getSampleData() != null) {
        context.putAll(
            convertSampleDataToCorrectTypes(testingBO.getSampleData(), testingBO.getBucketId()));
      } else if (testingBO.getSampleData() != null) {
        context.putAll(testingBO.getSampleData());
      }

      // Add variables based on bucket if available
      if (testingBO.getBucketId() != null) {
        // Get variables for the specific bucket
        dataBucketService
            .getVariables(testingBO.getBucketId())
            .forEach(
                variable -> {
                  // Add default values for any variables not in the sample data
                  if (!context.containsKey(variable.getName())) {
                    context.put(
                        variable.getName(),
                        getDefaultValueForType(variable.getDataType().getCode()));
                  }
                });
      }

      // Create a trace collector if debug info is requested
      List<RuleTestingResultBO.EvaluationStep> evaluationTrace = new ArrayList<>();
      if (testingBO.getOptions().isIncludeDebugInfo()) {
        // For now we'll just capture the final result
        // In a full implementation, you'd add MVEL debugging/tracing capabilities
        evaluationTrace.add(createEvaluationStep(1, mvelExpression, null));
      }

      // Evaluate the expression using the unified RuleEvaluationService
      boolean passed = false;
      try {
        // Extract customer ID from sample data or use a default
        String customerId = (String) context.getOrDefault("customerId", "UNKNOWN");

        // Use the bucket-aware RuleEvaluationService with bucketId
        passed =
            ruleEvaluationService.evaluateRule(
                testingBO.getRuleCondition(),
                customerId,
                context,
                testingBO.getOptions().isUseRealAggregations(),
                testingBO.getBucketId());

        // Record the result in the trace
        if (testingBO.getOptions().isIncludeDebugInfo() && !evaluationTrace.isEmpty()) {
          evaluationTrace.get(0).setValue(passed);
        }
      } catch (Exception e) {
        log.error("Error evaluating rule: {}", e.getMessage(), e);
        issues.add(
            createErrorIssue(
                "EVALUATION",
                "Error evaluating rule: " + e.getMessage(),
                mvelExpression,
                "Check the rule syntax and variable references"));
      }

      // Set the result
      result.setPassed(passed);

      // Set up debug info if requested
      if (testingBO.getOptions().isIncludeDebugInfo()) {
        RuleTestingResultBO.DebugInfo debugInfo = new RuleTestingResultBO.DebugInfo();
        debugInfo.setContext(context);
        debugInfo.setEvaluationTrace(evaluationTrace);
        result.setDebugInfo(debugInfo);
      }

      // Calculate execution time
      long endTime = System.nanoTime();
      long executionTimeMs = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
      result.setEvaluationTime(executionTimeMs);

      // Create analysis result if any analysis is requested
      if (testingBO.getOptions().isValidateTypes()
          || testingBO.getOptions().isValidateSyntax()
          || testingBO.getOptions().isCheckPerformance()) {

        RuleTestingResultBO.AnalysisResult analysis = new RuleTestingResultBO.AnalysisResult();

        // Syntax validation
        if (testingBO.getOptions().isValidateSyntax()) {
          // If we got this far without errors in expression generation, syntax is valid
          analysis.setSyntaxValid(true);
        }

        // Type validation
        if (testingBO.getOptions().isValidateTypes()) {
          analysis.setTypeCompatibility(validateTypes(testingBO.getRuleCondition(), issues));
        }

        // Performance analysis
        if (testingBO.getOptions().isCheckPerformance()) {
          analysis.setPerformance(analyzePerformance(testingBO.getRuleCondition(), mvelExpression));
        }

        result.setAnalysis(analysis);
      }

      // Generate explanation
      result.setExplanation(buildExplanation(testingBO.getRuleCondition(), passed));

      // Set any issues
      result.setIssues(issues);

    } catch (Exception e) {
      log.error("Error testing rule: {}", e.getMessage(), e);
      issues.add(
          createErrorIssue(
              "GENERAL",
              "Error testing rule: " + e.getMessage(),
              null,
              "Check the rule condition and sample data"));
      result.setIssues(issues);
      result.setPassed(false);
    }

    return result;
  }

  /**
   * Converts sample data values to their correct types based on bucket variable definitions.
   *
   * @param sampleData The raw sample data map
   * @param bucketId The ID of the bucket containing variable definitions
   * @return A new map with values converted to their proper types
   */
  private Map<String, Object> convertSampleDataToCorrectTypes(
      Map<String, Object> sampleData, Long bucketId) {
    Map<String, Object> convertedData = new HashMap<>();

    try {
      // Create a map of variable name to data type for quick lookup
      Map<String, String> variableTypes = new HashMap<>();
      List<DataBucketVariableEntity> bucketVariables = dataBucketService.getVariables(bucketId);

      for (DataBucketVariableEntity variable : bucketVariables) {
        variableTypes.put(variable.getName(), variable.getDataType().getCode());
      }

      // Convert each sample data value based on its corresponding variable type
      for (Map.Entry<String, Object> entry : sampleData.entrySet()) {
        String variableName = entry.getKey();
        Object rawValue = entry.getValue();

        // Skip null values
        if (rawValue == null) {
          convertedData.put(variableName, null);
          continue;
        }

        // Get the expected type for this variable
        String expectedType = variableTypes.get(variableName);

        // If we don't have type information, keep the original value
        if (expectedType == null) {
          convertedData.put(variableName, rawValue);
          continue;
        }

        // Convert the value based on the expected type
        Object convertedValue = convertValueToType(rawValue, expectedType);
        convertedData.put(variableName, convertedValue);

        log.debug(
            "Converted variable '{}' from {} to {} ({})",
            variableName,
            rawValue.getClass().getSimpleName(),
            convertedValue.getClass().getSimpleName(),
            expectedType);
      }

      // Add any values that weren't in the variable definitions
      for (Map.Entry<String, Object> entry : sampleData.entrySet()) {
        if (!convertedData.containsKey(entry.getKey())) {
          convertedData.put(entry.getKey(), entry.getValue());
        }
      }

    } catch (Exception e) {
      log.warn("Error converting sample data types: {}", e.getMessage());
      // Fall back to original data if conversion fails
      return sampleData;
    }

    return convertedData;
  }

  /**
   * Converts a value to the specified type.
   *
   * @param value The value to convert
   * @param type The target type
   * @return The converted value
   */
  private Object convertValueToType(Object value, String type) {
    if (value == null || type == null) {
      return value;
    }

    // Handle string conversion first as it's the most common source type
    if (value instanceof String) {
      String stringValue = (String) value;

      switch (type.toLowerCase()) {
        case "string":
          return stringValue;
        case "number":
        case "bigdecimal":
          try {
            return new BigDecimal(stringValue);
          } catch (NumberFormatException e) {
            log.warn("Failed to convert '{}' to BigDecimal", stringValue);
            return BigDecimal.ZERO;
          }
        case "integer":
        case "int":
          try {
            return Integer.parseInt(stringValue);
          } catch (NumberFormatException e) {
            log.warn("Failed to convert '{}' to Integer", stringValue);
            return 0;
          }
        case "long":
          try {
            return Long.parseLong(stringValue);
          } catch (NumberFormatException e) {
            log.warn("Failed to convert '{}' to Long", stringValue);
            return 0L;
          }
        case "double":
        case "float":
          try {
            return Double.parseDouble(stringValue);
          } catch (NumberFormatException e) {
            log.warn("Failed to convert '{}' to Double", stringValue);
            return 0.0;
          }
        case "boolean":
          return Boolean.parseBoolean(stringValue);
        case "date":
          try {
            // Try ISO format first
            return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(stringValue);
          } catch (ParseException e1) {
            try {
              // Try simple date format
              return new SimpleDateFormat("yyyy-MM-dd").parse(stringValue);
            } catch (ParseException e2) {
              log.warn("Failed to convert '{}' to Date", stringValue);
              return new Date();
            }
          }
        default:
          return value;
      }
    }

    // Handle numeric types
    if (value instanceof Number) {
      Number numValue = (Number) value;

      switch (type.toLowerCase()) {
        case "string":
          return numValue.toString();
        case "number":
        case "bigdecimal":
          return new BigDecimal(numValue.toString());
        case "integer":
        case "int":
          return numValue.intValue();
        case "long":
          return numValue.longValue();
        case "double":
          return numValue.doubleValue();
        case "float":
          return numValue.floatValue();
        case "boolean":
          return numValue.intValue() != 0;
        default:
          return value;
      }
    }

    // For other types, just return the original value
    return value;
  }

  /** Create an evaluation step for tracing. */
  private RuleTestingResultBO.EvaluationStep createEvaluationStep(
      int step, String expression, Object value) {
    RuleTestingResultBO.EvaluationStep evaluationStep = new RuleTestingResultBO.EvaluationStep();
    evaluationStep.setStep(step);
    evaluationStep.setExpression(expression);
    evaluationStep.setValue(value);
    return evaluationStep;
  }

  /** Create an issue with error severity. */
  private RuleTestingResultBO.Issue createErrorIssue(
      String category, String message, String location, String suggestion) {
    RuleTestingResultBO.Issue issue = new RuleTestingResultBO.Issue();
    issue.setType("ERROR");
    issue.setCategory(category);
    issue.setMessage(message);
    issue.setLocation(location);
    issue.setSuggestion(suggestion);
    return issue;
  }

  /** Generate a human-readable explanation of the rule evaluation. */
  private String buildExplanation(Condition condition, boolean passed) {
    StringBuilder explanation = new StringBuilder();
    explanation.append("The rule ");
    explanation.append(passed ? "matched" : "did not match");
    explanation.append(" the provided sample data.");

    // In a real implementation, you'd provide more details about which parts of the condition
    // matched/failed

    return explanation.toString();
  }

  /** Validate type compatibility in the rule condition. */
  private List<RuleTestingResultBO.TypeCompatibility> validateTypes(
      Condition condition, List<RuleTestingResultBO.Issue> issues) {
    List<RuleTestingResultBO.TypeCompatibility> typeCompats = new ArrayList<>();

    // In a full implementation, you would walk through the condition tree
    // and validate that each field and operator are type-compatible.

    // Add a sample type compatibility check
    if (condition != null && condition.getType() != null) {
      String conditionType = condition.getType();

      if ("SIMPLE".equals(conditionType)
          && condition instanceof com.progize.tms.services.models.SimpleCondition) {
        com.progize.tms.services.models.SimpleCondition simpleCondition =
            (com.progize.tms.services.models.SimpleCondition) condition;

        if (simpleCondition.getField() != null && simpleCondition.getOperator() != null) {
          RuleTestingResultBO.TypeCompatibility compatibility =
              new RuleTestingResultBO.TypeCompatibility();
          compatibility.setField(simpleCondition.getField());
          compatibility.setOperatorId(simpleCondition.getOperator());

          // Get the variable to determine its type
          String fieldType = "string"; // Default type if we can't determine it

          // Check if operator is in the supported list for this field type
          List<String> supportedOperators =
              operatorRegistry.getDefaultSupportedOperators(fieldType);
          boolean operatorValid = supportedOperators.contains(simpleCondition.getOperator());

          compatibility.setCompatible(operatorValid);
          if (!operatorValid) {
            compatibility.setReason(
                "Operator '"
                    + simpleCondition.getOperator()
                    + "' is not valid for field type '"
                    + fieldType
                    + "'");
            issues.add(
                createErrorIssue(
                    "TYPE_COMPATIBILITY",
                    "Invalid operator for field type",
                    simpleCondition.getField() + " " + simpleCondition.getOperator(),
                    "Use one of: " + String.join(", ", supportedOperators)));
          }

          typeCompats.add(compatibility);
        }
      }
    }

    return typeCompats;
  }

  /** Analyze the performance characteristics of the rule condition. */
  private RuleTestingResultBO.PerformanceAnalysis analyzePerformance(
      Condition condition, String mvelExpression) {
    RuleTestingResultBO.PerformanceAnalysis perfAnalysis =
        new RuleTestingResultBO.PerformanceAnalysis();

    // Simple complexity determination based on expression length and complexity
    if (mvelExpression.length() < 100
        && !mvelExpression.contains("&&")
        && !mvelExpression.contains("||")) {
      perfAnalysis.setComplexity("low");
    } else if (mvelExpression.length() < 500 && countLogicalOperators(mvelExpression) < 5) {
      perfAnalysis.setComplexity("medium");
    } else {
      perfAnalysis.setComplexity("high");
      perfAnalysis
          .getRecommendations()
          .add("Consider simplifying the rule by breaking it into smaller rules");
    }

    // Check for potential performance issues
    if (mvelExpression.contains("contains")) {
      perfAnalysis
          .getRecommendations()
          .add(
              "String 'contains' operations can be expensive for large strings; consider using more efficient methods if possible");
    }

    if (countLogicalOperators(mvelExpression) > 10) {
      perfAnalysis
          .getRecommendations()
          .add(
              "Rules with many logical operators may be difficult to maintain; consider simplifying");
    }

    return perfAnalysis;
  }

  /** Count the number of logical operators in an expression. */
  private int countLogicalOperators(String expression) {
    int count = 0;
    count += countOccurrences(expression, "&&");
    count += countOccurrences(expression, "||");
    count += countOccurrences(expression, "==");
    count += countOccurrences(expression, "!=");
    count += countOccurrences(expression, ">");
    count += countOccurrences(expression, "<");
    count += countOccurrences(expression, ">=");
    count += countOccurrences(expression, "<=");
    return count;
  }

  /** Count occurrences of a substring in a string. */
  private int countOccurrences(String str, String subStr) {
    int count = 0;
    int lastIndex = 0;

    while (lastIndex != -1) {
      lastIndex = str.indexOf(subStr, lastIndex);
      if (lastIndex != -1) {
        count++;
        lastIndex += subStr.length();
      }
    }

    return count;
  }

  /** Get a default value for a given type for testing purposes. */
  private Object getDefaultValueForType(String type) {
    if (type == null) {
      return null;
    }

    switch (type.toLowerCase()) {
      case "string":
        return "";
      case "number":
      case "bigdecimal":
        return new BigDecimal("0");
      case "integer":
      case "int":
        return 0;
      case "long":
        return 0L;
      case "double":
      case "float":
        return 0.0;
      case "boolean":
        return false;
      case "date":
        return new java.util.Date();
      default:
        return null;
    }
  }
}
