import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Tooltip,
  Skeleton,
  Alert,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Grid,
  useTheme,
  TablePagination
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  SmartToy as SmartToyIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import ComplianceWorkflowService from '../../services/ComplianceWorkflowService';

/**
 * Professional compliance task list with modern design and filtering
 */
const ComplianceTaskList = () => {
  const [tasks, setTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterTab, setFilterTab] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const navigate = useNavigate();
  const theme = useTheme();

  // Sample task data with compliance-specific fields
  const sampleTasks = [
    {
      id: 'TXN-001',
      customerName: 'Acme Corporation',
      ruleName: 'High Transaction Amount',
      transactionAmount: 125000,
      priority: 'High',
      status: 'Pending',
      slaTimeLeft: '2h 15m',
      assignedTo: 'current_user',
      isCompleted: false,
      aiInvestigated: true,
      createTime: '2024-01-15T10:30:00Z',
      dueDate: '2024-01-16T18:00:00Z'
    },
    {
      id: 'TXN-002',
      customerName: 'Global Investments Ltd',
      ruleName: 'Suspicious Pattern Detection',
      transactionAmount: 75000,
      priority: 'Medium',
      status: 'In Review',
      slaTimeLeft: '1d 4h',
      assignedTo: 'current_user',
      isCompleted: false,
      aiInvestigated: false,
      createTime: '2024-01-15T09:15:00Z',
      dueDate: '2024-01-17T12:00:00Z'
    },
    {
      id: 'TXN-003',
      customerName: 'Tech Innovations Inc',
      ruleName: 'Cross-Border Transaction',
      transactionAmount: 50000,
      priority: 'Low',
      status: 'Pending',
      slaTimeLeft: 'Overdue',
      assignedTo: null,
      isCompleted: false,
      aiInvestigated: false,
      createTime: '2024-01-14T14:20:00Z',
      dueDate: '2024-01-15T16:00:00Z'
    },
    {
      id: 'TXN-004',
      customerName: 'Financial Services Co',
      ruleName: 'PEP Screening Alert',
      transactionAmount: 200000,
      priority: 'High',
      status: 'Completed',
      slaTimeLeft: 'Completed',
      assignedTo: 'current_user',
      isCompleted: true,
      aiInvestigated: true,
      createTime: '2024-01-13T11:45:00Z',
      dueDate: '2024-01-14T17:00:00Z'
    }
  ];

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setLoading(true);
        // For now, use sample data. Later replace with actual API call
        // const taskData = await ComplianceWorkflowService.getComplianceTasks();
        setTasks(sampleTasks);
        setError(null);
      } catch (err) {
        console.error('Error fetching compliance tasks:', err);
        setError('Failed to load compliance tasks. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
  }, []);

  // Filter tasks based on search query and tab selection
  useEffect(() => {
    let filtered = [...tasks];

    // Apply tab filter
    switch (filterTab) {
      case 1: // Assigned to Me
        filtered = filtered.filter(task => task.assignedTo === 'current_user');
        break;
      case 2: // Unassigned
        filtered = filtered.filter(task => !task.assignedTo);
        break;
      case 3: // Completed
        filtered = filtered.filter(task => task.isCompleted);
        break;
      default: // All
        break;
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(task =>
        task.id.toLowerCase().includes(query) ||
        task.customerName.toLowerCase().includes(query) ||
        task.ruleName.toLowerCase().includes(query)
      );
    }

    setFilteredTasks(filtered);
    setPage(0); // Reset to first page when filters change
  }, [tasks, searchQuery, filterTab]);

  // Event handlers
  const handleViewTask = (taskId) => {
    navigate(`/compliance/tasks/${taskId}`);
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleTabChange = (event, newValue) => {
    setFilterTab(newValue);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Helper functions
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getPriorityColor = (priority) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.grey[500];
      default:
        return theme.palette.grey[400];
    }
  };

  const getPriorityBgColor = (priority) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return theme.palette.error.light + '20';
      case 'medium':
        return theme.palette.warning.light + '20';
      case 'low':
        return theme.palette.grey[100];
      default:
        return theme.palette.grey[50];
    }
  };

  const getStatusColor = (status) => {
    switch(status?.toLowerCase()) {
      case 'completed':
        return theme.palette.success.main;
      case 'in review':
        return theme.palette.info.main;
      case 'pending':
        return theme.palette.warning.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getSlaColor = (slaTimeLeft) => {
    if (slaTimeLeft === 'Overdue') return theme.palette.error.main;
    if (slaTimeLeft === 'Completed') return theme.palette.success.main;
    if (slaTimeLeft.includes('h') && !slaTimeLeft.includes('d')) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  // Loading state
  if (loading) {
    return (
      <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
        <Skeleton variant="text" width={200} height={40} sx={{ mb: 2 }} />
        <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2, borderRadius: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={400} sx={{ borderRadius: 1 }} />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Box>
    );
  }

  // Get paginated tasks
  const paginatedTasks = filteredTasks.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
      {/* Header */}
      <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, fontSize: '1.5rem' }}>
        Tasks Inbox
      </Typography>

      {/* Search and Filters */}
      <Paper
        elevation={0}
        sx={{
          mb: 2,
          p: 2,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search by Task ID, Customer, or Rule..."
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: theme.palette.text.secondary, fontSize: '1.2rem' }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  fontSize: '0.875rem',
                  '& fieldset': {
                    borderColor: theme.palette.grey[300]
                  }
                }
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
              <Tabs
                value={filterTab}
                onChange={handleTabChange}
                sx={{
                  minHeight: 36,
                  '& .MuiTab-root': {
                    minHeight: 36,
                    fontSize: '0.8rem',
                    fontWeight: 500,
                    textTransform: 'none',
                    color: theme.palette.text.secondary,
                    '&.Mui-selected': {
                      color: theme.palette.primary.main,
                      fontWeight: 600
                    }
                  },
                  '& .MuiTabs-indicator': {
                    height: 2
                  }
                }}
              >
                <Tab label="All" />
                <Tab label="Assigned to Me" />
                <Tab label="Unassigned" />
                <Tab label="Completed" />
              </Tabs>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Task Table */}
      <Paper
        elevation={0}
        sx={{
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
          overflow: 'hidden'
        }}
      >
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ bgcolor: theme.palette.grey[50] }}>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Task ID</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Customer</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Rule</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Amount</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Priority</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>SLA</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedTasks.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      No tasks found matching your criteria
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedTasks.map((task) => (
                  <TableRow
                    key={task.id}
                    onClick={() => handleViewTask(task.id)}
                    sx={{
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: theme.palette.grey[50]
                      },
                      ...(task.priority === 'High' && {
                        borderLeft: `3px solid ${theme.palette.error.main}`
                      }),
                      ...(task.slaTimeLeft === 'Overdue' && {
                        bgcolor: theme.palette.error.light + '10'
                      })
                    }}
                  >
                    <TableCell sx={{ py: 1.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.85rem' }}>
                          {task.id}
                        </Typography>
                        {task.aiInvestigated && (
                          <Tooltip title="AI Investigated">
                            <SmartToyIcon
                              sx={{
                                fontSize: '0.9rem',
                                color: theme.palette.primary.main
                              }}
                            />
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                        {task.customerName}
                      </Typography>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Typography variant="body2" sx={{ fontSize: '0.85rem', maxWidth: 150 }} noWrap>
                        {task.ruleName}
                      </Typography>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.85rem' }}>
                        {formatCurrency(task.transactionAmount)}
                      </Typography>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box
                        sx={{
                          display: 'inline-block',
                          px: 1,
                          py: 0.25,
                          bgcolor: getPriorityBgColor(task.priority),
                          color: getPriorityColor(task.priority),
                          borderRadius: 0.5,
                          fontSize: '0.7rem',
                          fontWeight: 600,
                          textTransform: 'uppercase',
                          letterSpacing: 0.5
                        }}
                      >
                        {task.priority}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box
                        sx={{
                          display: 'inline-block',
                          px: 1,
                          py: 0.25,
                          bgcolor: getStatusColor(task.status) + '20',
                          color: getStatusColor(task.status),
                          borderRadius: 0.5,
                          fontSize: '0.7rem',
                          fontWeight: 500
                        }}
                      >
                        {task.status}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box
                        sx={{
                          display: 'inline-block',
                          px: 1,
                          py: 0.25,
                          bgcolor: getSlaColor(task.slaTimeLeft) + '20',
                          color: getSlaColor(task.slaTimeLeft),
                          borderRadius: 0.5,
                          fontSize: '0.7rem',
                          fontWeight: 500
                        }}
                      >
                        {task.slaTimeLeft}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewTask(task.id);
                          }}
                          sx={{
                            color: theme.palette.primary.main,
                            '&:hover': { bgcolor: theme.palette.primary.light + '20' }
                          }}
                        >
                          <VisibilityIcon sx={{ fontSize: '1rem' }} />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          component="div"
          count={filteredTasks.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25, 50]}
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            '& .MuiTablePagination-toolbar': {
              fontSize: '0.8rem'
            },
            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
              fontSize: '0.8rem'
            }
          }}
        />
      </Paper>
    </Box>
  );
};

export default ComplianceTaskList;
