-- Audit Tables
CREATE TABLE revinfo (
    rev INTEGER NOT NULL AUTO_INCREMENT,
    revtstmp BIGINT,
    PRIMARY KEY (rev)
);

-- Core Tables
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGIN<PERSON>,
    updated_by BIGIN<PERSON>,
    CONSTRAINT fk_users_created_by <PERSON>OR<PERSON><PERSON><PERSON>EY (created_by) REFERENCES users(id),
    CONSTRAINT fk_users_updated_by FOREIG<PERSON> KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE refresh_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_refresh_tokens_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_refresh_tokens_updated_by FOREIGN KEY (updated_by) REFERENCES users(id),
    CONSTRAINT fk_refresh_tokens_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Role and Permission Tables
CREATE TABLE permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_permissions_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_permissions_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_roles_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_roles_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE roles_permissions (
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    CONSTRAINT fk_roles_permissions_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    CONSTRAINT fk_roles_permissions_permission FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

CREATE TABLE users_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    CONSTRAINT fk_users_roles_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_users_roles_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Data Type and Operator Tables
CREATE TABLE data_types (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    metadata JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_data_types_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_data_types_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE operators (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    symbol VARCHAR(20),
    metadata JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_operators_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_operators_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE data_type_supported_operators (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_type_id BIGINT NOT NULL,
    operator_id BIGINT NOT NULL,
    display_order INT NOT NULL DEFAULT 0,
    config JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_data_type_supported_operators_data_type FOREIGN KEY (data_type_id) REFERENCES data_types(id),
    CONSTRAINT fk_data_type_supported_operators_operator FOREIGN KEY (operator_id) REFERENCES operators(id),
    CONSTRAINT fk_data_type_supported_operators_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_data_type_supported_operators_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE operator_expressions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    operator_id BIGINT NOT NULL,
    data_type_id BIGINT,
    expression_template VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_operator_expressions_operator FOREIGN KEY (operator_id) REFERENCES operators(id),
    CONSTRAINT fk_operator_expressions_data_type FOREIGN KEY (data_type_id) REFERENCES data_types(id),
    CONSTRAINT fk_operator_expressions_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_operator_expressions_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Rule Engine Tables
CREATE TABLE rule_variable_source_tables (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_rule_variable_source_tables_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_rule_variable_source_tables_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE rule_variables (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    source_table_id BIGINT NOT NULL,
    data_type_id BIGINT NOT NULL,
    metadata JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_rule_variables_source_table FOREIGN KEY (source_table_id) REFERENCES rule_variable_source_tables(id),
    CONSTRAINT fk_rule_variables_data_type FOREIGN KEY (data_type_id) REFERENCES data_types(id),
    CONSTRAINT fk_rule_variables_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_rule_variables_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Data Bucket Tables
CREATE TABLE data_bucket (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_data_bucket_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_data_bucket_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE data_bucket_variable (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_bucket_id BIGINT NOT NULL,
    code VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    table_name VARCHAR(255) NOT NULL,
    column_name VARCHAR(255) NOT NULL,
    data_type_id BIGINT NOT NULL,
    aggregatable bit NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_data_bucket_variable_bucket FOREIGN KEY (data_bucket_id) REFERENCES data_bucket(id),
    CONSTRAINT fk_data_bucket_variable_data_type FOREIGN KEY (data_type_id) REFERENCES data_types(id),
    CONSTRAINT fk_data_bucket_variable_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_data_bucket_variable_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Rules Tables
CREATE TABLE rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    expression TEXT NOT NULL,
    flag_reason VARCHAR(255),
    active BOOLEAN NOT NULL DEFAULT TRUE,
    priority INTEGER NOT NULL DEFAULT 0,
    rule_json TEXT,
    data_bucket_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_rules_data_bucket FOREIGN KEY (data_bucket_id) REFERENCES data_bucket(id),
    CONSTRAINT fk_rules_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_rules_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE rules_aud (
    id BIGINT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,
    name VARCHAR(255),
    description TEXT,
    expression TEXT,
    flag_reason VARCHAR(255),
    active BOOLEAN,
    priority INTEGER,
    rule_json TEXT,
    data_bucket_id BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    PRIMARY KEY (id, rev),
    CONSTRAINT fk_rules_aud_rev FOREIGN KEY (rev) REFERENCES revinfo(rev)
);

-- Transaction Tables
CREATE TABLE transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    fund_id VARCHAR(255),
    fund_name VARCHAR(255),
    fund_short_name VARCHAR(100),
    fund_bank_account VARCHAR(100),
    trans_desc TEXT,
    transaction VARCHAR(255),
    customer_id VARCHAR(100),
    customer_name VARCHAR(255),
    customer_bank_account VARCHAR(100),
    customer_bank_code VARCHAR(50),
    customer_bank_name VARCHAR(255),
    customer_address TEXT,
    customer_phone VARCHAR(50),
    customer_email VARCHAR(255),
    amount DECIMAL(20,2),
    currency VARCHAR(3),
    transaction_date TIMESTAMP,
    value_date TIMESTAMP,
    status VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_transactions_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_transactions_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Workflow Tables
CREATE TABLE tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    CONSTRAINT fk_tags_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_tags_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);


-- Default Data
INSERT INTO users (username, email, password, first_name, last_name, enabled)
VALUES ('admin', '<EMAIL>', '$2a$10$xQ03w9Lnbko1lxV6DYIsxOMYWZRKWsLJoOy7qsCcl1WexbKvc4fj2', 'Admin', 'User', true);

INSERT INTO roles (name, description, created_by)
VALUES 
('ADMIN', 'Administrator with full access', (SELECT id FROM users WHERE username = 'admin')),
('USER', 'Regular user with limited access', (SELECT id FROM users WHERE username = 'admin'));

INSERT INTO permissions (name, description, created_by)
VALUES 
('user:read', 'Permission to read user data', (SELECT id FROM users WHERE username = 'admin')),
('user:write', 'Permission to create and update user data', (SELECT id FROM users WHERE username = 'admin')),
('user:delete', 'Permission to delete users', (SELECT id FROM users WHERE username = 'admin')),
('role:read', 'Permission to read role data', (SELECT id FROM users WHERE username = 'admin')),
('role:write', 'Permission to create and update role data', (SELECT id FROM users WHERE username = 'admin')),
('role:delete', 'Permission to delete roles', (SELECT id FROM users WHERE username = 'admin')),
('permission:read', 'Permission to read permission data', (SELECT id FROM users WHERE username = 'admin')),
('permission:write', 'Permission to create and update permission data', (SELECT id FROM users WHERE username = 'admin')),
('permission:delete', 'Permission to delete permissions', (SELECT id FROM users WHERE username = 'admin'));

-- Assign all permissions to ADMIN role
INSERT INTO roles_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'ADMIN'),
    id
FROM permissions;

-- Assign basic permissions to USER role
INSERT INTO roles_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'USER'),
    id
FROM permissions 
WHERE name IN ('user:read', 'role:read', 'permission:read');

-- Assign ADMIN role to admin user
INSERT INTO users_roles (user_id, role_id)
SELECT 
    (SELECT id FROM users WHERE username = 'admin'),
    (SELECT id FROM roles WHERE name = 'ADMIN');
