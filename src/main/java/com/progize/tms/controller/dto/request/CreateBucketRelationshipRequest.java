package com.progize.tms.controller.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateBucketRelationshipRequest {
  @NotNull(message = "Rule bucket ID is required")
  private Long ruleBucketId;

  @NotNull(message = "Lookup bucket ID is required")
  private Long lookupBucketId;

  @NotEmpty(message = "At least one join condition is required")
  private List<@Valid JoinConditionRequest> joinConditions;

  @Getter
  @Setter
  public static class JoinConditionRequest {
    @NotNull(message = "Rule bucket variable ID is required")
    private Long ruleBucketVariableId;

    @NotNull(message = "Lookup bucket variable ID is required")
    private Long lookupBucketVariableId;
  }
}
