package com.progize.tms.services;

import com.progize.tms.services.models.AggregationRequirement;
import java.util.Map;
import java.util.Set;

/** Interface for providers of aggregation data used in rule evaluation. */
public interface AggregationProvider {
  /**
   * Get aggregation values for a specific customer.
   *
   * @param customerId The customer ID to get aggregations for
   * @param requirements The set of aggregation requirements
   * @return Map of aggregation keys to their calculated values
   */
  Map<String, Object> getAggregations(String customerId, Set<AggregationRequirement> requirements);

  /**
   * Get aggregation values for a specific customer with bucket context.
   *
   * @param customerId The customer ID to get aggregations for
   * @param requirements The set of aggregation requirements
   * @param bucketId The ID of the data bucket to use for variable context, or null to use global
   *     variables
   * @return Map of aggregation keys to their calculated values
   */
  default Map<String, Object> getAggregations(
      String customerId, Set<AggregationRequirement> requirements, Long bucketId) {
    // Default implementation falls back to the non-bucket-aware method
    return getAggregations(customerId, requirements);
  }
}
