import React, { useState } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Button,
    Grid,
    Switch,
    FormControlLabel,
    Chip,
    Avatar,
    Divider,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Stepper,
    Step,
    StepLabel,
    StepContent,
    Paper,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    ListItemSecondaryAction,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    useTheme,
    alpha
} from '@mui/material';
import {
    Settings as SettingsIcon,
    Key as KeyIcon,
    Timeline as TimelineIcon,
    Analytics as AnalyticsIcon,
    Security as SecurityIcon,
    DataObject as DataObjectIcon,
    CheckCircle as CheckCircleIcon,
    RadioButtonUnchecked as RadioButtonUncheckedIcon,
    ExpandMore as ExpandMoreIcon,
    Save as SaveIcon,
    Cancel as CancelIcon,
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as VisibilityIcon,
    VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme }) => ({
    borderRadius: 16,
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.12)',
    }
}));

const PropertyCard = styled(Card)(({ theme, selected }) => ({
    borderRadius: 12,
    border: selected 
        ? `2px solid ${theme.palette.primary.main}` 
        : `1px solid ${alpha(theme.palette.divider, 0.2)}`,
    backgroundColor: selected 
        ? alpha(theme.palette.primary.main, 0.05) 
        : theme.palette.background.paper,
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: theme.shadows[4],
        borderColor: theme.palette.primary.main,
    }
}));

const VariableTypeChip = styled(Chip)(({ theme, variant }) => {
    const colors = {
        primary: { bg: theme.palette.primary.main, color: 'white' },
        secondary: { bg: theme.palette.secondary.main, color: 'white' },
        success: { bg: theme.palette.success.main, color: 'white' },
        warning: { bg: theme.palette.warning.main, color: 'white' },
        info: { bg: theme.palette.info.main, color: 'white' },
        default: { bg: theme.palette.grey[200], color: theme.palette.text.primary }
    };
    
    const colorScheme = colors[variant] || colors.default;
    
    return {
        backgroundColor: colorScheme.bg,
        color: colorScheme.color,
        fontWeight: 600,
        borderRadius: 8,
        height: 32,
        '& .MuiChip-label': {
            fontSize: '0.875rem',
            padding: '0 12px',
        }
    };
});

const ModernVariableConfigurator = ({ 
    open, 
    onClose, 
    variables = [], 
    onSave 
}) => {
    const theme = useTheme();
    const [activeStep, setActiveStep] = useState(0);
    const [selectedVariables, setSelectedVariables] = useState([]);
    const [variableConfigs, setVariableConfigs] = useState({});

    const steps = [
        {
            label: 'Select Variables',
            description: 'Choose which variables to configure',
            icon: <DataObjectIcon />
        },
        {
            label: 'Configure Properties',
            description: 'Set basic properties for each variable',
            icon: <SettingsIcon />
        },
        {
            label: 'Entity Keys',
            description: 'Define primary, secondary, and date dimension keys',
            icon: <KeyIcon />
        },
        {
            label: 'Review & Save',
            description: 'Review your configuration and save',
            icon: <CheckCircleIcon />
        }
    ];

    const propertyTypes = [
        {
            id: 'unique',
            label: 'Unique Values',
            description: 'Ensures this field has unique values across records',
            icon: <SecurityIcon />,
            color: 'warning'
        },
        {
            id: 'aggregatable',
            label: 'Aggregatable',
            description: 'Can be used in SUM, AVG, COUNT functions',
            icon: <AnalyticsIcon />,
            color: 'info'
        },
        {
            id: 'indexed',
            label: 'Indexed',
            description: 'Creates database index for faster queries',
            icon: <TimelineIcon />,
            color: 'success'
        }
    ];

    const entityKeyTypes = [
        {
            id: 'isPrimaryEntityKey',
            label: 'Primary Entity Key',
            description: 'Main entity identifier (customerId, employeeId, etc.)',
            icon: <KeyIcon />,
            color: 'primary',
            exclusive: true
        },
        {
            id: 'isSecondaryEntityKey',
            label: 'Secondary Entity Key',
            description: 'Secondary entity identifier (transactionId, requestId, etc.)',
            icon: <KeyIcon />,
            color: 'secondary',
            exclusive: true
        },
        {
            id: 'isDateDimension',
            label: 'Date Dimension',
            description: 'Time-based field for temporal analysis',
            icon: <TimelineIcon />,
            color: 'success',
            exclusive: true
        }
    ];

    const handleVariableToggle = (variable) => {
        setSelectedVariables(prev => {
            const isSelected = prev.some(v => v.id === variable.id);
            if (isSelected) {
                return prev.filter(v => v.id !== variable.id);
            } else {
                return [...prev, variable];
            }
        });
    };

    const handlePropertyToggle = (variableId, propertyId) => {
        setVariableConfigs(prev => ({
            ...prev,
            [variableId]: {
                ...prev[variableId],
                [propertyId]: !prev[variableId]?.[propertyId]
            }
        }));
    };

    const handleEntityKeyToggle = (variableId, keyType) => {
        setVariableConfigs(prev => {
            const newConfigs = { ...prev };
            
            // If this is an exclusive key type, unset it for all other variables
            if (entityKeyTypes.find(t => t.id === keyType)?.exclusive) {
                Object.keys(newConfigs).forEach(id => {
                    if (newConfigs[id] && newConfigs[id][keyType]) {
                        newConfigs[id] = { ...newConfigs[id], [keyType]: false };
                    }
                });
            }
            
            // Toggle for current variable
            newConfigs[variableId] = {
                ...newConfigs[variableId],
                [keyType]: !newConfigs[variableId]?.[keyType]
            };
            
            return newConfigs;
        });
    };

    const renderStepContent = (step) => {
        switch (step) {
            case 0:
                return (
                    <Box>
                        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                            Select Variables to Configure
                        </Typography>
                        <Grid container spacing={2}>
                            {variables.map((variable) => {
                                const isSelected = selectedVariables.some(v => v.id === variable.id);
                                return (
                                    <Grid item xs={12} sm={6} md={4} key={variable.id}>
                                        <PropertyCard
                                            selected={isSelected}
                                            onClick={() => handleVariableToggle(variable)}
                                        >
                                            <CardContent sx={{ p: 2 }}>
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                                    <Avatar sx={{ width: 32, height: 32, mr: 2, bgcolor: 'primary.main' }}>
                                                        {variable.name.charAt(0).toUpperCase()}
                                                    </Avatar>
                                                    <Box sx={{ flexGrow: 1 }}>
                                                        <Typography variant="subtitle2" fontWeight="medium">
                                                            {variable.name}
                                                        </Typography>
                                                        <Typography variant="caption" color="text.secondary">
                                                            {variable.dataType}
                                                        </Typography>
                                                    </Box>
                                                    {isSelected ? (
                                                        <CheckCircleIcon color="primary" />
                                                    ) : (
                                                        <RadioButtonUncheckedIcon color="disabled" />
                                                    )}
                                                </Box>
                                            </CardContent>
                                        </PropertyCard>
                                    </Grid>
                                );
                            })}
                        </Grid>
                    </Box>
                );

            case 1:
                return (
                    <Box>
                        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                            Configure Basic Properties
                        </Typography>
                        {selectedVariables.map((variable) => (
                            <Accordion key={variable.id} sx={{ mb: 2, borderRadius: 2 }}>
                                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Avatar sx={{ width: 32, height: 32, mr: 2, bgcolor: 'primary.main' }}>
                                            {variable.name.charAt(0).toUpperCase()}
                                        </Avatar>
                                        <Typography variant="subtitle1" fontWeight="medium">
                                            {variable.name}
                                        </Typography>
                                    </Box>
                                </AccordionSummary>
                                <AccordionDetails>
                                    <Grid container spacing={2}>
                                        {propertyTypes.map((property) => (
                                            <Grid item xs={12} sm={4} key={property.id}>
                                                <PropertyCard
                                                    selected={variableConfigs[variable.id]?.[property.id]}
                                                    onClick={() => handlePropertyToggle(variable.id, property.id)}
                                                >
                                                    <CardContent sx={{ p: 2, textAlign: 'center' }}>
                                                        <Avatar sx={{ 
                                                            bgcolor: `${property.color}.main`, 
                                                            mx: 'auto', 
                                                            mb: 1,
                                                            width: 40,
                                                            height: 40
                                                        }}>
                                                            {property.icon}
                                                        </Avatar>
                                                        <Typography variant="subtitle2" fontWeight="medium" sx={{ mb: 1 }}>
                                                            {property.label}
                                                        </Typography>
                                                        <Typography variant="caption" color="text.secondary">
                                                            {property.description}
                                                        </Typography>
                                                    </CardContent>
                                                </PropertyCard>
                                            </Grid>
                                        ))}
                                    </Grid>
                                </AccordionDetails>
                            </Accordion>
                        ))}
                    </Box>
                );

            case 2:
                return (
                    <Box>
                        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                            Configure Entity Keys
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                            Only one variable can be assigned to each entity key type
                        </Typography>
                        
                        <Grid container spacing={3}>
                            {entityKeyTypes.map((keyType) => (
                                <Grid item xs={12} md={4} key={keyType.id}>
                                    <StyledCard>
                                        <CardContent sx={{ p: 3 }}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                <Avatar sx={{ bgcolor: `${keyType.color}.main`, mr: 2 }}>
                                                    {keyType.icon}
                                                </Avatar>
                                                <Box>
                                                    <Typography variant="subtitle1" fontWeight="medium">
                                                        {keyType.label}
                                                    </Typography>
                                                    <Typography variant="caption" color="text.secondary">
                                                        {keyType.description}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                            
                                            <Divider sx={{ my: 2 }} />
                                            
                                            <List dense>
                                                {selectedVariables.map((variable) => (
                                                    <ListItem key={variable.id} sx={{ px: 0 }}>
                                                        <ListItemIcon sx={{ minWidth: 36 }}>
                                                            <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                                                                {variable.name.charAt(0).toUpperCase()}
                                                            </Avatar>
                                                        </ListItemIcon>
                                                        <ListItemText 
                                                            primary={variable.name}
                                                            primaryTypographyProps={{ variant: 'body2' }}
                                                        />
                                                        <ListItemSecondaryAction>
                                                            <Switch
                                                                checked={variableConfigs[variable.id]?.[keyType.id] || false}
                                                                onChange={() => handleEntityKeyToggle(variable.id, keyType.id)}
                                                                color={keyType.color}
                                                                size="small"
                                                            />
                                                        </ListItemSecondaryAction>
                                                    </ListItem>
                                                ))}
                                            </List>
                                        </CardContent>
                                    </StyledCard>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
                );

            case 3:
                return (
                    <Box>
                        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                            Review Configuration
                        </Typography>
                        {selectedVariables.map((variable) => {
                            const config = variableConfigs[variable.id] || {};
                            const activeProperties = Object.entries(config).filter(([key, value]) => value);
                            
                            return (
                                <StyledCard key={variable.id} sx={{ mb: 2 }}>
                                    <CardContent sx={{ p: 3 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                            <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: 'primary.main' }}>
                                                {variable.name.charAt(0).toUpperCase()}
                                            </Avatar>
                                            <Box sx={{ flexGrow: 1 }}>
                                                <Typography variant="h6" fontWeight="medium">
                                                    {variable.name}
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary">
                                                    {variable.dataType} • {variable.description || 'No description'}
                                                </Typography>
                                            </Box>
                                        </Box>
                                        
                                        {activeProperties.length > 0 ? (
                                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                                {activeProperties.map(([key]) => {
                                                    const property = [...propertyTypes, ...entityKeyTypes].find(p => p.id === key);
                                                    return property ? (
                                                        <VariableTypeChip
                                                            key={key}
                                                            label={property.label}
                                                            variant={property.color}
                                                            icon={property.icon}
                                                        />
                                                    ) : null;
                                                })}
                                            </Box>
                                        ) : (
                                            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                                                No special properties configured
                                            </Typography>
                                        )}
                                    </CardContent>
                                </StyledCard>
                            );
                        })}
                    </Box>
                );

            default:
                return null;
        }
    };

    const handleNext = () => {
        setActiveStep(prev => Math.min(prev + 1, steps.length - 1));
    };

    const handleBack = () => {
        setActiveStep(prev => Math.max(prev - 1, 0));
    };

    const handleSave = () => {
        const configuredVariables = selectedVariables.map(variable => ({
            ...variable,
            ...variableConfigs[variable.id]
        }));
        onSave(configuredVariables);
        onClose();
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="lg"
            fullWidth
            PaperProps={{
                sx: { 
                    borderRadius: 3,
                    minHeight: '80vh',
                    maxHeight: '90vh'
                }
            }}
        >
            <DialogTitle sx={{ pb: 1 }}>
                <Typography variant="h5" fontWeight="bold">
                    Variable Configuration Wizard
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    Configure properties and entity keys for your data bucket variables
                </Typography>
            </DialogTitle>
            
            <DialogContent sx={{ pt: 2 }}>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={3}>
                        <Stepper activeStep={activeStep} orientation="vertical">
                            {steps.map((step, index) => (
                                <Step key={step.label}>
                                    <StepLabel
                                        icon={
                                            <Avatar sx={{ 
                                                width: 32, 
                                                height: 32,
                                                bgcolor: index <= activeStep ? 'primary.main' : 'grey.300'
                                            }}>
                                                {step.icon}
                                            </Avatar>
                                        }
                                    >
                                        <Typography variant="subtitle2" fontWeight="medium">
                                            {step.label}
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                            {step.description}
                                        </Typography>
                                    </StepLabel>
                                </Step>
                            ))}
                        </Stepper>
                    </Grid>
                    
                    <Grid item xs={12} md={9}>
                        <Box sx={{ minHeight: 400 }}>
                            {renderStepContent(activeStep)}
                        </Box>
                    </Grid>
                </Grid>
            </DialogContent>
            
            <DialogActions sx={{ p: 3, pt: 1 }}>
                <Button onClick={onClose} startIcon={<CancelIcon />}>
                    Cancel
                </Button>
                <Box sx={{ flexGrow: 1 }} />
                <Button 
                    onClick={handleBack} 
                    disabled={activeStep === 0}
                    sx={{ mr: 1 }}
                >
                    Back
                </Button>
                {activeStep < steps.length - 1 ? (
                    <Button 
                        variant="contained" 
                        onClick={handleNext}
                        disabled={activeStep === 0 && selectedVariables.length === 0}
                    >
                        Next
                    </Button>
                ) : (
                    <Button 
                        variant="contained" 
                        onClick={handleSave}
                        startIcon={<SaveIcon />}
                    >
                        Save Configuration
                    </Button>
                )}
            </DialogActions>
        </Dialog>
    );
};

export default ModernVariableConfigurator;
