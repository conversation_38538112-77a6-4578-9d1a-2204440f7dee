package com.progize.tms.service.impl;

import com.progize.tms.repository.CaseCommentRepository;
import com.progize.tms.repository.entity.CaseComment;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.service.CaseCommentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** Implementation of the CaseCommentService. */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseCommentServiceImpl implements CaseCommentService {

  private final CaseCommentRepository caseCommentRepository;

  @Override
  @Transactional
  public CaseComment addComment(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      String commentText,
      String commentType,
      Long createdBy) {

    log.debug("Adding comment to case {}: {}", workflowCase.getId(), commentText);

    CaseComment comment =
        CaseComment.builder()
            .workflowCase(workflowCase)
            .processInstanceId(processInstanceId)
            .taskId(taskId)
            .commentText(commentText)
            .commentType(commentType)
            .createdBy(createdBy)
            .build();

    return caseCommentRepository.save(comment);
  }

  @Override
  public List<CaseComment> getCommentsByCase(WorkflowCase workflowCase) {
    log.debug("Getting comments for case {}", workflowCase.getId());
    return caseCommentRepository.findByWorkflowCaseOrderByCreatedAtDesc(workflowCase);
  }

  @Override
  public List<CaseComment> getCommentsByTask(String taskId) {
    log.debug("Getting comments for task {}", taskId);
    return caseCommentRepository.findByTaskIdOrderByCreatedAtDesc(taskId);
  }

  @Override
  public List<CaseComment> getCommentsByProcessInstance(String processInstanceId) {
    log.debug("Getting comments for process instance {}", processInstanceId);
    return caseCommentRepository.findByProcessInstanceIdOrderByCreatedAtDesc(processInstanceId);
  }
}
