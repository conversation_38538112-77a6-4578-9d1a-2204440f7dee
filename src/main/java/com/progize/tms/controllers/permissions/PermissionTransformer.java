package com.progize.tms.controllers.permissions;

import com.progize.tms.controllers.permissions.req.CreatePermissionRequest;
import com.progize.tms.controllers.permissions.req.UpdatePermissionRequest;
import com.progize.tms.controllers.permissions.res.PermissionResponse;
import com.progize.tms.repository.entity.Permission;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class PermissionTransformer {

  /** Transform a Permission entity to a PermissionResponse DTO */
  public PermissionResponse toPermissionResponse(Permission permission) {
    if (permission == null) {
      return null;
    }

    return PermissionResponse.builder()
        .id(permission.getId())
        .name(permission.getName())
        .description(permission.getDescription())
        .build();
  }

  /** Transform a list of Permission entities to a list of PermissionResponse DTOs */
  public List<PermissionResponse> toPermissionResponseList(List<Permission> permissions) {
    return permissions.stream().map(this::toPermissionResponse).collect(Collectors.toList());
  }

  /** Transform a CreatePermissionRequest DTO to a Permission entity */
  public Permission toPermission(CreatePermissionRequest request) {
    if (request == null) {
      return null;
    }

    return Permission.builder()
        .name(request.getName())
        .description(request.getDescription())
        .build();
  }

  /** Update a Permission entity with values from an UpdatePermissionRequest DTO */
  public void updatePermissionFromRequest(Permission permission, UpdatePermissionRequest request) {
    if (permission == null || request == null) {
      return;
    }

    permission.setName(request.getName());
    permission.setDescription(request.getDescription());
  }
}
