import React from 'react';
import { Box, Typography, Paper, Breadcrumbs, Link, useTheme } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import DataSourceForm from '../../components/datasources/DataSourceForm';

/**
 * Page component for creating a new data source
 */
const DataSourceCreatePage = () => {
  const theme = useTheme();
  return (
    <Box sx={{ bgcolor: theme.palette.mode === 'dark' ? 'transparent' : '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
      <DataSourceForm mode="create" />
    </Box>
  );
};

export default DataSourceCreatePage;
