package com.progize.tms.repository.entity;

public enum WorkflowType {
  FINANCE("finance"),
  COMPLIANCE("complianceReviewProcess"),
  RULE_TRIGGERED("rule-triggered"),
  CUSTOMER_ONBOARDING("customer-onboarding");

  private final String processDefinitionKey;

  WorkflowType(String processDefinitionKey) {
    this.processDefinitionKey = processDefinitionKey;
  }

  public String getProcessDefinitionKey() {
    return processDefinitionKey;
  }
}
