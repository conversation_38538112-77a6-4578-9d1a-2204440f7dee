import { createBrowserRouter } from 'react-router-dom';
import App from './App';
import RuleList from './components/rules/RuleList';
import RuleEditor from './components/rules/RuleEditor';
import WorkflowTestPage from './pages/WorkflowTestPage';
import RuleVariableRegistryPage from './components/rulevariables/RuleVariableRegistryPage';
import CasesPage from './pages/cases/CasesPage';
import ClosedCasesPage from './pages/cases/ClosedCasesPage';
import CaseDetailPage from './pages/cases/CaseDetailPage';
import CaseAnalyticsPage from './pages/cases/CaseAnalyticsPage';
import WorkflowDesigner from './components/workflow-designer/WorkflowDesigner';
import Login from './components/auth/Login';
import UserManagement from './components/users/UserManagement';
import RoleManagement from './components/roles/RoleManagement';
import PermissionManagement from './components/permissions/PermissionManagement';
import DataSourcesPage from './pages/datasources/DataSourcesPage';
import DataSourceCreatePage from './pages/datasources/DataSourceCreatePage';
import DataSourceEditPage from './pages/datasources/DataSourceEditPage';
import DataSourceDetailPage from './pages/datasources/DataSourceDetailPage';
import ComplianceTasksPage from './pages/ComplianceTasksPage';
import ComplianceCaseList from './components/compliance/ComplianceCaseList';
import ComplianceCaseDetail from './components/compliance/ComplianceCaseDetail';
import ComplianceTaskList from './components/compliance/ComplianceTaskList';
import ComplianceTaskDetail from './components/compliance/ComplianceTaskDetail';
import { dataBucketService } from './services/api';

// Loader function for rule variable registry
async function ruleVariableRegistryLoader() {
  const buckets = await dataBucketService.getAllBuckets();

  // Load variables for each bucket in parallel
  const variablesPromise = Promise.all(
    buckets.map(async (bucket) => {
      const variables = await dataBucketService.getVariablesByBucketId(bucket.id);
      return { bucketId: bucket.id, variables };
    })
  ).then(results => {
    // Convert array of results to an object keyed by bucketId
    return results.reduce((acc, { bucketId, variables }) => {
      acc[bucketId] = variables;
      return acc;
    }, {});
  });

  const variables = await variablesPromise;
  return { buckets, variables };
}

export const router = createBrowserRouter([
  {
    path: "/login",
    element: <Login />
  },
  {
    path: "/",
    element: <App />,
    children: [
      {
        path: "/",
        element: <RuleList />
      },
      {
        path: "/rules",
        element: <RuleList />
      },
      {
        path: "/rules/new",
        element: <RuleEditor />
      },
      {
        path: "/rules/:id",
        element: <RuleEditor />
      },
      {
        path: "rule-variable-registry",
        element: <RuleVariableRegistryPage />,
        loader: ruleVariableRegistryLoader
      },
      {
        path: "/cases",
        element: <CasesPage />
      },
      {
        path: "/cases/closed",
        element: <ClosedCasesPage />
      },
      {
        path: "/cases/:caseId",
        element: <CaseDetailPage />
      },
      {
        path: "/cases/analytics",
        element: <CaseAnalyticsPage />
      },
      {
        path: "/workflow-designer",
        element: <WorkflowDesigner />
      },
      {
        path: "/workflow-designer/:workflowId",
        element: <WorkflowDesigner />
      },
      {
        path: "/users",
        element: <UserManagement />
      },
      {
        path: "/roles",
        element: <RoleManagement />
      },
      {
        path: "/permissions",
        element: <PermissionManagement />
      },
      {
        path: "/data-sources",
        element: <DataSourcesPage />
      },
      {
        path: "/data-sources/new",
        element: <DataSourceCreatePage />
      },
      {
        path: "/data-sources/:id",
        element: <DataSourceDetailPage />
      },
      {
        path: "/data-sources/:id/edit",
        element: <DataSourceEditPage />
      },
      {
        path: "/workflow-test",
        element: <WorkflowTestPage />
      },
      {
        path: "/compliance/tasks/*",
        element: <ComplianceTasksPage />
      },
      {
        path: "/compliance/tasks",
        element: <ComplianceTaskList />
      },
      {
        path: "/compliance/tasks/:taskId",
        element: <ComplianceTaskDetail />
      },
      {
        path: "/compliance/cases",
        element: <ComplianceCaseList />
      },
      {
        path: "/compliance/cases/:caseId",
        element: <ComplianceCaseDetail />
      }
    ]
  }
]);
