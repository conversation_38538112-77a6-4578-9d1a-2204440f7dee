package com.progize.tms.controllers.rulevariables;

import com.progize.tms.controllers.rulevariables.req.RegisterColumnsRequest;
import com.progize.tms.controllers.rulevariables.req.RegisterTableRequest;
import com.progize.tms.controllers.rulevariables.req.UpdateAggregatableRequest;
import com.progize.tms.controllers.rulevariables.res.ColumnInfoResponse;
import com.progize.tms.controllers.rulevariables.res.RuleVariableResponse;
import com.progize.tms.controllers.rulevariables.res.RuleVariableSourceTableResponse;
import com.progize.tms.controllers.rulevariables.res.TableInfoResponse;
import com.progize.tms.repository.entity.RuleVariableEntity;
import com.progize.tms.repository.entity.RuleVariableSourceTable;
import com.progize.tms.services.RuleVariableManagementService;
import com.progize.tms.services.RuleVariableRegistryDb;
import com.progize.tms.services.SchemaDiscoveryService;
import com.progize.tms.services.models.ColumnInfo;
import com.progize.tms.services.models.RuleVariable;
import com.progize.tms.services.models.TableInfo;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing rule variables and discovering database schema. Provides endpoints
 * for admin to discover tables/columns and register them as variables.
 */
@RestController
@RequestMapping("/api/rule-registry")
@RequiredArgsConstructor
@CrossOrigin(origins = "*") // Enable CORS for the Rule Variables endpoints to work with the UI
public class RuleVariableRegistryController {
  private final RuleVariableRegistryDb ruleVariableRegistry;
  private final RuleVariableManagementService ruleVarManagementService;
  private final SchemaDiscoveryService schemaService;
  private final RuleRegistryVariableTransformer transformer;

  /** Get all rule variables. */
  @GetMapping
  public ResponseEntity<List<RuleVariableResponse>> getAllVariables() {
    Collection<RuleVariable> variables = ruleVariableRegistry.getAllVariables();
    return ResponseEntity.ok(
        variables.stream().map(transformer::toResponse).collect(Collectors.toList()));
  }

  /** Get rule variables by source table. */
  @GetMapping("/table/{tableName}")
  public ResponseEntity<List<RuleVariableResponse>> getVariablesByTable(
      @PathVariable String tableName) {
    List<RuleVariable> variables = ruleVariableRegistry.getVariablesBySourceTable(tableName);
    return ResponseEntity.ok(
        variables.stream().map(transformer::toResponse).collect(Collectors.toList()));
  }

  /** Discover all tables in the database. */
  @GetMapping("/schema/tables")
  public ResponseEntity<List<TableInfoResponse>> discoverTables() {
    List<TableInfo> tables = schemaService.discoverClickhouseTables();
    return ResponseEntity.ok(
        tables.stream().map(transformer::toTableInfoResponse).collect(Collectors.toList()));
  }

  /** Get all registered source tables. */
  @GetMapping("/source-tables")
  public ResponseEntity<List<RuleVariableSourceTableResponse>> getAllSourceTables() {
    List<RuleVariableSourceTable> sourceTables = ruleVarManagementService.getAllSourceTables();
    return ResponseEntity.ok(
        sourceTables.stream().map(transformer::toSourceTableResponse).collect(Collectors.toList()));
  }

  /** Get columns for a specific table. */
  @GetMapping("/schema/tables/{schema}/{table}/columns")
  public ResponseEntity<List<ColumnInfoResponse>> getTableColumns(
      @PathVariable String schema, @PathVariable String table) {
    List<ColumnInfo> columns = schemaService.getTableColumns(schema, table);
    return ResponseEntity.ok(
        columns.stream().map(transformer::toColumnInfoResponse).collect(Collectors.toList()));
  }

  /** Register a table as a source for rule variables. */
  @PostMapping("/schema/tables/{schema}/{table}/register")
  public ResponseEntity<RuleVariableSourceTableResponse> registerTable(
      @PathVariable String schema,
      @PathVariable String table,
      @RequestBody RegisterTableRequest request) {

    RuleVariableSourceTable sourceTable =
        ruleVarManagementService.registerSourceTable(
            schema, table, request.getDisplayName(), request.getDescription());

    return ResponseEntity.ok(transformer.toSourceTableResponse(sourceTable));
  }

  /** Register columns from a table as rule variables. */
  @PostMapping("/schema/tables/{tableId}/columns/register")
  public ResponseEntity<List<RuleVariableResponse>> registerColumns(
      @PathVariable Long tableId, @RequestBody RegisterColumnsRequest request) {

    List<RuleVariableEntity> variables =
        ruleVarManagementService.registerColumns(tableId, request.getColumns());

    return ResponseEntity.ok(
        variables.stream().map(transformer::toRuleVariableResponse).collect(Collectors.toList()));
  }

  /** Update the aggregatable flag for a rule variable. */
  @PutMapping("/{variableId}/aggregatable")
  public ResponseEntity<RuleVariableResponse> updateAggregatable(
      @PathVariable Long variableId, @RequestBody UpdateAggregatableRequest request) {

    RuleVariableEntity variable =
        ruleVarManagementService.updateAggregatable(variableId, request.isAggregatable());

    return ResponseEntity.ok(transformer.toRuleVariableResponse(variable));
  }

  /** Delete a rule variable. */
  @DeleteMapping("/{variableId}")
  public ResponseEntity<Void> deleteVariable(@PathVariable Long variableId) {
    ruleVarManagementService.deleteRuleVariable(variableId);
    return ResponseEntity.noContent().build();
  }
}
