/**
 * Axios configuration
 * 
 * Central configuration for Axios HTTP client.
 * Includes request/response interceptors and base URL configuration.
 */
import axios from 'axios';

// Create an axios instance with custom configuration
const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',  // Empty string as default
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      // Try to get token from user object (used by AuthService)
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          if (user && user.token) {
            config.headers.Authorization = `Bearer ${user.token}`;
          }
        } catch (e) {
          console.error('Error parsing user data:', e);
        }
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const status = error.response.status;
      
      if (status === 401) {
        // Unauthorized, redirect to login
        console.log('Unauthorized, redirecting to login');
        // window.location.href = '/login';
      } else if (status === 403) {
        // Forbidden
        console.log('Forbidden access');
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.log('Network error - no response received');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.log('Error setting up request', error.message);
    }
    
    return Promise.reject(error);
  }
);

export { axiosInstance as axios };