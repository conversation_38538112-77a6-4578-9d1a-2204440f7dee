package com.progize.tms.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.CaseAttachmentRepository;
import com.progize.tms.repository.entity.CaseAttachment;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.service.CaseAttachmentService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/** Implementation of the CaseAttachmentService. */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseAttachmentServiceImpl implements CaseAttachmentService {

  private final CaseAttachmentRepository caseAttachmentRepository;
  private final ObjectMapper objectMapper;

  @Value("${app.storage.provider:LOCAL}")
  private String storageProvider;

  @Value("${app.storage.base-path:uploads}")
  private String storagePath;

  @Override
  @Transactional
  public CaseAttachment addAttachment(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      MultipartFile file,
      Long createdBy) {

    log.debug("Adding attachment to case {}: {}", workflowCase.getId(), file.getOriginalFilename());

    try {
      String fileName = StringUtils.cleanPath(file.getOriginalFilename());
      String fileType = file.getContentType();
      String uniqueFileName = generateUniqueFileName(fileName);
      String filePath = storeFile(file, uniqueFileName);

      Map<String, Object> metadata = new HashMap<>();
      metadata.put("size", file.getSize());
      metadata.put("contentType", fileType);

      CaseAttachment attachment =
          CaseAttachment.builder()
              .workflowCase(workflowCase)
              .processInstanceId(processInstanceId)
              .taskId(taskId)
              .fileName(fileName)
              .fileType(fileType)
              .storagePath(filePath)
              .storageProvider(storageProvider)
              .metadata(objectMapper.writeValueAsString(metadata))
              .createdBy(createdBy)
              .build();

      return caseAttachmentRepository.save(attachment);
    } catch (IOException e) {
      log.error("Failed to store attachment", e);
      throw new RuntimeException("Failed to store attachment", e);
    }
  }

  @Override
  @Transactional
  public List<CaseAttachment> addAttachments(
      WorkflowCase workflowCase,
      String processInstanceId,
      String taskId,
      List<MultipartFile> files,
      Long createdBy) {

    if (files == null || files.isEmpty()) {
      return List.of();
    }

    log.debug("Adding {} attachments to case {}", files.size(), workflowCase.getId());

    List<CaseAttachment> attachments = new ArrayList<>();
    for (MultipartFile file : files) {
      attachments.add(addAttachment(workflowCase, processInstanceId, taskId, file, createdBy));
    }

    return attachments;
  }

  @Override
  public List<CaseAttachment> getAttachmentsByCase(WorkflowCase workflowCase) {
    log.debug("Getting attachments for case {}", workflowCase.getId());
    return caseAttachmentRepository.findByWorkflowCaseOrderByCreatedAtDesc(workflowCase);
  }

  @Override
  public List<CaseAttachment> getAttachmentsByTask(String taskId) {
    log.debug("Getting attachments for task {}", taskId);
    return caseAttachmentRepository.findByTaskIdOrderByCreatedAtDesc(taskId);
  }

  @Override
  public List<CaseAttachment> getAttachmentsByProcessInstance(String processInstanceId) {
    log.debug("Getting attachments for process instance {}", processInstanceId);
    return caseAttachmentRepository.findByProcessInstanceIdOrderByCreatedAtDesc(processInstanceId);
  }

  /**
   * Generate a unique file name to prevent collisions.
   *
   * @param originalFileName the original file name
   * @return a unique file name
   */
  private String generateUniqueFileName(String originalFileName) {
    String extension = "";
    if (originalFileName.contains(".")) {
      extension = originalFileName.substring(originalFileName.lastIndexOf("."));
      originalFileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
    }
    return originalFileName + "_" + UUID.randomUUID().toString().substring(0, 8) + extension;
  }

  /**
   * Store the file in the configured storage provider. This is a simple implementation that stores
   * files locally. In a production environment, this would be replaced with cloud storage
   * implementation.
   *
   * @param file the file to store
   * @param fileName the file name to use
   * @return the path to the stored file
   * @throws IOException if the file cannot be stored
   */
  private String storeFile(MultipartFile file, String fileName) throws IOException {
    // In a real implementation, this would store the file in the appropriate storage provider
    // For now, we'll just return a path as if it was stored
    String path = storagePath + "/" + fileName;
    log.debug("Storing file at: {}", path);

    // TODO: Implement actual file storage based on the configured provider
    // For now, we're just simulating storage

    return path;
  }
}
