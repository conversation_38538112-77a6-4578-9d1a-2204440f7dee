import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Chip,
  IconButton,
  Paper,
  Tooltip,
  useTheme,
  alpha,
  Alert,
  Snackbar,
  DialogContentText,
  styled,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  ListItemText
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import SecurityIcon from '@mui/icons-material/Security';
import { userService, roleService } from '../../services/api';
import AuthService from '../../services/AuthService';

// Styled components
const UserCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  transition: 'all 0.3s cubic-bezier(.25,.8,.25,1)',
  '&:hover': {
    boxShadow: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
    transform: 'translateY(-2px)'
  }
}));

const RoleChip = styled(Chip)(({ theme }) => ({
  height: 24,
  fontSize: '0.75rem',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.dark,
  borderColor: alpha(theme.palette.primary.main, 0.3),
  '& .MuiChip-label': {
    padding: '0 8px',
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(5),
  padding: theme.spacing(1, 3),
  textTransform: 'none',
  fontWeight: 500,
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  transition: 'all 0.2s ease',
  '&:hover': {
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    transform: 'translateY(-1px)'
  }
}));

const UserManagement = () => {
  const theme = useTheme();
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showRoleAssignModal, setShowRoleAssignModal] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    email: '',
    firstName: '',
    lastName: '',
    enabled: true
  });
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Get the current logged-in user
  const loggedInUser = AuthService.getCurrentUser();

  // Fetch users and roles on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [usersData, rolesData] = await Promise.all([
        userService.getAllUsers(),
        roleService.getAllRoles()
      ]);

      // Make sure we have the complete user data with roles
      const usersWithRoles = await Promise.all(
        (Array.isArray(usersData) ? usersData : []).map(async (user) => {
          try {
            // Fetch detailed user data with roles for each user
            const userData = await userService.getUser(user.id);
            return userData;
          } catch (error) {
            console.error(`Error fetching roles for user ${user.id}:`, error);
            return user; // Return original user if we can't get roles
          }
        })
      );

      setUsers(usersWithRoles);
      setRoles(Array.isArray(rolesData) ? rolesData : []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load users and roles',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Open role assignment modal
  const openRoleAssignModal = (user) => {
    setCurrentUser(user);
    setSelectedRoles(user.roles ? user.roles.map(role => role.id) : []);
    setShowRoleAssignModal(true);
  };

  // Handle role selection change
  const handleRoleSelectionChange = (event) => {
    const { value } = event.target;
    setSelectedRoles(value);
  };

  // Assign roles to user
  const assignRolesToUser = async () => {
    try {
      setLoading(true);
      const updatedUser = await userService.setUserRoles(currentUser.id, selectedRoles);
      
      // After assigning roles, fetch the updated user data to ensure we have the complete data
      const refreshedUser = await userService.getUser(currentUser.id);
      
      // Update the user in the list with the refreshed data
      setUsers(users.map(user => user.id === currentUser.id ? refreshedUser : user));
      
      setShowRoleAssignModal(false);
      setSnackbar({
        open: true,
        message: 'Roles assigned successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error assigning roles:', error);
      setSnackbar({
        open: true,
        message: 'Failed to assign roles',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenCreateModal = () => {
    setShowCreateModal(true);
  };

  const handleCloseCreateModal = () => {
    setShowCreateModal(false);
  };

  const handleOpenEditModal = (user) => {
    setCurrentUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      password: '',
      confirmPassword: '',
    });
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
  };

  const handleOpenDeleteModal = (user) => {
    setCurrentUser(user);
    setShowDeleteModal(true);
  };

  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.username.trim()) errors.username = 'Username is required';
    if (!formData.email.trim()) errors.email = 'Email is required';
    if (!formData.firstName.trim()) errors.firstName = 'First name is required';
    if (!formData.lastName.trim()) errors.lastName = 'Last name is required';
    
    if (!formData.password) errors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
    
    return errors;
  };

  const handleSubmit = async () => {
    const errors = validateForm();
    
    if (Object.keys(errors).length > 0) {
      setSnackbar({
        open: true,
        message: 'Please fix the errors',
        severity: 'error'
      });
      return;
    }
    
    try {
      setLoading(true);
      
      // Prepare user data for API
      const userData = {
        username: formData.username,
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        enabled: true
      };
      
      // Add password only if provided
      if (formData.password) {
        userData.password = formData.password;
      }
      
      if (currentUser) {
        await userService.updateUser(currentUser.id, userData);
        
        // Update user in the list
        setUsers(users.map(user => 
          user.id === currentUser.id ? { ...user, ...userData } : user
        ));
        
        setSnackbar({
          open: true,
          message: 'User updated successfully',
          severity: 'success'
        });
      } else {
        const newUser = await userService.createUser(userData);
        
        // Add new user to the list
        setUsers([...users, newUser]);
        
        setSnackbar({
          open: true,
          message: 'User created successfully',
          severity: 'success'
        });
      }
      
      handleCloseCreateModal();
      handleCloseEditModal();
    } catch (error) {
      console.error('Error saving user:', error);
      setSnackbar({
        open: true,
        message: `Failed to ${currentUser ? 'update' : 'create'} user: ${error.message || 'Unknown error'}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!currentUser) return;
    
    try {
      setLoading(true);
      await userService.deleteUser(currentUser.id);
      
      // Remove user from the list
      setUsers(users.filter(user => user.id !== currentUser.id));
      
      setSnackbar({
        open: true,
        message: 'User deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      setSnackbar({
        open: true,
        message: `Failed to delete user: ${error.message || 'Unknown error'}`,
        severity: 'error'
      });
    } finally {
      setShowDeleteModal(false);
      setCurrentUser(null);
      setLoading(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setCurrentUser(null);
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  return (
    <Container fluid>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 3 
        }}>
          <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
            User Management
          </Typography>
          <ActionButton
            variant="contained"
            color="primary"
            startIcon={<PersonAddIcon />}
            onClick={handleOpenCreateModal}
          >
            Create New User
          </ActionButton>
        </Box>

        {loading && !users.length ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {users.length > 0 ? (
              <Box>
                {users.map(user => (
                  <UserCard key={user.id}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <PersonIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {user.username}
                            </Typography>
                            <Chip 
                              size="small" 
                              label={user.enabled ? "Active" : "Inactive"} 
                              color={user.enabled ? "success" : "default"}
                              sx={{ ml: 2 }}
                            />
                          </Box>
                          <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                            {user.firstName} {user.lastName} • {user.email}
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', mt: 1 }}>
                            {user.roles && user.roles.map(role => (
                              <RoleChip key={role.id} label={role.name} size="small" />
                            ))}
                            {(!user.roles || user.roles.length === 0) && (
                              <Typography variant="caption" color="textSecondary">
                                No roles assigned
                              </Typography>
                            )}
                          </Box>
                        </Box>
                        <Box>
                          <Tooltip title="Assign Roles">
                            <IconButton 
                              onClick={() => openRoleAssignModal(user)}
                              sx={{ 
                                color: theme.palette.info.main,
                                '&:hover': { backgroundColor: alpha(theme.palette.info.main, 0.1) }
                              }}
                            >
                              <SecurityIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit User">
                            <IconButton 
                              onClick={() => handleOpenEditModal(user)}
                              sx={{ 
                                color: theme.palette.primary.main,
                                '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.1) }
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete User">
                            <IconButton 
                              onClick={() => handleOpenDeleteModal(user)}
                              sx={{ 
                                color: theme.palette.error.main,
                                '&:hover': { backgroundColor: alpha(theme.palette.error.main, 0.1) }
                              }}
                              disabled={user.username === loggedInUser?.username} // Prevent deleting admin user
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </CardContent>
                  </UserCard>
                ))}
              </Box>
            ) : (
              <Paper 
                sx={{ 
                  p: 4, 
                  borderRadius: 2,
                  textAlign: 'center',
                  bgcolor: alpha(theme.palette.background.paper, 0.6),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <Box sx={{ mb: 2 }}>
                  <PersonIcon sx={{ fontSize: 48, color: theme.palette.text.disabled }} />
                </Box>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 500 }}>
                  No users found
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                  Create a new user to get started
                </Typography>
                <ActionButton
                  variant="contained"
                  color="primary"
                  startIcon={<PersonAddIcon />}
                  onClick={handleOpenCreateModal}
                >
                  Create New User
                </ActionButton>
              </Paper>
            )}
          </>
        )}
      </Box>

      {/* Create User Modal */}
      <Dialog 
        open={showCreateModal} 
        onClose={handleCloseCreateModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
          py: 2
        }}>
          Create New User
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" sx={{ '& .MuiTextField-root': { my: 1 } }}>
            <TextField
              fullWidth
              label="Username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              margin="normal"
            />
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                fullWidth
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                margin="normal"
              />
            </Box>
            <TextField
              fullWidth
              label="Password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Confirm Password"
              name="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              margin="normal"
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ 
          px: 3, 
          py: 2,
          borderTop: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
        }}>
          <Button onClick={handleCloseCreateModal} color="inherit">
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Modal */}
      <Dialog 
        open={showEditModal} 
        onClose={handleCloseEditModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
          py: 2
        }}>
          Edit User
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" sx={{ '& .MuiTextField-root': { my: 1 } }}>
            <TextField
              fullWidth
              label="Username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              disabled
              margin="normal"
            />
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              margin="normal"
            />
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                fullWidth
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                margin="normal"
              />
            </Box>
            <TextField
              fullWidth
              label="New Password (leave blank to keep current)"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Confirm New Password"
              name="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              margin="normal"
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ 
          px: 3, 
          py: 2,
          borderTop: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
        }}>
          <Button onClick={handleCloseEditModal} color="inherit">
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            startIcon={<EditIcon />}
          >
            Update
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete User Modal */}
      <Dialog 
        open={showDeleteModal} 
        onClose={handleCloseDeleteModal}
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxWidth: '400px',
            backgroundImage: 'linear-gradient(to bottom, rgba(255,255,255,0.95), rgba(255,255,255,0.98))',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
          }
        }}
      >
        <DialogTitle sx={{
          fontWeight: 600,
          fontSize: '1.1rem',
          pb: 1,
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          color: theme.palette.error.main,
          display: 'flex',
          alignItems: 'center',
        }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Confirm Deletion
        </DialogTitle>
        <DialogContent sx={{ pt: 2, mt: 1 }}>
          <DialogContentText variant="body2" sx={{ color: theme.palette.text.primary }}>
            Are you sure you want to delete the user <Box component="span" sx={{ fontWeight: 600 }}>"{currentUser?.username}"</Box>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={handleCancelDelete}
            variant="outlined"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
            size="small"
          >
            Cancel
          </Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              backgroundColor: theme.palette.error.main,
              '&:hover': {
                backgroundColor: theme.palette.error.dark,
                boxShadow: '0 4px 12px rgba(211, 47, 47, 0.3)',
              }
            }}
            size="small"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Role Assignment Modal */}
      <Dialog 
        open={showRoleAssignModal} 
        onClose={() => setShowRoleAssignModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
          py: 2,
          display: 'flex',
          alignItems: 'center'
        }}>
          <SecurityIcon sx={{ mr: 1, color: theme.palette.info.main }} />
          Assign Roles to {currentUser?.username}
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Select the roles you want to assign to this user. The user will have all permissions associated with the selected roles.
          </Typography>
          <FormControl fullWidth sx={{ mt: 1 }}>
            <InputLabel id="role-select-label">Roles</InputLabel>
            <Select
              labelId="role-select-label"
              id="role-select"
              multiple
              value={selectedRoles}
              onChange={handleRoleSelectionChange}
              input={<OutlinedInput label="Roles" />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((roleId) => {
                    const role = roles.find(r => r.id === roleId);
                    return role ? (
                      <Chip 
                        key={roleId} 
                        label={role.name} 
                        size="small"
                        sx={{ 
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          fontWeight: 500
                        }}
                      />
                    ) : null;
                  })}
                </Box>
              )}
            >
              {roles.map((role) => (
                <MenuItem key={role.id} value={role.id}>
                  <Checkbox checked={selectedRoles.indexOf(role.id) > -1} />
                  <ListItemText 
                    primary={role.name} 
                    secondary={role.description || 'No description'}
                    primaryTypographyProps={{ fontWeight: 500 }}
                    secondaryTypographyProps={{ variant: 'caption' }}
                  />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions sx={{ 
          px: 3, 
          py: 2,
          borderTop: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
        }}>
          <Button 
            onClick={() => setShowRoleAssignModal(false)} 
            color="inherit"
          >
            Cancel
          </Button>
          <Button 
            onClick={assignRolesToUser}
            variant="contained" 
            color="primary"
            startIcon={<SecurityIcon />}
          >
            Assign Roles
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar 
        open={snackbar.open} 
        autoHideDuration={6000} 
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity} 
          sx={{ width: '100%' }}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default UserManagement;
