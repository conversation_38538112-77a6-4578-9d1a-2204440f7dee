package com.progize.tms.controllers.workflow.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/** DTO for Flowable form properties. */
@Data
public class FormPropertyDTO {
  private String id;
  private String name;
  private String type;
  private boolean required;
  private boolean readable = true;
  private boolean writable = true;
  private String expression;
  private String defaultValue;
  private List<FormPropertyValueDTO> values = new ArrayList<>();
}
