package com.progize.tms.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.services.models.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for validating rules against the rule variable registry. This ensures that rules only
 * reference variables and operators that exist and are compatible.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RuleValidationService {

  private final RuleVariableService ruleVariableService;
  private final DataBucketService dataBucketService;
  private final ObjectMapper objectMapper;

  /**
   * Validates a rule against the rule variable registry.
   *
   * @param rule The rule to validate
   * @return A ValidationResult containing validation status and any error messages
   */
  public ValidationResult validateRule(Rule rule) {
    Long bucketId = rule.getDataBucket() != null ? rule.getDataBucket().getId() : null;
    return validateRule(rule, bucketId);
  }

  /**
   * Validates a rule against bucket-specific variables if a bucket ID is provided, otherwise falls
   * back to the general rule variable registry.
   *
   * @param rule The rule to validate
   * @param bucketId The ID of the data bucket to validate against, or null to use general variables
   * @return A ValidationResult containing validation status and any error messages
   */
  public ValidationResult validateRule(Rule rule, Long bucketId) {
    ValidationResult result = new ValidationResult();

    if (rule == null) {
      result.addError("Rule cannot be null");
      return result;
    }

    if (rule.getRuleJson() == null || rule.getRuleJson().isEmpty()) {
      result.addError("Rule condition cannot be empty");
      return result;
    }

    try {
      Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

      // If bucket ID is provided, validate against bucket variables
      if (bucketId != null) {
        validateConditionAgainstBucket(condition, bucketId, result);
      } else {
        // Otherwise use the general rule variable registry
        validateCondition(condition, result);
      }
    } catch (JsonProcessingException e) {
      result.addError("Failed to parse rule condition: " + e.getMessage());
      log.error("Error parsing rule JSON", e);
    }

    return result;
  }

  /**
   * Recursively validates a condition against bucket-specific variables.
   *
   * @param condition The condition to validate
   * @param bucketId The ID of the data bucket to validate against
   * @param result The validation result to update
   */
  private void validateConditionAgainstBucket(
      Condition condition, Long bucketId, ValidationResult result) {
    if (condition == null) {
      result.addError("Condition cannot be null");
      return;
    }

    String type = condition.getType();

    if ("SIMPLE".equals(type)) {
      validateSimpleConditionAgainstBucket((SimpleCondition) condition, bucketId, result);
    } else if ("AGGREGATE".equals(type)) {
      validateAggregateConditionAgainstBucket((AggregateCondition) condition, bucketId, result);
    } else if ("AND".equals(type) || "OR".equals(type) || "NOT".equals(type)) {
      validateCompositeConditionAgainstBucket((CompositeCondition) condition, bucketId, result);
    } else if ("COMPARISON".equals(type)) {
      validateComparisonConditionAgainstBucket((ComparisonCondition) condition, bucketId, result);
    } else {
      result.addError("Unknown condition type: " + type);
    }
  }

  /**
   * Validates a simple condition against bucket-specific variables.
   *
   * @param condition The simple condition to validate
   * @param bucketId The ID of the data bucket to validate against
   * @param result The validation result to update
   */
  private void validateSimpleConditionAgainstBucket(
      SimpleCondition condition, Long bucketId, ValidationResult result) {
    // Fetch bucket variables and create a map for efficient lookup
    // Use the method that includes enriched variables from related lookup buckets
    Map<String, RuleVariable> bucketVariables = new HashMap<>();

    // Get all variables including enriched ones
    Map<DataBucketVariableEntity, List<String>> variablesWithOperators =
        dataBucketService.getVariablesWithSupportedOperatorsAndEnrichment(bucketId);

    // First, get the primary bucket's ID to prioritize its variables
    Long primaryBucketId = bucketId;

    // Process all variables
    for (DataBucketVariableEntity var : variablesWithOperators.keySet()) {
      // Convert DataBucketVariableEntity to RuleVariable
      RuleVariable ruleVar = new RuleVariable();
      ruleVar.setId(var.getCode());
      ruleVar.setName(var.getName());
      ruleVar.setType(var.getDataType().getCode());
      ruleVar.setCategory(var.getTableName());
      ruleVar.setAggregatable(var.isAggregatable());

      // Check if this variable name already exists in the map
      if (bucketVariables.containsKey(var.getName())) {
        // If the existing variable is from an enriched bucket and this one is from the primary
        // bucket,
        // replace it to prioritize the primary bucket's variables
        if (var.getDataBucket() != null && var.getDataBucket().getId().equals(primaryBucketId)) {
          bucketVariables.put(var.getName(), ruleVar);
        }
        // Otherwise, keep the existing variable (first one wins)
      } else {
        // If the variable name doesn't exist yet, add it
        bucketVariables.put(var.getName(), ruleVar);
      }
    }

    // Validate variable exists in the bucket
    String fieldName = condition.getField();
    RuleVariable variable = bucketVariables.get(fieldName);

    if (variable == null) {
      result.addError("Unknown variable: " + fieldName + " in bucket with ID: " + bucketId);
      return;
    }

    // Validate operator exists and is compatible with the variable
    String operatorId = condition.getOperator();
    Operator operator = findOperatorById(operatorId);

    if (operator == null) {
      result.addError("Unknown operator: " + operatorId);
      return;
    }

    // Check if operator is applicable to the variable type
    if (!operator.getApplicableTypes().contains(variable.getType())) {
      result.addError(
          String.format(
              "Operator '%s' is not applicable to variable '%s' of type '%s'",
              operator.getName(), variable.getName(), variable.getType()));
    }

    // Get supported operators for this variable
    // Use the method that includes enriched variables from related lookup buckets
    // We already have the variables with operators from above, reuse it

    // First try to find the variable in the primary bucket
    List<String> supportedOperators =
        variablesWithOperators.entrySet().stream()
            .filter(
                entry ->
                    entry.getKey().getName().equals(fieldName)
                        && entry.getKey().getDataBucket() != null
                        && entry.getKey().getDataBucket().getId().equals(bucketId))
            .map(Map.Entry::getValue)
            .findFirst()
            .orElse(null);

    // If not found in the primary bucket, look in all buckets
    if (supportedOperators == null) {
      supportedOperators =
          variablesWithOperators.entrySet().stream()
              .filter(entry -> entry.getKey().getName().equals(fieldName))
              .map(Map.Entry::getValue)
              .findFirst()
              .orElse(List.of());
    }

    // Check if operator is in the supported operators for this variable
    if (!supportedOperators.contains(operatorId)) {
      result.addError(
          String.format(
              "Operator '%s' is not supported for variable '%s'",
              operator.getName(), variable.getName()));
    }
  }

  /**
   * Validates an aggregate condition against bucket-specific variables.
   *
   * @param condition The aggregate condition to validate
   * @param bucketId The ID of the data bucket to validate against
   * @param result The validation result to update
   */
  private void validateAggregateConditionAgainstBucket(
      AggregateCondition condition, Long bucketId, ValidationResult result) {
    if (condition == null) {
      result.addError("Aggregate condition cannot be null");
      return;
    }

    // Fetch bucket variables and create a map for efficient lookup
    // Use the method that includes enriched variables from related lookup buckets
    Map<String, RuleVariable> bucketVariables = new HashMap<>();

    // Get all variables including enriched ones
    Map<DataBucketVariableEntity, List<String>> variablesWithOperators =
        dataBucketService.getVariablesWithSupportedOperatorsAndEnrichment(bucketId);

    // First, get the primary bucket's ID to prioritize its variables
    Long primaryBucketId = bucketId;

    // Process all variables
    for (DataBucketVariableEntity var : variablesWithOperators.keySet()) {
      // Convert DataBucketVariableEntity to RuleVariable
      RuleVariable ruleVar = new RuleVariable();
      ruleVar.setId(var.getCode());
      ruleVar.setName(var.getName());
      ruleVar.setType(var.getDataType().getCode());
      ruleVar.setCategory(var.getTableName());
      ruleVar.setAggregatable(var.isAggregatable());

      // Check if this variable name already exists in the map
      if (bucketVariables.containsKey(var.getName())) {
        // If the existing variable is from an enriched bucket and this one is from the primary
        // bucket,
        // replace it to prioritize the primary bucket's variables
        if (var.getDataBucket() != null && var.getDataBucket().getId().equals(primaryBucketId)) {
          bucketVariables.put(var.getName(), ruleVar);
        }
        // Otherwise, keep the existing variable (first one wins)
      } else {
        // If the variable name doesn't exist yet, add it
        bucketVariables.put(var.getName(), ruleVar);
      }
    }

    // Check field if provided
    String fieldName = condition.getField();
    if (fieldName != null && !fieldName.isEmpty()) {
      RuleVariable variable = bucketVariables.get(fieldName);

      if (variable == null) {
        result.addError("Unknown variable: " + fieldName + " in bucket with ID: " + bucketId);
      } else if (!Boolean.TRUE.equals(variable.isAggregatable())) {
        result.addError("Variable is not aggregatable: " + variable.getName());
      }
    }

    // Validate function exists and is applicable to the variable
    String functionId = condition.getFunction();
    if (functionId == null || functionId.isEmpty()) {
      result.addError("Aggregate function must be specified");
      return;
    }

    boolean functionExists =
        ruleVariableService.getAllAggregateFunctions().stream()
            .anyMatch(f -> f.getId().equals(functionId));

    if (!functionExists) {
      result.addError("Unknown aggregate function: " + functionId);
    }

    // Validate operator exists and is applicable to numbers (aggregation results are always
    // numeric)
    String operatorId = condition.getOperator();
    if (operatorId == null || operatorId.isEmpty()) {
      result.addError("Operator must be specified for aggregate conditions");
      return;
    }

    Operator operator = findOperatorById(operatorId);

    if (operator == null) {
      result.addError("Unknown operator: " + operatorId);
      return;
    }

    if (operator.getApplicableTypes() == null
        || !operator.getApplicableTypes().contains("number")) {
      result.addError(
          String.format(
              "Operator '%s' cannot be applied to aggregate results (which are numeric)",
              operator.getName()));
    }

    // Validate entity field exists
    String entityFieldId = condition.getEntityField();
    if (entityFieldId == null || entityFieldId.isEmpty()) {
      // If entity field is not specified, default to customer ID
      log.debug("Entity field not specified for aggregate condition, defaulting to customerId");
    }
  }

  /**
   * Validates a composite condition against bucket-specific variables.
   *
   * @param condition The composite condition to validate
   * @param bucketId The ID of the data bucket to validate against
   * @param result The validation result to update
   */
  private void validateCompositeConditionAgainstBucket(
      CompositeCondition condition, Long bucketId, ValidationResult result) {
    List<Condition> subConditions = condition.getConditions();

    if (subConditions == null || subConditions.isEmpty()) {
      result.addError("Composite condition must have at least one sub-condition");
      return;
    }

    for (Condition subCondition : subConditions) {
      validateConditionAgainstBucket(subCondition, bucketId, result);
    }
  }

  /**
   * Maps data type code to variable type format used in RuleVariableResponse. This is similar to
   * the method in DataBucketTransformer.
   */
  private String mapDataTypeToVariableType(String dataTypeCode) {
    switch (dataTypeCode) {
      case "VARCHAR":
      case "CHAR":
      case "TEXT":
        return "string";
      case "INT":
      case "SMALLINT":
      case "TINYINT":
        return "integer";
      case "BIGINT":
        return "bigint";
      case "DECIMAL":
      case "NUMERIC":
        return "bigdecimal";
      case "FLOAT":
      case "DOUBLE":
      case "REAL":
        return "double";
      case "DATE":
      case "TIMESTAMP":
        return "date";
      case "BOOLEAN":
        return "boolean";
      default:
        return "string"; // Default to string for unknown types
    }
  }

  /**
   * Recursively validates a condition and its sub-conditions.
   *
   * @param condition The condition to validate
   * @param result The validation result to update
   */
  private void validateCondition(Condition condition, ValidationResult result) {
    if (condition == null) {
      result.addError("Condition cannot be null");
      return;
    }

    String type = condition.getType();

    if ("SIMPLE".equals(type)) {
      validateSimpleCondition((SimpleCondition) condition, result);
    } else if ("AGGREGATE".equals(type)) {
      validateAggregateCondition((AggregateCondition) condition, result);
    } else if ("AND".equals(type) || "OR".equals(type) || "NOT".equals(type)) {
      validateCompositeCondition((CompositeCondition) condition, result);
    } else if ("COMPARISON".equals(type)) {
      validateComparisonCondition((ComparisonCondition) condition, result);
    } else {
      result.addError("Unknown condition type: " + type);
    }
  }

  /**
   * Validates a simple condition by checking if the variable and operator exist and are compatible.
   *
   * @param condition The simple condition to validate
   * @param result The validation result to update
   */
  private void validateSimpleCondition(SimpleCondition condition, ValidationResult result) {
    // Fetch all variables and create a map for efficient lookup
    Map<String, RuleVariable> variables =
        ruleVariableService.getAllVariables().stream()
            .collect(Collectors.toMap(RuleVariable::getName, Function.identity()));

    // Validate variable exists
    String fieldName = condition.getField();
    RuleVariable variable = variables.get(fieldName);

    if (variable == null) {
      result.addError("Unknown variable: " + fieldName);
      return;
    }

    // Validate operator exists and is compatible with the variable
    String operatorId = condition.getOperator();
    Operator operator = findOperatorById(operatorId);

    if (operator == null) {
      result.addError("Unknown operator: " + operatorId);
      return;
    }

    // Check if operator is applicable to the variable type
    if (!operator.getApplicableTypes().contains(variable.getType())) {
      result.addError(
          String.format(
              "Operator '%s' is not applicable to variable '%s' of type '%s'",
              operator.getName(), variable.getName(), variable.getType()));
    }

    // Check if operator is in the supported operators for this variable
    if (variable.getSupportedOperators() != null
        && !variable.getSupportedOperators().contains(operatorId)) {
      result.addError(
          String.format(
              "Operator '%s' is not supported for variable '%s'",
              operator.getName(), variable.getName()));
    }
  }

  /**
   * Validates an aggregate condition by checking if the aggregation function, variable, and
   * operator exist and are compatible.
   *
   * @param condition The aggregate condition to validate
   * @param result The validation result to update
   */
  private void validateAggregateCondition(AggregateCondition condition, ValidationResult result) {
    if (condition == null) {
      result.addError("Aggregate condition cannot be null");
      return;
    }

    // Fetch all variables and create a map for efficient lookup
    Map<String, RuleVariable> variables =
        ruleVariableService.getAllVariables().stream()
            .collect(Collectors.toMap(RuleVariable::getName, Function.identity()));

    // Check field if provided
    String fieldName = condition.getField();
    if (fieldName != null && !fieldName.isEmpty()) {
      RuleVariable variable = variables.get(fieldName);

      if (variable == null) {
        result.addError("Unknown variable: " + fieldName);
      } else if (!Boolean.TRUE.equals(variable.isAggregatable())) {
        result.addError("Variable is not aggregatable: " + variable.getName());
      }
    }

    // Validate function exists and is applicable to the variable
    String functionId = condition.getFunction();
    if (functionId == null || functionId.isEmpty()) {
      result.addError("Aggregate function must be specified");
      return;
    }

    boolean functionExists =
        ruleVariableService.getAllAggregateFunctions().stream()
            .anyMatch(f -> f.getId().equals(functionId));

    if (!functionExists) {
      result.addError("Unknown aggregate function: " + functionId);
    }

    // Validate operator exists and is applicable to numbers (aggregation results are always
    // numeric)
    String operatorId = condition.getOperator();
    if (operatorId == null || operatorId.isEmpty()) {
      result.addError("Operator must be specified for aggregate conditions");
      return;
    }

    Operator operator = findOperatorById(operatorId);

    if (operator == null) {
      result.addError("Unknown operator: " + operatorId);
      return;
    }

    if (operator.getApplicableTypes() == null
        || !operator.getApplicableTypes().contains("number")) {
      result.addError(
          String.format(
              "Operator '%s' cannot be applied to aggregate results (which are numeric)",
              operator.getName()));
    }

    // Validate entity field exists
    String entityFieldId = condition.getEntityField();
    if (entityFieldId == null || entityFieldId.isEmpty()) {
      // If entity field is not specified, default to customer ID
      log.debug("Entity field not specified for aggregate condition, defaulting to customerId");
    }
  }

  /**
   * Validates a composite condition by recursively validating its sub-conditions.
   *
   * @param condition The composite condition to validate
   * @param result The validation result to update
   */
  private void validateCompositeCondition(CompositeCondition condition, ValidationResult result) {
    List<Condition> subConditions = condition.getConditions();

    if (subConditions == null || subConditions.isEmpty()) {
      result.addError("Composite condition must have at least one sub-condition");
      return;
    }

    for (Condition subCondition : subConditions) {
      validateCondition(subCondition, result);
    }
  }

  /**
   * Finds an operator by its ID.
   *
   * @param operatorId The operator ID to look for
   * @return The operator, or null if not found
   */
  private Operator findOperatorById(String operatorId) {
    return ruleVariableService.getAllOperators().stream()
        .filter(op -> op.getId().equals(operatorId))
        .findFirst()
        .orElse(null);
  }

  /**
   * Finds an operator by its symbol (e.g., ">", "<=", "==").
   *
   * @param symbol The operator symbol to look for
   * @return The operator, or null if not found
   */
  private Operator findOperatorBySymbol(String symbol) {
    return ruleVariableService.getAllOperators().stream()
        .filter(op -> op.getSymbol() != null && op.getSymbol().equals(symbol))
        .findFirst()
        .orElse(null);
  }

  /**
   * Validates a comparison condition against bucket-specific variables.
   *
   * @param condition The comparison condition to validate
   * @param bucketId The ID of the data bucket to validate against
   * @param result The validation result to update
   */
  private void validateComparisonConditionAgainstBucket(
      ComparisonCondition condition, Long bucketId, ValidationResult result) {
    if (condition == null) {
      result.addError("Comparison condition cannot be null");
      return;
    }

    // Validate operator exists and is applicable to numbers (comparison results are always numeric)
    String operatorSymbol = condition.getOperator();
    if (operatorSymbol == null || operatorSymbol.isEmpty()) {
      result.addError("Operator must be specified for comparison conditions");
      return;
    }

    // First try to find by ID, then by symbol if not found
    Operator operator = findOperatorById(operatorSymbol);

    // If not found by ID, try to find by symbol
    if (operator == null) {
      operator = findOperatorBySymbol(operatorSymbol);
    }

    if (operator == null) {
      result.addError("Unknown operator: " + operatorSymbol);
      return;
    }

    if (operator.getApplicableTypes() == null
        || !operator.getApplicableTypes().contains("number")) {
      result.addError(
          String.format(
              "Operator '%s' cannot be applied to comparison results (which are numeric)",
              operator.getName()));
    }

    // Validate left and right conditions
    if (condition.getLeft() == null) {
      result.addError("Left side of comparison condition cannot be null");
    } else {
      // Special handling for AggregateConditions inside ComparisonConditions
      if (condition.getLeft() instanceof AggregateCondition) {
        validateAggregateInComparisonAgainstBucket(
            (AggregateCondition) condition.getLeft(), bucketId, result);
      } else {
        validateConditionAgainstBucket(condition.getLeft(), bucketId, result);
      }
    }

    if (condition.getRight() == null) {
      result.addError("Right side of comparison condition cannot be null");
    } else {
      // Special handling for AggregateConditions inside ComparisonConditions
      if (condition.getRight() instanceof AggregateCondition) {
        validateAggregateInComparisonAgainstBucket(
            (AggregateCondition) condition.getRight(), bucketId, result);
      } else {
        validateConditionAgainstBucket(condition.getRight(), bucketId, result);
      }
    }
  }

  /**
   * Gets all variables for a specific bucket, including enriched variables from related lookup
   * buckets.
   *
   * @param bucketId The ID of the data bucket
   * @return A map of variable names to RuleVariable objects
   */
  private Map<String, RuleVariable> getBucketVariables(Long bucketId) {
    Map<String, RuleVariable> bucketVariables = new HashMap<>();

    // Get all variables including enriched ones
    Map<DataBucketVariableEntity, List<String>> variablesWithOperators =
        dataBucketService.getVariablesWithSupportedOperatorsAndEnrichment(bucketId);

    // First, get the primary bucket's ID to prioritize its variables
    Long primaryBucketId = bucketId;

    // Process all variables
    for (DataBucketVariableEntity var : variablesWithOperators.keySet()) {
      // Convert DataBucketVariableEntity to RuleVariable
      RuleVariable ruleVar = new RuleVariable();
      ruleVar.setId(var.getCode());
      ruleVar.setName(var.getName());
      ruleVar.setType(var.getDataType().getCode());
      ruleVar.setCategory(var.getTableName());
      ruleVar.setAggregatable(var.isAggregatable());

      // Check if this variable name already exists in the map
      if (bucketVariables.containsKey(var.getName())) {
        // If the existing variable is from an enriched bucket and this one is from the primary
        // bucket,
        // replace it to prioritize the primary bucket's variables
        if (var.getDataBucket() != null && var.getDataBucket().getId().equals(primaryBucketId)) {
          bucketVariables.put(var.getName(), ruleVar);
        }
        // Otherwise, keep the existing variable (first one wins)
      } else {
        // If the variable name doesn't exist yet, add it
        bucketVariables.put(var.getName(), ruleVar);
      }
    }

    return bucketVariables;
  }

  /**
   * Validates an aggregate condition that is part of a comparison condition against bucket-specific
   * variables. This is different from regular aggregate validation because threshold and operator
   * are not relevant.
   *
   * @param condition The aggregate condition to validate
   * @param bucketId The ID of the data bucket to validate against
   * @param result The validation result to update
   */
  private void validateAggregateInComparisonAgainstBucket(
      AggregateCondition condition, Long bucketId, ValidationResult result) {
    if (condition == null) {
      result.addError("Aggregate condition cannot be null");
      return;
    }

    // Get bucket variables for validation
    Map<String, RuleVariable> bucketVariables = getBucketVariables(bucketId);

    // Check field if provided
    String fieldName = condition.getField();
    if (fieldName != null && !fieldName.isEmpty()) {
      RuleVariable variable = bucketVariables.get(fieldName);

      if (variable == null) {
        result.addError("Unknown variable: " + fieldName + " in bucket with ID: " + bucketId);
      } else if (!Boolean.TRUE.equals(variable.isAggregatable())) {
        result.addError("Variable is not aggregatable: " + variable.getName());
      }
    }

    // Validate function exists and is applicable to the variable
    String functionId = condition.getFunction();
    if (functionId == null || functionId.isEmpty()) {
      result.addError("Aggregate function must be specified");
      return;
    }

    boolean functionExists =
        ruleVariableService.getAllAggregateFunctions().stream()
            .anyMatch(f -> f.getId().equals(functionId));

    if (!functionExists) {
      result.addError("Unknown aggregate function: " + functionId);
    }

    // Note: We don't validate threshold or operator for aggregates in comparisons
    // since they're not used in the comparison context

    // Validate entity field exists
    String entityFieldId = condition.getEntityField();
    if (entityFieldId == null || entityFieldId.isEmpty()) {
      // If entity field is not specified, default to customer ID
      log.debug("Entity field not specified for aggregate condition, defaulting to customerId");
    }
  }

  /**
   * Validates a comparison condition by checking if the operator exists and is compatible, and
   * recursively validating the left and right conditions.
   *
   * @param condition The comparison condition to validate
   * @param result The validation result to update
   */
  private void validateComparisonCondition(ComparisonCondition condition, ValidationResult result) {
    if (condition == null) {
      result.addError("Comparison condition cannot be null");
      return;
    }

    // Validate operator exists and is applicable to numbers (comparison results are always numeric)
    String operatorSymbol = condition.getOperator();
    if (operatorSymbol == null || operatorSymbol.isEmpty()) {
      result.addError("Operator must be specified for comparison conditions");
      return;
    }

    // First try to find by ID, then by symbol if not found
    Operator operator = findOperatorById(operatorSymbol);

    // If not found by ID, try to find by symbol
    if (operator == null) {
      operator = findOperatorBySymbol(operatorSymbol);
    }

    if (operator == null) {
      result.addError("Unknown operator: " + operatorSymbol);
      return;
    }

    if (operator.getApplicableTypes() == null
        || !operator.getApplicableTypes().contains("number")) {
      result.addError(
          String.format(
              "Operator '%s' cannot be applied to comparison results (which are numeric)",
              operator.getName()));
    }

    // Validate left and right conditions
    if (condition.getLeft() == null) {
      result.addError("Left side of comparison condition cannot be null");
    } else {
      // Special handling for AggregateConditions inside ComparisonConditions
      if (condition.getLeft() instanceof AggregateCondition) {
        validateAggregateInComparison((AggregateCondition) condition.getLeft(), result);
      } else {
        validateCondition(condition.getLeft(), result);
      }
    }

    if (condition.getRight() == null) {
      result.addError("Right side of comparison condition cannot be null");
    } else {
      // Special handling for AggregateConditions inside ComparisonConditions
      if (condition.getRight() instanceof AggregateCondition) {
        validateAggregateInComparison((AggregateCondition) condition.getRight(), result);
      } else {
        validateCondition(condition.getRight(), result);
      }
    }
  }

  /**
   * Validates an aggregate condition that is part of a comparison condition. This is different from
   * regular aggregate validation because threshold and operator are not relevant.
   *
   * @param condition The aggregate condition to validate
   * @param result The validation result to update
   */
  private void validateAggregateInComparison(
      AggregateCondition condition, ValidationResult result) {
    if (condition == null) {
      result.addError("Aggregate condition cannot be null");
      return;
    }

    // Fetch all variables and create a map for efficient lookup
    Map<String, RuleVariable> variables =
        ruleVariableService.getAllVariables().stream()
            .collect(Collectors.toMap(RuleVariable::getName, Function.identity()));

    // Check field if provided
    String fieldName = condition.getField();
    if (fieldName != null && !fieldName.isEmpty()) {
      RuleVariable variable = variables.get(fieldName);

      if (variable == null) {
        result.addError("Unknown variable: " + fieldName);
      } else if (!Boolean.TRUE.equals(variable.isAggregatable())) {
        result.addError("Variable is not aggregatable: " + variable.getName());
      }
    }

    // Validate function exists and is applicable to the variable
    String functionId = condition.getFunction();
    if (functionId == null || functionId.isEmpty()) {
      result.addError("Aggregate function must be specified");
      return;
    }

    boolean functionExists =
        ruleVariableService.getAllAggregateFunctions().stream()
            .anyMatch(f -> f.getId().equals(functionId));

    if (!functionExists) {
      result.addError("Unknown aggregate function: " + functionId);
    }

    // Note: We don't validate threshold or operator for aggregates in comparisons
    // since they're not used in the comparison context

    // Validate entity field exists
    String entityFieldId = condition.getEntityField();
    if (entityFieldId == null || entityFieldId.isEmpty()) {
      // If entity field is not specified, default to customer ID
      log.debug("Entity field not specified for aggregate condition, defaulting to customerId");
    }
  }

  /** Result class for rule validation. */
  public static class ValidationResult {
    private boolean valid = true;
    private final List<String> errors = new ArrayList<>();

    public void addError(String error) {
      valid = false;
      errors.add(error);
    }

    public boolean isValid() {
      return valid;
    }

    public List<String> getErrors() {
      return errors;
    }
  }
}
