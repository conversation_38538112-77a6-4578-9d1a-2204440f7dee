/* eslint-disable */
import React, { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Typography,
  Tooltip,
  useTheme,
  Menu
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import LockIcon from '@mui/icons-material/Lock';
import SimpleCondition from './SimpleCondition';
import AggregateCondition from './AggregateCondition';
import ComparisonCondition from './ComparisonCondition';

const CompositeCondition = ({
  condition,
  onChange,
  onRemove,
  fields = [],
  operators = [],
  aggregateFunctions = [],
  isEditable = true
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const [anchorEl, setAnchorEl] = useState(null);

  // Use all fields directly
  const filteredFields = fields;

  // Helper function to check if a field is numeric
  const isNumericType = (type) => {
    if (!type) return false;
    return ['number', 'bigdecimal', 'integer', 'int', 'long', 'double', 'float'].includes(type.toLowerCase());
  };

  // To check if there are fields available for each condition type
  const hasSimpleFields = filteredFields.length > 0;
  const hasNumericFields = filteredFields.filter(f => isNumericType(f.dataTypeCode) || isNumericType(f.type)).length > 0;
  const hasAggregateFields = filteredFields.filter(f => f.aggregatable).length > 0;

  // Debug logging
  console.log('Fields available:', filteredFields);
  console.log('Fields with aggregatable=true:', filteredFields.filter(f => f.aggregatable));
  console.log('hasAggregateFields:', hasAggregateFields);

  // Ensure condition has proper structure
  const operator = condition.type || 'AND';
  const conditions = condition.conditions || [];

  const handleOperatorChange = (e) => {
    onChange({
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      type: e.target.value
    });
  };

  const handleAddConditionClick = (event) => {
    // Explicitly preventing the default behavior
    event.preventDefault();
    // Explicitly storing the current target
    const currentTarget = event.currentTarget;
    // Setting anchor with explicit timeout to ensure it happens after React's updates
    setTimeout(() => {
      setAnchorEl(currentTarget);
    }, 0);
  };

  const handleAddMenuClose = () => {
    setAnchorEl(null);
  };

  const handleAddSimpleCondition = () => {
    if (!hasSimpleFields) return;

    const fieldToUse = filteredFields[0];
    const compatibleOperators = operators.filter(op =>
      op.applicableTypes && op.applicableTypes.includes(fieldToUse.dataTypeCode || fieldToUse.type || 'string')
    );

    const newCondition = {
      id: `condition_${Date.now().toString()}`, // Ensure unique ID with prefix
      type: 'SIMPLE',
      field: fieldToUse.name, // Use name for rule creation
      fieldId: fieldToUse.id, // Store ID for UI selection
      operator: compatibleOperators.length > 0 ? compatibleOperators[0].id : '',
      value: ''
    };

    onChange({
      ...condition,
      id: condition.id, // Explicitly preserve parent ID
      conditions: [...conditions, newCondition]
    });

    handleAddMenuClose();
  };

  const handleAddAggregateCondition = () => {
    if (!hasAggregateFields) return;

    const aggregatableField = filteredFields.find(field => field.aggregatable);

    if (!aggregatableField) return;

    const numericOperators = operators.filter(op =>
      op.applicableTypes && op.applicableTypes.includes('number')
    );

    const newCondition = {
      id: `condition_${Date.now().toString()}`, // Ensure unique ID with prefix
      type: 'AGGREGATE',
      function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : '',
      field: aggregatableField.name, // Use name for rule creation
      fieldId: aggregatableField.id, // Store ID for UI selection
      period: 30,
      entityField: 'customerId',
      operator: numericOperators.length > 0 ? numericOperators[0].id : '',
      threshold: 1000
    };

    onChange({
      ...condition,
      id: condition.id, // Explicitly preserve parent ID
      conditions: [...conditions, newCondition]
    });

    handleAddMenuClose();
  };

  const handleAddNestedCondition = () => {
    const newCondition = {
      id: `condition_${Date.now().toString()}`, // Ensure unique ID with prefix
      type: 'AND',
      conditions: []
    };

    onChange({
      ...condition,
      id: condition.id, // Explicitly preserve parent ID
      conditions: [...conditions, newCondition]
    });

    handleAddMenuClose();
  };

  const handleAddComparisonCondition = () => {
    // Find an aggregatable field for the default conditions
    const aggregatableField = filteredFields.find(field => field.aggregatable) ||
                             (filteredFields.length > 0 ? filteredFields[0] : null);

    if (!aggregatableField) {
      console.error('No fields available for comparison condition');
      return;
    }

    // Get comparison operators
    const comparisonOperators = operators.filter(op =>
      ['>', '<', '>=', '<=', '==', '!='].includes(op.symbol)
    );

    // Create left and right conditions (both AGGREGATE by default)
    const leftCondition = {
      id: `left_${Date.now()}`,
      type: 'AGGREGATE',
      function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : 'AVG',
      field: aggregatableField.name,
      fieldId: aggregatableField.id,
      period: 7,
      operator: '>',
      threshold: 0,
      entityField: 'customerId'
    };

    const rightCondition = {
      id: `right_${Date.now() + 1}`,
      type: 'AGGREGATE',
      function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : 'AVG',
      field: aggregatableField.name,
      fieldId: aggregatableField.id,
      period: 7,
      operator: '>',
      threshold: 0,
      entityField: 'customerId'
    };

    // Create the comparison condition
    const newCondition = {
      id: `condition_${Date.now().toString()}`,
      type: 'COMPARISON',
      operator: comparisonOperators.length > 0 ? comparisonOperators[0].symbol : '>',
      left: leftCondition,
      right: rightCondition,
      multiplier: 2 // Default multiplier for "2x higher"
    };

    onChange({
      ...condition,
      id: condition.id,
      conditions: [...conditions, newCondition]
    });

    handleAddMenuClose();
  };

  const handleRemoveSubCondition = (id) => {
    // Ensure we don't delete everything if conditions don't have IDs
    if (!id) {
      console.error('Attempted to delete sub-condition without ID');
      return;
    }

    // Make sure we have a valid conditions array
    if (!Array.isArray(conditions)) {
      console.error('Sub-conditions is not an array:', conditions);
      return;
    }

    // Check if we have the condition we're trying to remove
    const conditionToRemove = conditions.find(c => c.id === id);
    if (!conditionToRemove) {
      console.error('Sub-condition ID not found in conditions:', id);
      return;
    }

    // Filter out just the one condition with the matching ID
    const newConditions = conditions.filter(c => c.id !== id);

    onChange({
      ...condition,
      id: condition.id, // Explicitly preserve parent ID
      conditions: newConditions
    });
  };

  const handleUpdateSubCondition = (updatedCondition) => {
    // Ensure we have a valid conditions array
    if (!Array.isArray(conditions)) {
      console.error('Sub-conditions is not an array:', conditions);
      return;
    }

    // Find index of condition to update
    const index = conditions.findIndex(c => c.id === updatedCondition.id);
    if (index === -1) {
      console.error('Sub-condition ID not found for update:', updatedCondition.id);
      return;
    }

    // Create a new array with the updated condition
    const newConditions = [...conditions];
    newConditions[index] = updatedCondition;

    // Update parent condition with new array
    onChange({
      ...condition,
      id: condition.id, // Explicitly preserve parent ID
      conditions: newConditions
    });
  };

  // Helper function to determine the type of condition
  const getConditionType = (condition) => {
    if (!condition || !condition.type) return null;

    // Check if it's a simple condition
    if (condition.type === 'SIMPLE') {
      return 'SIMPLE';
    }

    // Check if it's an aggregate condition
    if (condition.type === 'AGGREGATE') {
      return 'AGGREGATE';
    }

    // Otherwise assume it's a composite (AND/OR)
    return condition.type;
  };

  // Helper to get display name with category info if needed
  const getFieldDisplayName = (fieldId) => {
    if (!fieldId || !fields.length) return '';

    const field = fields.find(f => f.id === fieldId);
    if (!field) return fieldId;

    return field.category
      ? `${field.name} (${field.category})`
      : field.name;
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        position: 'relative',
        borderLeft: operator === 'AND'
          ? '4px solid #63B3ED'
          : '4px solid #FC8181',
        backgroundColor: theme.palette.background.paper
      }}
    >
      <Box sx={{ position: 'absolute', top: 8, right: 8, display: 'flex' }}>
        <IconButton
          size="small"
          color="error"
          onClick={onRemove}
        >
          <DeleteIcon />
        </IconButton>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          Group Condition
        </Typography>
        <FormControl size="small" sx={{ ml: 2, minWidth: 120 }}>
          <InputLabel>Operator</InputLabel>
          <Select
            value={operator}
            label="Operator"
            onChange={handleOperatorChange}
          >
            <MenuItem value="AND">AND</MenuItem>
            <MenuItem value="OR">OR</MenuItem>
          </Select>
        </FormControl>
        <Tooltip title={operator === 'AND'
          ? 'All conditions must be true'
          : 'At least one condition must be true'}>
          <Typography variant="caption" color="textSecondary" sx={{ ml: 1 }}>
            ({operator === 'AND' ? 'all must be true' : 'any can be true'})
          </Typography>
        </Tooltip>
      </Box>

      <Box sx={{ mt: 2 }}>
        {conditions.length === 0 ? (
          <Box
            sx={{
              p: 3,
              border: `1px dashed ${theme.palette.divider}`,
              borderRadius: 1,
              textAlign: 'center'
            }}
          >
            <Typography color="textSecondary" sx={{ mb: 1 }}>
              No conditions in this group
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<AddIcon />}
              size="small"
              onClick={handleAddConditionClick}
            >
              Add Condition
            </Button>
          </Box>
        ) : (
          <Grid container spacing={2} direction="column">
            {conditions.map((subCondition, index) => {
              const conditionType = getConditionType(subCondition);
              return (
                <Grid item key={subCondition.id || `fallback_${index}`}>
                  {conditionType === 'SIMPLE' && (
                    <SimpleCondition
                      key={`simple_${subCondition.id}`}
                      condition={subCondition}
                      onChange={handleUpdateSubCondition}
                      onRemove={() => handleRemoveSubCondition(subCondition.id)}
                      fields={fields}
                      filteredFields={filteredFields}
                      operators={operators}
                      isEditable={isEditable}
                      getFieldDisplayName={getFieldDisplayName}
                    />
                  )}

                  {conditionType === 'AGGREGATE' && (
                    <AggregateCondition
                      key={`aggregate_${subCondition.id}`}
                      condition={subCondition}
                      onChange={handleUpdateSubCondition}
                      onRemove={() => handleRemoveSubCondition(subCondition.id)}
                      fields={fields}
                      filteredFields={filteredFields}
                      operators={operators}
                      aggregateFunctions={aggregateFunctions}
                      isEditable={isEditable}
                      getFieldDisplayName={getFieldDisplayName}
                    />
                  )}

                  {(conditionType === 'AND' || conditionType === 'OR') && (
                    <CompositeCondition
                      key={`composite_${subCondition.id}`}
                      condition={subCondition}
                      onChange={handleUpdateSubCondition}
                      onRemove={() => handleRemoveSubCondition(subCondition.id)}
                      fields={fields}
                      operators={operators}
                      aggregateFunctions={aggregateFunctions}
                      isEditable={isEditable}
                    />
                  )}

                  {conditionType === 'COMPARISON' && (
                    <ComparisonCondition
                      key={`comparison_${subCondition.id}`}
                      condition={subCondition}
                      onChange={handleUpdateSubCondition}
                      onRemove={() => handleRemoveSubCondition(subCondition.id)}
                      fields={fields}
                      operators={operators}
                      aggregateFunctions={aggregateFunctions}
                      isEditable={isEditable}
                    />
                  )}
                </Grid>
              );
            })}

            <Grid item>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<AddIcon />}
                size="small"
                onClick={handleAddConditionClick}
                sx={{ mt: 1 }}
              >
                Add Condition
              </Button>
            </Grid>
          </Grid>
        )}
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleAddMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            style: { zIndex: 9999 }
          }
        }}
      >
        <MenuItem
          onClick={handleAddSimpleCondition}
          disabled={!hasSimpleFields}
        >
          Simple Condition
        </MenuItem>

        <MenuItem
          onClick={handleAddAggregateCondition}
          disabled={!hasAggregateFields}
        >
          Aggregate Condition
        </MenuItem>

        <MenuItem
          onClick={handleAddNestedCondition}
        >
          Nested Group
        </MenuItem>

        <MenuItem
          onClick={handleAddComparisonCondition}
        >
          Comparison Condition
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default CompositeCondition;
