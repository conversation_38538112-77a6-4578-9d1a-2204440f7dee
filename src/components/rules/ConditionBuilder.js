import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Grid,
  Divider,
  Typography,
  Menu,
  MenuItem,
  Tooltip,
  useTheme
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SimpleCondition from './SimpleCondition';
import AggregateCondition from './AggregateCondition';
import CompositeCondition from './CompositeCondition';
import ComparisonCondition from './ComparisonCondition';

const ConditionBuilder = ({
  conditions = [],
  onChange,
  fields = [],
  operators = [],
  aggregateFunctions = [],
  isEditable = true
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState(null);

  // Use all fields directly since we've removed category filtering
  const filteredFields = fields;

  // Helper function to check for numeric types
  const isNumericType = (type) => {
    return ['number', 'bigdecimal', 'integer', 'int', 'long', 'double', 'float'].includes(type);
  };

  // To check if there are fields available for each condition type
  const hasSimpleFields = filteredFields.length > 0;
  const hasAggregateFields = filteredFields.filter(f => f.aggregatable).length > 0;

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAddSimpleCondition = () => {
    if (!hasSimpleFields) return;

    const firstField = filteredFields[0];
    const compatibleOperators = operators.filter(op =>
      op.applicableTypes && op.applicableTypes.includes(firstField.type || 'string')
    );

    const newCondition = {
      id: `condition_${Date.now().toString()}`,
      type: 'SIMPLE',
      field: firstField.id,
      operator: compatibleOperators.length > 0 ? compatibleOperators[0].id : '',
      value: ''
    };

    onChange([...conditions, newCondition]);
    handleClose();
  };

  const handleAddAggregateCondition = () => {
    if (!hasAggregateFields) return;

    // Find first aggregatable numeric field
    const numericField = filteredFields.find(
      field => field.aggregatable
    );

    if (!numericField) return;

    const numericOperators = operators.filter(op =>
      op.applicableTypes && op.applicableTypes.includes('number')
    );

    const newCondition = {
      id: `condition_${Date.now().toString()}`,
      type: 'AGGREGATE',
      function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : '',
      field: numericField.name, // Use name for rule creation
      fieldId: numericField.id, // Store ID for UI selection
      period: 30,
      startOffset: 0, // Start from today
      entityField: 'customerId',
      operator: numericOperators.length > 0 ? numericOperators[0].id : '',
      threshold: 1000
    };

    onChange([...conditions, newCondition]);
    handleClose();
  };

  const handleAddCompositeCondition = () => {
    if (!hasSimpleFields) return;

    const newCondition = {
      id: `condition_${Date.now().toString()}`,
      type: 'AND',
      conditions: []
    };

    onChange([...conditions, newCondition]);
    handleClose();
  };

  const handleAddComparisonCondition = () => {
    if (!hasAggregateFields) return;

    // Find an aggregatable field for the default conditions
    const aggregatableField = filteredFields.find(field => field.aggregatable) ||
                             (filteredFields.length > 0 ? filteredFields[0] : null);

    if (!aggregatableField) {
      console.error('No fields available for comparison condition');
      return;
    }

    // Get comparison operators
    const comparisonOperators = operators.filter(op =>
      ['>', '<', '>=', '<=', '==', '!='].includes(op.symbol)
    );

    // Create left and right conditions (both AGGREGATE by default)
    const leftCondition = {
      id: `left_${Date.now()}`,
      type: 'AGGREGATE',
      function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : 'AVG',
      field: aggregatableField.name,
      fieldId: aggregatableField.id,
      period: 7,
      startOffset: 0, // Start from today
      operator: '>',
      threshold: 0,
      entityField: 'customerId'
    };

    const rightCondition = {
      id: `right_${Date.now() + 1}`,
      type: 'AGGREGATE',
      function: aggregateFunctions.length > 0 ? aggregateFunctions[0].id : 'AVG',
      field: aggregatableField.name,
      fieldId: aggregatableField.id,
      period: 7,  // Same period for comparison
      startOffset: 7, // Start from 7 days ago
      operator: '>',
      threshold: 0,
      entityField: 'customerId'
    };

    // Create the comparison condition
    const newCondition = {
      id: `condition_${Date.now().toString()}`,
      type: 'COMPARISON',
      operator: comparisonOperators.length > 0 ? comparisonOperators[0].symbol : '>',
      left: leftCondition,
      right: rightCondition,
      multiplier: 2 // Default multiplier for "2x higher"
    };

    onChange([...conditions, newCondition]);
    handleClose();
  };

  const handleRemoveCondition = (id) => {
    // Log information for debugging
    console.log('Attempting to remove condition with ID:', id);
    console.log('Current conditions:', conditions);

    // Ensure we don't delete everything if conditions don't have IDs
    if (!id) {
      console.error('Attempted to delete condition without ID');
      return;
    }

    // Make sure we have a valid conditions array
    if (!Array.isArray(conditions)) {
      console.error('Conditions is not an array:', conditions);
      return;
    }

    // Check if we have the condition we're trying to remove
    const conditionToRemove = conditions.find(c => c.id === id);
    if (!conditionToRemove) {
      console.error('Condition ID not found in conditions:', id);
      return;
    }

    // Filter out just the one condition with the matching ID
    const newConditions = conditions.filter(c => c.id !== id);
    console.log('New conditions after removal:', newConditions);
    onChange(newConditions);
  };

  const handleUpdateCondition = useCallback((updatedCondition) => {
    // Add debug logging to see what's being updated
    console.log('Condition update received:', updatedCondition);

    // Defensive check for condition ID
    if (!updatedCondition.id) {
      console.error('Updated condition is missing ID:', updatedCondition);
      updatedCondition.id = `condition_${Date.now()}`;
    }

    // Force React to detect this as a new state by creating a completely
    // new conditions array with a completely new condition object
    const updatedConditions = conditions.map(c => {
      if (c.id === updatedCondition.id) {
        // Create a completely new object with all properties from updatedCondition
        return JSON.parse(JSON.stringify(updatedCondition));
      }
      // Leave other conditions unchanged
      return c;
    });

    console.log('Setting updated conditions:', updatedConditions);

    // For additional safety, we can stringify and parse the conditions to
    // ensure all references are broken
    const safeUpdatedConditions = JSON.parse(JSON.stringify(updatedConditions));

    // Call the parent's onChange with the new conditions
    onChange(safeUpdatedConditions);
  }, [conditions, onChange]);

  // Helper to get display name with category info if needed
  const getFieldDisplayName = (fieldId) => {
    const field = fields.find(f => f.id === fieldId);
    if (!field) return fieldId;

    return field.category
      ? `${field.name} (${field.category})`
      : field.name;
  };

  // Check if a condition's field belongs to the current category
  const isConditionEditable = (condition) => {
    // Now that we've removed category filtering, all conditions should be editable
    return isEditable;
  };

  // Helper function to determine the type of condition
  const getConditionType = (condition) => {
    if (!condition) return null;
    if (!condition.type) return null;

    // Normalize condition type for consistency
    const type = condition.type.toUpperCase();

    if (type === 'SIMPLE') return 'SIMPLE';
    if (type === 'AGGREGATE') return 'AGGREGATE';
    if (type === 'AND' || type === 'OR') return type;

    return type;
  };

  return (
    <Box>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          Conditions
        </Typography>

        <Button
          variant="outlined"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleClick}
          size="small"
        >
          Add Condition
        </Button>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
        >
          <Tooltip
            title={!hasSimpleFields ? `No fields available` : ""}
            placement="right"
          >
            <div>
              <MenuItem
                onClick={handleAddSimpleCondition}
                disabled={!hasSimpleFields}
              >
                Simple Condition
              </MenuItem>
            </div>
          </Tooltip>

          <Tooltip
            title={!hasAggregateFields ? `No numeric fields available` : ""}
            placement="right"
          >
            <div>
              <MenuItem
                onClick={handleAddAggregateCondition}
                disabled={!hasAggregateFields}
              >
                Aggregate Condition
              </MenuItem>
            </div>
          </Tooltip>

          <Tooltip
            title={!hasSimpleFields ? `No fields available` : ""}
            placement="right"
          >
            <div>
              <MenuItem
                onClick={handleAddCompositeCondition}
                disabled={!hasSimpleFields}
              >
                Composite Condition
              </MenuItem>
            </div>
          </Tooltip>

          <Tooltip
            title={!hasAggregateFields ? `No numeric fields available` : ""}
            placement="right"
          >
            <div>
              <MenuItem
                onClick={handleAddComparisonCondition}
                disabled={!hasAggregateFields}
              >
                Comparison Condition
              </MenuItem>
            </div>
          </Tooltip>
        </Menu>
      </Box>

      <Divider sx={{ mb: 2 }} />

      <Grid container spacing={2} direction="column">
        {conditions.length === 0 ? (
          <Grid item>
            <Box
              sx={{
                p: 3,
                border: `1px dashed ${theme.palette.divider}`,
                borderRadius: 1,
                textAlign: 'center'
              }}
            >
              <Typography color="textSecondary">
                No conditions added yet. Click "Add Condition" to get started.
              </Typography>
            </Box>
          </Grid>
        ) : (
          conditions.map(condition => {
            const conditionType = getConditionType(condition);
            return (
              <Grid item key={condition.id || Math.random()}>
                {conditionType === 'SIMPLE' && (
                  <SimpleCondition
                    condition={condition}
                    onChange={handleUpdateCondition}
                    onRemove={() => handleRemoveCondition(condition.id)}
                    fields={fields}
                    filteredFields={filteredFields}
                    operators={operators}
                    isEditable={isConditionEditable(condition)}
                    getFieldDisplayName={getFieldDisplayName}
                  />
                )}

                {conditionType === 'AGGREGATE' && (
                  <AggregateCondition
                    condition={condition}
                    onChange={handleUpdateCondition}
                    onRemove={() => handleRemoveCondition(condition.id)}
                    fields={fields}
                    filteredFields={filteredFields}
                    operators={operators}
                    aggregateFunctions={aggregateFunctions}
                    isEditable={isConditionEditable(condition)}
                    getFieldDisplayName={getFieldDisplayName}
                  />
                )}

                {(conditionType === 'AND' || conditionType === 'OR') && (
                  <CompositeCondition
                    condition={condition}
                    onChange={handleUpdateCondition}
                    onRemove={() => handleRemoveCondition(condition.id)}
                    fields={fields}
                    operators={operators}
                    aggregateFunctions={aggregateFunctions}
                    isEditable={isConditionEditable(condition)}
                  />
                )}

                {conditionType === 'COMPARISON' && (
                  <ComparisonCondition
                    condition={condition}
                    onChange={handleUpdateCondition}
                    onRemove={() => handleRemoveCondition(condition.id)}
                    fields={fields}
                    operators={operators}
                    aggregateFunctions={aggregateFunctions}
                    isEditable={isConditionEditable(condition)}
                  />
                )}
              </Grid>
            );
          })
        )}
      </Grid>
    </Box>
  );
};

export default ConditionBuilder;
