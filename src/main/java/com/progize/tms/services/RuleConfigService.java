package com.progize.tms.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.RuleRepository;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.services.models.Condition;
import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class RuleConfigService {
  private static final Logger log = LoggerFactory.getLogger(RuleConfigService.class);

  private final RuleRepository ruleRepository;
  private final RuleExpressionBuilder ruleExpressionBuilder;
  private final ObjectMapper objectMapper;

  @Transactional
  public Rule createRule(Rule rule) {
    try {
      // Generate expression from the rule JSON condition
      if (rule.getRuleJson() != null && !rule.getRuleJson().isEmpty()) {
        Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

        // Use bucket-specific variables if a bucket is associated with the rule
        Long bucketId = rule.getDataBucket() != null ? rule.getDataBucket().getId() : null;
        String mvelExpression = ruleExpressionBuilder.buildMvelExpression(condition, bucketId);

        rule.setExpression(mvelExpression);
      }
      return ruleRepository.save(rule);
    } catch (JsonProcessingException e) {
      log.error("Error deserializing rule condition from JSON", e);
      throw new RuntimeException("Failed to process rule condition", e);
    }
  }

  @Transactional
  public Rule updateRule(String id, Rule updatedRule) {
    Rule existingRule =
        ruleRepository
            .findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Rule not found with id: " + id));

    existingRule.setName(updatedRule.getName());
    existingRule.setDescription(updatedRule.getDescription());
    existingRule.setFlagReason(updatedRule.getFlagReason());
    existingRule.setActive(updatedRule.isActive());
    existingRule.setPriority(updatedRule.getPriority());

    // Update data bucket association if provided
    if (updatedRule.getDataBucket() != null) {
      existingRule.setDataBucket(updatedRule.getDataBucket());
    }

    // Set and update the rule JSON
    existingRule.setRuleJson(updatedRule.getRuleJson());

    // Generate expression from the updated rule JSON condition
    try {
      if (updatedRule.getRuleJson() != null && !updatedRule.getRuleJson().isEmpty()) {
        Condition condition = objectMapper.readValue(updatedRule.getRuleJson(), Condition.class);

        // Use bucket-specific variables if a bucket is associated with the rule
        Long bucketId =
            existingRule.getDataBucket() != null ? existingRule.getDataBucket().getId() : null;
        String mvelExpression = ruleExpressionBuilder.buildMvelExpression(condition, bucketId);

        existingRule.setExpression(mvelExpression);
      }
    } catch (JsonProcessingException e) {
      log.error("Error deserializing rule condition from JSON", e);
      throw new RuntimeException("Failed to process rule condition", e);
    }

    return ruleRepository.save(existingRule);
  }

  public Rule getRule(String id) {
    return ruleRepository
        .findById(id)
        .orElseThrow(() -> new EntityNotFoundException("Rule not found with id: " + id));
  }

  public List<Rule> getAllRules() {
    return ruleRepository.findAll();
  }

  /**
   * Get all active rules ordered by priority.
   *
   * @return List of active rules
   */
  public List<Rule> getActiveRules() {
    return ruleRepository.findByActiveOrderByPriorityAsc(true);
  }

  /**
   * Get active rules for a specific data bucket ordered by priority.
   *
   * @param bucketId The ID of the data bucket
   * @return List of active rules for the specified bucket
   */
  public List<Rule> getActiveRulesByBucket(Long bucketId) {
    return ruleRepository.findByActiveAndDataBucket_IdOrderByPriorityAsc(true, bucketId);
  }

  @Transactional
  public void deleteRule(String id) {
    ruleRepository.deleteById(id);
  }

  @Transactional
  public Rule activateRule(String id) {
    Rule rule =
        ruleRepository
            .findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Rule not found with id: " + id));
    rule.setActive(true);
    return ruleRepository.save(rule);
  }

  @Transactional
  public Rule deactivateRule(String id) {
    Rule rule =
        ruleRepository
            .findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Rule not found with id: " + id));
    rule.setActive(false);
    return ruleRepository.save(rule);
  }

  /**
   * Validates a rule.
   *
   * @param rule The rule to validate
   * @return true if the rule is valid, false otherwise
   */
  public boolean validateRule(Rule rule) {
    if (rule == null) {
      return false;
    }

    try {
      // If we have a rule JSON, generate the expression and validate it
      if (rule.getRuleJson() != null && !rule.getRuleJson().isEmpty()) {
        Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

        // Use bucket-specific variables if a bucket is associated with the rule
        Long bucketId = rule.getDataBucket() != null ? rule.getDataBucket().getId() : null;
        String mvelExpression = ruleExpressionBuilder.buildMvelExpression(condition, bucketId);

        return validateRuleExpression(mvelExpression);
      } else if (rule.getExpression() != null && !rule.getExpression().trim().isEmpty()) {
        // If no JSON but we have an expression, validate the expression directly
        return validateRuleExpression(rule.getExpression());
      }
      return false;
    } catch (Exception e) {
      log.error("Error validating rule", e);
      return false;
    }
  }

  /**
   * Validates a rule expression.
   *
   * @param expression The expression to validate
   * @return true if the expression is valid, false otherwise
   */
  private boolean validateRuleExpression(String expression) {
    if (expression == null || expression.trim().isEmpty()) {
      return false;
    }

    try {
      // Basic validation - in a real application, this would use a proper expression parser
      // For now, we'll just check if it's not empty and has some basic structure
      return expression.contains("==")
          || expression.contains("!=")
          || expression.contains(">")
          || expression.contains("<")
          || expression.contains(">=")
          || expression.contains("<=");
    } catch (Exception e) {
      return false;
    }
  }
}
