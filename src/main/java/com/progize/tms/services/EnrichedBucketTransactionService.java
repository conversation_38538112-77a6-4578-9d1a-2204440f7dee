package com.progize.tms.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.repository.BucketRelationshipRepository;
import com.progize.tms.repository.DataBucketRepository;
import com.progize.tms.repository.DataBucketVariableRepository;
import com.progize.tms.repository.DataSourceRepository;
import com.progize.tms.repository.DatabaseSourceRepository;
import com.progize.tms.repository.RestApiSourceRepository;
import com.progize.tms.repository.RuleRepository;
import com.progize.tms.repository.entity.BucketRelationshipEntity;
import com.progize.tms.repository.entity.DataBucketEntity;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import com.progize.tms.repository.entity.RestApiSourceEntity;
import com.progize.tms.repository.entity.Rule;
import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.repository.entity.WorkflowType;
import com.progize.tms.repository.entity.enums.AuthenticationType;
import com.progize.tms.repository.entity.enums.BucketType;
import com.progize.tms.repository.entity.enums.DatabaseType;
import com.progize.tms.service.WorkflowCaseService;
import com.progize.tms.services.models.Condition;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * Service for processing transactions with enrichment from external data sources. This service
 * handles fetching data from rule and lookup buckets, joining them based on bucket relationships,
 * and processing the enriched data against rules.
 */
@Service
@Slf4j
public class EnrichedBucketTransactionService {

  private final DataBucketRepository bucketRepository;
  private final DataBucketVariableRepository variableRepository;
  private final BucketRelationshipRepository relationshipRepository;
  private final DataSourceRepository dataSourceRepository;
  private final DatabaseSourceRepository databaseSourceRepository;
  private final RestApiSourceRepository restApiSourceRepository;
  private final RuleRepository ruleRepository;
  private final RuleExecutor ruleExecutor;
  private final RuleEvaluationService ruleEvaluationService;
  private final WorkflowCaseService workflowCaseService;
  private final ObjectMapper objectMapper;
  private final RestTemplate restTemplate;

  public EnrichedBucketTransactionService(
      DataBucketRepository bucketRepository,
      DataBucketVariableRepository variableRepository,
      BucketRelationshipRepository relationshipRepository,
      DataSourceRepository dataSourceRepository,
      DatabaseSourceRepository databaseSourceRepository,
      RestApiSourceRepository restApiSourceRepository,
      RuleRepository ruleRepository,
      RuleExecutor ruleExecutor,
      RuleEvaluationService ruleEvaluationService,
      WorkflowCaseService workflowCaseService,
      ObjectMapper objectMapper,
      RestTemplate restTemplate) {
    this.bucketRepository = bucketRepository;
    this.variableRepository = variableRepository;
    this.relationshipRepository = relationshipRepository;
    this.dataSourceRepository = dataSourceRepository;
    this.databaseSourceRepository = databaseSourceRepository;
    this.restApiSourceRepository = restApiSourceRepository;
    this.ruleRepository = ruleRepository;
    this.ruleExecutor = ruleExecutor;
    this.ruleEvaluationService = ruleEvaluationService;
    this.workflowCaseService = workflowCaseService;
    this.objectMapper = objectMapper;
    this.restTemplate = restTemplate;
  }

  /**
   * Process transactions with enrichment from lookup buckets. This method fetches data from the
   * rule bucket and its related lookup buckets, joins them based on the defined relationships, and
   * processes the enriched data against rules.
   *
   * @param ruleBucketId The ID of the rule bucket
   * @param transactions The transactions to process (optional, if not provided will fetch from data
   *     source)
   * @return List of processed transactions with flag status
   */
  public List<Map<String, Object>> processEnrichedBucketTransactions(
      Long ruleBucketId, List<Map<String, Object>> transactions) {

    log.info("Processing enriched bucket transactions for bucket ID: {}", ruleBucketId);

    // Get the rule bucket
    DataBucketEntity ruleBucket =
        bucketRepository
            .findById(ruleBucketId)
            .orElseThrow(
                () -> new IllegalArgumentException("Rule bucket not found: " + ruleBucketId));

    // Verify it's a rule bucket
    if (ruleBucket.getBucketType() != BucketType.RULE_BUCKET) {
      throw new IllegalArgumentException("Bucket is not a rule bucket: " + ruleBucketId);
    }

    // Get the data source for the rule bucket
    DataSourceEntity dataSource = ruleBucket.getDataSource();
    if (dataSource == null) {
      throw new IllegalArgumentException(
          "Rule bucket has no data source configured: " + ruleBucketId);
    }

    // Get the bucket variables
    List<DataBucketVariableEntity> bucketVariables =
        variableRepository.findByDataBucketId(ruleBucketId);
    if (bucketVariables.isEmpty()) {
      throw new IllegalArgumentException("Rule bucket has no variables defined: " + ruleBucketId);
    }

    // Get relationships for this rule bucket
    List<BucketRelationshipEntity> relationships =
        relationshipRepository.findByRuleBucketId(ruleBucketId);
    log.info("Found {} relationships for rule bucket {}", relationships.size(), ruleBucketId);

    // If no transactions provided, fetch from data source
    List<Map<String, Object>> transactionsToProcess = transactions;
    if (transactionsToProcess == null || transactionsToProcess.isEmpty()) {
      log.info("No transactions provided, fetching from data source");
      transactionsToProcess = fetchDataFromSource(ruleBucket, bucketVariables);
      log.info("Fetched {} transactions from data source", transactionsToProcess.size());
    }

    // If there are relationships, enrich the transactions
    if (!relationships.isEmpty()) {
      log.info("Enriching transactions with lookup data");
      transactionsToProcess =
          enrichTransactionsWithLookupData(transactionsToProcess, relationships, bucketVariables);
      log.info("Enriched transactions count: {}", transactionsToProcess.size());
    }

    // Process the enriched transactions against rules
    return processTransactionsWithRules(ruleBucketId, transactionsToProcess);
  }

  /**
   * Fetch data from a data source based on the bucket configuration.
   *
   * @param bucket The data bucket
   * @param variables The bucket variables
   * @return List of data rows from the source
   */
  private List<Map<String, Object>> fetchDataFromSource(
      DataBucketEntity bucket, List<DataBucketVariableEntity> variables) {

    DataSourceEntity dataSource = bucket.getDataSource();
    if (dataSource == null) {
      throw new IllegalArgumentException("Bucket has no data source configured: " + bucket.getId());
    }

    // Handle different data source types
    switch (dataSource.getType()) {
      case DATABASE:
        return fetchDataFromDatabase(bucket, variables, dataSource);
      case REST_API:
        return fetchDataFromRestApi(bucket, variables, dataSource);
      default:
        throw new IllegalArgumentException("Unsupported data source type: " + dataSource.getType());
    }
  }

  /**
   * Fetch data from a database data source.
   *
   * @param bucket The data bucket
   * @param variables The bucket variables
   * @param dataSource The data source entity
   * @return List of data rows from the database
   */
  private List<Map<String, Object>> fetchDataFromDatabase(
      DataBucketEntity bucket,
      List<DataBucketVariableEntity> variables,
      DataSourceEntity dataSource) {

    // Get the database source details
    DatabaseSourceEntity databaseSource =
        databaseSourceRepository
            .findByDataSourceId(dataSource.getId())
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Database source not found for data source: " + dataSource.getId()));

    // Create a JDBC data source
    DataSource jdbcDataSource = createDataSource(databaseSource);
    JdbcTemplate jdbcTemplate = new JdbcTemplate(jdbcDataSource);

    // Build the query to fetch data
    String tableName = bucket.getTableName();
    List<String> columnNames =
        variables.stream()
            .map(DataBucketVariableEntity::getColumnName)
            .collect(Collectors.toList());

    String query = buildSelectQuery(tableName, columnNames);
    log.debug("Executing query: {}", query);

    // Execute the query and map results
    List<Map<String, Object>> results = new ArrayList<>();
    try {
      results =
          jdbcTemplate.query(
              query,
              (rs, rowNum) -> {
                Map<String, Object> row = new HashMap<>();
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                // Map database columns to variable names
                for (int i = 1; i <= columnCount; i++) {
                  String columnName = metaData.getColumnName(i);
                  Object value = rs.getObject(i);

                  // Find the corresponding variable
                  for (DataBucketVariableEntity variable : variables) {
                    if (variable.getColumnName().equalsIgnoreCase(columnName)) {
                      row.put(variable.getName(), value);
                      break;
                    }
                  }
                }

                return row;
              });
    } catch (Exception e) {
      log.error("Error executing query: {}", e.getMessage(), e);
      throw new RuntimeException("Error fetching data from database", e);
    }

    return results;
  }

  /**
   * Fetch data from a REST API data source.
   *
   * @param bucket The data bucketfadf
   * @param variables The bucket variables
   * @param dataSource The data source entity
   * @return List of data rows from the REST API
   */
  private List<Map<String, Object>> fetchDataFromRestApi(
      DataBucketEntity bucket,
      List<DataBucketVariableEntity> variables,
      DataSourceEntity dataSource) {

    // Get the REST API source details
    RestApiSourceEntity restApiSource =
        restApiSourceRepository
            .findByDataSourceId(dataSource.getId())
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "REST API source not found for data source: " + dataSource.getId()));

    // Build the request URL
    String baseUrl = restApiSource.getBaseUrl();
    String endpoint = bucket.getTableName(); // Use table name as endpoint
    String url = baseUrl;
    if (!baseUrl.endsWith("/") && !endpoint.startsWith("/")) {
      url += "/";
    }
    url += endpoint;

    log.debug("Fetching data from REST API: {}", url);

    // Set up headers based on authentication type
    HttpHeaders headers = new HttpHeaders();
    switch (restApiSource.getAuthenticationType()) {
      case BASIC:
        // Basic authentication would be handled by RestTemplate configuration
        break;
      case API_KEY:
        // API key authentication
        if (restApiSource.getApiKeyName() != null && restApiSource.getApiKeyValue() != null) {
          String location = restApiSource.getApiKeyLocation();
          if (location != null && location.equalsIgnoreCase("HEADER")) {
            headers.add(restApiSource.getApiKeyName(), restApiSource.getApiKeyValue());
          }
          // For query parameter API keys, they will be added to the URL later
        }
        break;
      case OAUTH2:
        // OAuth2 authentication would be handled by RestTemplate configuration
        break;
      case NONE:
        // No authentication needed
        break;
    }

    // Add API key as query parameter if needed
    if (restApiSource.getAuthenticationType() == AuthenticationType.API_KEY
        && restApiSource.getApiKeyName() != null
        && restApiSource.getApiKeyValue() != null) {
      String location = restApiSource.getApiKeyLocation();
      if (location != null && location.equalsIgnoreCase("QUERY_PARAM")) {
        // Check if URL already has query parameters
        if (url.contains("?")) {
          url += "&" + restApiSource.getApiKeyName() + "=" + restApiSource.getApiKeyValue();
        } else {
          url += "?" + restApiSource.getApiKeyName() + "=" + restApiSource.getApiKeyValue();
        }
      }
    }

    // Execute the request
    try {
      HttpEntity<String> entity = new HttpEntity<>(headers);
      ResponseEntity<List> response =
          restTemplate.exchange(url, HttpMethod.GET, entity, List.class);

      List<Map<String, Object>> results = new ArrayList<>();
      if (response.getBody() != null) {
        // Convert response to list of maps
        for (Object item : response.getBody()) {
          if (item instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> itemMap = (Map<String, Object>) item;
            Map<String, Object> row = new HashMap<>();

            // Map API response fields to variable names
            for (DataBucketVariableEntity variable : variables) {
              String fieldName = variable.getColumnName();
              if (itemMap.containsKey(fieldName)) {
                row.put(variable.getName(), itemMap.get(fieldName));
              }
            }

            results.add(row);
          }
        }
      }

      return results;
    } catch (Exception e) {
      log.error("Error fetching data from REST API: {}", e.getMessage(), e);
      throw new RuntimeException("Error fetching data from REST API", e);
    }
  }

  /**
   * Enrich transactions with data from lookup buckets.
   *
   * @param transactions The transactions to enrich
   * @param relationships The bucket relationships
   * @param ruleBucketVariables The rule bucket variables
   * @return List of enriched transactions
   */
  private List<Map<String, Object>> enrichTransactionsWithLookupData(
      List<Map<String, Object>> transactions,
      List<BucketRelationshipEntity> relationships,
      List<DataBucketVariableEntity> ruleBucketVariables) {

    // Create a map of variable ID to variable name for the rule bucket
    Map<Long, String> ruleVarIdToName =
        ruleBucketVariables.stream()
            .collect(
                Collectors.toMap(
                    DataBucketVariableEntity::getId, DataBucketVariableEntity::getName));

    List<Map<String, Object>> enrichedTransactions = new ArrayList<>(transactions);

    // For each relationship, fetch lookup data and join with transactions
    for (BucketRelationshipEntity relationship : relationships) {
      DataBucketEntity lookupBucket = relationship.getLookupBucket();
      List<DataBucketVariableEntity> lookupVariables =
          variableRepository.findByDataBucketId(lookupBucket.getId());

      if (lookupVariables.isEmpty()) {
        log.warn("Lookup bucket {} has no variables defined", lookupBucket.getId());
        continue;
      }

      // Create a map of variable ID to variable name for the lookup bucket
      Map<Long, String> lookupVarIdToName =
          lookupVariables.stream()
              .collect(
                  Collectors.toMap(
                      DataBucketVariableEntity::getId, DataBucketVariableEntity::getName));

      // Get the join conditions
      List<BucketRelationshipEntity.JoinCondition> joinConditions =
          relationship.getJoinConditions();
      if (joinConditions.isEmpty()) {
        log.warn(
            "Relationship between rule bucket {} and lookup bucket {} has no join conditions",
            relationship.getRuleBucket().getId(),
            lookupBucket.getId());
        continue;
      }

      log.info("Fetching lookup data from bucket: {}", lookupBucket.getId());

      // Extract unique join key values from rule data to filter lookup data
      Set<Object> joinKeyValues = new HashSet<>();
      String ruleJoinKeyName = null;
      String lookupJoinKeyName = null;

      // Get the first join condition to determine the join keys
      if (!joinConditions.isEmpty()) {
        BucketRelationshipEntity.JoinCondition firstCondition = joinConditions.get(0);
        ruleJoinKeyName = ruleVarIdToName.get(firstCondition.getRuleBucketVariableId());
        lookupJoinKeyName = lookupVarIdToName.get(firstCondition.getLookupBucketVariableId());

        // Collect all unique values for the join key from rule data
        for (Map<String, Object> ruleRow : transactions) {
          if (ruleRow.containsKey(ruleJoinKeyName)) {
            Object joinKeyValue = ruleRow.get(ruleJoinKeyName);
            if (joinKeyValue != null) {
              joinKeyValues.add(joinKeyValue);
            }
          }
        }

        log.info(
            "Found {} unique join key values for key '{}'", joinKeyValues.size(), ruleJoinKeyName);
      }

      // Fetch lookup data with filtering by join key values
      List<Map<String, Object>> lookupData;
      if (!joinKeyValues.isEmpty() && lookupJoinKeyName != null) {
        lookupData =
            fetchFilteredDataFromSource(
                lookupBucket, lookupVariables, lookupJoinKeyName, joinKeyValues);
      } else {
        // Fallback to fetching all data if we can't determine join keys
        lookupData = fetchDataFromSource(lookupBucket, lookupVariables);
      }

      log.info("Fetched {} rows from lookup bucket {}", lookupData.size(), lookupBucket.getId());

      // Join the data
      enrichedTransactions =
          joinData(
              enrichedTransactions,
              lookupData,
              joinConditions,
              ruleVarIdToName,
              lookupVarIdToName,
              lookupVariables,
              lookupBucket);
    }

    return enrichedTransactions;
  }

  /**
   * Join rule bucket data with lookup bucket data based on join conditions.
   *
   * @param ruleData The rule bucket data
   * @param lookupData The lookup bucket data
   * @param joinConditions The join conditions
   * @param ruleVarIdToName Map of rule variable ID to name
   * @param lookupVarIdToName Map of lookup variable ID to name
   * @param lookupVariables The lookup bucket variables
   * @param lookupBucket The lookup bucket entity
   * @return List of joined data
   */
  private List<Map<String, Object>> joinData(
      List<Map<String, Object>> ruleData,
      List<Map<String, Object>> lookupData,
      List<BucketRelationshipEntity.JoinCondition> joinConditions,
      Map<Long, String> ruleVarIdToName,
      Map<Long, String> lookupVarIdToName,
      List<DataBucketVariableEntity> lookupVariables,
      DataBucketEntity lookupBucket) {

    List<Map<String, Object>> result = new ArrayList<>();

    // For each rule data row
    for (Map<String, Object> ruleRow : ruleData) {
      Map<String, Object> enrichedRow = new HashMap<>(ruleRow);
      boolean anyMatch = false;

      // For each lookup data row
      for (Map<String, Object> lookupRow : lookupData) {
        boolean allConditionsMatch = true;

        // Check all join conditions
        for (BucketRelationshipEntity.JoinCondition condition : joinConditions) {
          String ruleVarName = ruleVarIdToName.get(condition.getRuleBucketVariableId());
          String lookupVarName = lookupVarIdToName.get(condition.getLookupBucketVariableId());

          if (ruleVarName == null || lookupVarName == null) {
            log.warn(
                "Could not find variable names for join condition. Rule var ID: {}, Lookup var ID: {}",
                condition.getRuleBucketVariableId(),
                condition.getLookupBucketVariableId());
            allConditionsMatch = false;
            break;
          }

          Object ruleValue = ruleRow.get(ruleVarName);
          Object lookupValue = lookupRow.get(lookupVarName);

          // If either value is null, condition fails
          if (ruleValue == null || lookupValue == null) {
            allConditionsMatch = false;
            break;
          }

          // Compare values with type handling
          boolean valuesMatch = compareValues(ruleValue, lookupValue);
          if (!valuesMatch) {
            allConditionsMatch = false;
            break;
          }
        }

        // If all conditions match, enrich the rule row with lookup data
        if (allConditionsMatch) {
          anyMatch = true;

          // Add lookup variables to the enriched row
          for (DataBucketVariableEntity lookupVar : lookupVariables) {
            String lookupVarName = lookupVar.getName();
            Object lookupValue = lookupRow.get(lookupVarName);

            // Skip join keys to avoid duplicates
            boolean isJoinKey =
                joinConditions.stream()
                    .anyMatch(
                        jc ->
                            lookupVarIdToName
                                .get(jc.getLookupBucketVariableId())
                                .equals(lookupVarName));

            if (!isJoinKey) {
              // Add the enriched variable with metadata
              enrichedRow.put(lookupVarName, lookupValue);

              // Mark this variable as enriched
              Map<String, Object> metadata = new HashMap<>();
              metadata.put("isEnriched", true);
              metadata.put("sourceBucketId", lookupBucket.getId());
              metadata.put("sourceBucketName", lookupBucket.getName());
              enrichedRow.put("__metadata_" + lookupVarName, metadata);
            }
          }
        }
      }

      // Add the enriched row (or original if no matches)
      result.add(enrichedRow);
    }

    return result;
  }

  /**
   * Process transactions with rules.
   *
   * @param bucketId The bucket ID
   * @param transactions The transactions to process
   * @return List of processed transactions with flag status
   */
  private List<Map<String, Object>> processTransactionsWithRules(
      Long bucketId, List<Map<String, Object>> transactions) {

    // Get all active rules for the specified bucket
    List<Rule> activeRules =
        ruleRepository.findByActiveAndDataBucket_IdOrderByPriorityAsc(true, bucketId);

    if (activeRules.isEmpty()) {
      log.info("No active rules found for bucket ID: {}", bucketId);
      // Return transactions as-is if no rules to process
      return transactions.stream()
          .map(
              transaction -> {
                Map<String, Object> result = new HashMap<>(transaction);
                result.put("flagged", false);
                return result;
              })
          .collect(Collectors.toList());
    }

    log.info(
        "Processing {} transactions against {} active rules",
        transactions.size(),
        activeRules.size());

    // Process each transaction against all rules
    return transactions.stream()
        .map(
            transaction -> {
              boolean flagged = false;
              StringBuilder flagReasonBuilder = new StringBuilder();
              List<Rule> matchingRules = new ArrayList<>();

              // Process each rule
              for (Rule rule : activeRules) {
                try {
                  // Extract customer ID from transaction or use a default
                  String customerId = extractCustomerId(transaction);

                  // Parse the rule condition from JSON
                  Condition condition = objectMapper.readValue(rule.getRuleJson(), Condition.class);

                  // Use the rule evaluation service to evaluate the rule
                  boolean ruleTriggered =
                      ruleEvaluationService.evaluateRule(
                          condition,
                          customerId,
                          transaction,
                          true, // Use real aggregations
                          bucketId);

                  if (ruleTriggered) {
                    flagged = true;
                    if (!flagReasonBuilder.isEmpty()) {
                      flagReasonBuilder.append(" | ");
                    }
                    flagReasonBuilder.append(rule.getFlagReason());
                    matchingRules.add(rule);

                    log.info("Transaction triggered rule: {}", rule.getName());
                  }
                } catch (Exception e) {
                  log.error(
                      "Error evaluating rule {} on transaction: {}",
                      rule.getId(),
                      e.getMessage(),
                      e);
                }
              }

              // Add flagged status to the result
              Map<String, Object> result = new HashMap<>(transaction);
              result.put("flagged", flagged);
              if (flagged) {
                result.put("flagReason", flagReasonBuilder.toString());

                // Create workflow cases for each matching rule
                for (Rule rule : matchingRules) {
                  try {
                    // Prepare variables for the workflow process
                    Map<String, Object> processVariables = new HashMap<>();

                    // Add rule information
                    processVariables.put("ruleId", rule.getId());
                    processVariables.put("ruleName", rule.getName());
                    processVariables.put("ruleDescription", rule.getDescription());
                    processVariables.put("flagReason", rule.getFlagReason());

                    // Add transaction data (only essential fields to avoid large objects)
                    for (Map.Entry<String, Object> entry : transaction.entrySet()) {
                      // Skip large objects, arrays, or complex nested structures
                      if (entry.getValue() == null
                          || !(entry.getValue() instanceof Map)
                              && !(entry.getValue() instanceof List)) {
                        processVariables.put(entry.getKey(), entry.getValue());
                      }
                    }

                    // Determine entity ID from transaction data
                    Long entityId = null;
                    if (transaction.containsKey("id")) {
                      Object idObj = transaction.get("id");
                      if (idObj != null) {
                        try {
                          if (idObj instanceof Number) {
                            entityId = ((Number) idObj).longValue();
                          } else {
                            entityId = Long.parseLong(idObj.toString());
                          }
                        } catch (NumberFormatException e) {
                          log.warn("Could not parse transaction ID: {}", idObj);
                        }
                      }
                    }

                    // Start the workflow case
                    log.info("Creating workflow case for rule {} on transaction", rule.getId());
                    WorkflowCase workflowCase =
                        workflowCaseService.startCase(
                            WorkflowType.COMPLIANCE, // Use COMPLIANCE type for compliance review
                            "TRANSACTION", // Entity type
                            entityId, // Entity ID
                            processVariables);

                    log.info(
                        "Created workflow case {} for rule {} on transaction",
                        workflowCase.getId(),
                        rule.getId());

                  } catch (Exception e) {
                    log.error(
                        "Error creating case for rule {}: {}", rule.getId(), e.getMessage(), e);
                  }
                }
              }

              return result;
            })
        .collect(Collectors.toList());
  }

  /**
   * Extract customer ID from transaction data.
   *
   * @param transaction The transaction data
   * @return The customer ID or a default value
   */
  private String extractCustomerId(Map<String, Object> transaction) {
    // Try common customer ID field names
    for (String field : new String[] {"customerId", "customer_id", "userId", "user_id", "id"}) {
      if (transaction.containsKey(field) && transaction.get(field) != null) {
        return transaction.get(field).toString();
      }
    }

    // Default value if no customer ID found
    return "UNKNOWN";
  }

  /**
   * Create a JDBC DataSource from a database source entity.
   *
   * @param databaseSource The database source entity
   * @return A configured DataSource
   */
  private DataSource createDataSource(DatabaseSourceEntity databaseSource) {
    DriverManagerDataSource dataSource = new DriverManagerDataSource();

    // Set the driver class if specified
    if (databaseSource.getDriverClass() != null && !databaseSource.getDriverClass().isEmpty()) {
      dataSource.setDriverClassName(databaseSource.getDriverClass());
    } else {
      // Set default driver based on database type
      DatabaseType dbType = databaseSource.getDatabaseType();
      switch (dbType) {
        case MYSQL:
          dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
          break;
        case CLICKHOUSE:
          dataSource.setDriverClassName("com.clickhouse.jdbc.ClickHouseDriver");
          break;
        case MSSQL:
          dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
          break;
        default:
          throw new IllegalArgumentException("Unsupported database type: " + dbType);
      }
    }

    dataSource.setUrl(databaseSource.getConnectionUrl());
    dataSource.setUsername(databaseSource.getUsername());
    dataSource.setPassword(databaseSource.getPassword());

    return dataSource;
  }

  /**
   * Build a SELECT query for the specified table and columns.
   *
   * @param tableName The table name
   * @param columnNames The column names
   * @return A SELECT query
   */
  private String buildSelectQuery(String tableName, List<String> columnNames) {
    StringBuilder query = new StringBuilder("SELECT ");

    if (columnNames.isEmpty()) {
      query.append("*");
    } else {
      query.append(String.join(", ", columnNames));
    }

    query.append(" FROM ").append(tableName);

    return query.toString();
  }

  /**
   * Build a SELECT query with a WHERE IN clause for filtering by join key values.
   *
   * @param tableName The table name
   * @param columnNames The column names
   * @param joinKeyColumn The column name to filter on
   * @param joinKeyValues The values to filter by
   * @return A filtered SELECT query
   */
  private String buildFilteredSelectQuery(
      String tableName, List<String> columnNames, String joinKeyColumn, Set<Object> joinKeyValues) {
    StringBuilder query = new StringBuilder("SELECT ");

    if (columnNames.isEmpty()) {
      query.append("*");
    } else {
      query.append(String.join(", ", columnNames));
    }

    query.append(" FROM ").append(tableName);

    // If no values to filter by, return a query that will return no results
    if (joinKeyValues.isEmpty()) {
      query.append(" WHERE 1=0");
      return query.toString();
    }

    // Build the WHERE IN clause
    query.append(" WHERE ").append(joinKeyColumn).append(" IN (");

    // Format values based on their type
    boolean first = true;
    for (Object value : joinKeyValues) {
      if (!first) {
        query.append(", ");
      }

      if (value instanceof String) {
        query
            .append("'")
            .append(((String) value).replace("'", "''"))
            .append("'"); // Escape single quotes
      } else if (value instanceof Number) {
        query.append(value.toString());
      } else if (value != null) {
        query.append("'").append(value.toString().replace("'", "''")).append("'");
      }

      first = false;
    }

    query.append(")");

    return query.toString();
  }

  /**
   * Fetch data from a data source with filtering by join key values.
   *
   * @param bucket The data bucket
   * @param variables The bucket variables
   * @param joinKeyName The variable name to filter on
   * @param joinKeyValues The values to filter by
   * @return List of filtered data rows from the source
   */
  private List<Map<String, Object>> fetchFilteredDataFromSource(
      DataBucketEntity bucket,
      List<DataBucketVariableEntity> variables,
      String joinKeyName,
      Set<Object> joinKeyValues) {

    DataSourceEntity dataSource = bucket.getDataSource();
    if (dataSource == null) {
      throw new IllegalArgumentException("Bucket has no data source configured: " + bucket.getId());
    }

    // Find the join key column name
    String joinKeyColumn = null;
    for (DataBucketVariableEntity variable : variables) {
      if (variable.getName().equals(joinKeyName)) {
        joinKeyColumn = variable.getColumnName();
        break;
      }
    }

    if (joinKeyColumn == null) {
      log.warn("Join key column not found for variable: {}", joinKeyName);
      // Fall back to using the variable name as the column name
      joinKeyColumn = joinKeyName;
    }

    // Handle different data source types
    switch (dataSource.getType()) {
      case DATABASE:
        return fetchFilteredDataFromDatabase(
            bucket, variables, dataSource, joinKeyColumn, joinKeyValues);
      case REST_API:
        // For REST APIs, filtering is more complex and depends on the API
        // For now, fetch all data and filter in memory
        List<Map<String, Object>> allData = fetchDataFromRestApi(bucket, variables, dataSource);
        return filterDataInMemory(allData, joinKeyName, joinKeyValues);
      default:
        throw new IllegalArgumentException("Unsupported data source type: " + dataSource.getType());
    }
  }

  /**
   * Fetch data from a database data source with filtering by join key values.
   *
   * @param bucket The data bucket
   * @param variables The bucket variables
   * @param dataSource The data source entity
   * @param joinKeyColumn The column name to filter on
   * @param joinKeyValues The values to filter by
   * @return List of filtered data rows from the database
   */
  private List<Map<String, Object>> fetchFilteredDataFromDatabase(
      DataBucketEntity bucket,
      List<DataBucketVariableEntity> variables,
      DataSourceEntity dataSource,
      String joinKeyColumn,
      Set<Object> joinKeyValues) {

    // Get the database source details
    DatabaseSourceEntity databaseSource =
        databaseSourceRepository
            .findByDataSourceId(dataSource.getId())
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Database source not found for data source: " + dataSource.getId()));

    // Create a JDBC data source
    DataSource jdbcDataSource = createDataSource(databaseSource);
    JdbcTemplate jdbcTemplate = new JdbcTemplate(jdbcDataSource);

    // Build the query to fetch data
    String tableName = bucket.getTableName();
    List<String> columnNames =
        variables.stream()
            .map(DataBucketVariableEntity::getColumnName)
            .collect(Collectors.toList());

    String query = buildFilteredSelectQuery(tableName, columnNames, joinKeyColumn, joinKeyValues);
    log.debug("Executing filtered query: {}", query);

    // Execute the query and map results
    List<Map<String, Object>> results = new ArrayList<>();
    try {
      results =
          jdbcTemplate.query(
              query,
              (rs, rowNum) -> {
                Map<String, Object> row = new HashMap<>();
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                // Map database columns to variable names
                for (int i = 1; i <= columnCount; i++) {
                  String columnName = metaData.getColumnName(i);
                  Object value = rs.getObject(i);

                  // Find the corresponding variable
                  for (DataBucketVariableEntity variable : variables) {
                    if (variable.getColumnName().equalsIgnoreCase(columnName)) {
                      row.put(variable.getName(), value);
                      break;
                    }
                  }
                }

                return row;
              });
    } catch (Exception e) {
      log.error("Error executing filtered query: {}", e.getMessage(), e);
      throw new RuntimeException("Error fetching filtered data from database", e);
    }

    return results;
  }

  /**
   * Filter data in memory based on join key values.
   *
   * @param data The data to filter
   * @param joinKeyName The variable name to filter on
   * @param joinKeyValues The values to filter by
   * @return Filtered list of data rows
   */
  private List<Map<String, Object>> filterDataInMemory(
      List<Map<String, Object>> data, String joinKeyName, Set<Object> joinKeyValues) {

    return data.stream()
        .filter(
            row -> {
              Object value = row.get(joinKeyName);
              if (value == null) {
                return false;
              }

              // Check if any join key value matches this value
              for (Object joinKeyValue : joinKeyValues) {
                if (compareValues(value, joinKeyValue)) {
                  return true;
                }
              }
              return false;
            })
        .collect(Collectors.toList());
  }

  /**
   * Compare two values with proper type handling. This method handles comparisons between different
   * numeric types and string representations.
   *
   * @param value1 The first value
   * @param value2 The second value
   * @return true if the values are semantically equal, false otherwise
   */
  private boolean compareValues(Object value1, Object value2) {
    // If both are null or same object, they're equal
    if (value1 == value2) {
      return true;
    }

    // If one is null but the other isn't, they're not equal
    if (value1 == null || value2 == null) {
      return false;
    }

    // If they're the same class, use equals
    if (value1.getClass() == value2.getClass()) {
      return value1.equals(value2);
    }

    // Handle numeric comparisons
    if (value1 instanceof Number && value2 instanceof Number) {
      // Compare as double to handle all numeric types
      double num1 = ((Number) value1).doubleValue();
      double num2 = ((Number) value2).doubleValue();

      // Use a small epsilon for floating point comparison
      return Math.abs(num1 - num2) < 0.0000001;
    }

    // Handle string comparisons (one is string, other might be numeric)
    if (value1 instanceof String || value2 instanceof String) {
      String str1 = value1.toString();
      String str2 = value2.toString();

      // Try parsing as numbers if they look like numbers
      if (str1.matches("-?\\d+(\\.\\d+)?") && str2.matches("-?\\d+(\\.\\d+)?")) {
        try {
          double num1 = Double.parseDouble(str1);
          double num2 = Double.parseDouble(str2);
          return Math.abs(num1 - num2) < 0.0000001;
        } catch (NumberFormatException e) {
          // If parsing fails, fall back to string comparison
        }
      }

      // Default to string comparison
      return str1.equals(str2);
    }

    // For other types, use toString comparison as last resort
    return value1.toString().equals(value2.toString());
  }
}
