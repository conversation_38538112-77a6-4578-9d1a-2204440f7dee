import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import AuthService from '../../services/AuthService';

/**
 * A wrapper component that protects routes by checking if the user is authenticated.
 * If not authenticated, redirects to the login page with the intended location saved.
 */
const ProtectedRoute = () => {
  const isAuthenticated = AuthService.isAuthenticated();
  const location = useLocation();
  
  // If not authenticated, redirect to login page with the return path
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  
  // If authenticated, render the child routes
  return <Outlet />;
};

export default ProtectedRoute;
