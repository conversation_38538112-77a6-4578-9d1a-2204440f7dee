import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, useLocation, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Switch,
  TextField,
  Typography,
  Alert,
  Snackbar,
  Stack,
  Tooltip,
  useTheme,
  Chip,
  Tabs,
  Tab,
  Fade,
  Zoom,
  Badge,
  Fab,
  alpha
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import InfoIcon from '@mui/icons-material/Info';
import RuleIcon from '@mui/icons-material/Gavel';
import CategoryIcon from '@mui/icons-material/Category';
import DescriptionIcon from '@mui/icons-material/Description';
import TuneIcon from '@mui/icons-material/Tune';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import FactCheckIcon from '@mui/icons-material/FactCheck';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StorageIcon from '@mui/icons-material/Storage';
import ConditionBuilder from './ConditionBuilder';
import RuleTestingPanel from './RuleTestingPanel';
import { ruleService, dataBucketService } from '../../services/api';
import { useSpring, animated } from 'react-spring';

// Animated components
const AnimatedFab = animated(Fab);

const RuleEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const bucketId = queryParams.get('bucketId');
  const isEditMode = !!id;
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Tab state
  const [activeTab, setActiveTab] = useState('details');

  const [rule, setRule] = useState({
    name: '',
    description: '',
    priority: 0,
    active: true,
    dataBucketId: bucketId || null,
    condition: {
      type: 'AND',
      conditions: []
    }
  });

  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [fields, setFields] = useState([]);
  const [operators, setOperators] = useState([]);
  const [aggregateFunctions, setAggregateFunctions] = useState([]);
  const [variableCategories, setVariableCategories] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);
  const [showValidationAlert, setShowValidationAlert] = useState(false);

  // Add state for bucket name
  const [bucketName, setBucketName] = useState('');
  const [loadingBucket, setLoadingBucket] = useState(false);

  // Cleanup effect to remove activeBucketId when component unmounts
  useEffect(() => {
    return () => {
      localStorage.removeItem('activeBucketId');
    };
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch rule data if in edit mode
        if (isEditMode) {
          const ruleData = await ruleService.getRule(id);

          // Ensure all conditions have IDs - critical for proper editing
          if (ruleData && ruleData.condition && Array.isArray(ruleData.condition.conditions)) {
            const ensureConditionIds = (conditions) => {
              return conditions.map(condition => {
                // Add ID if missing
                const conditionWithId = {
                  ...condition,
                  id: condition.id || `temp-${Math.random().toString(36).substring(2, 9)}`
                };

                // Process nested conditions recursively
                if (conditionWithId.type === 'AND' || conditionWithId.type === 'OR') {
                  if (Array.isArray(conditionWithId.conditions)) {
                    conditionWithId.conditions = ensureConditionIds(conditionWithId.conditions);
                  } else {
                    conditionWithId.conditions = [];
                  }
                }

                return conditionWithId;
              });
            };

            // Process all conditions to ensure IDs
            ruleData.condition.conditions = ensureConditionIds(ruleData.condition.conditions);
          }

          console.log('Loaded rule data with ensured IDs:', ruleData);
          setRule(ruleData);

          // If the rule has a bucket ID, use that for fetching variables
          if (ruleData.dataBucketId) {
            // Fetch the bucket name for display
            fetchBucketName(ruleData.dataBucketId);

            // Store the active bucket ID in localStorage for variable suggestions
            localStorage.setItem('activeBucketId', ruleData.dataBucketId);

            // Fetch variables from the bucket-specific endpoint
            const bucketVariables = await ruleService.getBucketVariables(ruleData.dataBucketId);
            console.log('Bucket variables from API:', bucketVariables);

            // Ensure all variables have the correct properties
            const processedVariables = bucketVariables.map(variable => {
              // Log each variable's enrichment status for debugging
              console.log(`Variable ${variable.name} - Raw isEnriched value:`, variable.isEnriched);
              console.log(`Variable ${variable.name} - Raw variable object:`, variable);

              // Check for camelCase vs snake_case issues
              const isEnrichedValue =
                variable.isEnriched === true ||
                variable.is_enriched === true ||
                variable.enriched === true;

              console.log(`Variable ${variable.name} - Computed isEnriched value: ${isEnrichedValue}`);

              return {
                ...variable,
                // Ensure aggregatable is a boolean
                aggregatable: variable.aggregatable === true || variable.aggregatable === 'true',
                // Pass through enrichment information with fallbacks for different property names
                isEnriched: isEnrichedValue,
                sourceBucketName: variable.sourceBucketName || variable.source_bucket_name || null,
                sourceBucketId: variable.sourceBucketId || variable.source_bucket_id || null
              };
            });

            // Count enriched vs main variables
            const enrichedCount = processedVariables.filter(v => v.isEnriched).length;
            const mainCount = processedVariables.length - enrichedCount;
            console.log(`Processed ${processedVariables.length} variables: ${mainCount} main, ${enrichedCount} enriched`);
            setFields(processedVariables);

            // Extract unique categories from variables
            const categories = [...new Set(bucketVariables.map(v => v.category))];
            setVariableCategories(categories);

            // Fetch operators and aggregate functions
            const [operatorsData, aggregateFunctionsData] = await Promise.all([
              ruleService.getOperators(),
              ruleService.getAggregateFunctions()
            ]);

            setOperators(operatorsData);
            setAggregateFunctions(aggregateFunctionsData);

            return; // Exit early since we've already fetched everything we need
          }
        }

        // For new rules or if the rule doesn't have a bucket ID
        if (bucketId) {
          try {
            // Fetch the bucket name for display
            fetchBucketName(bucketId);

            // Store the active bucket ID in localStorage for variable suggestions
            localStorage.setItem('activeBucketId', bucketId);

            // Fetch variables from the bucket-specific endpoint
            const bucketVariables = await ruleService.getBucketVariables(bucketId);
            console.log('Bucket variables from API:', bucketVariables);

            // Ensure all variables have the correct properties
            const processedVariables = bucketVariables.map(variable => {
              // Log each variable's enrichment status for debugging
              console.log(`Variable ${variable.name} - Raw isEnriched value:`, variable.isEnriched);
              console.log(`Variable ${variable.name} - Raw variable object:`, variable);

              // Check for camelCase vs snake_case issues
              const isEnrichedValue =
                variable.isEnriched === true ||
                variable.is_enriched === true ||
                variable.enriched === true;

              console.log(`Variable ${variable.name} - Computed isEnriched value: ${isEnrichedValue}`);

              return {
                ...variable,
                // Ensure aggregatable is a boolean
                aggregatable: variable.aggregatable === true || variable.aggregatable === 'true',
                // Pass through enrichment information with fallbacks for different property names
                isEnriched: isEnrichedValue,
                sourceBucketName: variable.sourceBucketName || variable.source_bucket_name || null,
                sourceBucketId: variable.sourceBucketId || variable.source_bucket_id || null
              };
            });

            // Count enriched vs main variables
            const enrichedCount = processedVariables.filter(v => v.isEnriched).length;
            const mainCount = processedVariables.length - enrichedCount;
            console.log(`Processed ${processedVariables.length} variables: ${mainCount} main, ${enrichedCount} enriched`);
            setFields(processedVariables);

            // Extract unique categories from variables
            const categories = [...new Set(bucketVariables.map(v => v.category))];
            setVariableCategories(categories);

            // Fetch operators and aggregate functions
            const [operatorsData, aggregateFunctionsData] = await Promise.all([
              ruleService.getOperators(),
              ruleService.getAggregateFunctions()
            ]);

            setOperators(operatorsData);
            setAggregateFunctions(aggregateFunctionsData);
          } catch (bucketError) {
            console.error('Error fetching bucket variables:', bucketError);
            setError('Failed to load variables for the selected bucket.');

            // No fallback - require a bucket to be selected
            setFields([]);
            setOperators([]);
            setAggregateFunctions([]);
            setVariableCategories([]);
          }
        } else {
          // No bucket ID - show error and empty state
          setError('Please select a data bucket to create a rule.');
          setFields([]);
          setOperators([]);
          setAggregateFunctions([]);
          setVariableCategories([]);
        }
      } catch (err) {
        console.error('Error fetching rule data:', err);
        setError('Failed to load rule data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Fetch bucket name if bucketId is provided
    if (bucketId) {
      fetchBucketName(bucketId);
    }
  }, [id, isEditMode, bucketId]);

  // Function to check if a variable belongs to the current category
  const isFieldInCurrentCategory = (fieldId) => {
    const field = fields.find(f => f.id === fieldId);
    return field && (!field.category);
  };

  // Function to get field name with category marker for fields outside current category
  const getFieldDisplayName = (fieldId) => {
    const field = fields.find(f => f.id === fieldId);
    if (!field) return fieldId;

    return field.category
      ? `${field.name} (${field.category})`
      : field.name;
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    setRule({ ...rule, [name]: newValue });
  };

  const handleConditionChange = (conditions) => {
    console.log('Condition change to:', conditions);

    // Clone the rule object to avoid reference issues
    const updatedRule = {
      ...rule,
      condition: {
        ...rule.condition,
        conditions
      }
    };

    // Important: Log the entire rule to verify the state
    console.log('Updated rule with new conditions:', updatedRule);

    // Validate conditions
    if (conditions.length === 0) {
      console.warn('Rule has no conditions');
    }

    // Update state
    setRule(updatedRule);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Reset previous validation errors
    setValidationErrors([]);

    // Check for empty conditions
    if (!rule.condition || !rule.condition.conditions || rule.condition.conditions.length === 0) {
      setValidationErrors(['Rule must have at least one condition']);
      setShowValidationAlert(true);
      return;
    }

    // Basic validation
    if (!rule.name || rule.name.trim() === '') {
      setValidationErrors(['Rule name is required']);
      setShowValidationAlert(true);
      return;
    }

    try {
      setSaving(true);

      // Ensure the rule has the bucket ID as a number
      const ruleToSave = {
        ...rule,
        dataBucketId: Number(rule.dataBucketId || bucketId)
      };

      // First validate the rule
      const validationResult = await ruleService.validateRule(ruleToSave);

      if (!validationResult.valid) {
        console.error('Validation failed:', validationResult.errors);
        setValidationErrors(validationResult.errors || ['Validation failed']);
        setShowValidationAlert(true);
        return;
      }

      // Save the rule
      if (isEditMode) {
        await ruleService.updateRule(id, ruleToSave);
      } else {
        await ruleService.createRule(ruleToSave);
      }

      setSuccess(true);

      // Navigate back to rule list after 1 second
      setTimeout(() => {
        navigate('/rules');
      }, 1000);
    } catch (err) {
      console.error('Error saving rule:', err);
      setError('Failed to save rule. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSuccessClose = () => {
    setSuccess(false);
  };

  // Function to fetch bucket name and validate bucket type
  const fetchBucketName = async (id) => {
    try {
      setLoadingBucket(true);
      const buckets = await dataBucketService.getAllBuckets();
      const bucket = buckets.find(b => b.id === parseInt(id, 10) || b.id === id);

      if (bucket) {
        // Check if this is a rule bucket
        const bucketType = bucket.bucketType || bucket.type;
        const isRuleBucket = bucketType === 'RULE_BUCKET' ||
                            bucketType === 'RULE' ||
                            bucketType === 3 ||
                            (bucketType && bucketType.toString().toUpperCase().includes('RULE'));

        if (isRuleBucket) {
          setBucketName(bucket.name);
        } else {
          // Not a rule bucket - show error
          setError(`Cannot create rules for bucket "${bucket.name}" because it is not a rule bucket. Please select a rule bucket.`);
          // Redirect back to rules list after a delay
          setTimeout(() => {
            navigate('/rules');
          }, 3000);
        }
      } else {
        console.warn(`Bucket with ID ${id} not found`);
        setError(`Bucket with ID ${id} not found. Please select a valid bucket.`);
      }
    } catch (error) {
      console.error('Error fetching bucket name:', error);
      setError('Error loading bucket information. Please try again.');
    } finally {
      setLoadingBucket(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (loading) {
    return (
      <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress
            size={40}
            thickness={4}
            sx={{ color: theme.palette.primary.main }}
          />
          <Typography variant="body1" sx={{ mt: 2, color: 'text.secondary' }}>
            {isEditMode ? 'Loading rule data...' : 'Preparing rule editor...'}
          </Typography>
        </Box>
      </Box>
    );
  }

  // Get color scheme for progize branding
  const getProgizeColor = (variant = 'primary') => {
    const colors = {
      primary: isDarkMode ? '#63B3ED' : '#3182CE', // Blue
      secondary: isDarkMode ? '#68D391' : '#38A169', // Green
      accent: isDarkMode ? '#F6AD55' : '#DD6B20', // Orange
      light: isDarkMode ? '#2D3748' : '#F7FAFC', // Background
      dark: isDarkMode ? '#E2E8F0' : '#1A202C', // Text
    };
    return colors[variant] || colors.primary;
  };

  return (
    <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Header with title, description and save button */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        mb: 3,
        pb: 2,
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
      }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1 }}>
            {isEditMode ? 'Edit Rule' : 'Create New Rule'}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              Define the rule details, conditions, and test your rule before saving.
            </Typography>
          </Box>
        </Box>
        <Button
          variant="contained"
          color="primary"
          startIcon={saving ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
          onClick={handleSubmit}
          disabled={saving}
          sx={{
            borderRadius: '28px',
            px: 3,
            py: 1,
            boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0 6px 20px rgba(0,118,255,0.39)'
            }
          }}
        >
          {saving ? 'Saving...' : isEditMode ? 'Update Rule' : 'Create Rule'}
        </Button>
      </Box>

      {/* Display bucket name if available */}
      {bucketName && (
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center',
          p: 2,
          bgcolor: alpha(theme.palette.primary.main, 0.05),
          borderRadius: 2,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
        }}>
          <StorageIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography variant="body1" sx={{ fontWeight: 500 }}>
            {isEditMode ? 'Editing' : 'Creating'} rule for bucket: <strong>{bucketName}</strong>
          </Typography>
        </Box>
      )}

      {/* Tab Navigation */}
      <Box sx={{ mb: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          aria-label="rule editor tabs"
          sx={{
            '& .MuiTabs-indicator': {
              backgroundColor: theme.palette.primary.main,
              height: 3
            }
          }}
        >
          <Tab
            value="details"
            label="Basic Details"
            icon={<DescriptionIcon />}
            iconPosition="start"
            sx={{
              textTransform: 'none',
              fontWeight: activeTab === 'details' ? 600 : 400,
              minHeight: 48
            }}
          />
          <Tab
            value="condition"
            label="Rule Condition"
            icon={<TuneIcon />}
            iconPosition="start"
            sx={{
              textTransform: 'none',
              fontWeight: activeTab === 'condition' ? 600 : 400,
              minHeight: 48
            }}
          />
          <Tab
            value="testing"
            label="Test Rule"
            icon={<PlayArrowIcon />}
            iconPosition="start"
            sx={{
              textTransform: 'none',
              fontWeight: activeTab === 'testing' ? 600 : 400,
              minHeight: 48
            }}
          />
        </Tabs>
      </Box>

      <Card sx={{
        mt: 2,
        overflow: 'visible',
        position: 'relative',
        borderRadius: 2,
        boxShadow: theme.shadows[5],
        transition: 'all 0.3s ease'
      }}>
        {/* Tab content panels */}
        <form onSubmit={handleSubmit}>
          {/* Basic Details Tab */}
          <Box
            role="tabpanel"
            hidden={activeTab !== 'details'}
            id="tabpanel-details"
            aria-labelledby="tab-details"
            sx={{ p: 3 }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{
                  p: 2,
                  mb: 3,
                  borderRadius: 2,
                  bgcolor: isDarkMode
                    ? alpha(getProgizeColor('primary'), 0.05)
                    : alpha(getProgizeColor('light'), 0.5),
                  border: `1px dashed ${alpha(getProgizeColor(), 0.3)}`,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <InfoIcon sx={{ color: getProgizeColor(), mr: 2, opacity: 0.7 }} />
                  <Typography variant="body2" color="text.secondary">
                    {isEditMode
                      ? "Update your rule information below. Rules are used to identify transactions that require additional scrutiny."
                      : "Define your rule by providing a name, description, and priority. Rules are used to identify transactions that require additional scrutiny."}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={8}>
                <TextField
                  fullWidth
                  label="Rule Name"
                  name="name"
                  value={rule.name}
                  onChange={handleInputChange}
                  variant="outlined"
                  required
                  InputProps={{
                    sx: {
                      borderRadius: 1.5,
                      '&.Mui-focused': {
                        boxShadow: `0 0 0 2px ${alpha(getProgizeColor(), 0.2)}`
                      }
                    }
                  }}
                  helperText="Required. Choose a descriptive name for your rule"
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Stack spacing={1}>
                  <FormControl fullWidth>
                    <InputLabel>Priority</InputLabel>
                    <Select
                      value={rule.priority}
                      name="priority"
                      onChange={handleInputChange}
                      label="Priority"
                      sx={{
                        borderRadius: 1.5,
                        '&.Mui-focused': {
                          boxShadow: `0 0 0 2px ${alpha(getProgizeColor(), 0.2)}`
                        }
                      }}
                    >
                      <MenuItem value={0}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box
                            sx={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              height: '22px',
                              px: 1,
                              py: 0.5,
                              mr: 1.5,
                              borderRadius: 0, // Rectangular shape
                              bgcolor: '#FFF3E0', // Light Orange
                              color: '#E65100', // Dark Orange
                              fontSize: '12px',
                              fontWeight: 500,
                              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                            }}
                          >
                            <Box
                              component="span"
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                bgcolor: '#E65100',
                                display: 'inline-block',
                                mr: 0.5
                              }}
                            />
                            Low
                          </Box>
                          <Typography variant="body2">Low Priority</Typography>
                        </Box>
                      </MenuItem>
                      <MenuItem value={1}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box
                            sx={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              height: '22px',
                              px: 1,
                              py: 0.5,
                              mr: 1.5,
                              borderRadius: 0, // Rectangular shape
                              bgcolor: '#E3F2FD', // Light Blue
                              color: '#0D47A1', // Dark Blue
                              fontSize: '12px',
                              fontWeight: 500,
                              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                            }}
                          >
                            <Box
                              component="span"
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                bgcolor: '#0D47A1',
                                display: 'inline-block',
                                mr: 0.5
                              }}
                            />
                            Medium
                          </Box>
                          <Typography variant="body2">Medium Priority</Typography>
                        </Box>
                      </MenuItem>
                      <MenuItem value={2}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box
                            sx={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              height: '22px',
                              px: 1,
                              py: 0.5,
                              mr: 1.5,
                              borderRadius: 0, // Rectangular shape
                              bgcolor: '#FFEBEE', // Light Red
                              color: '#B71C1C', // Dark Red
                              fontSize: '12px',
                              fontWeight: 500,
                              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                            }}
                          >
                            <Box
                              component="span"
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                bgcolor: '#B71C1C',
                                display: 'inline-block',
                                mr: 0.5
                              }}
                            />
                            High
                          </Box>
                          <Typography variant="body2">High Priority</Typography>
                        </Box>
                      </MenuItem>
                    </Select>
                  </FormControl>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={rule.active}
                        onChange={handleInputChange}
                        name="active"
                        color="primary"
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: getProgizeColor('secondary'),
                            '&:hover': {
                              backgroundColor: alpha(getProgizeColor('secondary'), 0.1),
                            },
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: getProgizeColor('secondary'),
                          },
                        }}
                      />
                    }
                    label={
                      <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box component="span" sx={{ color: rule.active ? getProgizeColor('secondary') : 'text.disabled', fontWeight: rule.active ? 600 : 400 }}>
                          {rule.active ? 'Active' : 'Inactive'}
                        </Box>
                      </Typography>
                    }
                  />
                </Stack>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={rule.description}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={4}
                  InputProps={{
                    sx: {
                      borderRadius: 1.5,
                      '&.Mui-focused': {
                        boxShadow: `0 0 0 2px ${alpha(getProgizeColor(), 0.2)}`
                      }
                    }
                  }}
                  helperText="Optional. Provide details about when this rule should be triggered"
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 4, textAlign: 'right' }}>
              <Button
                variant="outlined"
                sx={{
                  mr: 2,
                  borderRadius: 8,
                  px: 3,
                  borderColor: alpha(theme.palette.text.primary, 0.2),
                  '&:hover': {
                    borderColor: alpha(theme.palette.text.primary, 0.3),
                    backgroundColor: alpha(theme.palette.text.primary, 0.03)
                  }
                }}
                onClick={() => navigate('/rules')}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                sx={{
                  borderRadius: 8,
                  px: 3,
                  bgcolor: getProgizeColor(),
                  '&:hover': {
                    bgcolor: isDarkMode ? alpha(getProgizeColor(), 0.8) : alpha(getProgizeColor(), 1.2),
                  },
                  boxShadow: `0 4px 14px ${alpha(getProgizeColor(), 0.4)}`,
                  transition: 'all 0.3s'
                }}
                onClick={() => setActiveTab('condition')}
                endIcon={<TuneIcon />}
              >
                Configure Conditions
              </Button>
            </Box>
          </Box>

          {/* Rule Conditions Tab */}
          <Box
            role="tabpanel"
            hidden={activeTab !== 'condition'}
            id="tabpanel-condition"
            aria-labelledby="tab-condition"
          >
            <Box sx={{
              p: 2,
              borderBottom: isDarkMode
                ? `1px solid ${alpha(theme.palette.common.white, 0.1)}`
                : `1px solid ${alpha(theme.palette.common.black, 0.1)}`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              bgcolor: rule?.condition?.type === 'AND'
                ? alpha(getProgizeColor('primary'), isDarkMode ? 0.08 : 0.05)
                : alpha(getProgizeColor('accent'), isDarkMode ? 0.08 : 0.05)
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  Match:
                </Typography>
                <FormControl size="small" sx={{ minWidth: 200 }}>
                  <Select
                    value={(rule?.condition?.type) || 'AND'}
                    onChange={(e) => {
                      setRule({
                        ...rule,
                        condition: {
                          ...(rule?.condition || {}),
                          type: e.target.value
                        }
                      });
                    }}
                    variant="outlined"
                    sx={{
                      borderRadius: 1.5,
                      bgcolor: theme.palette.background.paper,
                      '&.Mui-focused': {
                        boxShadow: `0 0 0 2px ${alpha(getProgizeColor(), 0.2)}`
                      }
                    }}
                  >
                    <MenuItem value="AND">ALL conditions (AND)</MenuItem>
                    <MenuItem value="OR">ANY condition (OR)</MenuItem>
                  </Select>
                </FormControl>

                <Chip
                  icon={<FactCheckIcon fontSize="small" />}
                  label={`${rule.condition?.conditions?.length} condition${rule.condition?.conditions?.length !== 1 ? 's' : ''}`}
                  variant="outlined"
                  color={rule.condition?.conditions?.length > 0 ? "primary" : "default"}
                  sx={{
                    borderRadius: 1.5,
                    bgcolor: rule.condition?.conditions?.length > 0
                      ? alpha(getProgizeColor(), 0.1)
                      : 'transparent',
                    borderColor: rule.condition?.conditions?.length > 0
                      ? alpha(getProgizeColor(), 0.3)
                      : theme.palette.divider,
                    '& .MuiChip-label': { px: 1 }
                  }}
                />
              </Box>

              <Chip
                label={`${fields.length} available variables`}
                variant="outlined"
                size="small"
                color="info"
                sx={{ borderRadius: 1.5 }}
              />
            </Box>

            <Box sx={{
              p: 3,
              minHeight: '450px',
              maxHeight: 'calc(100vh - 400px)',
              overflow: 'auto',
              bgcolor: theme.palette.background.paper
            }}>
              {/* Variable Type Legend */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                mb: 2,
                p: 1.5,
                borderRadius: 1,
                bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)',
                border: '1px solid',
                borderColor: 'divider'
              }}>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  Variable Types:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    borderLeft: '3px solid #3182CE',
                    pl: 1,
                    pr: 1,
                    py: 0.5,
                    borderRadius: '0 4px 4px 0',
                    bgcolor: isDarkMode ? 'rgba(49, 130, 206, 0.1)' : 'rgba(49, 130, 206, 0.05)'
                  }}>
                    <Box
                      sx={{
                        fontSize: '0.75rem',
                        color: 'primary.main',
                        border: '1px solid',
                        borderColor: 'primary.main',
                        borderRadius: '4px',
                        px: 0.5,
                        py: 0.1,
                        mr: 1,
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      Main
                    </Box>
                    <Typography variant="body2">
                      Variables from main bucket
                    </Typography>
                  </Box>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    borderLeft: '3px solid #4CAF50',
                    pl: 1,
                    pr: 1,
                    py: 0.5,
                    borderRadius: '0 4px 4px 0',
                    bgcolor: isDarkMode ? 'rgba(76, 175, 80, 0.1)' : 'rgba(76, 175, 80, 0.05)'
                  }}>
                    <Box
                      sx={{
                        fontSize: '0.75rem',
                        color: 'success.main',
                        border: '1px solid',
                        borderColor: 'success.main',
                        borderRadius: '4px',
                        px: 0.5,
                        py: 0.1,
                        mr: 1,
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      Enriched
                    </Box>
                    <Typography variant="body2">
                      Variables from lookup buckets
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <ConditionBuilder
                conditions={rule.condition?.conditions || []}
                onChange={handleConditionChange}
                fields={fields}
                operators={operators}
                aggregateFunctions={aggregateFunctions}
                isEditable={true}
                getFieldDisplayName={getFieldDisplayName}
              />
            </Box>
          </Box>

          {/* Rule Testing Tab */}
          <Box
            role="tabpanel"
            hidden={activeTab !== 'testing'}
            id="tabpanel-testing"
            aria-labelledby="tab-testing"
          >
            <RuleTestingPanel
              ruleCondition={rule.condition}
              fields={fields}
              bucketId={rule.dataBucketId || bucketId}
            />

            {/* Save button for Test Rule tab */}
            <Box sx={{ p: 3, display: 'flex', justifyContent: 'flex-end', borderTop: 1, borderColor: 'divider' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={saving ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                onClick={handleSubmit}
                disabled={saving}
                sx={{
                  borderRadius: 8,
                  px: 3,
                  bgcolor: getProgizeColor(),
                  '&:hover': {
                    bgcolor: isDarkMode ? alpha(getProgizeColor(), 0.8) : alpha(getProgizeColor(), 1.2),
                  },
                  boxShadow: `0 4px 14px ${alpha(getProgizeColor(), 0.4)}`
                }}
              >
                {saving ? 'Saving...' : 'Save Rule'}
              </Button>
            </Box>
          </Box>
        </form>
      </Card>

      <Snackbar
        open={showValidationAlert}
        autoHideDuration={6000}
        onClose={() => setShowValidationAlert(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowValidationAlert(false)}
          severity="error"
          sx={{ width: '100%', borderRadius: 2 }}
        >
          <Typography variant="subtitle2">Rule Validation Failed:</Typography>
          <ul style={{ marginTop: 5, paddingLeft: 20, marginBottom: 0 }}>
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </Alert>
      </Snackbar>

      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={handleSuccessClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSuccessClose}
          severity="success"
          sx={{ borderRadius: 2 }}
        >
          Rule {isEditMode ? 'updated' : 'created'} successfully!
        </Alert>
      </Snackbar>
    </Box>
  );
};



export default RuleEditor;
