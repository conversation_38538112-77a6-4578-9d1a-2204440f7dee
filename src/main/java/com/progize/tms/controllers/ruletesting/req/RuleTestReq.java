package com.progize.tms.controllers.ruletesting.req;

import com.progize.tms.controllers.rules.req.RuleCondition;
import java.util.Map;
import lombok.Data;

/** Request object for testing a rule with sample data. */
@Data
public class RuleTestReq {
  /** The rule definition to test */
  private RuleCondition ruleDefinition;

  /**
   * Sample data to test the rule against. Using a Map to support dynamic fields based on bucket
   * variables.
   */
  private Map<String, Object> sampleData;

  /** The bucket ID for context */
  private Long bucketId;

  /** Options for rule testing */
  private TestOptions options;

  /** Options for controlling the rule testing process */
  @Data
  public static class TestOptions {
    /** Whether to validate the rule syntax */
    private boolean validateSyntax = true;

    /** Whether to validate type compatibility */
    private boolean validateTypes = true;

    /** Whether to analyze performance characteristics */
    private boolean checkPerformance = true;

    /** Whether to include detailed debug information */
    private boolean includeDebugInfo = true;

    /** Whether to use real database aggregations (ClickHouse) or mock data */
    private boolean useRealAggregations = false;
  }
}
