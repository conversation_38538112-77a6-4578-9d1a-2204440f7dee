package com.progize.tms.services.impl;

import com.progize.tms.services.AggregationProvider;
import com.progize.tms.services.models.AggregationRequirement;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/** Implementation of AggregationProvider that provides mock data for testing purposes. */
@Component
public class MockAggregationProvider implements AggregationProvider {
  private static final Logger log = LoggerFactory.getLogger(MockAggregationProvider.class);

  @Override
  public Map<String, Object> getAggregations(
      String customerId, Set<AggregationRequirement> requirements) {
    log.debug(
        "Generating mock aggregations for customer: {} with {} requirements",
        customerId,
        requirements.size());

    Map<String, Object> mockAggregations = new HashMap<>();

    // Populate with mock data based on requirements
    requirements.forEach(
        req -> {
          Object mockValue = generateMockValue(req);
          mockAggregations.put(req.getAggregationKey(), mockValue);
          log.debug("Mock aggregation for {}: {}", req.getAggregationKey(), mockValue);
        });

    return mockAggregations;
  }

  @Override
  public Map<String, Object> getAggregations(
      String customerId, Set<AggregationRequirement> requirements, Long bucketId) {
    log.debug(
        "Generating mock aggregations for customer: {} with {} requirements and bucketId: {}",
        customerId,
        requirements.size(),
        bucketId);

    // For mock data, we don't need to use the bucketId differently
    // In a real implementation, we might adjust the mock data based on bucket-specific schemas
    return getAggregations(customerId, requirements);
  }

  /** Generates appropriate mock values based on aggregation type. */
  private Object generateMockValue(AggregationRequirement req) {
    String aggType = req.getFunction();

    // Generate values based on aggregation type
    if ("COUNT".equals(aggType)) {
      return 5L; // Mock count of transactions
    } else if ("SUM".equals(aggType)) {
      return new BigDecimal("1000.00"); // Mock sum
    } else if ("AVG".equals(aggType)) {
      return new BigDecimal("200.00"); // Mock average
    } else if ("MAX".equals(aggType)) {
      return new BigDecimal("500.00"); // Mock maximum
    } else if ("MIN".equals(aggType)) {
      return new BigDecimal("50.00"); // Mock minimum
    }

    // Default fallback
    log.warn("Unknown aggregation type: {}. Using default mock value.", aggType);
    return 0L;
  }
}
