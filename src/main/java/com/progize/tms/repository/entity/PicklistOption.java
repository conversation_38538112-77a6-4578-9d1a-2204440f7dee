package com.progize.tms.repository.entity;

import com.progize.tms.repository.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "picklist_options")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PicklistOption extends BaseEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "picklist", nullable = false)
  private Picklist picklist;

  @Column(name = "option_value", nullable = false)
  private String optionValue;

  @Column(name = "option_label", nullable = false)
  private String optionLabel;

  //  @Column(name = "description")
  //  private String description;

  @Column(name = "display_order", nullable = false)
  private Integer displayOrder;

  //  @Column(name = "is_default")
  //  private Boolean isDefault;
}
