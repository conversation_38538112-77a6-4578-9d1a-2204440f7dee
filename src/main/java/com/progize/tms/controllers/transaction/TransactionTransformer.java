package com.progize.tms.controllers.transaction;

import com.progize.tms.controllers.transaction.req.ProcessTransactionsRequest;
import com.progize.tms.controllers.transaction.req.TransactionReqMdl;
import com.progize.tms.controllers.transaction.res.TransactionResMdl;
import com.progize.tms.repository.entity.Transaction;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/** Transformer for transaction-related objects between REST and service layers. */
@Component
@RequiredArgsConstructor
public class TransactionTransformer {

  /** Transforms a Transaction entity to a response model. */
  public TransactionResMdl toResponse(Transaction transaction) {
    if (transaction == null) {
      return null;
    }

    TransactionResMdl response = new TransactionResMdl();
    response.setId(transaction.getId());
    response.setFundId(transaction.getFundId());
    response.setFundName(transaction.getFundName());
    response.setFundShortName(transaction.getFundShortName());
    response.setFundBankAccount(transaction.getFundBankAccount());
    response.setTransDecs(transaction.getTransDecs());
    response.setTransaction(transaction.getTransaction());
    response.setCustomerId(transaction.getCustomerId());
    response.setPortfolioId(transaction.getPortfolioId());
    response.setCustomerName(transaction.getCustomerName());
    response.setCrmBankAccountNo(transaction.getCrmBankAccountNo());
    response.setOrderType(transaction.getOrderType());
    response.setUnitTypeCode(transaction.getUnitTypeCode());
    response.setUnitTypeName(transaction.getUnitTypeName());
    response.setDealDate(transaction.getDealDate());
    response.setMonth(transaction.getMonth());
    response.setTransType(transaction.getTransType());
    response.setFundNetAmount(transaction.getFundNetAmount());
    response.setAmountPerUnit(transaction.getAmountPerUnit());
    response.setNav(transaction.getNav());
    response.setTransactionUnit(transaction.getTransactionUnit());
    response.setEntryLoad(transaction.getEntryLoad());
    response.setDiscountAmt(transaction.getDiscountAmt());
    response.setGrossLoad(transaction.getGrossLoad());
    response.setNetLoad(transaction.getNetLoad());
    response.setExitLoad(transaction.getExitLoad());
    response.setZakatAmt(transaction.getZakatAmt());
    response.setCgt(transaction.getCgt());
    response.setTransCost(transaction.getTransCost());
    response.setCol22(transaction.getCol22());
    response.setRmCode(transaction.getRmCode());
    response.setRmName(transaction.getRmName());
    response.setBranch(transaction.getBranch());
    response.setNewExist(transaction.getNewExist());
    response.setRegion(transaction.getRegion());
    response.setFundCategory(transaction.getFundCategory());
    response.setIndustryDesc(transaction.getIndustryDesc());
    response.setSecpSector(transaction.getSecpSector());
    response.setCity(transaction.getCity());
    response.setChannel(transaction.getChannel());
    response.setTradeDateTime(transaction.getTradeDateTime());
    response.setFlagged(transaction.isFlagged());
    response.setFlagReason(transaction.getFlagReason());
    response.setCustomerRiskLevel(transaction.getCustomerRiskLevel());
    response.setKycStatus(transaction.getKycStatus());
    response.setProfileLastUpdated(transaction.getProfileLastUpdated());
    response.setLoginAttempts(transaction.getLoginAttempts());

    return response;
  }

  /** Transforms a list of Transaction entities to response models. */
  public List<TransactionResMdl> toResponseList(List<Transaction> transactions) {
    return transactions.stream().map(this::toResponse).collect(Collectors.toList());
  }

  /** Transforms a request model to a Transaction entity. */
  public Transaction toEntity(TransactionReqMdl request) {
    if (request == null) {
      return null;
    }

    Transaction transaction = new Transaction();
    transaction.setId(request.getId());
    transaction.setFundId(request.getFundId());
    transaction.setFundName(request.getFundName());
    transaction.setFundShortName(request.getFundShortName());
    transaction.setFundBankAccount(request.getFundBankAccount());
    transaction.setTransDecs(request.getTransDecs());
    transaction.setTransaction(request.getTransaction());
    transaction.setCustomerId(request.getCustomerId());
    transaction.setPortfolioId(request.getPortfolioId());
    transaction.setCustomerName(request.getCustomerName());
    transaction.setCrmBankAccountNo(request.getCrmBankAccountNo());
    transaction.setOrderType(request.getOrderType());
    transaction.setUnitTypeCode(request.getUnitTypeCode());
    transaction.setUnitTypeName(request.getUnitTypeName());
    transaction.setDealDate(request.getDealDate());
    transaction.setMonth(request.getMonth());
    transaction.setTransType(request.getTransType());
    transaction.setFundNetAmount(request.getFundNetAmount());
    transaction.setAmountPerUnit(request.getAmountPerUnit());
    transaction.setNav(request.getNav());
    transaction.setTransactionUnit(request.getTransactionUnit());
    transaction.setEntryLoad(request.getEntryLoad());
    transaction.setDiscountAmt(request.getDiscountAmt());
    transaction.setGrossLoad(request.getGrossLoad());
    transaction.setNetLoad(request.getNetLoad());
    transaction.setExitLoad(request.getExitLoad());
    transaction.setZakatAmt(request.getZakatAmt());
    transaction.setCgt(request.getCgt());
    transaction.setTransCost(request.getTransCost());
    transaction.setCol22(request.getCol22());
    transaction.setRmCode(request.getRmCode());
    transaction.setRmName(request.getRmName());
    transaction.setBranch(request.getBranch());
    transaction.setNewExist(request.getNewExist());
    transaction.setRegion(request.getRegion());
    transaction.setFundCategory(request.getFundCategory());
    transaction.setIndustryDesc(request.getIndustryDesc());
    transaction.setSecpSector(request.getSecpSector());
    transaction.setCity(request.getCity());
    transaction.setChannel(request.getChannel());
    transaction.setTradeDateTime(request.getTradeDateTime());
    transaction.setCustomerRiskLevel(request.getCustomerRiskLevel());
    transaction.setKycStatus(request.getKycStatus());
    transaction.setProfileLastUpdated(request.getProfileLastUpdated());
    transaction.setLoginAttempts(request.getLoginAttempts());

    return transaction;
  }

  /** Transforms a list of request models to Transaction entities. */
  public List<Transaction> toEntityList(List<TransactionReqMdl> requests) {
    return requests.stream().map(this::toEntity).collect(Collectors.toList());
  }

  /** Transforms a processing request to a list of Transaction entities. */
  public List<Transaction> toEntityList(ProcessTransactionsRequest request) {
    if (request == null || request.getTransactions() == null) {
      return List.of();
    }
    return toEntityList(request.getTransactions());
  }
}
