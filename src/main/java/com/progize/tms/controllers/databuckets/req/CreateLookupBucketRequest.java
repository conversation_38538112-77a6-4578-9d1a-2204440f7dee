package com.progize.tms.controllers.databuckets.req;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/** Request DTO for creating a new lookup bucket with its variables. */
@Getter
@Setter
public class CreateLookupBucketRequest {
  @NotBlank(message = "Name is required")
  private String name;

  @NotBlank(message = "Description is required")
  private String description;

  @NotBlank(message = "Table name is required")
  private String tableName;

  private String bucketType = "LOOKUP";

  @NotNull(message = "Data source ID is required")
  private Long dataSourceId;

  @NotEmpty(message = "At least one variable is required")
  @Valid
  private List<LookupBucketVariableRequest> variables;

  @Getter
  @Setter
  public static class LookupBucketVariableRequest {
    @NotBlank(message = "Variable code is required")
    private String code;

    @NotBlank(message = "Variable name is required")
    private String name;

    private String description;

    @NotBlank(message = "Column name is required")
    private String columnName;

    @NotBlank(message = "Data type code is required")
    private String dataTypeCode;

    @NotNull(message = "Is unique flag is required")
    private Boolean isUnique;
  }
}
