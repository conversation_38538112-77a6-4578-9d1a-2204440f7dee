package com.progize.tms.workflow.core;

import com.progize.tms.workflow.ProcessHistory;
import java.util.List;
import java.util.Map;
import org.flowable.task.api.Task;
import org.springframework.web.multipart.MultipartFile;

/** Interface for workflow process operations. */
public interface WorkflowService {

  /**
   * Start a new process instance.
   *
   * @param processType The type of process to start
   * @param variables Variables to pass to the process
   * @return The process instance ID
   */
  String startProcess(String processType, Map<String, Object> variables);

  /**
   * Complete a task.
   *
   * @param taskId The ID of the task to complete
   * @param variables Variables to pass to the task
   * @param attachments Optional attachments for the task
   */
  void completeTask(String taskId, Map<String, Object> variables, List<MultipartFile> attachments);

  /**
   * Get tasks assigned to a user.
   *
   * @param username The username to get tasks for
   * @return List of tasks
   */
  List<Task> getUserTasks(String username);

  /**
   * Get the history of a process instance.
   *
   * @param processInstanceId The process instance ID
   * @return Process history
   */
  ProcessHistory getProcessHistory(String processInstanceId);
}
