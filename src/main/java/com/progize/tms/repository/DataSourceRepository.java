package com.progize.tms.repository;

import com.progize.tms.repository.entity.DataSourceEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Repository for DataSourceEntity. */
@Repository
public interface DataSourceRepository extends JpaRepository<DataSourceEntity, Long> {
  Optional<DataSourceEntity> findByName(String name);
}
