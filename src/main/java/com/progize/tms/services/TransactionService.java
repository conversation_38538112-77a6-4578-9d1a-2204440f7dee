package com.progize.tms.services;

import com.progize.tms.repository.ClickHouseTransactionRepository;
import com.progize.tms.repository.entity.Transaction;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class TransactionService {
  private final ClickHouseTransactionRepository transactionRepository;

  public TransactionService(ClickHouseTransactionRepository transactionRepository) {
    this.transactionRepository = transactionRepository;
  }

  public Transaction processTransaction(Transaction transaction) {
    transactionRepository.save(transaction);
    return transaction;
  }

  public List<Transaction> getRecentTransactions(int limit) {
    return transactionRepository.findRecent(limit);
  }

  public List<Transaction> getTransactionsByFlagged(boolean flagged) {
    return transactionRepository.findByFlagged(flagged);
  }

  public List<Transaction> getTransactionsByAccount(String accountId) {
    return transactionRepository.findByAccountId(accountId);
  }
}
