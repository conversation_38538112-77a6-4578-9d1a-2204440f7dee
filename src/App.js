import React, { useState, useEffect } from 'react';
import { Outlet, Navigate, useNavigate } from 'react-router-dom';
import { Box, IconButton, useTheme, useMediaQuery, CircularProgress } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import RuleList from './components/rules/RuleList';
import RuleEditor from './components/rules/RuleEditor';
import RuleVariableRegistryLoader, { ruleVariableRegistryLoader } from './components/rulevariables/RuleVariableRegistryLoader';
import CasesPage from './pages/cases/CasesPage';
import ClosedCasesPage from './pages/cases/ClosedCasesPage';
import CaseDetailPage from './pages/cases/CaseDetailPage';
import CaseAnalyticsPage from './pages/cases/CaseAnalyticsPage';
import WorkflowDesigner from './components/workflow-designer/WorkflowDesigner';
import Login from './components/auth/Login';
import ProtectedRoute from './components/auth/ProtectedRoute';
import UserManagement from './components/users/UserManagement';
import RoleManagement from './components/roles/RoleManagement';
import PermissionManagement from './components/permissions/PermissionManagement';
import patternBg from './assets/pattern.svg';
import { useThemeContext } from './contexts/ThemeContext';
import AuthService from './services/AuthService';

function App() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const { isDarkMode } = useThemeContext();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Check authentication status when component mounts
  useEffect(() => {
    const checkAuth = () => {
      const authStatus = AuthService.isAuthenticated();
      setIsAuthenticated(authStatus);
      setIsLoading(false);

      // Redirect to login if not authenticated
      if (!authStatus) {
        navigate('/login');
      }
    };

    // Check immediately
    checkAuth();

    // Set up event listener for storage changes (for logout in other tabs)
    const handleStorageChange = () => {
      checkAuth();
    };

    window.addEventListener('storage', handleStorageChange);

    // Clean up
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [navigate]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: theme.palette.background.default
      }}>
        <CircularProgress />
      </Box>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Sidebar mobileOpen={mobileOpen} handleDrawerToggle={handleDrawerToggle} />

      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'auto',
        backgroundImage: isDarkMode ? 'none' : `url(${patternBg})`,
        backgroundAttachment: 'fixed',
        backgroundSize: '400px',
        backgroundPosition: 'center',
        position: 'relative',
        backgroundColor: theme.palette.background.default,
        color: theme.palette.text.primary,
        transition: 'background-color 0.3s ease, color 0.3s ease',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: isDarkMode
            ? 'none'
            : 'linear-gradient(180deg, rgba(248,249,250,0.97) 0%, rgba(255,255,255,0.99) 100%)',
          zIndex: -1,
        },
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          position: 'sticky',
          top: 0,
          zIndex: 10,
        }}>
          {isMobile && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{
                ml: 1,
                mr: 1,
                display: { md: 'none' },
                color: theme.palette.mode === 'dark' ? 'white' : 'inherit'
              }}
            >
              <MenuIcon />
            </IconButton>
          )}
          <Box sx={{ flexGrow: 1 }}>
            <Header onLogout={() => setIsAuthenticated(false)} />
          </Box>
        </Box>

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            py: 3,
            px: { xs: 2, sm: 3 },
            backgroundColor: theme.palette.background.default,
            transition: 'background-color 0.3s ease',
          }}
        >
          <Outlet />
        </Box>

        <Box
          component="footer"
          sx={{
            py: 1.5,
            px: 2,
            borderTop: theme.palette.mode === 'dark'
              ? '1px solid rgba(255, 255, 255, 0.05)'
              : '1px solid rgba(0,0,0,0.05)',
            textAlign: 'center',
            fontSize: '0.75rem',
            color: 'text.secondary',
            background: theme.palette.mode === 'dark'
              ? 'rgba(18, 18, 18, 0.8)'
              : 'rgba(255,255,255,0.5)',
            backdropFilter: 'blur(5px)',
            mt: 'auto'
          }}
        >
          <Box component="span" sx={{ color: 'primary.main', fontWeight: 500 }}>Progize</Box> SoniQue Transaction Management System {new Date().getFullYear()}
        </Box>
      </Box>
    </Box>
  );
}

export default App;
