import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Collapse,
  Card,
  CardContent
} from '@mui/material';
import {
  KeyboardArrowDown as ExpandMoreIcon,
  KeyboardArrowUp as ExpandLessIcon
} from '@mui/icons-material';
import CaseService from '../../services/CaseService';

/**
 * Component for displaying transactions associated with a case
 */
const CaseTransactions = ({ caseId, refreshKey }) => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedRows, setExpandedRows] = useState({});

  // Fetch transactions when the component mounts or refreshKey changes
  useEffect(() => {
    const fetchTransactions = async () => {
      if (!caseId) {
        setLoading(false);
        setTransactions([]);
        return;
      }
      
      setLoading(true);
      try {
        const data = await CaseService.getTransactions(caseId);
        // Filter out null transactions
        const validTransactions = (data || []).filter(transaction => transaction != null);
        setTransactions(validTransactions);
        setError(null);
      } catch (err) {
        console.error('Error fetching transactions:', err);
        setError('Failed to fetch transactions. Please try again later.');
        setTransactions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [caseId, refreshKey]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Toggle expanded row
  const toggleRowExpansion = (id) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Parse transaction data from JSON string
  const parseTransactionData = (jsonString) => {
    try {
      return typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString || {};
    } catch (e) {
      console.error('Error parsing transaction data:', e);
      return {};
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  // Render empty state
  if (!transactions || transactions.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">No transactions associated with this case.</Typography>
      </Paper>
    );
  }

  // Render transactions
  return (
    <TableContainer component={Paper}>
      <Table aria-label="case transactions table">
        <TableHead>
          <TableRow sx={{ backgroundColor: 'background.default' }}>
            <TableCell padding="checkbox"></TableCell>
            <TableCell>Transaction ID</TableCell>
            <TableCell>Rule</TableCell>
            <TableCell>Triggered At</TableCell>
            <TableCell>Customer ID</TableCell>
            <TableCell>Amount</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {transactions.map((transaction) => {
            // Skip null transactions
            if (!transaction) return null;
            
            const transactionData = parseTransactionData(transaction.transactionData);
            const isExpanded = expandedRows[transaction.id] || false;
            
            return (
              <React.Fragment key={transaction.id || `transaction-${Math.random()}`}>
                <TableRow hover>
                  <TableCell padding="checkbox">
                    <IconButton
                      aria-label="expand row"
                      size="small"
                      onClick={() => toggleRowExpansion(transaction.id)}
                    >
                      {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </TableCell>
                  <TableCell component="th" scope="row">
                    {transactionData.id || 'N/A'}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={transaction.ruleName || (transaction.ruleId ? `Rule #${transaction.ruleId}` : 'Unknown')} 
                      color="primary" 
                      size="small" 
                    />
                  </TableCell>
                  <TableCell>{formatDate(transaction.triggeredAt)}</TableCell>
                  <TableCell>{transactionData.customerId || 'N/A'}</TableCell>
                  <TableCell>
                    {transactionData.amount ? 
                      `$${parseFloat(transactionData.amount).toFixed(2)}` : 
                      transactionData.fundNetAmount ? 
                      `$${parseFloat(transactionData.fundNetAmount).toFixed(2)}` : 
                      'N/A'}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
                    <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                      <Box sx={{ margin: 1, py: 2 }}>
                        <Typography variant="h6" gutterBottom component="div">
                          Transaction Details
                        </Typography>
                        <Card variant="outlined" sx={{ mb: 2 }}>
                          <CardContent>
                            <Typography variant="subtitle2" gutterBottom>
                              Transaction Data
                            </Typography>
                            <Box sx={{ 
                              maxHeight: '300px', 
                              overflow: 'auto', 
                              backgroundColor: 'background.default',
                              p: 2,
                              borderRadius: 1
                            }}>
                              <pre style={{ margin: 0 }}>
                                {JSON.stringify(transactionData, null, 2)}
                              </pre>
                            </Box>
                          </CardContent>
                        </Card>
                      </Box>
                    </Collapse>
                  </TableCell>
                </TableRow>
              </React.Fragment>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default CaseTransactions;
