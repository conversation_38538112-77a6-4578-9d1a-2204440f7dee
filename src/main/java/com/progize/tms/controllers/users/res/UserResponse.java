package com.progize.tms.controllers.users.res;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** DTO for user response. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserResponse {

  private Long id;
  private String username;
  private String email;
  private String firstName;
  private String lastName;
  private boolean enabled;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  @Builder.Default private Set<RoleDto> roles = new HashSet<>();

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class RoleDto {
    private Long id;
    private String name;
    private String description;
  }
}
