# SoniQue Rule Configurator

A React-based UI for configuring transaction rules in the SoniQue Transaction Management System.

## Features

- Create and edit rules with intuitive UI
- Support for simple conditions (field-based comparisons)
- Support for aggregate conditions (SUM, COUNT, AVG, MIN, MAX)
- Hierarchical rule building with AND/OR/NOT operators
- Real-time rule validation
- Interactive rule testing

## Quick Start

1. Install dependencies:
   ```
   cd /Users/<USER>/code/tms-ui
   npm install
   ```

2. Start the development server:
   ```
   npm start
   ```

   This will open the application in your default browser at `http://localhost:3000`.

## Troubleshooting

If you see an empty screen or encounter issues:

1. Make sure all dependencies are installed:
   ```
   npm install react react-dom react-router-dom @mui/material @mui/icons-material @emotion/react @emotion/styled axios
   ```

2. Ensure the development server is running:
   ```
   npm start
   ```

3. Check the browser console for errors (F12 or right-click > Inspect > Console)

4. If the page is blank, try clearing your browser cache or open in incognito/private mode

## API Configuration

The application connects to the SoniQue backend API. Configure the API endpoint in the `.env` file:

```
REACT_APP_API_URL=http://localhost:8080/api
```

## Rule Configuration

The rule configurator supports:
- Simple conditions: Compare transaction fields with static values
- Aggregate conditions: Perform calculations over transaction data with time windows
- Logical operators: Combine conditions with AND/OR/NOT

## Styling

The UI follows the Progize brand guidelines with:
- Blue primary colors (#0959a1)
- Orange accent colors (#f47d15)
- Clean, professional interface with proper spacing and typography
