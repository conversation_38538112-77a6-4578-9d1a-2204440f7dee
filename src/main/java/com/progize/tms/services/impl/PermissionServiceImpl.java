package com.progize.tms.services.impl;

import com.progize.tms.repository.PermissionRepository;
import com.progize.tms.repository.entity.Permission;
import com.progize.tms.services.PermissionService;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PermissionServiceImpl implements PermissionService {

  private final PermissionRepository permissionRepository;

  @Autowired
  public PermissionServiceImpl(PermissionRepository permissionRepository) {
    this.permissionRepository = permissionRepository;
  }

  @Override
  public List<Permission> findAll() {
    return permissionRepository.findAll();
  }

  @Override
  public Optional<Permission> findById(Long id) {
    return permissionRepository.findById(id);
  }

  @Override
  public Optional<Permission> findByName(String name) {
    return permissionRepository.findByName(name);
  }

  @Override
  @Transactional
  public Permission save(Permission permission) {
    return permissionRepository.save(permission);
  }

  @Override
  @Transactional
  public void deleteById(Long id) {
    permissionRepository.deleteById(id);
  }

  @Override
  public boolean existsById(Long id) {
    return permissionRepository.existsById(id);
  }

  @Override
  public boolean existsByName(String name) {
    return permissionRepository.existsByName(name);
  }
}
