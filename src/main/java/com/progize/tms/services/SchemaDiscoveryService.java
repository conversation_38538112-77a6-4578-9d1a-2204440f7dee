package com.progize.tms.services;

import com.progize.tms.services.models.ColumnInfo;
import com.progize.tms.services.models.TableInfo;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * Service for discovering database schemas, tables, and columns from ClickHouse. This service is
 * used to discover available tables and columns for rule variable creation.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SchemaDiscoveryService {
  private final JdbcTemplate clickhouseJdbcTemplate;

  /**
   * Discovers all tables in the ClickHouse database.
   *
   * @return List of discovered tables with metadata
   */
  public List<TableInfo> discoverClickhouseTables() {
    log.info("Discovering tables in ClickHouse database");

    try {
      // First, let's check what databases are available
      String dbSql = "SELECT name FROM system.databases ORDER BY name";
      List<String> databases = clickhouseJdbcTemplate.queryForList(dbSql, String.class);
      log.info("Available ClickHouse databases: {}", databases);

      // Now query for tables in all databases (we'll filter later if needed)
      String sql =
          "SELECT database, name, engine, total_rows, total_bytes "
              + "FROM system.tables "
              + "ORDER BY database, name";

      log.info("Executing SQL: {}", sql);

      return clickhouseJdbcTemplate.query(
          sql,
          (rs, rowNum) -> {
            TableInfo table = new TableInfo();
            String database = rs.getString("database");
            String tableName = rs.getString("name");

            table.setSchemaName(database);
            table.setTableName(tableName);
            table.setEngine(rs.getString("engine"));
            table.setTotalRows(rs.getLong("total_rows"));
            table.setTotalBytes(rs.getLong("total_bytes"));

            log.debug("Found table: {}.{}", database, tableName);
            return table;
          });
    } catch (Exception e) {
      log.error("Error discovering ClickHouse tables", e);
      throw e;
    }
  }

  /**
   * Gets all columns for a specific table.
   *
   * @param schemaName Database/schema name
   * @param tableName Table name
   * @return List of columns with metadata
   */
  public List<ColumnInfo> getTableColumns(String schemaName, String tableName) {
    log.info("Discovering columns for table {}.{}", schemaName, tableName);

    try {
      // First, let's verify the table exists
      String checkSql = "SELECT count(*) FROM system.tables WHERE database = ? AND name = ?";
      Integer tableCount =
          clickhouseJdbcTemplate.queryForObject(checkSql, Integer.class, schemaName, tableName);

      if (tableCount == null || tableCount == 0) {
        log.warn("Table {}.{} not found in ClickHouse", schemaName, tableName);
        return List.of(); // Return empty list if table doesn't exist
      }

      log.info("Table {}.{} exists, fetching columns", schemaName, tableName);

      // Now get the columns
      String sql =
          "SELECT name, type, position "
              + "FROM system.columns "
              + "WHERE database = ? AND table = ? "
              + "ORDER BY position";

      log.info("Executing SQL: {} with params: [{}, {}]", sql, schemaName, tableName);

      return clickhouseJdbcTemplate.query(
          sql,
          new Object[] {schemaName, tableName},
          (rs, rowNum) -> {
            String columnName = rs.getString("name");
            String dataType = rs.getString("type");
            int position = rs.getInt("position");

            ColumnInfo column = new ColumnInfo();
            column.setName(columnName);
            column.setDataType(dataType);
            column.setPosition(position);

            // Map ClickHouse types to our data types
            String mappedType = mapClickhouseTypeToDataType(dataType);
            column.setMappedDataType(mappedType);

            log.debug(
                "Found column: {}.{}.{} of type {} (mapped to {})",
                schemaName,
                tableName,
                columnName,
                dataType,
                mappedType);
            return column;
          });
    } catch (Exception e) {
      log.error("Error discovering columns for table {}.{}", schemaName, tableName, e);
      throw e;
    }
  }

  /**
   * Maps ClickHouse data types to our system data types.
   *
   * @param clickhouseType The ClickHouse data type
   * @return The mapped data type code
   */
  private String mapClickhouseTypeToDataType(String clickhouseType) {
    if (clickhouseType.startsWith("String")) {
      return "string";
    } else if (clickhouseType.startsWith("Int") || clickhouseType.startsWith("UInt")) {
      return "integer";
    } else if (clickhouseType.startsWith("Float")) {
      return "float";
    } else if (clickhouseType.startsWith("Decimal")) {
      return "bigdecimal";
    } else if (clickhouseType.startsWith("Date") || clickhouseType.startsWith("DateTime")) {
      return "date";
    } else if (clickhouseType.equals("Bool")) {
      return "boolean";
    }

    // Default fallback for unknown types
    return "string";
  }
}
