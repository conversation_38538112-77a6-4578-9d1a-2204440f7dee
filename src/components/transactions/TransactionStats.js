import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Container,
  Grid,
  Typography,
  Chip,
  Paper,
  Divider,
  CircularProgress,
  LinearProgress,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Refresh as RefreshIcon,
  Bar<PERSON>hart as BarChartIcon,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Timeline as TimelineIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { alpha, useTheme } from '@mui/material/styles';

// Import date utility functions here
import { format, subDays } from 'date-fns';

// Dummy data for transaction statistics
const generateDummyData = () => {
  return {
    summary: {
      totalTransactions: Math.floor(Math.random() * 5000) + 10000,
      totalAmount: Math.floor(Math.random() * 10000000) + 5000000,
      averageAmount: Math.floor(Math.random() * 5000) + 2000,
      flaggedTransactions: Math.floor(Math.random() * 500) + 100,
      successRate: Math.floor(Math.random() * 15) + 80, // 80% to 95%
    },
    statusBreakdown: {
      completed: Math.floor(Math.random() * 20) + 75, // 75% to 95%
      pending: Math.floor(Math.random() * 10) + 2, // 2% to 12%
      failed: Math.floor(Math.random() * 5) + 1, // 1% to 6%
      flagged: Math.floor(Math.random() * 8) + 2, // 2% to 10%
    },
    failureReasons: [
      { reason: 'Insufficient Funds', count: Math.floor(Math.random() * 20) + 10 },
      { reason: 'Invalid Account', count: Math.floor(Math.random() * 15) + 5 },
      { reason: 'Suspicious Activity', count: Math.floor(Math.random() * 12) + 8 },
      { reason: 'Limit Exceeded', count: Math.floor(Math.random() * 10) + 5 },
      { reason: 'Technical Error', count: Math.floor(Math.random() * 8) + 3 },
    ],
    dailyTransactions: Array.from({ length: 7 }, (_, i) => ({
      date: format(subDays(new Date(), 6 - i), 'MMM dd'),
      count: Math.floor(Math.random() * 1000) + 500,
      amount: Math.floor(Math.random() * 1000000) + 200000,
      flagged: Math.floor(Math.random() * 50) + 10,
    })),
    customerSegments: [
      { segment: 'Retail', count: Math.floor(Math.random() * 3000) + 5000 },
      { segment: 'Corporate', count: Math.floor(Math.random() * 1000) + 1000 },
      { segment: 'High Net Worth', count: Math.floor(Math.random() * 500) + 200 },
      { segment: 'Small Business', count: Math.floor(Math.random() * 800) + 800 },
    ],
    apiCallStats: {
      total: Math.floor(Math.random() * 2000) + 3000,
      successful: Math.floor(Math.random() * 10) + 88, // 88% to 98% success rate
      averageResponseTime: Math.floor(Math.random() * 200) + 100, // 100ms to 300ms
      errorRate: Math.floor(Math.random() * 5) + 1, // 1% to 6%
      throttled: Math.floor(Math.random() * 3) + 1, // 1% to 4%
    },
  };
};

const StatCard = ({ title, value, subtitle, icon, trend, trendValue, bgColor }) => {
  const theme = useTheme();
  const isPositive = trend === 'up';
  
  return (
    <Card sx={{ 
      height: '100%', 
      boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
      position: 'relative',
      overflow: 'hidden',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '4px',
        background: theme => bgColor || theme.palette.primary.main,
      }
    }}>
      <CardContent sx={{ p: 3, pb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Box 
            sx={{ 
              p: 1.2, 
              borderRadius: '8px', 
              mr: 2,
              bgcolor: alpha(bgColor || theme.palette.primary.main, 0.1),
              color: bgColor || theme.palette.primary.main,
              display: 'flex',
            }}
          >
            {icon}
          </Box>
          <Typography 
            variant="body2" 
            color="text.secondary" 
            fontWeight={500}
          >
            {title}
          </Typography>
        </Box>
        
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 0.5 }}>
          {value}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
          <Chip 
            size="small" 
            icon={isPositive ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />}
            label={`${trendValue}%`} 
            sx={{ 
              bgcolor: isPositive ? alpha(theme.palette.success.main, 0.1) : alpha(theme.palette.error.main, 0.1),
              color: isPositive ? theme.palette.success.main : theme.palette.error.main,
              fontSize: '0.75rem',
              height: '24px',
              mr: 1
            }}
          />
          <Typography variant="caption" color="text.secondary">
            {subtitle}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

const TransactionStats = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const [timeRange, setTimeRange] = useState('7d');
  
  useEffect(() => {
    // Simulate API fetch
    const fetchData = () => {
      setLoading(true);
      setTimeout(() => {
        setData(generateDummyData());
        setLoading(false);
      }, 1500);
    };
    
    fetchData();
  }, [timeRange]);
  
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setData(generateDummyData());
      setLoading(false);
    }, 1500);
  };
  
  if (loading && !data) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={40} sx={{ mb: 2 }} />
          <Typography variant="h6">Loading transaction statistics...</Typography>
        </Box>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ 
        mb: 4, 
        p: 3, 
        borderRadius: 2,
        background: 'linear-gradient(135deg, rgba(12, 78, 122, 0.03) 0%, rgba(12, 78, 122, 0.07) 100%)',
        backdropFilter: 'blur(8px)',
        border: '1px solid rgba(123, 220, 181, 0.2)',
        boxShadow: '0 4px 20px rgba(12, 78, 122, 0.07)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <BarChartIcon sx={{ color: 'primary.main', mr: 1.5, fontSize: 24 }} />
            <Typography variant="h5" sx={{ fontWeight: 600 }}>
              Transaction Analytics
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormControl size="small" sx={{ width: 120, mr: 2 }}>
              <InputLabel id="time-range-label">Time Range</InputLabel>
              <Select
                labelId="time-range-label"
                id="time-range"
                value={timeRange}
                label="Time Range"
                onChange={(e) => setTimeRange(e.target.value)}
              >
                <MenuItem value="24h">Last 24h</MenuItem>
                <MenuItem value="7d">Last 7 days</MenuItem>
                <MenuItem value="30d">Last 30 days</MenuItem>
                <MenuItem value="90d">Last 90 days</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              size="small"
            >
              Refresh
            </Button>
          </Box>
        </Box>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          Monitor real-time transaction metrics, success rates, and suspicious activity flags 
          to optimize your transaction monitoring system.
        </Typography>
      </Box>
      
      {loading && (
        <Box sx={{ width: '100%', mb: 4 }}>
          <LinearProgress />
        </Box>
      )}
      
      {/* Key Metrics */}
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
        Key Metrics
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Total Transactions" 
            value={data.summary.totalTransactions.toLocaleString()} 
            subtitle="vs previous period"
            icon={<TimelineIcon />}
            trend="up"
            trendValue={5.2}
            bgColor={theme.palette.progize.darkBlue}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Success Rate" 
            value={`${data.summary.successRate}%`} 
            subtitle="vs previous period"
            icon={<BarChartIcon />}
            trend="up"
            trendValue={1.8}
            bgColor={theme.palette.success.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Flagged Transactions" 
            value={data.summary.flaggedTransactions.toLocaleString()} 
            subtitle="vs previous period"
            icon={<InfoIcon />}
            trend="down"
            trendValue={2.3}
            bgColor={theme.palette.warning.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Average Amount" 
            value={`$${data.summary.averageAmount.toLocaleString()}`} 
            subtitle="vs previous period"
            icon={<PieChartIcon />}
            trend="up"
            trendValue={3.7}
            bgColor={theme.palette.progize.lightGreen}
          />
        </Grid>
      </Grid>
      
      {/* Status Breakdown */}
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
        Transaction Status
      </Typography>
      
      <Paper sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            Transaction Status Breakdown
          </Typography>
          <Tooltip title="Based on all transactions within the selected time period">
            <IconButton size="small">
              <InfoIcon fontSize="small" color="action" />
            </IconButton>
          </Tooltip>
        </Box>
        
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Completed</Typography>
                <Typography variant="body2" fontWeight={500}>{data.statusBreakdown.completed}%</Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.statusBreakdown.completed} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: alpha(theme.palette.success.main, 0.1),
                  '& .MuiLinearProgress-bar': {
                    bgcolor: theme.palette.success.main
                  }
                }} 
              />
            </Box>
            
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Pending</Typography>
                <Typography variant="body2" fontWeight={500}>{data.statusBreakdown.pending}%</Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.statusBreakdown.pending} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: alpha(theme.palette.info.main, 0.1),
                  '& .MuiLinearProgress-bar': {
                    bgcolor: theme.palette.info.main
                  }
                }} 
              />
            </Box>
            
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Failed</Typography>
                <Typography variant="body2" fontWeight={500}>{data.statusBreakdown.failed}%</Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.statusBreakdown.failed} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: alpha(theme.palette.error.main, 0.1),
                  '& .MuiLinearProgress-bar': {
                    bgcolor: theme.palette.error.main
                  }
                }} 
              />
            </Box>
            
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Flagged</Typography>
                <Typography variant="body2" fontWeight={500}>{data.statusBreakdown.flagged}%</Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.statusBreakdown.flagged} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  '& .MuiLinearProgress-bar': {
                    bgcolor: theme.palette.warning.main
                  }
                }} 
              />
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Box sx={{ border: '1px solid', borderColor: 'divider', borderRadius: 1, p: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>Top Failure Reasons</Typography>
              <Box>
                {data.failureReasons.map((item, index) => (
                  <Box key={index} sx={{ mb: index < data.failureReasons.length - 1 ? 1.5 : 0 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="body2" color="text.secondary">{item.reason}</Typography>
                      <Typography variant="body2" fontWeight={500}>{item.count}</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={(item.count / Math.max(...data.failureReasons.map(r => r.count))) * 100} 
                      sx={{ 
                        height: 4, 
                        borderRadius: 2,
                        bgcolor: alpha(theme.palette.error.main, 0.08),
                        '& .MuiLinearProgress-bar': {
                          bgcolor: theme.palette.error.main
                        }
                      }} 
                    />
                  </Box>
                ))}
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      {/* API Call Statistics */}
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
        External API Performance
      </Typography>
      
      <Paper sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            External API Call Statistics
          </Typography>
          <Tooltip title="Performance metrics for calls to external APIs from transaction rules">
            <IconButton size="small">
              <InfoIcon fontSize="small" color="action" />
            </IconButton>
          </Tooltip>
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h4" sx={{ color: theme.palette.progize.darkBlue, fontWeight: 600 }}>
                {data.apiCallStats.total.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total API Calls
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h4" sx={{ color: theme.palette.success.main, fontWeight: 600 }}>
                {data.apiCallStats.successful}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Success Rate
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h4" sx={{ color: theme.palette.warning.main, fontWeight: 600 }}>
                {data.apiCallStats.averageResponseTime}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg. Response Time
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h4" sx={{ color: theme.palette.error.main, fontWeight: 600 }}>
                {data.apiCallStats.errorRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Error Rate
              </Typography>
            </Box>
          </Grid>
        </Grid>
        
        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            API Rate Limiting: <Typography component="span" color={data.apiCallStats.throttled > 2 ? "error.main" : "success.main"} fontWeight={500}>{data.apiCallStats.throttled}%</Typography> throttled requests
          </Typography>
          
          <Button size="small" variant="outlined" color="primary">
            View API Details
          </Button>
        </Box>
      </Paper>
      
      {/* Daily Transaction Trend */}
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
        Transaction Trends
      </Typography>
      
      <Paper sx={{ p: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            Daily Transaction Volume
          </Typography>
          <Box>
            <Button size="small" variant="text">
              By Count
            </Button>
            <Button size="small" variant="text">
              By Amount
            </Button>
          </Box>
        </Box>
        
        <Box sx={{ height: 300, display: 'flex', alignItems: 'flex-end' }}>
          {data.dailyTransactions.map((day, index) => (
            <Box 
              key={index} 
              sx={{ 
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '100%',
                justifyContent: 'flex-end',
                px: 1
              }}
            >
              <Box 
                sx={{ 
                  width: '100%',
                  maxWidth: 50,
                  height: `${(day.count / Math.max(...data.dailyTransactions.map(d => d.count))) * 70}%`,
                  minHeight: 20,
                  bgcolor: alpha(theme.palette.progize.darkBlue, 0.7),
                  borderRadius: '4px 4px 0 0',
                  position: 'relative',
                  '&:hover': {
                    bgcolor: theme.palette.progize.darkBlue,
                  },
                  transition: 'background-color 0.2s'
                }}
              >
                <Tooltip 
                  title={
                    <React.Fragment>
                      <Typography variant="body2">Count: {day.count.toLocaleString()}</Typography>
                      <Typography variant="body2">Amount: ${day.amount.toLocaleString()}</Typography>
                      <Typography variant="body2">Flagged: {day.flagged} transactions</Typography>
                    </React.Fragment>
                  }
                >
                  <Box sx={{ width: '100%', height: '100%' }} />
                </Tooltip>
                
                {/* Flagged portion */}
                <Box 
                  sx={{ 
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    width: '100%',
                    height: `${(day.flagged / day.count) * 100}%`,
                    bgcolor: theme.palette.warning.main,
                    borderRadius: '0 0 0 0',
                  }}
                />
              </Box>
              <Typography variant="caption" sx={{ mt: 1, color: 'text.secondary' }}>
                {day.date}
              </Typography>
            </Box>
          ))}
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
            <Box sx={{ width: 12, height: 12, borderRadius: 1, bgcolor: alpha(theme.palette.progize.darkBlue, 0.7), mr: 1 }} />
            <Typography variant="caption">Transactions</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: 12, height: 12, borderRadius: 1, bgcolor: theme.palette.warning.main, mr: 1 }} />
            <Typography variant="caption">Flagged</Typography>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default TransactionStats;
