# Server Configuration
server.port=8081
server.servlet.context-path=/
# MySQL Configuration (for Rules and Users)
spring.datasource.mysql.jdbc-url=*********************************************************************
spring.datasource.mysql.username=root
spring.datasource.mysql.password=root_password_here
spring.datasource.mysql.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.mysql.hikari.maximum-pool-size=10
spring.datasource.mysql.hikari.minimum-idle=5
spring.datasource.clickhouse.jdbc-url=************************************
spring.datasource.clickhouse.username=default
spring.datasource.clickhouse.password=
spring.datasource.clickhouse.driver-class-name=com.clickhouse.jdbc.ClickHouseDriver
# JPA Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
# ClickHouse Configuration (for Transactions)
#clickhouse.enabled=false
#clickhouse.datasource.jdbc-url=************************************
#clickhouse.datasource.driver-class-name=com.clickhouse.jdbc.ClickHouseDriver
#clickhouse.datasource.username=default
#clickhouse.datasource.password=
# Redis Configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379
# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus,loggers,env,mappings
management.endpoint.health.show-details=always
management.info.env.enabled=true
management.endpoint.prometheus.enabled=true
# Logging Configuration
logging.level.org.springframework.web=INFO
logging.level.com.progize.tms=DEBUG
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.org.springframework.boot.autoconfigure.jdbc=DEBUG
# Enable colored logs
spring.output.ansi.enabled=ALWAYS
# Include correlation ID in logs with improved padding and formatting
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){blue} %clr(|){faint} %clr(%-20.20thread){magenta} %clr(|){faint} %clr(%-15.15X{correlationId:-NONE}){yellow} %clr(|){faint} %clr(%5p) %clr(|){faint} %clr(%-40.40logger{39}){cyan} %clr(|){faint} %m%n
# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration
spring.flyway.url=********************************
spring.flyway.user=${spring.datasource.mysql.username}
spring.flyway.password=${spring.datasource.mysql.password}
spring.flyway.driver-class-name=com.mysql.cj.jdbc.Driver
spring.flyway.out-of-order=true
# Hibernate Envers Properties
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_aud
spring.jpa.properties.org.hibernate.envers.revision_field_name=rev
spring.jpa.properties.org.hibernate.envers.revision_type_field_name=revtype
# JWT Configuration
jwt.secret=tmsSecretKey2025!@#$%^&*()_+
jwt.expiration=86400000
jwt.refresh-expiration=86400000

# Flowable Configuration
#spring.flowable.database-schema-update=true
#spring.flowable.history-level=full
#spring.flowable.async-executor-activate=true
#spring.flowable.async-history-executor-activate=true
#spring.flowable.process-definition-location-prefix=classpath:/processes/
#spring.flowable.process-definition-location-suffixes=**.bpmn20.xml


# FLOWABLE DATASOURCE (tms_wf)
# flowable.datasource.jdbc-url=**********************************
# flowable.datasource.username=root
# flowable.datasource.password=root_password_here
# flowable.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# # HikariCP connection pool for Flowable
# flowable.datasource.hikari.maximum-pool-size=10
# flowable.datasource.hikari.minimum-idle=2
# flowable.datasource.hikari.idle-timeout=30000
# flowable.datasource.hikari.pool-name=FlowableHikariCP
# flowable.datasource.hikari.connection-timeout=20000
# flowable.datasource.hikari.max-lifetime=1800000


flowable.datasource.jdbc-url=**********************************
flowable.datasource.username=root
flowable.datasource.password=root_password_here
flowable.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# Disable security for Flowable Modeler App
flowable.modeler.app.security.enabled=false

# Also, Flowable UI apps often use a common security layer, disable that too
flowable.common.app.security.enabled=false

# And the IDM app security which Modeler might pull in
flowable.idm.app.security.enabled=false
flowable.idm.app.admin.user-id: admin
flowable.idm.app.admin.password: test

