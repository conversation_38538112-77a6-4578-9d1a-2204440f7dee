import React from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import DataSourceList from '../../components/datasources/DataSourceList';

/**
 * Page component for displaying the list of data sources
 */
const DataSourcesPage = () => {
  const theme = useTheme();

  return (
    <Box sx={{ bgcolor: theme.palette.mode === 'dark' ? 'transparent' : '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
      <DataSourceList />
    </Box>
  );
};

export default DataSourcesPage;
