package com.progize.tms.repository.entity;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

import com.progize.tms.repository.entity.base.BaseEntity;
import com.progize.tms.repository.entity.enums.DatabaseType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.Audited;

/** Entity representing database-specific configuration for a data source. */
@Entity
@Table(name = "data_source_database")
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Audited(targetAuditMode = NOT_AUDITED)
public class DatabaseSourceEntity extends BaseEntity {

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_source_id", nullable = false)
  private DataSourceEntity dataSource;

  @Column(name = "connection_url", nullable = false)
  private String connectionUrl;

  @Column(name = "driver_class")
  private String driverClass;

  @Column(name = "username")
  private String username;

  @Column(name = "password")
  private String password;

  @Enumerated(EnumType.STRING)
  @Column(name = "database_type", nullable = false)
  private DatabaseType databaseType;

  @Column(name = "schema_name")
  private String schemaName;

  @Column(name = "additional_properties", columnDefinition = "TEXT")
  private String additionalProperties;
}
