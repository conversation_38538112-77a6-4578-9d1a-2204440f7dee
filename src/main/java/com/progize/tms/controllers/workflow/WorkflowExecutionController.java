package com.progize.tms.controllers.workflow;

import com.progize.tms.controllers.workflow.dto.CompleteTaskRequest;
import com.progize.tms.controllers.workflow.dto.StartProcessRequest;
import com.progize.tms.workflow.ProcessHistory;
import com.progize.tms.workflow.core.WorkflowService;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.flowable.task.api.Task;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/workflow/execution")
@RequiredArgsConstructor
public class WorkflowExecutionController {
  private final Map<String, WorkflowService> workflowServices;

  @PostMapping("/start")
  public ResponseEntity<String> startProcess(@Valid @RequestBody StartProcessRequest request) {
    WorkflowService service = getWorkflowService(request.getProcessType());
    String processInstanceId =
        service.startProcess(request.getProcessType(), request.getVariables());
    return ResponseEntity.ok(processInstanceId);
  }

  @PostMapping("/tasks/{taskId}/complete")
  public ResponseEntity<Void> completeTask(
      @PathVariable String taskId,
      @Valid @RequestBody CompleteTaskRequest request,
      @RequestParam(required = false) List<MultipartFile> attachments) {
    WorkflowService service = getWorkflowService(request.getProcessType());
    service.completeTask(taskId, request.getVariables(), attachments);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/tasks")
  public ResponseEntity<List<Task>> getUserTasks(@AuthenticationPrincipal UserDetails userDetails) {
    // Since a user might have tasks from different workflow types,
    // we'll aggregate tasks from all workflow services
    List<Task> allTasks =
        workflowServices.values().stream()
            .flatMap(service -> service.getUserTasks(userDetails.getUsername()).stream())
            .sorted((t1, t2) -> t2.getCreateTime().compareTo(t1.getCreateTime()))
            .toList();

    return ResponseEntity.ok(allTasks);
  }

  @GetMapping("/process/{processInstanceId}/history")
  public ResponseEntity<ProcessHistory> getProcessHistory(
      @PathVariable String processInstanceId, @RequestParam String processType) {
    WorkflowService service = getWorkflowService(processType);
    return ResponseEntity.ok(service.getProcessHistory(processInstanceId));
  }

  private WorkflowService getWorkflowService(String processType) {
    WorkflowService service = workflowServices.get(processType);
    if (service == null) {
      throw new IllegalArgumentException("Unknown process type: " + processType);
    }
    return service;
  }
}
