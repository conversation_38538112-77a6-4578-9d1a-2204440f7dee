# dependencies
/node_modules

# build output
/build
/dist

# runtime data
/.pnp
.pnp.js

# testing
/coverage

# production
.env.production

# misc
.DS_Store
.env.local
.env.development
.env.test
.env
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.iml
.idea/
# vscode
.vscode/

# Mac system files
*.DS_Store

# log files
logs
*.log

# Editor directories and files
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# Optional: if you use storybook
out/

# Optional: if you use Netlify
/netlify/

# Optional: if you use Docker
docker-compose.override.yml

