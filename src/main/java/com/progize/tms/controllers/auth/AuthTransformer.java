package com.progize.tms.controllers.auth;

import com.progize.tms.controllers.auth.req.RegisterRequest;
import com.progize.tms.controllers.auth.res.JwtResponse;
import com.progize.tms.repository.entity.User;
import java.time.LocalDateTime;
import org.springframework.stereotype.Component;

@Component
public class AuthTransformer {

  /** Transforms a registration request DTO to a User entity */
  public User toEntity(RegisterRequest request) {
    return User.builder()
        .username(request.getUsername())
        .email(request.getEmail())
        .firstName(request.getFirstName())
        .lastName(request.getLastName())
        .enabled(true)
        .createdAt(LocalDateTime.now())
        .build();
  }

  /** Transforms a User entity and token information to a JWT response DTO */
  public JwtResponse toJwtResponse(String token, String refreshToken, User user) {
    return JwtResponse.builder()
        .token(token)
        .refreshToken(refreshToken)
        .id(user.getId())
        .username(user.getUsername())
        .email(user.getEmail())
        .firstName(user.getFirstName())
        .lastName(user.getLastName())
        .build();
  }
}
