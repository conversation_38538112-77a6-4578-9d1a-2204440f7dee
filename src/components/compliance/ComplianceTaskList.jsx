import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Skeleton,
  Alert,
  Card,
  CardContent,
  Divider,
  useTheme
} from '@mui/material';
import { 
  AccessTime as AccessTimeIcon,
  Assignment as AssignmentIcon,
  ArrowForward as ArrowForwardIcon,
  Flag as FlagIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import ComplianceWorkflowService from '../../services/ComplianceWorkflowService';
// Using MUI transitions instead of framer-motion

/**
 * Professional component to display a list of compliance tasks
 */
const ComplianceTaskList = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const theme = useTheme();

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setLoading(true);
        const taskData = await ComplianceWorkflowService.getComplianceTasks();
        setTasks(taskData);
        setError(null);
      } catch (err) {
        console.error('Error fetching compliance tasks:', err);
        setError('Failed to load compliance tasks. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
  }, []);

  const handleViewTask = (taskId) => {
    navigate(`/compliance/tasks/${taskId}`);
  };

  // Format date to a readable string
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get priority color based on priority level
  const getPriorityColor = (priority) => {
    // Convert priority to string and handle null/undefined values
    const priorityStr = String(priority || '').toLowerCase();
    
    switch(priorityStr) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.success.main;
      default:
        return theme.palette.info.main;
    }
  };

  // Get status chip color based on status
  const getStatusChip = (status) => {
    let color = 'default';
    let label = status || 'Pending';
    
    switch(status?.toLowerCase()) {
      case 'completed':
        color = 'success';
        break;
      case 'in progress':
        color = 'primary';
        break;
      case 'pending':
        color = 'warning';
        break;
      case 'rejected':
        color = 'error';
        break;
      default:
        color = 'default';
    }
    
    return (
      <Chip 
        label={label} 
        color={color} 
        size="small" 
        variant="outlined"
      />
    );
  };

  // Loading skeleton
  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
          <Skeleton width={300} />
        </Typography>
        <Paper elevation={2} sx={{ borderRadius: 2, overflow: 'hidden' }}>
          <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
            <Skeleton variant="rectangular" height={40} sx={{ mb: 2 }} />
            {[1, 2, 3, 4].map((item) => (
              <Skeleton key={item} variant="rectangular" height={60} sx={{ mb: 1 }} />
            ))}
          </Box>
        </Paper>
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button 
          variant="contained" 
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </Box>
    );
  }

  // Empty state
  if (tasks.length === 0) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
          Compliance Tasks
        </Typography>
        <Card 
          sx={{ 
            borderRadius: 2, 
            textAlign: 'center',
            p: 4,
            bgcolor: 'background.paper',
            animation: `${theme.transitions.create(['opacity', 'transform'], {
              duration: theme.transitions.duration.standard,
              easing: theme.transitions.easing.easeOut
            })}`,
            opacity: 1,
            transform: 'translateY(0)'
          }}
        >
          <AssignmentIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No Compliance Tasks Found
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
            There are currently no compliance tasks assigned to you.
          </Typography>
          <Button 
            variant="outlined" 
            onClick={() => window.location.reload()}
            startIcon={<SearchIcon />}
          >
            Refresh Tasks
          </Button>
        </Card>
      </Box>
    );
  }

  // Main content with tasks
  return (
    <Box sx={{ p: 3 }}>
      <Typography 
        variant="h4" 
        sx={{ 
          mb: 3, 
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          animation: `${theme.transitions.create('all', {
            duration: theme.transitions.duration.standard,
            easing: theme.transitions.easing.easeOut
          })}`
        }}
      >
        <AssignmentIcon sx={{ mr: 1 }} /> Compliance Tasks
      </Typography>
      
      <Card 
        sx={{ 
          borderRadius: 2, 
          overflow: 'hidden',
          boxShadow: theme.shadows[3],
          animation: `${theme.transitions.create('all', {
            duration: theme.transitions.duration.standard,
            easing: theme.transitions.easing.easeOut
          })}`
        }}
      >
        <Box sx={{ p: 2, bgcolor: theme.palette.primary.main, color: 'white' }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            {tasks.length} {tasks.length === 1 ? 'Task' : 'Tasks'} Requiring Your Attention
          </Typography>
        </Box>
        
        <Divider />
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: theme.palette.grey[50] }}>
                <TableCell sx={{ fontWeight: 'bold' }}>Task Name</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Created</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Due Date</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Priority</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tasks.map((task, index) => (
                <TableRow 
                  key={task.id}

                  sx={{ 
                    '&:hover': { 
                      bgcolor: theme.palette.action.hover 
                    }
                  }}
                >
                  <TableCell>
                    <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
                      {task.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                      {task.description || 'No description'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {formatDate(task.createTime)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {task.dueDate ? (
                      <Typography variant="body2">
                        {formatDate(task.dueDate)}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No due date
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <FlagIcon 
                        fontSize="small" 
                        sx={{ 
                          mr: 1, 
                          color: getPriorityColor(task.priority)
                        }} 
                      />
                      <Typography variant="body2">
                        {task.priority || 'Normal'}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {getStatusChip(task.status)}
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="View Task Details">
                      <IconButton 
                        color="primary"
                        onClick={() => handleViewTask(task.id)}
                        size="small"
                        sx={{ 
                          bgcolor: theme.palette.primary.light,
                          color: theme.palette.primary.contrastText,
                          '&:hover': {
                            bgcolor: theme.palette.primary.main,
                          }
                        }}
                      >
                        <ArrowForwardIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>
    </Box>
  );
};

export default ComplianceTaskList;
