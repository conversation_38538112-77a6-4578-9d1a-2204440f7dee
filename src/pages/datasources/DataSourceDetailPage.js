import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Breadcrumbs,
  Link,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  useTheme
} from '@mui/material';
import DataSourceDetail from '../../components/datasources/DataSourceDetail';
import dataSourceService from '../../services/dataSourceService';

/**
 * Page component for displaying detailed information about a data source
 */
const DataSourceDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [dataSourceToDelete, setDataSourceToDelete] = useState(null);

  // Handle delete button click
  const handleDeleteClick = (dataSource) => {
    setDataSourceToDelete(dataSource);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      await dataSourceService.deleteDataSource(dataSourceToDelete.id);
      navigate('/data-sources', {
        state: {
          notification: {
            message: `Data source "${dataSourceToDelete.name}" deleted successfully`,
            severity: 'success'
          }
        }
      });
    } catch (err) {
      console.error('Error deleting data source:', err);
      // Stay on the page and show an error
    } finally {
      setDeleteDialogOpen(false);
      setDataSourceToDelete(null);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setDataSourceToDelete(null);
  };

  return (
    <Box sx={{ bgcolor: theme.palette.mode === 'dark' ? 'transparent' : '#f8f9fa', minHeight: '100vh', margin: -3, padding: 3 }}>
      <DataSourceDetail
        dataSourceId={parseInt(id)}
        onDelete={handleDeleteClick}
      />

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the data source "{dataSourceToDelete?.name}"?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DataSourceDetailPage;
