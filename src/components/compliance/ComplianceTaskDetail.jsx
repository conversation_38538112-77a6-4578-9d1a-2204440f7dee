import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Divider, 
  Button, 
  TextField, 
  MenuItem, 
  FormControl, 
  FormLabel, 
  FormControlLabel, 
  RadioGroup, 
  Radio, 
  Checkbox, 
  CircularProgress, 
  Paper, 
  Chip, 
  Alert, 
  AlertTitle, 
  Tabs,
  Tab,
  LinearProgress,
  Avatar,
  Badge,
  IconButton,
  Tooltip,
  Fade,
  Zoom,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  Stack
} from '@mui/material';
import { 
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
  ArrowBack as ArrowBackIcon,
  AttachFile as AttachFileIcon,
  Send as SendIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Flag as FlagIcon,
  AccountBalance as AccountBalanceIcon,
  Business as BusinessIcon,
  Public as PublicIcon,
  Security as SecurityIcon,
  Gavel as GavelIcon,
  Timeline as TimelineIcon,
  Bar<PERSON>hart as BarChartIcon,
  SmartToy as SmartToyIcon,
  Close as CloseIcon,
  Help as HelpIcon,
  MoreVert as MoreVertIcon,
  Escalator as EscalateIcon,
  Cancel as RejectIcon,
  CheckCircleOutline as ApproveIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import ComplianceWorkflowService from '../../services/ComplianceWorkflowService';


/**
 * Modern compliance task detail component with tabbed interface and AI investigator
 */
const ComplianceTaskDetail = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();

  // Sample task data based on your variables structure
  const sampleTaskData = {
    id: taskId,
    name: "High Risk Transaction Review",
    variables: {
      deal_date: "2025-06-04T07:39:13.207Z",
      nav: 1000.5,
      officerComments: "adsfasfsdafasdfas",
      trade_date_time: "2025-06-04T07:39:13.207Z",
      aml_risk_score_channel: 100,
      is_pep: "Sample is_pep",
      fund_id: 100,
      fund_name: "Sample fund_name",
      ruleDescription: "High Transaction Amount with High Risk",
      fund_net_amount: 1000.5,
      flagReason: null,
      managerDecision: "return_to_officer",
      source_of_fund: "Sample source_of_fund",
      caseId: 3,
      aml_risk_profile: "HIGH",
      aml_risk_score_geography: 100,
      ruleName: "High Transaction Amount with High Risk",
      aml_risk_score_customer: 100,
      portfolio_id: 100,
      managerComments: "This should be returned to the officer, more information needed.",
      fund_category: "Sample fund_category",
      officerDecision: "positive",
      ruleId: 1
    }
  };

  // Core state
  const [task, setTask] = useState(null);
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [attachments, setAttachments] = useState([]);

  // UI state
  const [tabValue, setTabValue] = useState(0);
  const [aiChatOpen, setAiChatOpen] = useState(false);
  const [aiMessages, setAiMessages] = useState([
    { sender: 'ai', text: 'Hello! I can help investigate this case. What would you like to know?' }
  ]);
  const [aiInput, setAiInput] = useState('');
  const [confirmationDialog, setConfirmationDialog] = useState({
    open: false,
    action: null,
    title: '',
    message: ''
  });
  
  useEffect(() => {
    const fetchTaskDetails = async () => {
      try {
        setLoading(true);
        // For now, use sample data. Later this will be replaced with actual API call
        // const taskDetails = await ComplianceWorkflowService.getTaskDetails(taskId);
        setTask(sampleTaskData);
        setError(null);
      } catch (err) {
        console.error('Error fetching task details:', err);
        setError('Failed to load task details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTaskDetails();
  }, [taskId]);
  
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  const handleFileChange = (e) => {
    setAttachments(Array.from(e.target.files));
  };
  
  // Tab handling
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // AI Chat functions
  const toggleAiChat = () => {
    setAiChatOpen(!aiChatOpen);
  };
  
  const handleAiInputChange = (e) => {
    setAiInput(e.target.value);
  };
  
  const sendAiMessage = () => {
    if (!aiInput.trim()) return;

    // Add user message
    const userMessage = {
      sender: 'user',
      text: aiInput,
      timestamp: new Date()
    };
    setAiMessages(prev => [...prev, userMessage]);

    // Simulate AI response (in a real app, this would call an API)
    setTimeout(() => {
      const aiResponse = {
        sender: 'ai',
        text: `I've analyzed this case and found that the transaction of $${task?.variables?.fund_net_amount?.toLocaleString() || 'unknown amount'} has a high risk score of ${task?.variables?.aml_risk_score_customer || '?'}/100. This requires additional verification based on the rule "${task?.variables?.ruleName}".`,
        timestamp: new Date()
      };
      setAiMessages(prev => [...prev, aiResponse]);
    }, 1000);

    setAiInput('');
  };
  
  // Action button handlers
  const handleActionButtonClick = (action) => {
    let title, message;
    
    switch(action) {
      case 'request_info':
        title = 'Request Additional Information';
        message = 'Are you sure you want to request additional information for this case?';
        break;
      case 'escalate':
        title = 'Escalate Case';
        message = 'Are you sure you want to escalate this case to a higher authority?';
        break;
      case 'reject':
        title = 'Reject Transaction';
        message = 'Are you sure you want to reject this transaction? This action cannot be undone.';
        break;
      case 'approve':
        title = 'Approve Transaction';
        message = 'Are you sure you want to approve this transaction?';
        break;
      default:
        return;
    }
    
    setConfirmationDialog({
      open: true,
      action,
      title,
      message
    });
  };
  
  const handleConfirmAction = async () => {
    const { action } = confirmationDialog;
    setConfirmationDialog(prev => ({ ...prev, open: false }));

    try {
      setSubmitting(true);
      setError(null);

      // Simulate API call for now
      console.log(`Performing action: ${action} on task ${taskId}`);

      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSubmitSuccess(true);

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/compliance/tasks');
      }, 1500);

    } catch (err) {
      console.error('Error submitting task:', err);
      setError('Failed to submit task. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };
  
  const handleCancelAction = () => {
    setConfirmationDialog(prev => ({ ...prev, open: false }));
  };
  

  
  // Helper functions
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getRiskLevelColor = (riskLevel) => {
    if (!riskLevel) return theme.palette.grey[500];
    const level = riskLevel.toString().toUpperCase();
    if (level.includes('HIGH')) return theme.palette.error.main;
    if (level.includes('MEDIUM')) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getPriorityColor = (priority) => {
    if (!priority) return theme.palette.grey[500];
    const level = priority.toString().toUpperCase();
    if (level.includes('HIGH')) return theme.palette.error.main;
    if (level.includes('MEDIUM')) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getSlaStatusColor = (dueDate) => {
    if (!dueDate) return theme.palette.grey[500];
    const now = new Date();
    const due = new Date(dueDate);
    const diffHours = (due - now) / (1000 * 60 * 60);

    if (diffHours < 0) return theme.palette.error.main; // Overdue
    if (diffHours < 24) return theme.palette.warning.main; // Due soon
    return theme.palette.success.main; // On track
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendAiMessage();
    }
  };




  // Loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <AlertTitle>Error</AlertTitle>
          {error}
        </Alert>
        <Button
          variant="outlined"
          onClick={() => navigate('/compliance/tasks')}
          sx={{ mt: 2 }}
          startIcon={<ArrowBackIcon />}
        >
          Back to Tasks
        </Button>
      </Box>
    );
  }

  // Success state
  if (submitSuccess) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="success">
          <AlertTitle>Success</AlertTitle>
          Task completed successfully! Redirecting...
        </Alert>
      </Box>
    );
  }

  // No task found state
  if (!task) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          <AlertTitle>No Task Found</AlertTitle>
          The requested task could not be found or has been completed.
        </Alert>
        <Button
          variant="outlined"
          onClick={() => navigate('/compliance/tasks')}
          sx={{ mt: 2 }}
          startIcon={<ArrowBackIcon />}
        >
          Back to Tasks
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
      {/* Header with back button */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Tooltip title="Back to Tasks">
          <IconButton
            onClick={() => navigate('/compliance/tasks')}
            sx={{ mr: 2 }}
          >
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Compliance Task Detail
        </Typography>
      </Box>

      {/* Summary Banner */}
      <Card sx={{ mb: 3, borderRadius: 2, boxShadow: theme.shadows[4] }}>
        <CardContent sx={{ p: 3 }}>
          <Grid container spacing={3} alignItems="center">
            {/* Transaction Info */}
            <Grid item xs={12} md={6}>
              <Stack spacing={1}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
                  Transaction #{task?.variables?.portfolio_id || 'N/A'}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Rule: {task?.variables?.ruleName || 'N/A'}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Customer: {task?.variables?.fund_name || 'N/A'}
                </Typography>
              </Stack>
            </Grid>

            {/* Status Badges */}
            <Grid item xs={12} md={6}>
              <Stack direction="row" spacing={1} justifyContent={{ xs: 'flex-start', md: 'flex-end' }} flexWrap="wrap">
                <Chip
                  label="High Priority"
                  color="error"
                  size="small"
                  icon={<FlagIcon />}
                />
                <Chip
                  label="In Review"
                  color="warning"
                  size="small"
                  icon={<ScheduleIcon />}
                />
                <Chip
                  label="SLA: 2 days"
                  color="success"
                  size="small"
                  icon={<AccessTimeIcon />}
                />
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabbed Interface */}
      <Card sx={{ mb: 3, borderRadius: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="task detail tabs">
            <Tab
              label="Transaction Info"
              icon={<MoneyIcon />}
              iconPosition="start"
              sx={{ minHeight: 64 }}
            />
            <Tab
              label="Rule Info"
              icon={<GavelIcon />}
              iconPosition="start"
              sx={{ minHeight: 64 }}
            />
            <Tab
              label="Customer Info"
              icon={<PersonIcon />}
              iconPosition="start"
              sx={{ minHeight: 64 }}
            />
          </Tabs>
        </Box>

        {/* Transaction Info Tab */}
        {tabValue === 0 && (
          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <MoneyIcon color="primary" />
                      <Typography variant="h6">Amount</Typography>
                    </Box>
                    <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                      {formatCurrency(task?.variables?.fund_net_amount)}
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BusinessIcon color="primary" />
                      <Typography variant="h6">Channel</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {task?.variables?.fund_category || 'N/A'}
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocationIcon color="primary" />
                      <Typography variant="h6">Geography Risk</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      Score: {task?.variables?.aml_risk_score_geography || 'N/A'}/100
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <RiskIcon color="primary" />
                      <Typography variant="h6">Risk Score</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="h5" sx={{ fontWeight: 600 }}>
                        {task?.variables?.aml_risk_score_customer || 0}/100
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={task?.variables?.aml_risk_score_customer || 0}
                        sx={{
                          flexGrow: 1,
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: theme.palette.grey[200],
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getRiskLevelColor(task?.variables?.aml_risk_score_customer)
                          }
                        }}
                      />
                    </Box>
                  </Stack>
                </Paper>
              </Grid>
            </Grid>
          </CardContent>
        )}
        {/* Rule Info Tab */}
        {tabValue === 1 && (
          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <GavelIcon color="primary" />
                      <Typography variant="h6">Rule Name</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {task?.variables?.ruleName || 'N/A'}
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <WarningIcon color="primary" />
                      <Typography variant="h6">Trigger Condition</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      Amount &gt; $50,000
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TimelineIcon color="primary" />
                      <Typography variant="h6">Rule Version</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      v1.0
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DescriptionIcon color="primary" />
                      <Typography variant="h6">Description</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {task?.variables?.ruleDescription || 'N/A'}
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>
            </Grid>
          </CardContent>
        )}

        {/* Customer Info Tab */}
        {tabValue === 2 && (
          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PersonIcon color="primary" />
                      <Typography variant="h6">Customer Name</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {task?.variables?.fund_name || 'N/A'}
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PublicIcon color="primary" />
                      <Typography variant="h6">Nationality</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      Not Available
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <SecurityIcon color="primary" />
                      <Typography variant="h6">Risk Level</Typography>
                    </Box>
                    <Chip
                      label={task?.variables?.aml_risk_profile || 'UNKNOWN'}
                      color={task?.variables?.aml_risk_profile === 'HIGH' ? 'error' : 'warning'}
                      sx={{ fontWeight: 600 }}
                    />
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <VerifiedIcon color="primary" />
                      <Typography variant="h6">KYC Status</Typography>
                    </Box>
                    <Chip
                      label="Verified"
                      color="success"
                      icon={<VerifiedIcon />}
                      sx={{ fontWeight: 600 }}
                    />
                  </Stack>
                </Paper>
              </Grid>

              <Grid item xs={12}>
                <Paper sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FlagIcon color="primary" />
                      <Typography variant="h6">PEP Status</Typography>
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {task?.variables?.is_pep || 'Not Available'}
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>
            </Grid>
          </CardContent>
        )}
      </Card>
      {/* Action Buttons */}
      <Card sx={{ borderRadius: 2, boxShadow: theme.shadows[2] }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            Actions
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<InfoIcon />}
                onClick={() => handleActionButtonClick('request_info')}
                disabled={submitting}
                sx={{ height: 48 }}
              >
                Request Info
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="outlined"
                color="warning"
                startIcon={<EscalateIcon />}
                onClick={() => handleActionButtonClick('escalate')}
                disabled={submitting}
                sx={{ height: 48 }}
              >
                Escalate
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="outlined"
                color="error"
                startIcon={<RejectIcon />}
                onClick={() => handleActionButtonClick('reject')}
                disabled={submitting}
                sx={{ height: 48 }}
              >
                Reject
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="contained"
                color="success"
                startIcon={<ApproveIcon />}
                onClick={() => handleActionButtonClick('approve')}
                disabled={submitting}
                sx={{ height: 48 }}
              >
                Approve
              </Button>
            </Grid>
          </Grid>

          {/* AI Investigator Button */}
          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SmartToyIcon />}
              onClick={toggleAiChat}
              sx={{
                px: 4,
                py: 1.5,
                borderRadius: 3,
                fontWeight: 600,
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                boxShadow: theme.shadows[4]
              }}
            >
              Investigate with AI
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* AI Chat Interface */}
      <Slide direction="up" in={aiChatOpen} mountOnEnter unmountOnExit>
        <Paper
          sx={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            width: 400,
            height: 500,
            borderRadius: 3,
            boxShadow: theme.shadows[10],
            display: 'flex',
            flexDirection: 'column',
            zIndex: 1300,
            overflow: 'hidden'
          }}
        >
          {/* Chat Header */}
          <Box
            sx={{
              p: 2,
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SmartToyIcon />
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                AI Investigator
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={toggleAiChat}
              sx={{ color: 'white' }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Chat Messages */}
          <Box
            sx={{
              flexGrow: 1,
              p: 2,
              overflowY: 'auto',
              backgroundColor: theme.palette.grey[50]
            }}
          >
            {aiMessages.map((message, index) => (
              <Box
                key={index}
                sx={{
                  mb: 2,
                  display: 'flex',
                  justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'
                }}
              >
                <Paper
                  sx={{
                    p: 1.5,
                    maxWidth: '80%',
                    backgroundColor: message.sender === 'user'
                      ? theme.palette.primary.main
                      : 'white',
                    color: message.sender === 'user' ? 'white' : 'inherit',
                    borderRadius: 2
                  }}
                >
                  <Typography variant="body2">
                    {message.text}
                  </Typography>
                </Paper>
              </Box>
            ))}
          </Box>

          {/* Chat Input */}
          <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Ask about this case..."
                value={aiInput}
                onChange={handleAiInputChange}
                onKeyDown={handleKeyPress}
                variant="outlined"
              />
              <IconButton
                color="primary"
                onClick={sendAiMessage}
                disabled={!aiInput.trim()}
              >
                <SendIcon />
              </IconButton>
            </Box>
          </Box>
        </Paper>
      </Slide>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmationDialog.open}
        onClose={handleCancelAction}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ fontWeight: 600 }}>
          {confirmationDialog.title}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {confirmationDialog.message}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCancelAction} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmAction}
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Processing...' : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ComplianceTaskDetail;
