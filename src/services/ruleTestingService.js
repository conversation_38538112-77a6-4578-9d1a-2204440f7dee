import axios from 'axios';
import { API_BASE_URL } from '../config';

/**
 * Test a rule with sample data.
 * 
 * @param {Object} testRequest - The test request containing rule definition and sample data
 * @param {Object} testRequest.ruleDefinition - The rule condition to test
 * @param {Object} testRequest.sampleData - Sample data to test against
 * @param {Object} testRequest.options - Options for testing
 * @returns {Promise} - Promise resolving to test results
 */
export const testRule = async (testRequest) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/rule-testing`, testRequest);
    
    // Check if response has the expected structure
    if (response.data && response.data.success === true && response.data.data) {
      return response.data;
    } else {
      console.warn('Unexpected API response format:', response.data);
      // Return a standardized format even if the API response is unexpected
      return {
        success: response.data?.success || false,
        message: response.data?.message || 'Received unexpected response format from server',
        data: response.data?.data || response.data || null
      };
    }
  } catch (error) {
    console.error('Error testing rule:', error);
    
    // Create a user-friendly error response
    const errorResponse = {
      success: false,
      message: error.response?.data?.message || error.message || 'An error occurred while testing the rule',
      data: null
    };
    
    // For debugging purposes, log more details
    if (error.response) {
      console.error('API error response:', error.response.data);
    }
    
    return errorResponse;
  }
};
