import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  But<PERSON>,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  FormGroup,
  FormControlLabel,
  useTheme
} from '@mui/material';
import { People as PeopleIcon } from '@mui/icons-material';
import WorkflowDesignerService from '../../services/WorkflowDesignerService';

const RoleSelector = ({ node, roles, updateNode }) => {
  const theme = useTheme();
  
  // Local state
  const [availableRoles, setAvailableRoles] = useState(roles || []);
  const [selectedRoles, setSelectedRoles] = useState(node.data.eligibleRoles || []);
  
  // Fetch available roles
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const fetchedRoles = await WorkflowDesignerService.getRoles();
        setAvailableRoles(fetchedRoles);
      } catch (error) {
        console.error('Error fetching roles:', error);
      }
    };
    
    fetchRoles();
  }, []);
  
  // Handle role selection
  const handleRoleToggle = (role) => {
    const currentIndex = selectedRoles.indexOf(role);
    const newSelectedRoles = [...selectedRoles];
    
    if (currentIndex === -1) {
      newSelectedRoles.push(role);
    } else {
      newSelectedRoles.splice(currentIndex, 1);
    }
    
    setSelectedRoles(newSelectedRoles);
  };
  
  // Apply role changes
  const handleApplyRoles = () => {
    updateNode(node.id, { eligibleRoles: selectedRoles });
  };
  
  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom>
        Role Assignment
      </Typography>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        Select the roles that are eligible to perform actions in this step.
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Selected Roles:
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {selectedRoles.length > 0 ? (
            selectedRoles.map((role) => (
              <Chip
                key={role}
                label={role}
                onDelete={() => handleRoleToggle(role)}
                color="primary"
                variant="outlined"
                icon={<PeopleIcon />}
              />
            ))
          ) : (
            <Typography variant="body2" color="text.secondary">
              No roles selected. All users will be eligible.
            </Typography>
          )}
        </Box>
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        Available Roles:
      </Typography>
      
      <List sx={{ maxHeight: 300, overflow: 'auto' }}>
        {availableRoles.map((role) => (
          <ListItem key={role.name} dense button onClick={() => handleRoleToggle(role.name)}>
            <ListItemIcon>
              <Checkbox
                edge="start"
                checked={selectedRoles.indexOf(role.name) !== -1}
                tabIndex={-1}
                disableRipple
              />
            </ListItemIcon>
            <ListItemText 
              primary={role.name} 
              secondary={role.description} 
            />
          </ListItem>
        ))}
      </List>
      
      <Button
        variant="contained"
        color="primary"
        onClick={handleApplyRoles}
        fullWidth
        sx={{ mt: 2 }}
      >
        Apply Role Changes
      </Button>
    </Box>
  );
};

export default RoleSelector;
