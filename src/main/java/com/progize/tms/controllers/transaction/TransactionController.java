package com.progize.tms.controllers.transaction;

import com.progize.tms.controllers.transaction.req.ProcessBucketTransactionsRequest;
import com.progize.tms.controllers.transaction.req.ProcessEnrichedBucketTransactionsRequest;
import com.progize.tms.controllers.transaction.req.ProcessTransactionsRequest;
import com.progize.tms.controllers.transaction.res.TransactionResMdl;
import com.progize.tms.payload.ApiResponse;
import com.progize.tms.repository.entity.Transaction;
import com.progize.tms.services.EnrichedBucketTransactionService;
import com.progize.tms.services.RuleExecutor;
import com.progize.tms.services.TransactionService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/transactions")
public class TransactionController {
  private final TransactionService transactionService;
  private final RuleExecutor ruleExecutor;
  private final EnrichedBucketTransactionService enrichedBucketTransactionService;
  private final TransactionTransformer transformer;

  @PostMapping
  public ResponseEntity<TransactionResMdl> processTransaction(
      @RequestBody Transaction transaction) {
    return ResponseEntity.ok(
        transformer.toResponse(transactionService.processTransaction(transaction)));
  }

  @GetMapping
  public ResponseEntity<List<TransactionResMdl>> getRecentTransactions() {
    List<Transaction> transactions = transactionService.getRecentTransactions(1000);
    return ResponseEntity.ok(transformer.toResponseList(transactions));
  }

  @GetMapping("/flagged")
  public ResponseEntity<List<TransactionResMdl>> getFlaggedTransactions() {
    List<Transaction> transactions = transactionService.getTransactionsByFlagged(true);
    return ResponseEntity.ok(transformer.toResponseList(transactions));
  }

  @GetMapping("/account/{accountId}")
  public ResponseEntity<List<TransactionResMdl>> getTransactionsByAccount(
      @PathVariable String accountId) {
    List<Transaction> transactions = transactionService.getTransactionsByAccount(accountId);
    return ResponseEntity.ok(transformer.toResponseList(transactions));
  }

  /**
   * Process a list of transactions against all active rules.
   *
   * @param request The request containing transactions to process
   * @return List of processed transactions with flag status updated based on matching rules
   */
  @PostMapping("/process-rules")
  public ResponseEntity<ApiResponse<List<TransactionResMdl>>> processTransactionsWithRules(
      @RequestBody ProcessTransactionsRequest request) {
    // Convert request to entities
    List<Transaction> transactionEntities = transformer.toEntityList(request);

    // Use the rule executor to process the transactions
    List<Transaction> processedTransactions = ruleExecutor.processTransactions(transactionEntities);

    // Convert processed entities back to response models
    List<TransactionResMdl> responseList = transformer.toResponseList(processedTransactions);

    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Processed " + transactionEntities.size() + " transactions with rules",
            responseList));
  }

  /**
   * Process transactions using a specific data bucket's structure. This endpoint allows clients to
   * submit transaction data that matches the variables defined in the specified bucket.
   *
   * @param request The request containing the bucket ID and transaction data
   * @return List of processed transactions with flag status updated based on matching rules
   */
  @PostMapping("/process-bucket-transactions")
  public ResponseEntity<ApiResponse<List<Map<String, Object>>>> processBucketTransactions(
      @RequestBody ProcessBucketTransactionsRequest request) {
    // Validate the request
    if (request.getBucketId() == null) {
      return ResponseEntity.badRequest()
          .body(new ApiResponse<>(false, "Bucket ID is required", null));
    }

    if (request.getTransactions() == null || request.getTransactions().isEmpty()) {
      return ResponseEntity.badRequest()
          .body(new ApiResponse<>(false, "At least one transaction is required", null));
    }

    // Process the transactions using the bucket-aware rule executor
    List<Map<String, Object>> processedTransactions =
        ruleExecutor.processBucketTransactions(request.getBucketId(), request.getTransactions());

    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Processed "
                + request.getTransactions().size()
                + " transactions with bucket ID "
                + request.getBucketId(),
            processedTransactions));
  }

  /**
   * Process transactions with enrichment from lookup buckets. This endpoint allows for processing
   * transactions with data automatically enriched from related lookup buckets based on the bucket
   * relationships defined in the system.
   *
   * <p>If no transactions are provided in the request, data will be fetched from the bucket's
   * configured data source.
   *
   * @param request The request containing the bucket ID and optional transaction data
   * @return List of processed transactions with flag status updated based on matching rules
   */
  @PostMapping("/process-enriched-bucket-transactions")
  public ResponseEntity<ApiResponse<List<Map<String, Object>>>> processEnrichedBucketTransactions(
      @RequestBody ProcessEnrichedBucketTransactionsRequest request) {
    // Validate the request
    if (request.getBucketId() == null) {
      return ResponseEntity.badRequest()
          .body(new ApiResponse<>(false, "Bucket ID is required", null));
    }

    // Process the transactions using the enriched bucket transaction service
    List<Map<String, Object>> processedTransactions =
        enrichedBucketTransactionService.processEnrichedBucketTransactions(
            request.getBucketId(), request.getTransactions());

    return ResponseEntity.ok(
        new ApiResponse<>(
            true,
            "Processed transactions with enrichment for bucket ID " + request.getBucketId(),
            processedTransactions));
  }
}
