package com.progize.tms.controllers.transaction.req;

import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * Request model for processing transactions with enrichment from lookup buckets. This allows for
 * dynamic data structures based on the bucket's variables and automatically enriches the data with
 * related lookup buckets.
 */
@Data
public class ProcessEnrichedBucketTransactionsRequest {
  /** The ID of the rule bucket that defines the structure of the transaction data */
  private Long bucketId;

  /**
   * Optional list of transaction data maps. If not provided, data will be fetched from the bucket's
   * configured data source.
   *
   * <p>Each map contains variable names as keys and their values. The variable names must match
   * those defined in the specified bucket.
   */
  private List<Map<String, Object>> transactions;
}
