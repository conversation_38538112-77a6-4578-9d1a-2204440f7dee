package com.progize.tms.repository.entity;

import com.progize.tms.repository.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "data_bucket_variables")
@Getter
@Setter
public class DataBucketVariable extends BaseEntity {
  @Column(name = "name", nullable = false)
  private String name;

  @Column(name = "code", nullable = false)
  private String code;

  @Column(name = "description")
  private String description;

  @Column(name = "is_unique", nullable = false)
  private Boolean isUnique = false; // Default to false for backward compatibility

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_bucket_id", nullable = false)
  private DataBucket dataBucket;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_type_id", nullable = false)
  private DataType dataType;
}
