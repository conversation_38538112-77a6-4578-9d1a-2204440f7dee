package com.progize.tms.services.models;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a variable that can be used in rule conditions. These are typically transaction fields
 * that rules can reference.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleVariable {
  private String id;
  private String name;
  private String description;
  private String type; // string, number, boolean, date, etc.
  private String category; // transaction, customer, account, etc.
  private boolean aggregatable;
  private List<String> supportedOperators;
  private String columnName; // Actual database column name
  private String tableName; // Database table name (defaults to "transactions" if not specified)
  private Map<String, Object> metadata; // Additional metadata for the variable
}
