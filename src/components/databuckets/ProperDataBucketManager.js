import React, { useState, useEffect } from 'react';
import { Box, CircularProgress, Alert, TextField } from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { dataBucketService } from '../../services/api';
import DataBucketsPanel from '../rulevariables/DataBucketsPanel';
import ProperBucketCreator from './ProperBucketCreator';
import ProperBucketEditor from './ProperBucketEditor';
import ProperVariableManager from './ProperVariableManager';

const ProperDataBucketManager = () => {
    const [buckets, setBuckets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');

    // View states
    const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'variables'
    const [selectedBucket, setSelectedBucket] = useState(null);
    const [createDrawerOpen, setCreateDrawerOpen] = useState(false);

    useEffect(() => {
        fetchBuckets();
    }, []);

    const fetchBuckets = async () => {
        try {
            setLoading(true);
            const data = await dataBucketService.getAllBuckets();
            setBuckets(Array.isArray(data) ? data : []);
            setError(null);
        } catch (err) {
            setError('Failed to load data buckets. Please try again later.');
            console.error('Error fetching buckets:', err);
            setBuckets([]);
        } finally {
            setLoading(false);
        }
    };

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    // Filter buckets based on search query
    const filteredBuckets = searchQuery.trim()
        ? buckets.filter(bucket =>
            bucket.name.toLowerCase().includes(searchQuery.toLowerCase().trim())
          )
        : buckets;

    const handleCreateBucket = () => {
        setCurrentView('create');
    };

    const handleEditBucket = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('edit');
    };

    const handleManageVariables = (bucket) => {
        setSelectedBucket(bucket);
        setCurrentView('variables');
    };

    const handleBackToList = () => {
        setCurrentView('list');
        setSelectedBucket(null);
        fetchBuckets(); // Refresh the list
    };

    const handleBucketCreated = (newBucket) => {
        setBuckets(prev => [...prev, newBucket]);
        setCurrentView('list');
    };

    const handleBucketUpdated = (updatedBucket) => {
        setBuckets(prev => prev.map(bucket =>
            bucket.id === updatedBucket.id ? updatedBucket : bucket
        ));
        setCurrentView('list');
    };

    // Custom DataBucketsPanel with search and wizard integration
    const CustomDataBucketsPanel = () => {
        return (
            <Box>
                {/* Search Bar */}
                <Box sx={{ mb: 3, width: { xs: '100%', sm: 400 } }}>
                    <TextField
                        fullWidth
                        size="small"
                        placeholder="Search buckets by name..."
                        value={searchQuery}
                        onChange={handleSearchChange}
                        InputProps={{
                            startAdornment: (
                                <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                                    <SearchIcon sx={{ color: 'text.secondary', fontSize: '1.2rem' }} />
                                </Box>
                            ),
                        }}
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                fontSize: '0.875rem',
                                '& fieldset': {
                                    borderColor: 'grey.300'
                                }
                            }
                        }}
                    />
                </Box>

                {/* DataBucketsPanel with custom create handler */}
                <DataBucketsPanel
                    initialBuckets={filteredBuckets}
                    createDrawerOpen={createDrawerOpen}
                    setCreateDrawerOpen={(open) => {
                        if (open) {
                            handleCreateBucket(); // Use our wizard instead
                        } else {
                            setCreateDrawerOpen(false);
                        }
                    }}
                    onEditBucket={handleEditBucket}
                    onManageVariables={handleManageVariables}
                />
            </Box>
        );
    };

    const renderContent = () => {
        switch (currentView) {
            case 'create':
                return (
                    <ProperBucketCreator
                        onCancel={handleBackToList}
                        onBucketCreated={handleBucketCreated}
                    />
                );

            case 'edit':
                return (
                    <ProperBucketEditor
                        bucket={selectedBucket}
                        onCancel={handleBackToList}
                        onBucketUpdated={handleBucketUpdated}
                    />
                );

            case 'variables':
                return (
                    <ProperVariableManager
                        bucket={selectedBucket}
                        onBack={handleBackToList}
                    />
                );

            default:
                return <CustomDataBucketsPanel />;
        }
    };

    // Match the exact structure of RuleVariableRegistryPage
    return (
        <Box sx={{ width: '100%', height: '100%', backgroundColor: 'transparent' }}>
            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
            )}

            {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                    <CircularProgress />
                </Box>
            ) : (
                renderContent()
            )}
        </Box>
    );
};

export default ProperDataBucketManager;
