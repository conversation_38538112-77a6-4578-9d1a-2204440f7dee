package com.progize.tms.controllers.permissions;

import com.progize.tms.controllers.permissions.req.CreatePermissionRequest;
import com.progize.tms.controllers.permissions.req.UpdatePermissionRequest;
import com.progize.tms.controllers.permissions.res.PermissionResponse;
import com.progize.tms.repository.entity.Permission;
import com.progize.tms.services.PermissionService;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/permissions")
@CrossOrigin(origins = "*")
public class PermissionController {

  private final PermissionService permissionService;
  private final PermissionTransformer permissionTransformer;

  @Autowired
  public PermissionController(
      PermissionService permissionService, PermissionTransformer permissionTransformer) {
    this.permissionService = permissionService;
    this.permissionTransformer = permissionTransformer;
  }

  @GetMapping
  public ResponseEntity<List<PermissionResponse>> getAllPermissions() {
    List<Permission> permissions = permissionService.findAll();
    return ResponseEntity.ok(permissionTransformer.toPermissionResponseList(permissions));
  }

  @GetMapping("/{id}")
  public ResponseEntity<PermissionResponse> getPermissionById(@PathVariable Long id) {
    return permissionService
        .findById(id)
        .map(permissionTransformer::toPermissionResponse)
        .map(ResponseEntity::ok)
        .orElse(ResponseEntity.notFound().build());
  }

  @PostMapping
  public ResponseEntity<PermissionResponse> createPermission(
      @Valid @RequestBody CreatePermissionRequest request) {
    if (permissionService.existsByName(request.getName())) {
      return ResponseEntity.badRequest().build();
    }

    Permission permission = permissionTransformer.toPermission(request);
    Permission savedPermission = permissionService.save(permission);

    return ResponseEntity.status(HttpStatus.CREATED)
        .body(permissionTransformer.toPermissionResponse(savedPermission));
  }

  @PutMapping("/{id}")
  public ResponseEntity<PermissionResponse> updatePermission(
      @PathVariable Long id, @Valid @RequestBody UpdatePermissionRequest request) {
    try {
      Permission existingPermission =
          permissionService
              .findById(id)
              .orElseThrow(
                  () -> new EntityNotFoundException("Permission not found with id: " + id));

      // Check if the new name is already taken by another permission
      if (!existingPermission.getName().equals(request.getName())
          && permissionService.existsByName(request.getName())) {
        return ResponseEntity.badRequest().build();
      }

      permissionTransformer.updatePermissionFromRequest(existingPermission, request);
      Permission updatedPermission = permissionService.save(existingPermission);

      return ResponseEntity.ok(permissionTransformer.toPermissionResponse(updatedPermission));
    } catch (EntityNotFoundException e) {
      return ResponseEntity.notFound().build();
    }
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deletePermission(@PathVariable Long id) {
    if (!permissionService.existsById(id)) {
      return ResponseEntity.notFound().build();
    }

    permissionService.deleteById(id);
    return ResponseEntity.noContent().build();
  }
}
