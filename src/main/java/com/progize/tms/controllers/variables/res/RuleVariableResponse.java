package com.progize.tms.controllers.variables.res;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleVariableResponse {
  private String id;
  private String name;
  private String description;
  private String type;
  private String category;
  private boolean aggregatable;
  private List<String> supportedOperators;
  private String dataTypeCode; // Added for backward compatibility with UI
}
