package com.progize.tms.repository;

import com.progize.tms.repository.entity.Rule;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RuleRepository extends JpaRepository<Rule, String> {
  List<Rule> findByActiveOrderByPriorityAsc(boolean active);

  /**
   * Find active rules for a specific data bucket ordered by priority.
   *
   * @param active Whether the rules should be active
   * @param dataBucketId The ID of the data bucket
   * @return List of matching rules
   */
  List<Rule> findByActiveAndDataBucket_IdOrderByPriorityAsc(boolean active, Long dataBucketId);
}
