package com.progize.tms.services.impl;

import com.progize.tms.services.AggregationProvider;
import com.progize.tms.services.DynamicAggregationService;
import com.progize.tms.services.models.AggregationRequirement;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Implementation of AggregationProvider that uses dynamic data sources to retrieve aggregation
 * data. This provider uses the configured data source for each bucket to execute aggregation
 * queries.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DynamicAggregationProvider implements AggregationProvider {

  private final DynamicAggregationService dynamicAggregationService;

  @Override
  public Map<String, Object> getAggregations(
      String customerId, Set<AggregationRequirement> requirements) {
    log.warn("Dynamic aggregation provider requires a bucket ID. Returning empty results.");
    return Collections.emptyMap();
  }

  @Override
  public Map<String, Object> getAggregations(
      String customerId, Set<AggregationRequirement> requirements, Long bucketId) {

    if (bucketId == null) {
      log.warn("No bucket ID provided for dynamic aggregation. Returning empty results.");
      return Collections.emptyMap();
    }

    log.debug(
        "Calculating dynamic aggregations for customer: {} with {} requirements using bucket {}",
        customerId,
        requirements.size(),
        bucketId);

    // Convert customerId to a singleton set
    Set<String> entityIds = Collections.singleton(customerId);

    // Calculate aggregations using the dynamic service
    Map<String, Number> aggregationResults =
        dynamicAggregationService.calculateAggregations(requirements, entityIds, bucketId);

    // Convert to Object map for the interface
    Map<String, Object> results = new HashMap<>();
    aggregationResults.forEach(results::put);

    return results;
  }
}
