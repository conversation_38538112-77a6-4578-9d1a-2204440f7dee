import React, { useState, useEffect } from 'react';
import {
  Drawer,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  CircularProgress,
  Snackbar,
  Alert,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Grid,
  Tooltip,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import { styled } from '@mui/material/styles';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import SelectAllIcon from '@mui/icons-material/DoneAll';
import ClearAllIcon from '@mui/icons-material/LayersClear';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import InfoIcon from '@mui/icons-material/Info';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { ruleVariableRegistryService } from '../../services/api';

// Styled components
const DrawerHeader = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  padding: theme.spacing(2, 3),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between'
}));

const DrawerContent = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '100%',
  display: 'flex',
  flexDirection: 'column'
}));

const StyledSearchField = styled(TextField)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  '& .MuiInputBase-root': {
    borderRadius: theme.shape.borderRadius * 2,
  }
}));

const ColumnTypeChip = styled(Chip)(({ theme, dataType }) => ({
  backgroundColor: getTypeColor(dataType, theme),
  color: theme.palette.getContrastText(getTypeColor(dataType, theme)),
  fontWeight: 500,
  fontSize: '0.75rem'
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  backgroundColor: status === 'registered' 
    ? alpha(theme.palette.success.main, 0.1) 
    : alpha(theme.palette.grey[500], 0.1),
  color: status === 'registered' ? theme.palette.success.main : theme.palette.text.secondary,
  fontWeight: 500,
  fontSize: '0.75rem',
  '& .MuiChip-icon': {
    color: 'inherit'
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  textTransform: 'none',
  borderRadius: theme.shape.borderRadius * 1.5,
  fontWeight: 500
}));

const TableHeader = styled(TableCell)(({ theme }) => ({
  backgroundColor: theme.palette.mode === 'dark' 
    ? theme.palette.grey[800] 
    : theme.palette.grey[100],
  color: theme.palette.text.secondary,
  fontWeight: 600,
  whiteSpace: 'nowrap'
}));

const StyledTableRow = styled(TableRow)(({ theme, selected, registered }) => ({
  backgroundColor: registered 
    ? alpha(theme.palette.success.main, 0.05) 
    : selected 
      ? alpha(theme.palette.primary.main, 0.05) 
      : 'inherit',
  '&:hover': {
    backgroundColor: registered 
      ? alpha(theme.palette.success.main, 0.08) 
      : alpha(theme.palette.primary.main, 0.08)
  },
  cursor: registered ? 'default' : 'pointer'
}));

const DrawerFooter = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  borderTop: `1px solid ${theme.palette.divider}`,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginTop: 'auto',
  position: 'sticky',
  bottom: 0,
  backgroundColor: theme.palette.background.paper,
  zIndex: 10,
  boxShadow: '0 -4px 8px -4px rgba(0, 0, 0, 0.05)'
}));

const SelectionSummary = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1, 2),
  backgroundColor: theme.palette.mode === 'dark' 
    ? alpha(theme.palette.primary.dark, 0.2) 
    : alpha(theme.palette.primary.light, 0.1),
  borderRadius: theme.shape.borderRadius,
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between'
}));

const ColumnCard = styled(Paper)(({ theme, selected, registered }) => ({
  padding: theme.spacing(1.5),
  marginBottom: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  boxShadow: 'none',
  border: `1px solid ${
    registered 
      ? alpha(theme.palette.success.main, 0.3) 
      : selected
        ? alpha(theme.palette.primary.main, 0.3)
        : theme.palette.divider
  }`,
  backgroundColor: registered 
    ? alpha(theme.palette.success.main, 0.05) 
    : selected 
      ? alpha(theme.palette.primary.main, 0.05) 
      : 'inherit',
  transition: 'all 0.2s ease',
  cursor: registered ? 'default' : 'pointer',
  '&:hover': {
    backgroundColor: registered 
      ? alpha(theme.palette.success.main, 0.08) 
      : alpha(theme.palette.primary.main, 0.08),
    transform: registered ? 'none' : 'translateY(-2px)',
    boxShadow: registered ? 'none' : theme.shadows[2]
  },
  display: 'flex',
  alignItems: 'center'
}));

// Helper function to get color based on data type
function getTypeColor(type, theme) {
  switch (type?.toLowerCase()) {
    case 'string':
      return alpha(theme.palette.info.main, 0.8);
    case 'integer':
    case 'float':
    case 'double':
    case 'decimal':
    case 'bigdecimal':
    case 'number':
      return alpha(theme.palette.success.main, 0.8);
    case 'boolean':
      return alpha(theme.palette.warning.main, 0.8);
    case 'date':
    case 'timestamp':
    case 'datetime':
      return alpha(theme.palette.secondary.main, 0.8);
    default:
      return alpha(theme.palette.grey[500], 0.8);
  }
}

const ColumnRegistrationDrawer = ({
  open,
  onClose,
  table,
  columns = [],
  existingVariables = [],
  loading,
  onRegistrationComplete
}) => {
  const theme = useTheme();
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [registering, setRegistering] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [searchTerm, setSearchTerm] = useState('');
  const drawerWidth = 600;

  // Reset selected columns when the drawer opens or table changes
  useEffect(() => {
    if (open && table) {
      setSelectedColumns([]);
      setSearchTerm('');
    }
  }, [open, table]);

  // Map existing variables by column name for quick lookups
  const existingVariablesByColumn = existingVariables.reduce((acc, variable) => {
    acc[variable.columnName] = variable;
    return acc;
  }, {});

  // Filter columns based on search term
  const filteredColumns = columns.filter(column => 
    column.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    column.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    column.mappedDataType?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle column selection
  const handleColumnSelect = (column) => {
    if (isColumnRegistered(column.name)) {
      return; // Don't allow selecting already registered columns
    }
    
    setSelectedColumns(prev => {
      const exists = prev.some(c => c.name === column.name);
      if (exists) {
        return prev.filter(c => c.name !== column.name);
      } else {
        return [...prev, column];
      }
    });
  };

  // Check if a column is already registered as a variable
  const isColumnRegistered = (columnName) => {
    return !!existingVariablesByColumn[columnName];
  };

  // Register selected columns as variables
  const handleRegisterColumns = async () => {
    if (!table || selectedColumns.length === 0) {
      return;
    }
    
    setRegistering(true);
    try {
      await ruleVariableRegistryService.registerColumns(table.id, selectedColumns);
      
      setNotification({
        open: true,
        message: `Successfully registered ${selectedColumns.length} column${selectedColumns.length > 1 ? 's' : ''} as ${selectedColumns.length > 1 ? 'variables' : 'a variable'}`,
        severity: 'success'
      });
      
      if (onRegistrationComplete) {
        onRegistrationComplete();
      }
    } catch (error) {
      console.error('Error registering columns:', error);
      setNotification({
        open: true,
        message: 'Failed to register columns. Please try again.',
        severity: 'error'
      });
    } finally {
      setRegistering(false);
    }
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification(prev => ({
      ...prev,
      open: false
    }));
  };

  // Select all unregistered columns
  const handleSelectAll = () => {
    const unregisteredColumns = columns.filter(column => !isColumnRegistered(column.name));
    setSelectedColumns(unregisteredColumns);
  };

  // Clear all selected columns
  const handleClearAll = () => {
    setSelectedColumns([]);
  };

  // Handle row click for selection
  const handleRowClick = (column) => {
    if (!isColumnRegistered(column.name)) {
      handleColumnSelect(column);
    }
  };

  // Count variables stats
  const totalColumns = columns.length;
  const registeredColumns = Object.keys(existingVariablesByColumn).length;
  const availableColumns = totalColumns - registeredColumns;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          boxShadow: '0 0 20px rgba(0,0,0,0.1)',
        },
        '& .MuiBackdrop-root': {
          opacity: '0 !important',
          transition: 'opacity 0.3s ease-in-out !important',
        },
        '& .MuiBackdrop-root.MuiModal-backdrop': {
          opacity: '0.5 !important',
        },
      }}
      transitionDuration={{
        enter: 400,
        exit: 300
      }}
      SlideProps={{
        easing: {
          enter: 'cubic-bezier(0.2, 0.9, 0.3, 1)',
          exit: 'cubic-bezier(0.7, 0, 0.8, 0.1)'
        }
      }}
    >
      <DrawerHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton 
            color="inherit" 
            onClick={onClose} 
            aria-label="close"
            size="small"
            sx={{ mr: 1 }}
          >
            <ChevronRightIcon />
          </IconButton>
          <Box>
            <Typography variant="h6" component="div">
              Register Columns as Variables
            </Typography>
            {table && (
              <Typography variant="subtitle2" sx={{ opacity: 0.8, fontWeight: 400, mt: 0.5 }}>
                {table.schemaName}.{table.tableName}
              </Typography>
            )}
          </Box>
        </Box>
      </DrawerHeader>

      <DrawerContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12}>
                <StyledSearchField
                  placeholder="Search columns by name or type..."
                  fullWidth
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  variant="outlined"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: searchTerm && (
                      <InputAdornment position="end">
                        <IconButton
                          size="small"
                          aria-label="clear search"
                          onClick={() => setSearchTerm('')}
                          edge="end"
                        >
                          <CloseIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>
              <Grid item xs={12} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Tooltip title="Available for registration">
                  <Chip 
                    label={`${availableColumns} Available`} 
                    size="small" 
                    color="default"
                    variant="outlined"
                    icon={<FiberManualRecordIcon fontSize="small" />}
                  />
                </Tooltip>
                <Tooltip title="Already registered as variables">
                  <Chip 
                    label={`${registeredColumns} Registered`} 
                    size="small" 
                    color="success"
                    variant="outlined"
                    icon={<FiberManualRecordIcon fontSize="small" />}
                  />
                </Tooltip>
                <Box sx={{ flexGrow: 1 }} />
                <ActionButton 
                  variant="outlined" 
                  size="small" 
                  onClick={handleSelectAll}
                  startIcon={<SelectAllIcon />}
                  color="primary"
                  disabled={availableColumns === 0}
                >
                  Select All
                </ActionButton>
                <ActionButton 
                  variant="outlined" 
                  size="small" 
                  onClick={handleClearAll}
                  startIcon={<ClearAllIcon />}
                  color="secondary"
                  disabled={selectedColumns.length === 0}
                >
                  Clear
                </ActionButton>
              </Grid>
            </Grid>

            {selectedColumns.length > 0 && (
              <SelectionSummary>
                <Typography variant="body2" fontWeight={500}>
                  {selectedColumns.length} column{selectedColumns.length > 1 ? 's' : ''} selected
                </Typography>
              </SelectionSummary>
            )}

            <Box sx={{ flexGrow: 1, overflow: 'auto', mb: 2 }}>
              {filteredColumns.length === 0 ? (
                <Box sx={{ py: 4, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {searchTerm 
                      ? 'No columns match your search criteria' 
                      : 'No columns available for this table'}
                  </Typography>
                </Box>
              ) : (
                filteredColumns.map((column) => {
                  const isRegistered = isColumnRegistered(column.name);
                  const isSelected = selectedColumns.some(c => c.name === column.name);
                  
                  return (
                    <ColumnCard
                      key={column.name}
                      selected={isSelected}
                      registered={isRegistered}
                      onClick={() => handleRowClick(column)}
                    >
                      <Checkbox
                        checked={isSelected}
                        disabled={isRegistered}
                        onChange={() => handleColumnSelect(column)}
                        onClick={(e) => e.stopPropagation()}
                        sx={{ mr: 1 }}
                      />
                      <Box sx={{ flexGrow: 1 }}>
                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center',
                          mb: 0.5
                        }}>
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              fontWeight: isRegistered || isSelected ? 600 : 400,
                              fontFamily: 'monospace',
                              color: isRegistered 
                                ? theme.palette.success.main 
                                : isSelected 
                                  ? theme.palette.primary.main 
                                  : 'inherit'
                            }}
                          >
                            {column.name}
                          </Typography>
                          {isRegistered && (
                            <Tooltip title={`Used by variable: ${existingVariablesByColumn[column.name].displayName}`}>
                              <IconButton size="small" sx={{ ml: 0.5 }}>
                                <InfoIcon fontSize="small" color="success" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                        <Grid container spacing={1} alignItems="center">
                          <Grid item xs={5}>
                            <Typography 
                              variant="caption" 
                              sx={{ 
                                fontFamily: 'monospace',
                                opacity: 0.7,
                                display: 'inline-block'
                              }}
                            >
                              {column.type}
                            </Typography>
                          </Grid>
                          <Grid item xs={3}>
                            <ColumnTypeChip 
                              label={column.mappedDataType} 
                              size="small"
                              dataType={column.mappedDataType}
                            />
                          </Grid>
                          <Grid item xs={4}>
                            <StatusChip 
                              icon={<FiberManualRecordIcon fontSize="small" />}
                              label={isRegistered ? "Registered" : "Available"} 
                              size="small"
                              status={isRegistered ? "registered" : "available"}
                            />
                          </Grid>
                        </Grid>
                      </Box>
                    </ColumnCard>
                  );
                })
              )}
            </Box>
          </>
        )}
      </DrawerContent>

      <DrawerFooter>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          disabled={selectedColumns.length === 0 || registering}
          onClick={handleRegisterColumns}
          startIcon={registering ? <CircularProgress size={20} /> : null}
        >
          {registering ? 'Registering...' : `Register ${selectedColumns.length > 0 ? `(${selectedColumns.length})` : ''}`}
        </Button>
      </DrawerFooter>

      {/* Notifications */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Drawer>
  );
};

export default ColumnRegistrationDrawer;
