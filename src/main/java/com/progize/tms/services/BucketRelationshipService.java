package com.progize.tms.services;

import com.progize.tms.repository.BucketRelationshipRepository;
import com.progize.tms.repository.DataBucketRepository;
import com.progize.tms.repository.DataBucketVariableRepository;
import com.progize.tms.repository.entity.BucketRelationshipEntity;
import com.progize.tms.repository.entity.DataBucketEntity;
import com.progize.tms.repository.entity.DataBucketVariableEntity;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class BucketRelationshipService {
  private final BucketRelationshipRepository relationshipRepo;
  private final DataBucketRepository bucketRepo;
  private final DataBucketVariableRepository variableRepo;

  @Transactional
  public BucketRelationshipEntity createRelationship(
      Long ruleBucketId,
      Long lookupBucketId,
      List<BucketRelationshipEntity.JoinCondition> joinConditions) {
    // Validate bucket types
    DataBucketEntity ruleBucket =
        bucketRepo
            .findById(ruleBucketId)
            .orElseThrow(
                () -> new IllegalArgumentException("Rule bucket not found: " + ruleBucketId));

    DataBucketEntity lookupBucket =
        bucketRepo
            .findById(lookupBucketId)
            .orElseThrow(
                () -> new IllegalArgumentException("Lookup bucket not found: " + lookupBucketId));

    if (!"RULE_BUCKET".equals(ruleBucket.getBucketType().toString())) {
      throw new IllegalArgumentException("Source bucket must be a rule bucket");
    }

    if (!"LOOKUP".equals(lookupBucket.getBucketType().toString())) {
      throw new IllegalArgumentException("Target bucket must be a lookup bucket");
    }

    // Check if relationship already exists
    Optional<BucketRelationshipEntity> existingRelationship =
        relationshipRepo.findByRuleBucketIdAndLookupBucketId(ruleBucketId, lookupBucketId);

    if (existingRelationship.isPresent()) {
      throw new IllegalArgumentException("Relationship already exists between these buckets");
    }

    // Validate variables exist and belong to the correct buckets
    for (BucketRelationshipEntity.JoinCondition condition : joinConditions) {
      DataBucketVariableEntity ruleVariable =
          variableRepo
              .findById(condition.getRuleBucketVariableId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Rule variable not found: " + condition.getRuleBucketVariableId()));

      DataBucketVariableEntity lookupVariable =
          variableRepo
              .findById(condition.getLookupBucketVariableId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Lookup variable not found: " + condition.getLookupBucketVariableId()));

      // Validate variables belong to the correct buckets
      if (!ruleVariable.getDataBucket().getId().equals(ruleBucketId)) {
        throw new IllegalArgumentException("Rule variable does not belong to the rule bucket");
      }

      if (!lookupVariable.getDataBucket().getId().equals(lookupBucketId)) {
        throw new IllegalArgumentException("Lookup variable does not belong to the lookup bucket");
      }
    }

    // Create new relationship
    BucketRelationshipEntity relationship = new BucketRelationshipEntity();
    relationship.setRuleBucket(ruleBucket);
    relationship.setLookupBucket(lookupBucket);
    relationship.setJoinConditions(joinConditions);

    return relationshipRepo.save(relationship);
  }

  public List<BucketRelationshipEntity> getRelationshipsForRuleBucket(Long ruleBucketId) {
    return relationshipRepo.findByRuleBucketId(ruleBucketId);
  }

  @Transactional
  public void deleteRelationship(Long relationshipId) {
    relationshipRepo.deleteById(relationshipId);
  }
}
