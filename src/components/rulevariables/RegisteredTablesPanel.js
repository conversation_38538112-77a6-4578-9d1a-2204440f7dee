import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Snackbar,
  Alert,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Grid,
  Divider,
  Paper,
  Switch,
  Tooltip,
  useTheme,
  alpha,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Badge,
  Collapse,
  Avatar
} from '@mui/material';
import { styled } from '@mui/material/styles';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import StorageIcon from '@mui/icons-material/Storage';
import FunctionsIcon from '@mui/icons-material/Functions';
import DataObjectIcon from '@mui/icons-material/DataObject';
import { ruleVariableRegistryService } from '../../services/api';
import ColumnRegistrationDrawer from './ColumnRegistrationDrawer';
import { TransitionGroup } from 'react-transition-group';

// Styled components
const SourceTableCard = styled(Card, {
  shouldForwardProp: (prop) => prop !== 'registered'
})(({ theme, registered }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  boxShadow: theme.shadows[2],
  border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
  transition: 'all 0.2s ease-in-out',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 8px 16px -4px rgba(0, 0, 0, 0.1)',
  }
}));

const TableHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  backgroundColor: theme.palette.mode === 'dark' ? 
    alpha(theme.palette.primary.main, 0.1) : 
    alpha(theme.palette.grey[100], 0.8),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderBottom: `1px solid ${theme.palette.divider}`
}));

const VariableChip = styled(Chip, {
  shouldForwardProp: (prop) => prop !== 'registered' && prop !== 'aggregatable'
})(({ theme, registered, aggregatable }) => ({
  margin: theme.spacing(0.5),
  backgroundColor: registered
    ? aggregatable
      ? alpha(theme.palette.success.main, 0.1)
      : alpha(theme.palette.info.main, 0.1)
    : alpha(theme.palette.grey[500], 0.1),
  borderColor: registered
    ? aggregatable
      ? theme.palette.success.main
      : theme.palette.info.main
    : theme.palette.grey[500],
  '& .MuiChip-icon': {
    color: registered
      ? aggregatable
        ? theme.palette.success.main
        : theme.palette.info.main
      : theme.palette.grey[600]
  },
  '&:hover': {
    backgroundColor: registered
      ? aggregatable
        ? alpha(theme.palette.success.main, 0.2)
        : alpha(theme.palette.info.main, 0.2)
      : alpha(theme.palette.grey[500], 0.2),
  }
}));

const RegisteredTablesPanel = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [sourceTables, setSourceTables] = useState([]);
  const [selectedTable, setSelectedTable] = useState(null);
  const [expandedTables, setExpandedTables] = useState({});
  const [registrationDrawerOpen, setRegistrationDrawerOpen] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [actionsMenuAnchor, setActionsMenuAnchor] = useState({ el: null, tableId: null });
  const [columns, setColumns] = useState([]);
  const [columnsLoading, setColumnsLoading] = useState(false);
  
  // Mock data for development when backend is not available
  const MOCK_MODE = false; // Now connecting to real API

  const MOCK_SOURCE_TABLES = [
    { 
      id: '1', 
      schemaName: 'public', 
      tableName: 'customers', 
      displayName: 'Customers', 
      description: 'Main customer information table',
      variables: [
        {
          id: '1',
          code: 'customer_id',
          displayName: 'Customer ID',
          description: 'Unique customer identifier',
          columnName: 'id',
          type: 'string',
          aggregatable: false,
          sourceTable: { id: '1', displayName: 'Customers' }
        },
        {
          id: '2',
          code: 'customer_risk_score',
          displayName: 'Risk Score',
          description: 'Customer risk assessment score',
          columnName: 'risk_score',
          type: 'integer',
          aggregatable: true,
          sourceTable: { id: '1', displayName: 'Customers' }
        }
      ]
    },
    { 
      id: '2', 
      schemaName: 'public', 
      tableName: 'transactions', 
      displayName: 'Transactions', 
      description: 'Financial transaction records',
      variables: [
        {
          id: '3',
          code: 'transaction_amount',
          displayName: 'Transaction Amount',
          description: 'Amount of the transaction',
          columnName: 'amount',
          type: 'decimal',
          aggregatable: true,
          sourceTable: { id: '2', displayName: 'Transactions' }
        }
      ]
    }
  ];

  // Load source tables
  const loadSourceTables = async () => {
    setLoading(true);
    try {
      if (MOCK_MODE) {
        // Use mock data when backend is not available
        setTimeout(() => {
          setSourceTables(MOCK_SOURCE_TABLES);
          setLoading(false);
        }, 500);
        return;
      }
      
      const tables = await ruleVariableRegistryService.getAllSourceTables();
      
      // Load variables for each table
      const tablesWithVariables = await Promise.all(
        tables.map(async (table) => {
          try {
            const variables = await ruleVariableRegistryService.getVariablesByTable(table.tableName);
            return { ...table, variables };
          } catch (error) {
            console.error(`Error loading variables for table ${table.id}:`, error);
            return { ...table, variables: [] };
          }
        })
      );
      
      setSourceTables(tablesWithVariables);
    } catch (error) {
      console.error('Error loading source tables:', error);
      showNotification('Failed to load source tables. Using mock data.', 'warning');
      setSourceTables(MOCK_SOURCE_TABLES);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadSourceTables();
  }, []);

  // Toggle variable aggregatable flag
  const handleToggleAggregatable = async (variable) => {
    try {
      if (MOCK_MODE) {
        // In mock mode, just update the local state
        setSourceTables((prevTables) =>
          prevTables.map((table) => ({
            ...table,
            variables: table.variables.map((v) =>
              v.id === variable.id ? { ...v, aggregatable: !v.aggregatable } : v
            ),
          }))
        );
        
        showNotification(`Variable ${variable.aggregatable ? 'is no longer' : 'is now'} aggregatable`, 'success');
        return;
      }
      
      await ruleVariableRegistryService.updateAggregatable(variable.id, !variable.aggregatable);
      
      // Update local state
      setSourceTables((prevTables) =>
        prevTables.map((table) => ({
          ...table,
          variables: table.variables.map((v) =>
            v.id === variable.id ? { ...v, aggregatable: !v.aggregatable } : v
          ),
        }))
      );
      
      showNotification(`Variable ${variable.aggregatable ? 'is no longer' : 'is now'} aggregatable`, 'success');
    } catch (error) {
      console.error('Error updating variable:', error);
      showNotification('Failed to update variable', 'error');
    }
  };

  // Delete variable
  const handleDeleteVariable = async (variable) => {
    if (!window.confirm(`Are you sure you want to delete the variable "${variable.name}"?`)) {
      return;
    }
    
    try {
      if (MOCK_MODE) {
        // In mock mode, just update the local state
        setSourceTables((prevTables) =>
          prevTables.map((table) => ({
            ...table,
            variables: table.variables.filter((v) => v.id !== variable.id),
          }))
        );
        
        showNotification('Variable deleted successfully', 'success');
        return;
      }
      
      await ruleVariableRegistryService.deleteVariable(variable.id);
      
      // Update local state
      setSourceTables((prevTables) =>
        prevTables.map((table) => ({
          ...table,
          variables: table.variables.filter((v) => v.id !== variable.id),
        }))
      );
      
      showNotification('Variable deleted successfully', 'success');
    } catch (error) {
      console.error('Error deleting variable:', error);
      showNotification('Failed to delete variable', 'error');
    }
  };

  // Open registration drawer and load columns
  const handleOpenRegistrationDrawer = async (table) => {
    setSelectedTable(table);
    setRegistrationDrawerOpen(true);
    setColumnsLoading(true);
    try {
      const cols = await ruleVariableRegistryService.getTableColumns(
        table.schemaName,
        table.tableName
      );
      setColumns(cols);
    } catch (error) {
      console.error('Error loading columns:', error);
      showNotification('Failed to load columns', 'error');
      setColumns([]);
    }
    setColumnsLoading(false);
  };

  // Handle variable registration completion
  const handleRegistrationComplete = async (registeredCount) => {
    setRegistrationDrawerOpen(false);
    
    if (registeredCount > 0) {
      showNotification(`${registeredCount} variable(s) registered successfully`, 'success');
      await loadSourceTables();
    }
  };

  // Show notification
  const showNotification = (message, severity) => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification((prev) => ({
      ...prev,
      open: false,
    }));
  };

  // Toggle expanded state for table
  const toggleTableExpanded = (tableId) => {
    setExpandedTables(prev => ({
      ...prev,
      [tableId]: !prev[tableId]
    }));
  };

  // Open actions menu
  const handleOpenActionsMenu = (event, tableId) => {
    event.stopPropagation();
    setActionsMenuAnchor({ el: event.currentTarget, tableId });
  };

  // Close actions menu
  const handleCloseActionsMenu = () => {
    setActionsMenuAnchor({ el: null, tableId: null });
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Registered Tables ({sourceTables.length})
        </Typography>
        <Button
          variant="outlined"
          onClick={() => loadSourceTables()}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Loading...' : 'Refresh'}
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : sourceTables.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center', borderRadius: theme.spacing(1) }}>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            No tables have been registered yet.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Use the "Discover Tables" tab to find and register tables from your database.
          </Typography>
        </Paper>
      ) : (
        <Grid container spacing={2}>
          {sourceTables.map((table, index) => {
            const isExpanded = expandedTables[table.id] || false;
            const variablesCount = table.variables?.length || 0;
            
            return (
              <Grid item xs={12} key={table.id}>
                <SourceTableCard>
                  <TableHeader 
                    onClick={() => toggleTableExpanded(table.id)}
                    sx={{ cursor: 'pointer' }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        sx={{
                          mr: 2,
                          bgcolor: 'secondary.main',
                          width: 40,
                          height: 40
                        }}
                      >
                        <StorageIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" component="div" sx={{ fontWeight: 500, lineHeight: 1.2 }}>
                          {table.displayName}
                          <Typography
                            component="span"
                            variant="caption"
                            sx={{
                              ml: 1,
                              color: 'text.secondary',
                              fontWeight: 'normal'
                            }}
                          >
                            {table.schemaName}.{table.tableName}
                          </Typography>
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <Badge
                            badgeContent={variablesCount}
                            color="secondary"
                            sx={{ mr: 2 }}
                          >
                            <Typography variant="body2" component="span" color="text.secondary">
                              Variables
                            </Typography>
                          </Badge>
                          {table.description && (
                            <Typography variant="body2" color="text.secondary" noWrap sx={{ maxWidth: 500 }}>
                              {table.description}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOpenRegistrationDrawer(table);
                        }}
                        sx={{ mr: 1 }}
                      >
                        Add Variables
                      </Button>
                      <IconButton
                        onClick={(e) => handleOpenActionsMenu(e, table.id)}
                        size="small"
                        sx={{ mr: 1 }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                      <IconButton
                        onClick={() => toggleTableExpanded(table.id)}
                        size="small"
                      >
                        {isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                      </IconButton>
                    </Box>
                  </TableHeader>
                  
                  <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                    <CardContent sx={{ pt: 2, pb: 3 }}>
                      {variablesCount === 0 ? (
                        <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                          <Typography variant="body2">
                            No variables registered for this table.
                          </Typography>
                          <Button
                            variant="contained"
                            onClick={() => handleOpenRegistrationDrawer(table)}
                            sx={{
                              mt: 1,
                              background: `linear-gradient(135deg, ${theme.palette.progize.gradientStart} 0%, ${theme.palette.progize.gradientEnd} 100%)`,
                              color: theme.palette.primary.contrastText,
                              '&:hover': {
                                background: `linear-gradient(135deg, ${theme.palette.progize.gradientHoverStart} 0%, ${theme.palette.progize.gradientHoverEnd} 100%)`,
                              },
                            }}
                          >
                            Register Variables
                          </Button>
                        </Box>
                      ) : (
                        <>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                              Registered Variables ({variablesCount})
                            </Typography>
                          </Box>
                          <Paper 
                            variant="outlined" 
                            sx={{ 
                              p: 2, 
                              display: 'flex', 
                              flexWrap: 'wrap',
                              borderRadius: theme.spacing(1)
                            }}
                          >
                            {table.variables.map((variable) => (
                              <Box key={variable.id} sx={{ display: 'inline-block', m: 0.5 }}>
                                <VariableChip
                                  registered={true}
                                  aggregatable={variable.aggregatable}
                                  variant="outlined"
                                  icon={variable.aggregatable ? <FunctionsIcon /> : <DataObjectIcon />}
                                  label={
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <Typography variant="body2" component="span" sx={{ mr: 0.5, fontWeight: 500 }}>
                                        {variable.name}
                                      </Typography>
                                      <Typography variant="caption" component="span" color="text.secondary">
                                        ({variable.type})
                                      </Typography>
                                    </Box>
                                  }
                                  onDelete={() => handleDeleteVariable(variable)}
                                  deleteIcon={
                                    <Tooltip title="Delete Variable">
                                      <DeleteIcon fontSize="small" />
                                    </Tooltip>
                                  }
                                  onClick={() => handleToggleAggregatable(variable)}
                                  sx={{ height: 'auto', '& .MuiChip-label': { py: 0.5 } }}
                                />
                                <Tooltip 
                                  title={variable.aggregatable ? "Click to disable aggregation" : "Click to enable aggregation"}
                                  placement="top"
                                  arrow
                                >
                                  <span></span>
                                </Tooltip>
                              </Box>
                            ))}
                          </Paper>
                        </>
                      )}
                    </CardContent>
                  </Collapse>
                </SourceTableCard>
              </Grid>
            );
          })}
        </Grid>
      )}

      {/* Column registration drawer */}
      <ColumnRegistrationDrawer
        open={registrationDrawerOpen}
        onClose={() => setRegistrationDrawerOpen(false)}
        table={selectedTable}
        columns={columns}
        existingVariables={selectedTable?.variables || []}
        loading={columnsLoading}
        onRegistrationComplete={handleRegistrationComplete}
      />
      
      {/* Actions menu */}
      <Menu
        anchorEl={actionsMenuAnchor.el}
        open={Boolean(actionsMenuAnchor.el)}
        onClose={handleCloseActionsMenu}
      >
        <MenuItem
          onClick={() => {
            const table = sourceTables.find(t => t.id === actionsMenuAnchor.tableId);
            if (table) {
              handleOpenRegistrationDrawer(table);
            }
            handleCloseActionsMenu();
          }}
        >
          <ListItemIcon>
            <AddIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Add Variables</ListItemText>
        </MenuItem>
        {/* Future actions like edit table or delete table could go here */}
      </Menu>

      {/* Notifications */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RegisteredTablesPanel;
