package com.progize.tms.config.web;

import com.progize.tms.filters.CorrelationIdFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

  @Bean
  public CorrelationIdFilter correlationIdFilter() {
    return new CorrelationIdFilter();
  }

  //  @Override
  //  public void addCorsMappings(CorsRegistry registry) {
  //    registry
  //        .addMapping("/**")
  //        .allowedOrigins(
  //            "http://localhost:3000",  // React default port
  //            "http://localhost:8080",  // Common backend port
  //            "http://localhost:8081",  // Current backend port
  //            "http://localhost:4200"   // Angular default port if needed
  //        )
  //        .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
  //        .allowedHeaders("*")
  //        .allowCredentials(true);
  //  }

  //  @Bean
  //  public CommonsRequestLoggingFilter requestLoggingFilter() {
  //    CommonsRequestLoggingFilter filter = new CommonsRequestLoggingFilter();
  //    filter.setIncludeQueryString(true);
  //    filter.setIncludePayload(true);
  //    filter.setMaxPayloadLength(10000);
  //    filter.setIncludeHeaders(true);
  //    filter.setAfterMessagePrefix("REQUEST DATA: ");
  //    return filter;
  //  }
}
