import React, { useState, useEffect } from 'react';
import {
    Box,
    <PERSON>pography,
    Button,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Grid,
    Card,
    CardContent,
    Alert,
    Breadcrumbs,
    Link,
    Divider,
    CircularProgress,
    Tabs,
    Tab,
    Paper,
    useTheme,
    alpha
} from '@mui/material';
import {
    ArrowBack as ArrowBackIcon,
    Save as SaveIcon,
    Storage as StorageIcon,
    TableChart as TableChartIcon,
    DataObject as DataObjectIcon,
    Info as InfoIcon,
    Settings as SettingsIcon,
    Preview as PreviewIcon
} from '@mui/icons-material';
import { dataBucketService, ruleVariableRegistryService } from '../../services/api';
import dataSourceService from '../../services/dataSourceService';
import ProperVariableSelector from './ProperVariableSelector';

const BUCKET_TYPES = [
    { value: 'RULE_BUCKET', label: 'Rule Bucket', description: 'For rule-specific variables and transaction data' },
    { value: 'LOOKUP', label: 'Lookup Bucket', description: 'For lookup variables from database tables to enrich rule buckets' }
];

const ProperBucketCreator = ({ dataSources, onCancel, onBucketCreated }) => {
    const theme = useTheme();
    const [activeTab, setActiveTab] = useState(0);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);

    // Form data
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        bucketType: 'RULE_BUCKET',
        dataSourceId: '',
        tableName: ''
    });

    // Table and variable data
    const [tables, setTables] = useState([]);
    const [loadingTables, setLoadingTables] = useState(false);
    const [selectedVariables, setSelectedVariables] = useState([]);

    const [formErrors, setFormErrors] = useState({});
    const [completedTabs, setCompletedTabs] = useState(new Set());

    const tabs = [
        {
            label: 'Basic Info',
            icon: <InfoIcon />,
            description: 'Bucket details'
        },
        {
            label: 'Data Source',
            icon: <StorageIcon />,
            description: 'Source & table'
        },
        {
            label: 'Variables',
            icon: <SettingsIcon />,
            description: 'Configure fields'
        },
        {
            label: 'Review',
            icon: <PreviewIcon />,
            description: 'Finalize & create'
        }
    ];

    useEffect(() => {
        if (formData.dataSourceId) {
            loadTables();
        }
    }, [formData.dataSourceId]);

    const loadTables = async () => {
        try {
            setLoadingTables(true);
            // Use the rule registry endpoint to discover tables
            const tablesData = await ruleVariableRegistryService.discoverTables();
            setTables(Array.isArray(tablesData) ? tablesData : []);
        } catch (err) {
            console.error('Error loading tables:', err);
            setError('Failed to load tables from database schema');
            setTables([]);
        } finally {
            setLoadingTables(false);
        }
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error for this field
        if (formErrors[field]) {
            setFormErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    const validateTab = (tab) => {
        const errors = {};

        switch (tab) {
            case 0:
                if (!formData.name.trim()) errors.name = 'Bucket name is required';
                if (!formData.description.trim()) errors.description = 'Description is required';
                break;
            case 1:
                if (!formData.dataSourceId) errors.dataSourceId = 'Data source is required';
                if (!formData.tableName) errors.tableName = 'Table is required';
                break;
            default:
                break;
        }

        setFormErrors(errors);
        const isValid = Object.keys(errors).length === 0;

        if (isValid) {
            setCompletedTabs(prev => new Set([...prev, tab]));
        }

        return isValid;
    };

    const handleTabChange = (event, newValue) => {
        // Allow navigation to any tab, but validate current tab first
        if (newValue > activeTab) {
            // Moving forward - validate current tab
            if (validateTab(activeTab)) {
                setActiveTab(newValue);
            }
        } else {
            // Moving backward - always allow
            setActiveTab(newValue);
        }
    };

    const handleNext = () => {
        if (validateTab(activeTab)) {
            setActiveTab(prev => Math.min(prev + 1, tabs.length - 1));
        }
    };

    const handleBack = () => {
        setActiveTab(prev => Math.max(prev - 1, 0));
    };

    const handleCreateBucket = async () => {
        try {
            setLoading(true);
            setError(null);

            // Extract just the table name from schema.table format
            const tableNameOnly = formData.tableName.includes('.')
                ? formData.tableName.split('.')[1]
                : formData.tableName;

            const bucketData = {
                name: formData.name,
                description: formData.description,
                bucketType: formData.bucketType,
                tableName: tableNameOnly,
                dataSourceId: formData.dataSourceId
            };

            let result;

            if (selectedVariables.length > 0) {
                // Create bucket with variables
                const lookupBucketData = {
                    ...bucketData,
                    variables: selectedVariables.map(variable => ({
                        name: variable.name,
                        code: variable.code,
                        description: variable.description || '',
                        dataTypeCode: variable.dataTypeCode,
                        isUnique: variable.isUnique || false,
                        aggregatable: variable.aggregatable || false,
                        isPrimaryEntityKey: variable.isPrimaryEntityKey || false,
                        isSecondaryEntityKey: variable.isSecondaryEntityKey || false,
                        isDateDimension: variable.isDateDimension || false,
                        columnName: variable.name
                    }))
                };

                result = await dataBucketService.createLookupBucket(lookupBucketData);
                setSuccess(`Bucket "${formData.name}" created successfully with ${selectedVariables.length} variables`);
            } else {
                // Create bucket without variables
                result = await dataBucketService.createBucket(bucketData);
                setSuccess(`Bucket "${formData.name}" created successfully`);
            }

            // Notify parent component
            onBucketCreated(result);
        } catch (err) {
            console.error('Failed to create bucket:', err);
            setError(`Failed to create bucket: ${err.response?.data?.message || err.message}`);
        } finally {
            setLoading(false);
        }
    };

    const renderTabContent = (tab) => {
        switch (tab) {
            case 0:
                return (
                    <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                            <TextField
                                fullWidth
                                label="Bucket Name"
                                value={formData.name}
                                onChange={(e) => handleInputChange('name', e.target.value)}
                                error={!!formErrors.name}
                                helperText={formErrors.name}
                                placeholder="e.g., Customer Transactions"
                                sx={{ mb: 3 }}
                            />
                            
                            <TextField
                                fullWidth
                                label="Description"
                                value={formData.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                error={!!formErrors.description}
                                helperText={formErrors.description}
                                multiline
                                rows={3}
                                placeholder="Brief description of the bucket purpose"
                                sx={{ mb: 3 }}
                            />
                        </Grid>
                        
                        <Grid item xs={12} md={6}>
                            <FormControl fullWidth sx={{ mb: 3 }}>
                                <InputLabel>Bucket Type</InputLabel>
                                <Select
                                    value={formData.bucketType}
                                    onChange={(e) => handleInputChange('bucketType', e.target.value)}
                                    label="Bucket Type"
                                >
                                    {BUCKET_TYPES.map(type => (
                                        <MenuItem key={type.value} value={type.value}>
                                            <Box>
                                                <Typography variant="body2" fontWeight="medium">
                                                    {type.label}
                                                </Typography>
                                                <Typography variant="caption" color="text.secondary">
                                                    {type.description}
                                                </Typography>
                                            </Box>
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                            
                            <Alert severity="info">
                                <Typography variant="body2">
                                    <strong>Rule Buckets:</strong> Used for transaction monitoring rules<br/>
                                    <strong>Lookup Buckets:</strong> Used for enriching data from other tables
                                </Typography>
                            </Alert>
                        </Grid>
                    </Grid>
                );

            case 1:
                return (
                    <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                            <FormControl fullWidth sx={{ mb: 3 }}>
                                <InputLabel>Data Source</InputLabel>
                                <Select
                                    value={formData.dataSourceId}
                                    onChange={(e) => handleInputChange('dataSourceId', e.target.value)}
                                    error={!!formErrors.dataSourceId}
                                    label="Data Source"
                                >
                                    {dataSources.map(source => (
                                        <MenuItem key={source.id} value={source.id}>
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <StorageIcon sx={{ mr: 1 }} />
                                                <Box>
                                                    <Typography variant="body2">{source.name}</Typography>
                                                    <Typography variant="caption" color="text.secondary">
                                                        {source.type}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        </MenuItem>
                                    ))}
                                </Select>
                                {formErrors.dataSourceId && (
                                    <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                                        {formErrors.dataSourceId}
                                    </Typography>
                                )}
                            </FormControl>
                        </Grid>
                        
                        <Grid item xs={12} md={6}>
                            <FormControl fullWidth sx={{ mb: 3 }}>
                                <InputLabel>Table</InputLabel>
                                <Select
                                    value={formData.tableName}
                                    onChange={(e) => handleInputChange('tableName', e.target.value)}
                                    error={!!formErrors.tableName}
                                    label="Table"
                                    disabled={!formData.dataSourceId || loadingTables}
                                >
                                    {loadingTables ? (
                                        <MenuItem disabled>
                                            <CircularProgress size={20} sx={{ mr: 1 }} />
                                            Loading tables...
                                        </MenuItem>
                                    ) : (
                                        tables.map(table => (
                                            <MenuItem key={`${table.schemaName}.${table.tableName}`} value={`${table.schemaName}.${table.tableName}`}>
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <TableChartIcon sx={{ mr: 1 }} />
                                                    <Box>
                                                        <Typography variant="body2">{table.tableName}</Typography>
                                                        <Typography variant="caption" color="text.secondary">
                                                            {table.schemaName} • {table.engine}
                                                        </Typography>
                                                    </Box>
                                                </Box>
                                            </MenuItem>
                                        ))
                                    )}
                                </Select>
                                {formErrors.tableName && (
                                    <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                                        {formErrors.tableName}
                                    </Typography>
                                )}
                            </FormControl>
                            
                            {formData.dataSourceId && formData.tableName && (
                                <Alert severity="success">
                                    <Typography variant="body2">
                                        Ready to configure variables from <strong>{formData.tableName}</strong>
                                    </Typography>
                                </Alert>
                            )}
                        </Grid>
                    </Grid>
                );

            case 2:
                return (
                    <Box>
                        {formData.tableName ? (
                            <ProperVariableSelector
                                tableName={formData.tableName}
                                selectedVariables={selectedVariables}
                                onVariablesChange={setSelectedVariables}
                            />
                        ) : (
                            <Alert severity="warning">
                                Please complete the previous steps to configure variables
                            </Alert>
                        )}
                    </Box>
                );

            case 3:
                return (
                    <Box>
                        <Typography variant="h6" sx={{ mb: 3 }}>
                            Review Bucket Configuration
                        </Typography>
                        
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={6}>
                                <Card variant="outlined">
                                    <CardContent>
                                        <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                                            Basic Information
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Name:</strong> {formData.name}
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Type:</strong> {BUCKET_TYPES.find(t => t.value === formData.bucketType)?.label}
                                        </Typography>
                                        <Typography variant="body2">
                                            <strong>Description:</strong> {formData.description}
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid>
                            
                            <Grid item xs={12} md={6}>
                                <Card variant="outlined">
                                    <CardContent>
                                        <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                                            Data Source
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Source:</strong> {dataSources.find(ds => ds.id === formData.dataSourceId)?.name}
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Table:</strong> {formData.tableName}
                                        </Typography>
                                        <Typography variant="body2">
                                            <strong>Variables:</strong> {selectedVariables.length} configured
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>
                        
                        {selectedVariables.length > 0 && (
                            <Card variant="outlined" sx={{ mt: 3 }}>
                                <CardContent>
                                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                                        Configured Variables
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                        {selectedVariables.map((variable, index) => (
                                            <Box key={index} sx={{ 
                                                p: 1, 
                                                border: '1px solid', 
                                                borderColor: 'divider', 
                                                borderRadius: 1,
                                                bgcolor: 'background.paper'
                                            }}>
                                                <Typography variant="caption" fontWeight="medium">
                                                    {variable.name}
                                                </Typography>
                                                {(variable.isPrimaryEntityKey || variable.isSecondaryEntityKey || variable.isDateDimension) && (
                                                    <Typography variant="caption" color="primary" sx={{ display: 'block' }}>
                                                        {variable.isPrimaryEntityKey && 'Primary Key'}
                                                        {variable.isSecondaryEntityKey && 'Secondary Key'}
                                                        {variable.isDateDimension && 'Date Dimension'}
                                                    </Typography>
                                                )}
                                            </Box>
                                        ))}
                                    </Box>
                                </CardContent>
                            </Card>
                        )}
                    </Box>
                );

            default:
                return null;
        }
    };

    return (
        <Box>
            <Breadcrumbs sx={{ mb: 3 }}>
                <Link color="inherit" href="#" onClick={onCancel} sx={{ textDecoration: 'none' }}>
                    Data Buckets
                </Link>
                <Typography color="text.primary">Create New Bucket</Typography>
            </Breadcrumbs>

            <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
                Create New Data Bucket
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Follow the steps below to create and configure your data bucket
            </Typography>

            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                    {success}
                </Alert>
            )}

            <Paper sx={{ overflow: 'hidden' }}>
                {/* Tab Headers */}
                <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: alpha(theme.palette.primary.main, 0.02) }}>
                    <Tabs
                        value={activeTab}
                        onChange={handleTabChange}
                        variant="fullWidth"
                        sx={{
                            '& .MuiTab-root': {
                                minHeight: 80,
                                textTransform: 'none',
                                fontWeight: 500,
                                '&.Mui-selected': {
                                    fontWeight: 600,
                                    color: theme.palette.primary.main
                                }
                            },
                            '& .MuiTabs-indicator': {
                                height: 3,
                                backgroundColor: theme.palette.primary.main
                            }
                        }}
                    >
                        {tabs.map((tab, index) => {
                            const isCompleted = completedTabs.has(index);
                            const isActive = activeTab === index;

                            return (
                                <Tab
                                    key={index}
                                    label={
                                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                                            <Box sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                color: isCompleted ? 'success.main' : isActive ? 'primary.main' : 'text.secondary'
                                            }}>
                                                {tab.icon}
                                            </Box>
                                            <Box sx={{ textAlign: 'center' }}>
                                                <Typography variant="body2" fontWeight="inherit">
                                                    {tab.label}
                                                </Typography>
                                                <Typography variant="caption" color="text.secondary">
                                                    {tab.description}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    }
                                    disabled={index > activeTab + 1} // Allow going back or one step forward
                                />
                            );
                        })}
                    </Tabs>
                </Box>

                {/* Tab Content */}
                <Box sx={{ p: 4 }}>
                    {renderTabContent(activeTab)}
                </Box>

                {/* Navigation */}
                <Box sx={{
                    p: 3,
                    borderTop: 1,
                    borderColor: 'divider',
                    bgcolor: alpha(theme.palette.grey[50], 0.5),
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}>
                    <Button
                        onClick={activeTab === 0 ? onCancel : handleBack}
                        disabled={loading}
                        variant="outlined"
                    >
                        {activeTab === 0 ? 'Cancel' : 'Back'}
                    </Button>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                            Step {activeTab + 1} of {tabs.length}
                        </Typography>
                        <Button
                            variant="contained"
                            onClick={activeTab === tabs.length - 1 ? handleCreateBucket : handleNext}
                            disabled={loading}
                            startIcon={activeTab === tabs.length - 1 ? <SaveIcon /> : null}
                        >
                            {loading ? (
                                <CircularProgress size={20} />
                            ) : (
                                activeTab === tabs.length - 1 ? 'Create Bucket' : 'Next'
                            )}
                        </Button>
                    </Box>
                </Box>
            </Paper>
        </Box>
    );
};

export default ProperBucketCreator;
