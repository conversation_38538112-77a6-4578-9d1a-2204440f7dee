package com.progize.tms.services.models;

import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a unique aggregation requirement extracted from rules. Each requirement defines a
 * specific aggregation that needs to be calculated at transaction processing time.
 */
@Data
@NoArgsConstructor
public class AggregationRequirement {
  private String function; // SUM, COUNT, AVG, etc.
  private String field; // Field to aggregate
  private Integer period; // Time period in days
  private Integer
      startOffset; // Days in the past to start the time window (0 means start from today)
  private String entityField; // Entity identifier field
  private SimpleCondition filterCondition; // Optional filter for the aggregation

  /**
   * Returns a unique key for this aggregation requirement. This key can be used for caching and
   * lookup.
   *
   * @return A unique string key for this aggregation
   */
  public String getAggregationKey() {
    StringBuilder keyBuilder = new StringBuilder();

    // Abbreviate function name (e.g., count -> cnt)
    String funcAbbrev = abbreviateFunction(function);

    // Keep the original field name as is (no abbreviation)
    String fieldName = field != null ? toCamelCase(field) : "";

    // Format period with 'd' prefix (e.g., 30 -> d30)
    String periodStr = "d" + period;

    // Format startOffset with 'o' prefix if it exists (e.g., 7 -> o7)
    // Make sure it's always included in the key, even if it's 0
    String offsetStr = "o" + (startOffset != null ? startOffset : 0);

    // Build the base key using camelCase
    keyBuilder.append(funcAbbrev).append(fieldName).append(periodStr).append(offsetStr);

    // If filter condition exists, include it in the key to make it unique
    if (filterCondition != null) {
      // Keep the original filter field as is
      String filterFieldName =
          filterCondition.getField() != null ? toCamelCase(filterCondition.getField()) : "";

      // Abbreviate the operator
      String operatorAbbrev = abbreviateOperator(filterCondition.getOperator());

      // Abbreviate or truncate the value if needed
      String valueStr = abbreviateValue(filterCondition.getValue());

      // Append filter information in camelCase
      keyBuilder.append("F").append(filterFieldName).append(operatorAbbrev).append(valueStr);
    }

    return keyBuilder.toString();
  }

  /** Converts a string to camelCase */
  private String toCamelCase(String input) {
    if (input == null || input.isEmpty()) return "";

    // Split by non-alphanumeric characters
    String[] parts = input.split("[^a-zA-Z0-9]");
    StringBuilder result = new StringBuilder();

    for (int i = 0; i < parts.length; i++) {
      String part = parts[i];
      if (part.isEmpty()) continue;

      if (i == 0) {
        // First part starts with lowercase
        result.append(part.substring(0, 1).toLowerCase());
      } else {
        // Subsequent parts start with uppercase
        result.append(part.substring(0, 1).toUpperCase());
      }

      // Rest of the part stays as is
      if (part.length() > 1) {
        result.append(part.substring(1));
      }
    }

    return result.toString();
  }

  /** Abbreviates function names to keep keys shorter */
  private String abbreviateFunction(String function) {
    if (function == null) return "";

    switch (function.toLowerCase()) {
      case "count":
        return "cnt";
      case "sum":
        return "sum";
      case "avg":
        return "avg";
      case "min":
        return "min";
      case "max":
        return "max";
      default:
        return function.toLowerCase().substring(0, Math.min(3, function.length()));
    }
  }

  /** Abbreviates operators to keep keys shorter */
  private String abbreviateOperator(String operator) {
    if (operator == null) return "";

    switch (operator.toLowerCase()) {
      case "eq":
      case "equals":
        return "Eq";
      case "neq":
      case "notequals":
        return "Neq";
      case "lt":
      case "lessthan":
        return "Lt";
      case "gt":
      case "greaterthan":
        return "Gt";
      case "lte":
      case "lessthanequals":
        return "Lte";
      case "gte":
      case "greaterthanequals":
        return "Gte";
      case "contains":
        return "Cont";
      case "startswith":
        return "Strt";
      case "endswith":
        return "End";
      case "in":
        return "In";
      case "isnull":
        return "Null";
      case "isnotnull":
        return "NotNull";
      default:
        return operator.substring(0, Math.min(2, operator.length())).toUpperCase();
    }
  }

  /** Abbreviates or truncates values to keep keys shorter */
  private String abbreviateValue(Object value) {
    if (value == null) return "Null";

    // For common values, provide specific abbreviations
    String stringValue = value.toString();

    // Common transaction types
    if ("DEBIT".equalsIgnoreCase(stringValue)) return "Deb";
    if ("CREDIT".equalsIgnoreCase(stringValue)) return "Cred";
    if ("REFUND".equalsIgnoreCase(stringValue)) return "Ref";
    if ("PAYMENT".equalsIgnoreCase(stringValue)) return "Pay";
    if ("WITHDRAWAL".equalsIgnoreCase(stringValue)) return "Wdraw";
    if ("DEPOSIT".equalsIgnoreCase(stringValue)) return "Dep";

    // Statuses
    if ("COMPLETED".equalsIgnoreCase(stringValue)) return "Comp";
    if ("PENDING".equalsIgnoreCase(stringValue)) return "Pend";
    if ("FAILED".equalsIgnoreCase(stringValue)) return "Fail";
    if ("DECLINED".equalsIgnoreCase(stringValue)) return "Decl";
    if ("AUTHORIZED".equalsIgnoreCase(stringValue)) return "Auth";

    // For numbers, just use the value
    if (value instanceof Number) return value.toString();

    // For boolean values, use Y/N
    if (value instanceof Boolean) return ((Boolean) value) ? "Y" : "N";

    // For other strings, take first 5 chars or use a hash if too complex
    if (stringValue.length() <= 5) {
      return stringValue;
    } else if (stringValue.length() <= 10) {
      return stringValue.substring(0, 5);
    } else {
      // For very long strings or complex values, use a hash code
      // but limit to 5 digits for readability
      return "h" + Math.abs(stringValue.hashCode() % 100000);
    }
  }

  public AggregationRequirement(String function, String field, Integer period, String entityField) {
    this(function, field, period, 0, entityField, null);
  }

  public AggregationRequirement(
      String function,
      String field,
      Integer period,
      String entityField,
      SimpleCondition filterCondition) {
    this(function, field, period, 0, entityField, filterCondition);
  }

  public AggregationRequirement(
      String function, String field, Integer period, Integer startOffset, String entityField) {
    this(function, field, period, startOffset, entityField, null);
  }

  public AggregationRequirement(
      String function,
      String field,
      Integer period,
      Integer startOffset,
      String entityField,
      SimpleCondition filterCondition) {
    this.function = function;
    this.field = field;
    this.period = period;
    this.startOffset = startOffset;
    this.entityField = entityField;
    this.filterCondition = filterCondition;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    AggregationRequirement that = (AggregationRequirement) o;
    return Objects.equals(function, that.function)
        && Objects.equals(field, that.field)
        && Objects.equals(period, that.period)
        && Objects.equals(startOffset, that.startOffset)
        && Objects.equals(entityField, that.entityField)
        && Objects.equals(filterCondition, that.filterCondition);
  }

  @Override
  public int hashCode() {
    return Objects.hash(function, field, period, startOffset, entityField, filterCondition);
  }
}
