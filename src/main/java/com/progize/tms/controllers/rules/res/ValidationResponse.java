package com.progize.tms.controllers.rules.res;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response class for rule validation results. Used in the REST layer to return validation status
 * and error messages.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResponse {
  private boolean valid;
  private List<String> errors;
}
