import React, { memo } from 'react';
import { Handle, Position } from 'reactflow';
import {
  Box,
  Typography,
  Paper,
  Chip,
  Tooltip,
  alpha,
  useTheme
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  People as PeopleIcon,
  List as ListIcon,
  AttachFile as AttachFileIcon,
  Comment as CommentIcon
} from '@mui/icons-material';

const StepNode = ({ data, selected }) => {
  const theme = useTheme();
  
  return (
    <Paper
      elevation={selected ? 8 : 3}
      sx={{
        padding: 1,
        borderRadius: 1,
        minWidth: 150,
        maxWidth: 180,
        border: selected ? `2px solid ${theme.palette.primary.main}` : 'none',
        backgroundColor: alpha(theme.palette.background.paper, 0.9),
        transition: 'all 0.2s ease',
        '&:hover': {
          boxShadow: theme.shadows[8]
        }
      }}
    >
      {/* Multiple handles for better edge connections */}
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: theme.palette.primary.main, top: '25%' }}
        id="target-top"
      />
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: theme.palette.primary.main, top: '75%' }}
        id="target-bottom"
      />
      
      <Box sx={{ mb: 0.5 }}>
        <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 0.5 }}>
          {data.label}
        </Typography>
        
        <Chip
          label={data.statusLabel || 'Status'}
          size="small"
          color="info"
          sx={{ mb: 0.5, height: 20, '& .MuiChip-label': { fontSize: '0.7rem', px: 1 } }}
        />
        
        {data.description && (
          <Typography 
            variant="caption" 
            color="text.secondary" 
            sx={{ 
              mb: 0.5, 
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              lineHeight: 1.2
            }}
          >
            {data.description}
          </Typography>
        )}
      </Box>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {data.picklist && (
          <Tooltip title="Has Picklist">
            <Chip
              icon={<ListIcon sx={{ fontSize: '0.7rem' }} />}
              label={data.picklist.name || 'Picklist'}
              size="small"
              variant="outlined"
              color="primary"
              sx={{ height: 18, '& .MuiChip-label': { fontSize: '0.6rem', px: 0.5 } }}
            />
          </Tooltip>
        )}
        
        {data.eligibleRoles && data.eligibleRoles.length > 0 && (
          <Tooltip title={`Roles: ${data.eligibleRoles.join(', ')}`}>
            <Chip
              icon={<PeopleIcon sx={{ fontSize: '0.7rem' }} />}
              label={`${data.eligibleRoles.length} Role(s)`}
              size="small"
              variant="outlined"
              color="secondary"
              sx={{ height: 18, '& .MuiChip-label': { fontSize: '0.6rem', px: 0.5 } }}
            />
          </Tooltip>
        )}
        
        {data.requiresAttachment && (
          <Tooltip title="Requires Attachment">
            <Chip
              icon={<AttachFileIcon sx={{ fontSize: '0.7rem' }} />}
              size="small"
              variant="outlined"
              sx={{ height: 18, '& .MuiChip-label': { fontSize: '0.6rem', px: 0.5 } }}
            />
          </Tooltip>
        )}
        
        {data.requiresComments && (
          <Tooltip title="Requires Comments">
            <Chip
              icon={<CommentIcon sx={{ fontSize: '0.7rem' }} />}
              size="small"
              variant="outlined"
              sx={{ height: 18, '& .MuiChip-label': { fontSize: '0.6rem', px: 0.5 } }}
            />
          </Tooltip>
        )}
      </Box>
      
      {/* Multiple handles for better edge connections */}
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: theme.palette.primary.main, top: '25%' }}
        id="source-top"
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: theme.palette.primary.main, top: '75%' }}
        id="source-bottom"
      />
    </Paper>
  );
};

export default memo(StepNode);
