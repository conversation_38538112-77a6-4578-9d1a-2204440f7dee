package com.progize.tms.repository;

import com.progize.tms.repository.entity.RuleVariableEntity;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/** Repository for accessing rule variables. */
public interface RuleVariableRepository extends JpaRepository<RuleVariableEntity, Long> {

  /** Find a variable by its unique code. */
  Optional<RuleVariableEntity> findByCode(String code);

  /** Find all variables for a specific source table. */
  @Query("SELECT v FROM RuleVariableEntity v WHERE v.sourceTable.id = :sourceTableId")
  List<RuleVariableEntity> findBySourceTableId(@Param("sourceTableId") Long sourceTableId);

  /** Find all variables for a specific source table name. */
  @Query("SELECT v FROM RuleVariableEntity v JOIN v.sourceTable t WHERE t.tableName = :tableName")
  List<RuleVariableEntity> findBySourceTableName(@Param("tableName") String tableName);

  /** Find all variables with a specific data type. */
  @Query("SELECT v FROM RuleVariableEntity v JOIN v.dataType dt WHERE dt.code = :dataTypeCode")
  List<RuleVariableEntity> findByDataTypeCode(@Param("dataTypeCode") String dataTypeCode);

  /** Find variables updated since a given timestamp. */
  @Query("SELECT v FROM RuleVariableEntity v WHERE v.updatedAt > :since")
  List<RuleVariableEntity> findUpdatedSince(@Param("since") LocalDateTime since);
}
