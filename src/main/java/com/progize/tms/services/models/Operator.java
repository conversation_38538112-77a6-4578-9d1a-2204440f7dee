package com.progize.tms.services.models;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents an operator that can be used in rule conditions. Examples include: equals, not equals,
 * greater than, etc.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Operator {
  private String id;
  private String name;
  private String description;
  private String symbol; // =, !=, >, <, etc.
  private List<String> applicableTypes; // string, number, boolean, date, etc.
}
