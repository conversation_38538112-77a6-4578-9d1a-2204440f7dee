-- Add bucket_type column with default value 1 (RULE_BUCKET)
ALTER TABLE data_bucket ADD COLUMN bucket_type INTEGER NOT NULL DEFAULT 1;

-- Add is_unique column to data_bucket_variables
ALTER TABLE data_bucket_variable ADD COLUMN is_unique BOOLEAN NOT NULL DEFAULT FALSE;



ALTER TABLE data_bucket ADD COLUMN table_name VA<PERSON>HAR(255) NOT NULL;



-- Add bucket_type column with default value 1 (RULE_BUCKET)
ALTER TABLE data_bucket_aud ADD COLUMN bucket_type INTEGER NOT NULL DEFAULT 1;

-- Add is_unique column to data_bucket_variables
ALTER TABLE data_bucket_variable_aud ADD COLUMN is_unique BOOLEAN NOT NULL DEFAULT FALSE;



ALTER TABLE data_bucket_aud ADD COLUMN table_name VARCHAR(255) NOT NULL;