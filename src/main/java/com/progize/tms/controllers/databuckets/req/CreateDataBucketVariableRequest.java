package com.progize.tms.controllers.databuckets.req;

import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/** Request DTO for creating a variable in a data bucket. */
@Getter
@Setter
public class CreateDataBucketVariableRequest {
  private String code;
  private String name;
  private String description;
  private String tableName;
  private String columnName;
  private String dataTypeCode;
  private boolean aggregatable;
  private Boolean isUnique;
  private Boolean isPrimaryEntityKey = false;
  private Boolean isSecondaryEntityKey = false;
  private Map<String, Object> metadata;
}
