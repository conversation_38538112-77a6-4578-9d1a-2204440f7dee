package com.progize.tms.services;

import java.util.Map;

/**
 * Service for looking up entities by type and ID. This provides a generic way to access any entity
 * in the system.
 */
public interface EntityLookupService {

  /**
   * Look up any entity by its type and ID.
   *
   * @param entityType The entity type (e.g., "CASE", "USER", etc.)
   * @param entityId The entity ID
   * @return The entity object
   * @throws com.progize.tms.exceptions.ResourceNotFoundException if the entity is not found
   */
  Object findEntityByTypeAndId(String entityType, Long entityId);

  /**
   * Get a display name for any entity (for UI purposes).
   *
   * @param entityType The entity type
   * @param entityId The entity ID
   * @return A display name for the entity
   * @throws com.progize.tms.exceptions.ResourceNotFoundException if the entity is not found
   */
  String getEntityDisplayName(String entityType, Long entityId);

  /**
   * Get entity details as a map (for display purposes).
   *
   * @param entityType The entity type
   * @param entityId The entity ID
   * @return A map containing key entity details
   * @throws com.progize.tms.exceptions.ResourceNotFoundException if the entity is not found
   */
  Map<String, Object> getEntityDetails(String entityType, Long entityId);
}
