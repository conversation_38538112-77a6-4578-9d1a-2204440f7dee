import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Button,
  IconButton,
  Tooltip,
  Skeleton,
  Alert,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Grid,
  useTheme,
  TablePagination,
  Chip
} from '@mui/material';
import { 
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

/**
 * Professional compliance case management dashboard for admins/managers
 */
const ComplianceCaseList = () => {
  const [cases, setCases] = useState([]);
  const [filteredCases, setFilteredCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterTab, setFilterTab] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const navigate = useNavigate();
  const theme = useTheme();

  // Sample case data for compliance management
  const sampleCases = [
    {
      id: 'CASE-2024-001',
      customerId: 'CUST-12345',
      customerName: 'Acme Corporation Ltd',
      ruleTriggered: 'High Transaction Amount Alert',
      transactionAmount: 250000,
      transactionDate: '2024-01-15T10:30:00Z',
      currentStatus: 'Under Review',
      priority: 'High',
      assignedOfficer: 'John Smith',
      createdDate: '2024-01-15T10:35:00Z',
      lastUpdated: '2024-01-16T14:20:00Z',
      slaStatus: 'On Track',
      riskScore: 85,
      totalTasks: 3,
      completedTasks: 1,
      escalationLevel: 'L1'
    },
    {
      id: 'CASE-2024-002',
      customerId: 'CUST-67890',
      customerName: 'Global Investments Inc',
      ruleTriggered: 'Suspicious Pattern Detection',
      transactionAmount: 150000,
      transactionDate: '2024-01-14T16:45:00Z',
      currentStatus: 'Closed - False Positive',
      priority: 'Medium',
      assignedOfficer: 'Sarah Johnson',
      createdDate: '2024-01-14T16:50:00Z',
      lastUpdated: '2024-01-15T11:30:00Z',
      slaStatus: 'Completed',
      riskScore: 65,
      totalTasks: 2,
      completedTasks: 2,
      escalationLevel: 'L1'
    },
    {
      id: 'CASE-2024-003',
      customerId: 'CUST-11111',
      customerName: 'Tech Innovations LLC',
      ruleTriggered: 'Cross-Border Transaction Alert',
      transactionAmount: 75000,
      transactionDate: '2024-01-13T09:15:00Z',
      currentStatus: 'Escalated',
      priority: 'High',
      assignedOfficer: 'Mike Wilson',
      createdDate: '2024-01-13T09:20:00Z',
      lastUpdated: '2024-01-16T16:45:00Z',
      slaStatus: 'Overdue',
      riskScore: 92,
      totalTasks: 4,
      completedTasks: 2,
      escalationLevel: 'L2'
    },
    {
      id: 'CASE-2024-004',
      customerId: 'CUST-22222',
      customerName: 'Financial Services Co',
      ruleTriggered: 'PEP Screening Alert',
      transactionAmount: 500000,
      transactionDate: '2024-01-12T14:20:00Z',
      currentStatus: 'Approved',
      priority: 'High',
      assignedOfficer: 'Lisa Chen',
      createdDate: '2024-01-12T14:25:00Z',
      lastUpdated: '2024-01-14T10:15:00Z',
      slaStatus: 'Completed',
      riskScore: 78,
      totalTasks: 3,
      completedTasks: 3,
      escalationLevel: 'L1'
    },
    {
      id: 'CASE-2024-005',
      customerId: 'CUST-33333',
      customerName: 'International Trading Corp',
      ruleTriggered: 'Velocity Check Alert',
      transactionAmount: 180000,
      transactionDate: '2024-01-16T11:30:00Z',
      currentStatus: 'Open',
      priority: 'Medium',
      assignedOfficer: null,
      createdDate: '2024-01-16T11:35:00Z',
      lastUpdated: '2024-01-16T11:35:00Z',
      slaStatus: 'On Track',
      riskScore: 70,
      totalTasks: 1,
      completedTasks: 0,
      escalationLevel: 'L1'
    }
  ];

  useEffect(() => {
    const fetchCases = async () => {
      try {
        setLoading(true);
        // For now, use sample data. Later replace with actual API call
        setCases(sampleCases);
        setError(null);
      } catch (err) {
        console.error('Error fetching compliance cases:', err);
        setError('Failed to load compliance cases. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCases();
  }, []);

  // Filter cases based on search query and tab selection
  useEffect(() => {
    let filtered = [...cases];

    // Apply tab filter
    switch (filterTab) {
      case 1: // Open Cases
        filtered = filtered.filter(case_ => ['Open', 'Under Review'].includes(case_.currentStatus));
        break;
      case 2: // Escalated
        filtered = filtered.filter(case_ => case_.currentStatus === 'Escalated');
        break;
      case 3: // Closed
        filtered = filtered.filter(case_ => ['Closed - False Positive', 'Approved', 'Rejected'].includes(case_.currentStatus));
        break;
      case 4: // Overdue
        filtered = filtered.filter(case_ => case_.slaStatus === 'Overdue');
        break;
      default: // All
        break;
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(case_ => 
        case_.id.toLowerCase().includes(query) ||
        case_.customerName.toLowerCase().includes(query) ||
        case_.ruleTriggered.toLowerCase().includes(query) ||
        case_.assignedOfficer?.toLowerCase().includes(query)
      );
    }

    setFilteredCases(filtered);
    setPage(0); // Reset to first page when filters change
  }, [cases, searchQuery, filterTab]);

  // Event handlers
  const handleViewCase = (caseId) => {
    navigate(`/compliance/cases/${caseId}`);
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleTabChange = (event, newValue) => {
    setFilterTab(newValue);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Helper functions
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.grey[500];
      default:
        return theme.palette.grey[400];
    }
  };

  const getPriorityBgColor = (priority) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return theme.palette.error.light + '20';
      case 'medium':
        return theme.palette.warning.light + '20';
      case 'low':
        return theme.palette.grey[100];
      default:
        return theme.palette.grey[50];
    }
  };

  const getStatusColor = (status) => {
    switch(status?.toLowerCase()) {
      case 'approved':
        return theme.palette.success.main;
      case 'closed - false positive':
        return theme.palette.info.main;
      case 'under review':
        return theme.palette.warning.main;
      case 'escalated':
        return theme.palette.error.main;
      case 'open':
        return theme.palette.primary.main;
      case 'rejected':
        return theme.palette.error.dark;
      default:
        return theme.palette.grey[500];
    }
  };

  const getSlaColor = (slaStatus) => {
    switch(slaStatus?.toLowerCase()) {
      case 'overdue':
        return theme.palette.error.main;
      case 'completed':
        return theme.palette.success.main;
      case 'on track':
        return theme.palette.success.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getRiskScoreColor = (score) => {
    if (score >= 80) return theme.palette.error.main;
    if (score >= 60) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  // Loading state
  if (loading) {
    return (
      <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
        <Skeleton variant="text" width={250} height={40} sx={{ mb: 2 }} />
        <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2, borderRadius: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={400} sx={{ borderRadius: 1 }} />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Box>
    );
  }

  // Get paginated cases
  const paginatedCases = filteredCases.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Box sx={{ p: 3, maxWidth: '1400px', margin: '0 auto' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, fontSize: '1.5rem' }}>
          Case Management Dashboard
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Chip 
            label={`${cases.length} Total Cases`} 
            variant="outlined" 
            size="small"
            sx={{ fontSize: '0.75rem' }}
          />
          <Chip 
            label={`${cases.filter(c => ['Open', 'Under Review'].includes(c.currentStatus)).length} Active`} 
            color="primary" 
            size="small"
            sx={{ fontSize: '0.75rem' }}
          />
        </Box>
      </Box>

      {/* Search and Filters */}
      <Paper
        elevation={0}
        sx={{
          mb: 2,
          p: 2,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search by Case ID, Customer, Rule, or Officer..."
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: theme.palette.text.secondary, fontSize: '1.2rem' }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  fontSize: '0.875rem',
                  '& fieldset': {
                    borderColor: theme.palette.grey[300]
                  }
                }
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
              <Tabs
                value={filterTab}
                onChange={handleTabChange}
                sx={{
                  minHeight: 36,
                  '& .MuiTab-root': {
                    minHeight: 36,
                    fontSize: '0.8rem',
                    fontWeight: 500,
                    textTransform: 'none',
                    color: theme.palette.text.secondary,
                    '&.Mui-selected': {
                      color: theme.palette.primary.main,
                      fontWeight: 600
                    }
                  },
                  '& .MuiTabs-indicator': {
                    height: 2
                  }
                }}
              >
                <Tab label="All" />
                <Tab label="Open" />
                <Tab label="Escalated" />
                <Tab label="Closed" />
                <Tab label="Overdue" />
              </Tabs>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Case Table */}
      <Paper
        elevation={0}
        sx={{
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
          overflow: 'hidden'
        }}
      >
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ bgcolor: theme.palette.grey[50] }}>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Case ID</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Customer</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Rule Triggered</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Amount</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Priority</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Officer</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Risk Score</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Progress</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>SLA</TableCell>
                <TableCell sx={{ fontWeight: 600, fontSize: '0.8rem', py: 1.5 }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedCases.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={11} sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      No cases found matching your criteria
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedCases.map((case_) => (
                  <TableRow
                    key={case_.id}
                    onClick={() => handleViewCase(case_.id)}
                    sx={{
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: theme.palette.grey[50]
                      },
                      ...(case_.priority === 'High' && {
                        borderLeft: `3px solid ${theme.palette.error.main}`
                      }),
                      ...(case_.slaStatus === 'Overdue' && {
                        bgcolor: theme.palette.error.light + '10'
                      })
                    }}
                  >
                    <TableCell sx={{ py: 1.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.85rem' }}>
                          {case_.id}
                        </Typography>
                        {case_.escalationLevel === 'L2' && (
                          <Tooltip title="Level 2 Escalation">
                            <TrendingUpIcon
                              sx={{
                                fontSize: '0.9rem',
                                color: theme.palette.error.main
                              }}
                            />
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box>
                        <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                          {case_.customerName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                          {case_.customerId}
                        </Typography>
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Typography variant="body2" sx={{ fontSize: '0.85rem', maxWidth: 150 }} noWrap>
                        {case_.ruleTriggered}
                      </Typography>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.85rem' }}>
                        {formatCurrency(case_.transactionAmount)}
                      </Typography>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box
                        sx={{
                          display: 'inline-block',
                          px: 1,
                          py: 0.25,
                          bgcolor: getStatusColor(case_.currentStatus) + '20',
                          color: getStatusColor(case_.currentStatus),
                          borderRadius: 0.5,
                          fontSize: '0.7rem',
                          fontWeight: 500
                        }}
                      >
                        {case_.currentStatus}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box
                        sx={{
                          display: 'inline-block',
                          px: 1,
                          py: 0.25,
                          bgcolor: getPriorityBgColor(case_.priority),
                          color: getPriorityColor(case_.priority),
                          borderRadius: 0.5,
                          fontSize: '0.7rem',
                          fontWeight: 600,
                          textTransform: 'uppercase',
                          letterSpacing: 0.5
                        }}
                      >
                        {case_.priority}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                        {case_.assignedOfficer || 'Unassigned'}
                      </Typography>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: '0.85rem',
                            fontWeight: 600,
                            color: getRiskScoreColor(case_.riskScore)
                          }}
                        >
                          {case_.riskScore}
                        </Typography>
                        <Box
                          sx={{
                            width: 4,
                            height: 4,
                            borderRadius: '50%',
                            bgcolor: getRiskScoreColor(case_.riskScore)
                          }}
                        />
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                          {case_.completedTasks}/{case_.totalTasks}
                        </Typography>
                        <Box
                          sx={{
                            width: 30,
                            height: 4,
                            bgcolor: theme.palette.grey[200],
                            borderRadius: 2,
                            overflow: 'hidden'
                          }}
                        >
                          <Box
                            sx={{
                              width: `${(case_.completedTasks / case_.totalTasks) * 100}%`,
                              height: '100%',
                              bgcolor: case_.completedTasks === case_.totalTasks
                                ? theme.palette.success.main
                                : theme.palette.primary.main,
                              borderRadius: 2
                            }}
                          />
                        </Box>
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Box
                        sx={{
                          display: 'inline-block',
                          px: 1,
                          py: 0.25,
                          bgcolor: getSlaColor(case_.slaStatus) + '20',
                          color: getSlaColor(case_.slaStatus),
                          borderRadius: 0.5,
                          fontSize: '0.7rem',
                          fontWeight: 500
                        }}
                      >
                        {case_.slaStatus}
                      </Box>
                    </TableCell>

                    <TableCell sx={{ py: 1.5 }}>
                      <Tooltip title="View Case Details">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewCase(case_.id);
                          }}
                          sx={{
                            color: theme.palette.primary.main,
                            '&:hover': { bgcolor: theme.palette.primary.light + '20' }
                          }}
                        >
                          <VisibilityIcon sx={{ fontSize: '1rem' }} />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          component="div"
          count={filteredCases.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25, 50]}
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            '& .MuiTablePagination-toolbar': {
              fontSize: '0.8rem'
            },
            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
              fontSize: '0.8rem'
            }
          }}
        />
      </Paper>
    </Box>
  );
};

export default ComplianceCaseList;
