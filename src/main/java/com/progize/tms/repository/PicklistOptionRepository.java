package com.progize.tms.repository;

import com.progize.tms.repository.entity.Picklist;
import com.progize.tms.repository.entity.PicklistOption;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PicklistOptionRepository extends JpaRepository<PicklistOption, Long> {
  List<PicklistOption> findByPicklistOrderByDisplayOrder(Picklist picklist);

  Optional<PicklistOption> findByPicklistAndOptionValue(Picklist picklist, String optionValue);
}
