package com.progize.tms.services;

import com.progize.tms.repository.RuleVariableRepository;
import com.progize.tms.repository.entity.RuleVariableEntity;
import com.progize.tms.services.models.RuleVariable;
import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Database-backed rule variable registry that caches rule variables and provides methods to access
 * them.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleVariableRegistryDb {
  private final RuleVariableRepository ruleVariableRepository;
  private final OperatorRegistryDb operatorRegistryDb;

  // Caches for performance
  private final Map<String, RuleVariable> variablesCache = new ConcurrentHashMap<>();
  private final Map<String, List<RuleVariable>> tableVariablesCache = new ConcurrentHashMap<>();
  private LocalDateTime lastCacheRefresh = LocalDateTime.now().minusDays(1);

  /** Initialize the cache on startup */
  @PostConstruct
  public void initCache() {
    log.info("Initializing RuleVariableRegistryDb cache");
    refreshCaches();
  }

  /** Refresh caches periodically */
  @Scheduled(fixedRate = 300000) // Refresh every 5 minutes
  public void refreshCaches() {
    //    log.debug("Checking for rule variable updates since {}", lastCacheRefresh);
    //
    //    // Check for updated variables
    //    List<RuleVariableEntity> updatedVariables =
    //        ruleVariableRepository.findUpdatedSince(lastCacheRefresh);
    //
    //    if (!updatedVariables.isEmpty()) {
    //      log.info("Found {} updated rule variables, refreshing cache", updatedVariables.size());
    //      doFullCacheRefresh();
    //    } else {
    //      log.debug("No updated rule variables found, cache remains valid");
    //    }
  }

  /** Perform a full refresh of all caches */
  private void doFullCacheRefresh() {
    log.info("Performing full rule variable cache refresh");

    // Clear existing caches
    variablesCache.clear();
    tableVariablesCache.clear();

    // Load all variables from the database
    List<RuleVariableEntity> allVariables = ruleVariableRepository.findAll();
    log.info("Loaded {} rule variables from database", allVariables.size());

    // Convert entities to model objects and populate caches
    for (RuleVariableEntity entity : allVariables) {
      RuleVariable variable = convertToModel(entity);
      variablesCache.put(variable.getId(), variable);

      // Group by source table
      String tableName = entity.getSourceTable().getTableName();
      tableVariablesCache.computeIfAbsent(tableName, k -> new ArrayList<>()).add(variable);
    }

    // Sort table variables by display order
    for (List<RuleVariable> tableVars : tableVariablesCache.values()) {
      tableVars.sort(
          Comparator.comparingInt(
              rv -> {
                if (rv.getMetadata() != null && rv.getMetadata().containsKey("ui_display_order")) {
                  return (Integer) rv.getMetadata().get("ui_display_order");
                }
                return Integer.MAX_VALUE;
              }));
    }

    lastCacheRefresh = LocalDateTime.now();
    log.info("Rule variable cache refresh completed");
  }

  /** Get a rule variable by its ID */
  public RuleVariable getVariableById(String id) {
    return variablesCache.get(id);
  }

  /** Get all rule variables */
  public Collection<RuleVariable> getAllVariables() {
    return variablesCache.values();
  }

  /** Get all variables for a source table */
  public List<RuleVariable> getVariablesBySourceTable(String tableName) {
    return tableVariablesCache.getOrDefault(tableName, Collections.emptyList());
  }

  /** Convert a rule variable entity to a model object */
  private RuleVariable convertToModel(RuleVariableEntity entity) {
    String dataTypeCode = entity.getDataType().getCode();

    // Get supported operators - either from overrides or from data type
    List<String> supportedOperators;
    if (entity.getOperatorOverrides() != null
        && entity.getOperatorOverrides().containsKey("supportedOperators")) {
      supportedOperators = entity.getOperatorOverrides().get("supportedOperators");
    } else {
      supportedOperators = operatorRegistryDb.getDefaultSupportedOperators(dataTypeCode);
    }

    return RuleVariable.builder()
        .id(entity.getCode())
        .name(entity.getDisplayName())
        .description(entity.getDescription())
        .type(dataTypeCode)
        .category(
            entity
                .getSourceTable()
                .getTableName()) // Use table name as category for backward compatibility
        .aggregatable(entity.isAggregatable())
        .supportedOperators(supportedOperators)
        .metadata((Map<String, Object>) entity.getMetadata())
        .build();
  }
}
