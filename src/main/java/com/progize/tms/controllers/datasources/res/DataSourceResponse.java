package com.progize.tms.controllers.datasources.res;

import java.time.Instant;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/** Response class for data source information. */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DataSourceResponse {
  private Long id;
  private String name;
  private String description;
  private String type;
  private Boolean active;
  private Instant createdAt;
  private LocalDateTime lastSchemaDiscovery;
}
