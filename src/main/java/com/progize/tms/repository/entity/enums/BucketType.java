package com.progize.tms.repository.entity.enums;

public enum BucketType {
  SYSTEM(1), // For system-level variables
  LOOKUP(2), // For lookup variables from database tables
  RULE_BUCKET(3), // For rule-specific variables (legacy)
  LOOKUP_BUCKET(4); // For system-level lookup variables (legacy)

  private final int value;

  BucketType(int value) {
    this.value = value;
  }

  public int getValue() {
    return value;
  }

  public static BucketType fromValue(int value) {
    for (BucketType type : BucketType.values()) {
      if (type.value == value) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown BucketType value: " + value);
  }
}
