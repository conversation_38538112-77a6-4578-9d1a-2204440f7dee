package com.progize.tms.controller.dto.response;

import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BucketRelationshipResponse {
  private Long id;
  private Long ruleBucketId;
  private String ruleBucketName;
  private Long lookupBucketId;
  private String lookupBucketName;
  private List<JoinConditionResponse> joinConditions;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  @Getter
  @Setter
  public static class JoinConditionResponse {
    private Long ruleBucketVariableId;
    private String ruleBucketVariableName;
    private Long lookupBucketVariableId;
    private String lookupBucketVariableName;
  }
}
