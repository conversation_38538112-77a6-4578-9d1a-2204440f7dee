import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  Divider,
  CircularProgress,
  Container,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  styled,
  useTheme,
  alpha,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import TimelineIcon from '@mui/icons-material/Timeline';
import CaseService from '../../services/CaseService';
import WorkflowService from '../../services/WorkflowService';

// Status color mapping
const statusColors = {
  OPEN: 'error',
  IN_PROGRESS: 'warning',
  UNDER_REVIEW: 'info',
  CLOSED: 'success',
  PENDING: 'default'
};

// Priority color mapping
const priorityColors = {
  HIGH: 'error',
  MEDIUM: 'warning',
  LOW: 'info'
};

// Styled components
const CaseCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  transition: 'all 0.3s cubic-bezier(.25,.8,.25,1)',
  cursor: 'pointer',
  '&:hover': {
    boxShadow: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
    transform: 'translateY(-2px)'
  }
}));

const StatusChip = styled(Chip)(({ theme, color }) => ({
  height: 24,
  fontSize: '0.75rem',
  backgroundColor: alpha(theme.palette[color]?.main || theme.palette.grey[500], 0.1),
  color: theme.palette[color]?.dark || theme.palette.grey[700],
  borderColor: alpha(theme.palette[color]?.main || theme.palette.grey[500], 0.3),
  '& .MuiChip-label': {
    padding: '0 8px',
  }
}));

const PriorityChip = styled(Chip)(({ theme, color }) => ({
  height: 24,
  fontSize: '0.75rem',
  backgroundColor: alpha(theme.palette[color]?.main || theme.palette.grey[500], 0.1),
  color: theme.palette[color]?.dark || theme.palette.grey[700],
  borderColor: alpha(theme.palette[color]?.main || theme.palette.grey[500], 0.3),
  '& .MuiChip-label': {
    padding: '0 8px',
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: 20,
  textTransform: 'none',
  fontWeight: 500,
  boxShadow: 'none',
  '&:hover': {
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
  }
}));

/**
 * Component for displaying a list of cases with filtering and pagination
 */
const CaseList = ({ status = 'OPEN' }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [totalCases, setTotalCases] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);
  const [sortBy, setSortBy] = useState('createdAt');
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [selectedCase, setSelectedCase] = useState(null);
  const [nextActions, setNextActions] = useState([]);
  const [loadingActions, setLoadingActions] = useState(false);

  // Fetch cases on component mount and when status or refreshKey changes
  useEffect(() => {
    const fetchCases = async () => {
      setLoading(true);
      try {
        let data;
        if (status === 'OPEN') {
          // Use workflow API for open cases
          data = await WorkflowService.getOpenCasesWithWorkflow();
          
          // Map the response to include workflowInstance property for consistent handling
          data = data.map(caseItem => ({
            ...caseItem,
            // Create a workflowInstance object from the response data
            workflowInstance: {
              id: caseItem.workflowInstanceId,
              currentStepId: caseItem.currentStepId,
              currentStepName: caseItem.currentStepName
            }
          }));
          
          console.log('Fetched open cases with workflow:', data);
        } else {
          // Use regular case API for other statuses
          data = await CaseService.getCasesByStatus(status);
        }
        setCases(data);
        setTotalCases(data.length);
        setError(null);
      } catch (err) {
        console.error('Error fetching cases:', err);
        setError('Failed to fetch cases. Please try again later.');
        setCases([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCases();
  }, [status, refreshKey]);

  // Handle page change
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  // Handle sort change
  const handleSortChange = (event) => {
    setSortBy(event.target.value);
  };

  // Handle search term change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(1);
  };

  // Handle refresh button click
  const handleRefresh = () => {
    setRefreshKey(prevKey => prevKey + 1);
  };

  // Handle view case click
  const handleViewCase = (caseId) => {
    navigate(`/cases/${caseId}`);
  };

  // Handle opening the next actions menu
  const handleNextActionsClick = async (event, caseItem) => {
    event.stopPropagation();
    setSelectedCase(caseItem);
    setActionMenuAnchor(event.currentTarget);
    setLoadingActions(true);
    
    try {
      // Get available actions for the workflow instance
      const instanceId = caseItem.workflowInstanceId || 
                        (caseItem.workflowInstance && caseItem.workflowInstance.id);
      
      if (instanceId) {
        console.log(`Fetching picklist options for workflow instance: ${instanceId}`);
        // Always use getPicklistOptions to get the available actions
        const options = await WorkflowService.getPicklistOptions(instanceId);
        console.log('Picklist options for menu:', options);
        
        if (options && options.length > 0) {
          setNextActions(options);
        } else {
          console.warn('No picklist options found');
          setNextActions([]);
        }
      } else {
        console.warn('No workflow instance ID found');
        setNextActions([]);
      }
    } catch (err) {
      console.error('Error fetching next actions:', err);
      setNextActions([]);
    } finally {
      setLoadingActions(false);
    }
  };

  // Handle closing the next actions menu
  const handleCloseActionsMenu = () => {
    setActionMenuAnchor(null);
  };

  // Handle executing a workflow action
  const handleExecuteAction = async (action) => {
    if (!selectedCase) return;
    
    const instanceId = selectedCase.workflowInstanceId || 
                      (selectedCase.workflowInstance && selectedCase.workflowInstance.id);
    
    if (!instanceId) return;
    
    try {
      console.log('Executing action:', action);
      
      // Check if the action has a toStepId
      if (!action.toStepId) {
        console.error('Action is missing toStepId:', action);
        return;
      }
      
      await WorkflowService.executeAction(instanceId, {
        toStepId: action.toStepId,
        actionName: action.name || "User Action",
        currentUserId: "admin",
        comments: "",
        picklistOptionId: action.id // Use the action ID as the picklist option ID if available
      });
      
      // Refresh the case list after action execution
      setRefreshKey(prevKey => prevKey + 1);
      handleCloseActionsMenu();
    } catch (err) {
      console.error('Error executing action:', err);
    }
  };

  // Filter cases based on search term
  const filteredCases = cases.filter(caseItem => 
    caseItem.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    caseItem.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    String(caseItem.id).includes(searchTerm)
  );

  // Sort cases
  const sortedCases = [...filteredCases].sort((a, b) => {
    if (sortBy === 'priority') {
      const priorityOrder = { HIGH: 1, MEDIUM: 2, LOW: 3 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    } else if (sortBy === 'createdAt') {
      return new Date(b.createdAt) - new Date(a.createdAt);
    } else if (sortBy === 'lastUpdated') {
      return new Date(b.lastUpdated) - new Date(a.lastUpdated);
    }
    return 0;
  });

  // Calculate pagination
  const paginatedCases = sortedCases.slice(
    (page - 1) * rowsPerPage,
    (page - 1) * rowsPerPage + rowsPerPage
  );

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get relative time for display
  const getRelativeTime = (dateString) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffDay > 0) {
      return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else if (diffHour > 0) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffMin > 0) {
      return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          {status === 'OPEN' ? 'Open Cases' : status === 'CLOSED' ? 'Closed Cases' : 'All Cases'}
        </Typography>
        <Box>
          <ActionButton 
            variant="outlined" 
            startIcon={<RefreshIcon />} 
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </ActionButton>
          <ActionButton 
            variant="contained" 
            startIcon={<FilterListIcon />}
            color="primary"
          >
            Filter
          </ActionButton>
        </Box>
      </Box>

      <Card sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
        <CardContent sx={{ bgcolor: alpha(theme.palette.primary.light, 0.05) }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h6" color="text.secondary">Total {status.toLowerCase()} cases</Typography>
                <Typography variant="h3" color="primary.main">{totalCases}</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 1, borderLeft: { xs: 'none', md: `1px solid ${alpha(theme.palette.divider, 0.2)}` }, borderRight: { xs: 'none', md: `1px solid ${alpha(theme.palette.divider, 0.2)}` } }}>
                <Typography variant="h6" color="text.secondary">High Priority</Typography>
                <Typography variant="h3" color="error.main">
                  {cases.filter(c => c.priority === 'HIGH').length}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h6" color="text.secondary">Average Resolution Time</Typography>
                <Typography variant="h3" color="text.primary">2.3 days</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search cases by ID, title, or description..."
          value={searchTerm}
          onChange={handleSearchChange}
          size="small"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            sx: { borderRadius: 2 }
          }}
        />
        <FormControl sx={{ minWidth: 200 }} size="small">
          <InputLabel id="sort-by-label">Sort By</InputLabel>
          <Select
            labelId="sort-by-label"
            id="sort-by"
            value={sortBy}
            label="Sort By"
            onChange={handleSortChange}
            sx={{ borderRadius: 2 }}
          >
            <MenuItem value="createdAt">Newest First</MenuItem>
            <MenuItem value="lastUpdated">Recently Updated</MenuItem>
            <MenuItem value="priority">Priority</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Paper sx={{ p: 3, textAlign: 'center', color: 'error.main', borderRadius: 2 }}>
          {error}
        </Paper>
      ) : filteredCases.length === 0 ? (
        <Paper 
          sx={{ 
            p: 5, 
            textAlign: 'center', 
            borderRadius: 2,
            bgcolor: alpha(theme.palette.background.paper, 0.6),
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <FolderOpenIcon sx={{ fontSize: 48, color: theme.palette.text.disabled, mb: 2 }} />
          <Typography variant="h6" sx={{ mb: 1, fontWeight: 500 }}>
            No cases found
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Try adjusting your search or filters
          </Typography>
        </Paper>
      ) : (
        <>
          <Box>
            {paginatedCases.map((caseItem) => (
              <CaseCard 
                key={caseItem.id} 
                onClick={() => handleViewCase(caseItem.id)}
              >
                <Box 
                  sx={{ 
                    height: 6, 
                    bgcolor: caseItem.priority && priorityColors[caseItem.priority] ? 
                      theme.palette[priorityColors[caseItem.priority]].main : 
                      theme.palette.grey[500]
                  }} 
                />
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 500, mb: 0.5 }}>
                        {caseItem.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1.5 }}>
                        Case #{caseItem.id} • Rule: {caseItem.ruleName || 'Unknown Rule'}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <StatusChip 
                        label={caseItem.status} 
                        color={statusColors[caseItem.status] || 'default'} 
                        size="small"
                        variant="outlined"
                      />
                      <PriorityChip 
                        label={caseItem.priority || 'MEDIUM'} 
                        color={priorityColors[caseItem.priority] || 'default'} 
                        size="small"
                        variant="outlined"
                        icon={<PriorityHighIcon fontSize="small" />}
                      />
                      {(caseItem.currentStepName || (caseItem.workflowInstance && caseItem.workflowInstance.currentStepName)) && (
                        <Tooltip title="Current Workflow Step">
                          <Chip
                            label={(caseItem.currentStepName) || 
                                  (caseItem.workflowInstance && caseItem.workflowInstance.currentStepName) || 
                                  'Unknown Step'}
                            size="small"
                            variant="outlined"
                            color="info"
                            icon={<TimelineIcon fontSize="small" />}
                          />
                        </Tooltip>
                      )}
                    </Box>
                  </Box>
                  
                  {caseItem.description && (
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      mb: 2,
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {caseItem.description}
                    </Typography>
                  )}
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, color: 'text.disabled' }} />
                        <Typography variant="caption" color="text.secondary">
                          Created {getRelativeTime(caseItem.createdAt)}
                        </Typography>
                      </Box>
                      {caseItem.lastUpdated && (
                        <Typography variant="caption" color="text.secondary">
                          Updated {getRelativeTime(caseItem.lastUpdated)}
                        </Typography>
                      )}
                      {(caseItem.currentStepName || (caseItem.workflowInstance && caseItem.workflowInstance.currentStepName)) && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <TimelineIcon fontSize="small" sx={{ mr: 0.5, color: 'text.disabled' }} />
                          <Typography variant="caption" color="text.secondary">
                            Step: {(caseItem.currentStepName) || 
                                  (caseItem.workflowInstance && caseItem.workflowInstance.currentStepName) || 
                                  'Unknown'}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {(caseItem.workflowInstanceId || (caseItem.workflowInstance && caseItem.workflowInstance.id)) && (
                        <Tooltip title="View Next Actions">
                          <IconButton 
                            color="primary" 
                            size="small"
                            onClick={(e) => handleNextActionsClick(e, caseItem)}
                            sx={{ 
                              bgcolor: alpha(theme.palette.success.main, 0.1),
                              '&:hover': {
                                bgcolor: alpha(theme.palette.success.main, 0.2),
                              }
                            }}
                          >
                            <PlayArrowIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      <IconButton 
                        color="primary" 
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewCase(caseItem.id);
                        }}
                        sx={{ 
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.2),
                          }
                        }}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>
                </CardContent>
              </CaseCard>
            ))}
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination 
              count={Math.ceil(filteredCases.length / rowsPerPage)} 
              page={page}
              onChange={handlePageChange}
              color="primary"
              shape="rounded"
            />
          </Box>

          {/* Next Actions Menu */}
          <Menu
            anchorEl={actionMenuAnchor}
            open={Boolean(actionMenuAnchor)}
            onClose={handleCloseActionsMenu}
            PaperProps={{
              sx: {
                mt: 1.5,
                width: 250,
                borderRadius: 2,
                boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
              }
            }}
          >
            <Typography variant="subtitle2" sx={{ px: 2, py: 1, fontWeight: 600 }}>
              Available Actions
            </Typography>
            <Divider />
            {loadingActions ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : nextActions.length === 0 ? (
              <MenuItem disabled>
                <ListItemText primary="No actions available" />
              </MenuItem>
            ) : (
              nextActions.map((action) => (
                <MenuItem 
                  key={action.id} 
                  onClick={() => handleExecuteAction(action)}
                  sx={{ py: 1 }}
                >
                  <ListItemIcon>
                    <ArrowForwardIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary={action.name} 
                    secondary={action.description || 'Execute this action'}
                  />
                </MenuItem>
              ))
            )}
          </Menu>
        </>
      )}
    </Container>
  );
};

export default CaseList;
