import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  Paper,
  Divider,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  FormControlLabel,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { testRule } from '../../services/ruleTestingService';

/**
 * Component for testing rules against sample transaction data.
 * Allows users to input sample data, test the rule, and view results.
 */
const RuleTestingPanel = ({ ruleCondition, fields = [], bucketId }) => {
  // Get a list of enriched variables from the fields
  const getEnrichedVariables = () => {
    if (!fields || !Array.isArray(fields)) return [];

    return fields.filter(field => field && field.isEnriched).map(field => ({
      name: field.name,
      sourceBucket: field.sourceBucketName || 'lookup bucket',
      dataType: field.dataTypeCode || field.type || 'unknown'
    }));
  };

  // Generate sample data based on bucket variables
  const generateSampleData = () => {
    const sampleData = {};

    // Process each field from the bucket variables
    fields.forEach(field => {
      // Generate appropriate sample value based on data type
      let sampleValue;
      const dataType = field.dataTypeCode || field.type || 'string';

      switch(dataType.toLowerCase()) {
        case 'number':
        case 'bigdecimal':
        case 'double':
        case 'float':
          sampleValue = 1000.50;
          break;
        case 'integer':
        case 'int':
        case 'long':
          sampleValue = 100;
          break;
        case 'boolean':
          sampleValue = true;
          break;
        case 'date':
        case 'datetime':
          sampleValue = new Date().toISOString();
          break;
        default: // string or any other type
          sampleValue = `Sample ${field.name}`;
      }

      // Use the field name as the key
      sampleData[field.name] = sampleValue;

      // Add a comment about the field source if it's enriched
      if (field.isEnriched) {
        console.log(`Field ${field.name} is enriched from ${field.sourceBucketName || 'a lookup bucket'}`);
      }
    });

    // Add a note about enriched variables
    if (getEnrichedVariables().length > 0) {
      sampleData['__note'] = 'Enriched variables from lookup buckets will be automatically included by the backend';
    }

    return sampleData;
  };

  const [sampleDataString, setSampleDataString] = useState(() => {
    const initialData = generateSampleData();
    return JSON.stringify(initialData, null, 2);
  });

  const [testOptions, setTestOptions] = useState({
    validateSyntax: true,
    validateTypes: true,
    checkPerformance: true,
    includeDebugInfo: true,
    useRealAggregations: false // Default to mock data for safety
  });

  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [error, setError] = useState(null);

  const handleSampleDataChange = (event) => {
    setSampleDataString(event.target.value);
  };

  const handleRunTest = async () => {
    if (!ruleCondition) {
      setError("No rule condition defined. Please create a rule first.");
      return;
    }

    // Parse the sample data from the textarea
    let sampleData;
    try {
      sampleData = JSON.parse(sampleDataString);
    } catch (err) {
      setError("Invalid JSON in sample data. Please correct the format.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await testRule({
        ruleDefinition: ruleCondition,
        sampleData,
        options: testOptions,
        bucketId: bucketId // Pass the bucket ID for context
      });

      setTestResult(result.data);

      // Check if the test failed and provide additional guidance
      if (result.data && result.data.passed === false) {
        // Check if there are any enriched variables in the fields
        const hasEnrichedVariables = fields.some(field => field.isEnriched);

        if (hasEnrichedVariables) {
          // Add a helpful message about enriched variables
          console.log('Rule test failed with enriched variables:', getEnrichedVariables());

          // Check if the backend is properly configured to include enriched variables
          setError(
            "Rule condition failed. The backend should automatically include enriched variables. " +
            "If you're seeing this error, the backend may need to be updated to use getVariablesWithEnrichment() " +
            "instead of getVariables() in the RuleEvaluationService and RuleTestingService."
          );
        }
      }
    } catch (err) {
      console.error('Error testing rule:', err);
      setError(err.response?.data?.message || err.message || 'An error occurred while testing the rule');
    } finally {
      setIsLoading(false);
    }
  };

  // Render different issue types with appropriate severity
  const getIssueSeverity = (type) => {
    switch (type?.toLowerCase()) {
      case 'error': return 'error';
      case 'warning': return 'warning';
      case 'info': return 'info';
      default: return 'info';
    }
  };

  return (
    <Paper elevation={0} sx={{ p: 2, mt: 2 }}>
      <Typography variant="h6" gutterBottom>
        Test Rule
      </Typography>
      <Divider sx={{ mb: 2 }} />

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom>
          Sample Transaction Data (JSON)
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Box sx={{ flex: 1, mr: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Alert severity="info">
              Provide sample data matching the bucket variables. All fields must match the exact data types expected.
            </Alert>
            {getEnrichedVariables().length > 0 ? (
              <Alert severity="info">
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    This rule uses the following enriched variables from lookup buckets:
                  </Typography>
                  <Box component="ul" sx={{ mt: 1, pl: 2 }}>
                    {getEnrichedVariables().map(variable => (
                      <Box component="li" key={variable.name}>
                        <Typography variant="body2">
                          <b>{variable.name}</b> ({variable.dataType}) - from {variable.sourceBucket}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    These variables will be automatically included in rule testing.
                  </Typography>
                </Box>
              </Alert>
            ) : (
              <Alert severity="info">
                If your rule uses enriched variables from lookup buckets, they will be automatically included in rule testing.
              </Alert>
            )}
          </Box>
          <Button
            variant="outlined"
            size="small"
            onClick={() => setSampleDataString(JSON.stringify(generateSampleData(), null, 2))}
            sx={{ alignSelf: 'flex-start', whiteSpace: 'nowrap' }}
          >
            Regenerate Sample
          </Button>
        </Box>
        <TextField
          multiline
          fullWidth
          variant="outlined"
          value={sampleDataString}
          onChange={handleSampleDataChange}
          rows={10}
          sx={{ fontFamily: 'monospace' }}
        />
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom>
          Test Options
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <FormControlLabel
            control={
              <Switch
                checked={testOptions.useRealAggregations}
                onChange={(e) => setTestOptions({
                  ...testOptions,
                  useRealAggregations: e.target.checked
                })}
                color="primary"
              />
            }
            label="Use real aggregation data (connects to ClickHouse)"
          />
        </Box>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <PlayArrowIcon />}
          onClick={handleRunTest}
          disabled={isLoading || !ruleCondition}
        >
          {isLoading ? 'Testing...' : 'Run Test'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {testResult && (
        <Box sx={{ mt: 2 }}>
          <Alert
            severity={testResult.passed ? "success" : "error"}
            sx={{ mb: 2 }}
          >
            {testResult.passed ? "Rule condition passed ✓" : "Rule condition failed ✗"}
            {testResult.explanation && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                {testResult.explanation}
              </Typography>
            )}
          </Alert>

          {/* Display enriched variables used in the rule */}
          {getEnrichedVariables().length > 0 && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Enriched Variables Used in This Rule:
              </Typography>
              <Box component="ul" sx={{ mt: 0.5, pl: 2, mb: 0 }}>
                {getEnrichedVariables().map(variable => (
                  <Box component="li" key={variable.name}>
                    <Typography variant="body2">
                      <b>{variable.name}</b> ({variable.dataType}) - from {variable.sourceBucket}
                    </Typography>
                  </Box>
                ))}
              </Box>
              <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                These variables should be automatically included by the backend during rule testing.
              </Typography>
            </Alert>
          )}

          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1">Generated Expression:</Typography>
            <TextField
              fullWidth
              multiline
              variant="outlined"
              value={testResult.generatedExpression || ''}
              InputProps={{
                readOnly: true,
              }}
              rows={2}
              sx={{ fontFamily: 'monospace', mt: 1 }}
            />
            {testResult.evaluationTime != null && (
              <Typography variant="body2" sx={{ mt: 0.5, fontStyle: 'italic' }}>
                Evaluation time: {testResult.evaluationTime}ms
              </Typography>
            )}
          </Box>

          {testResult.issues && testResult.issues.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Issues:
              </Typography>
              {testResult.issues.map((issue, idx) => (
                <Alert
                  key={idx}
                  severity={getIssueSeverity(issue.type)}
                  sx={{ mb: 1 }}
                >
                  <Typography variant="subtitle2">
                    {issue.category}: {issue.message}
                  </Typography>
                  {issue.location && (
                    <Typography variant="body2" sx={{ mt: 0.5 }}>
                      Location: {issue.location}
                    </Typography>
                  )}
                  {issue.suggestion && (
                    <Typography variant="body2" sx={{ mt: 0.5 }}>
                      Suggestion: {issue.suggestion}
                    </Typography>
                  )}
                </Alert>
              ))}
            </Box>
          )}

          {testResult.analysis && (
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">Analysis</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box>
                  {testResult.analysis.syntaxValid != null && (
                    <Alert
                      severity={testResult.analysis.syntaxValid ? "success" : "error"}
                      sx={{ mb: 2 }}
                    >
                      Syntax: {testResult.analysis.syntaxValid ? "Valid" : "Invalid"}
                    </Alert>
                  )}

                  {testResult.analysis.performance && (
                    <Box sx={{ mb: 2 }}>
                      <Alert
                        severity={
                          testResult.analysis.performance.complexity === 'high' ? 'warning' :
                          testResult.analysis.performance.complexity === 'medium' ? 'info' : 'success'
                        }
                        sx={{ mb: 1 }}
                      >
                        Complexity: {testResult.analysis.performance.complexity}
                      </Alert>

                      {testResult.analysis.performance.recommendations &&
                       testResult.analysis.performance.recommendations.length > 0 && (
                        <Box sx={{ ml: 2, mt: 1 }}>
                          <Typography variant="body2" gutterBottom>
                            Recommendations:
                          </Typography>
                          <ul>
                            {testResult.analysis.performance.recommendations.map((rec, idx) => (
                              <li key={idx}>{rec}</li>
                            ))}
                          </ul>
                        </Box>
                      )}
                    </Box>
                  )}

                  {testResult.analysis.typeCompatibility &&
                   testResult.analysis.typeCompatibility.length > 0 && (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Type Compatibility:
                      </Typography>
                      {testResult.analysis.typeCompatibility.map((tc, idx) => (
                        <Alert
                          key={idx}
                          severity={tc.compatible ? "success" : "error"}
                          sx={{ mb: 1 }}
                        >
                          Field: {tc.field} ({tc.declaredType || 'unknown'}) with operator '{tc.operatorId}'
                          {tc.reason && (
                            <Typography variant="body2" sx={{ mt: 0.5 }}>
                              {tc.reason}
                            </Typography>
                          )}
                        </Alert>
                      ))}
                    </Box>
                  )}
                </Box>
              </AccordionDetails>
            </Accordion>
          )}

          {testResult.debugInfo && (
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">Debug Information</Typography>
              </AccordionSummary>
              <AccordionDetails>
                {testResult.debugInfo.evaluationTrace &&
                 testResult.debugInfo.evaluationTrace.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Evaluation Trace:
                    </Typography>
                    {testResult.debugInfo.evaluationTrace.map((step, idx) => (
                      <Box key={idx} sx={{ mb: 1 }}>
                        <Typography variant="body2">
                          Step {step.step}: {step.expression} = {JSON.stringify(step.value)}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                )}

                {testResult.debugInfo.context && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Evaluation Context:
                    </Typography>
                    <TextField
                      multiline
                      fullWidth
                      variant="outlined"
                      value={JSON.stringify(testResult.debugInfo.context, null, 2)}
                      InputProps={{
                        readOnly: true,
                      }}
                      rows={6}
                      sx={{ fontFamily: 'monospace' }}
                    />
                  </Box>
                )}
              </AccordionDetails>
            </Accordion>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default RuleTestingPanel;
