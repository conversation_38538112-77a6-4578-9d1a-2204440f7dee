package com.progize.tms.config.persistence;

import com.progize.tms.repository.UserRepository;
import java.util.Optional;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class JpaAuditingConfig {

  private final UserRepository userRepository;

  public JpaAuditingConfig(UserRepository userRepository) {
    this.userRepository = userRepository;
  }

  @Bean
  public AuditorAware<Long> auditorProvider() {
    return () -> {
      return Optional.of(1L);
      //      Authentication authentication =
      // SecurityContextHolder.getContext().getAuthentication();
      //      if (authentication == null || !authentication.isAuthenticated()) {
      //        return userRepository.findByUsername("admin");
      //      }
      //      return userRepository.findByUsername(authentication.getName());
    };
  }
}
