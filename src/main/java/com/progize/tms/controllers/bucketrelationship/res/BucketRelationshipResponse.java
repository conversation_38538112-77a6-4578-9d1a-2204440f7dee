package com.progize.tms.controllers.bucketrelationship.res;

import java.time.Instant;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BucketRelationshipResponse {
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getRuleBucketId() {
    return ruleBucketId;
  }

  public void setRuleBucketId(Long ruleBucketId) {
    this.ruleBucketId = ruleBucketId;
  }

  public String getRuleBucketName() {
    return ruleBucketName;
  }

  public void setRuleBucketName(String ruleBucketName) {
    this.ruleBucketName = ruleBucketName;
  }

  public Long getLookupBucketId() {
    return lookupBucketId;
  }

  public void setLookupBucketId(Long lookupBucketId) {
    this.lookupBucketId = lookupBucketId;
  }

  public String getLookupBucketName() {
    return lookupBucketName;
  }

  public void setLookupBucketName(String lookupBucketName) {
    this.lookupBucketName = lookupBucketName;
  }

  public List<JoinConditionResponse> getJoinConditions() {
    return joinConditions;
  }

  public void setJoinConditions(List<JoinConditionResponse> joinConditions) {
    this.joinConditions = joinConditions;
  }

  private Long id;
  private Long ruleBucketId;
  private String ruleBucketName;
  private Long lookupBucketId;
  private String lookupBucketName;
  private List<JoinConditionResponse> joinConditions;
  private Instant createdAt;
  private Instant updatedAt;

  @Getter
  @Setter
  public static class JoinConditionResponse {
    public Long getRuleBucketVariableId() {
      return ruleBucketVariableId;
    }

    public void setRuleBucketVariableId(Long ruleBucketVariableId) {
      this.ruleBucketVariableId = ruleBucketVariableId;
    }

    public String getRuleBucketVariableName() {
      return ruleBucketVariableName;
    }

    public void setRuleBucketVariableName(String ruleBucketVariableName) {
      this.ruleBucketVariableName = ruleBucketVariableName;
    }

    public Long getLookupBucketVariableId() {
      return lookupBucketVariableId;
    }

    public void setLookupBucketVariableId(Long lookupBucketVariableId) {
      this.lookupBucketVariableId = lookupBucketVariableId;
    }

    public String getLookupBucketVariableName() {
      return lookupBucketVariableName;
    }

    public void setLookupBucketVariableName(String lookupBucketVariableName) {
      this.lookupBucketVariableName = lookupBucketVariableName;
    }

    private Long ruleBucketVariableId;
    private String ruleBucketVariableName;
    private Long lookupBucketVariableId;
    private String lookupBucketVariableName;
  }
}
