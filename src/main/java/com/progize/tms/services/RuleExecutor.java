package com.progize.tms.services;

import com.progize.tms.repository.entity.Transaction;
import java.util.List;
import java.util.Map;

/**
 * Service interface responsible for executing rules on transactions. Fetches active rules and
 * applies them to transactions.
 */
public interface RuleExecutor {

  /**
   * Process a list of transactions against all active rules.
   *
   * @param transactions The list of transactions to process
   * @return The list of processed transactions (flagged if they match any rule)
   */
  List<Transaction> processTransactions(List<Transaction> transactions);

  /**
   * Process a single transaction against all active rules.
   *
   * @param transaction The transaction to process
   * @return The processed transaction (flagged if it matches any rule)
   */
  Transaction processTransaction(Transaction transaction);

  /**
   * Process a list of dynamic transactions against all active rules associated with a specific
   * bucket. This method handles transactions where the structure is defined by the bucket's
   * variables.
   *
   * @param bucketId The ID of the data bucket that defines the transaction structure
   * @param transactions List of transaction data maps matching the bucket's variable structure
   * @return The list of processed transaction data with flag status updated based on matching rules
   */
  List<Map<String, Object>> processBucketTransactions(
      Long bucketId, List<Map<String, Object>> transactions);
}
