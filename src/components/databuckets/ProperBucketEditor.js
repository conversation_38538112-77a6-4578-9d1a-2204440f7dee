import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Button,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Grid,
    Card,
    CardContent,
    Alert,
    Breadcrumbs,
    Link,
    Divider,
    CircularProgress
} from '@mui/material';
import {
    ArrowBack as ArrowBackIcon,
    Save as SaveIcon,
    Storage as StorageIcon
} from '@mui/icons-material';
import { dataBucketService } from '../../services/api';

const BUCKET_TYPES = [
    { value: 'RULE_BUCKET', label: 'Rule Bucket', description: 'For rule-specific variables and transaction data' },
    { value: 'LOOKUP', label: 'Lookup Bucket', description: 'For lookup variables from database tables to enrich rule buckets' }
];

const ProperBucketEditor = ({ bucket, dataSources, onCancel, onBucketUpdated }) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);
    
    // Form data
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        bucketType: 'RULE_BUCKET',
        dataSourceId: '',
        tableName: ''
    });
    
    const [formErrors, setFormErrors] = useState({});

    useEffect(() => {
        if (bucket) {
            setFormData({
                name: bucket.name || '',
                description: bucket.description || '',
                bucketType: bucket.bucketType || bucket.type || 'RULE_BUCKET',
                dataSourceId: bucket.dataSourceId || '',
                tableName: bucket.tableName || ''
            });
        }
    }, [bucket]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error for this field
        if (formErrors[field]) {
            setFormErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    const validateForm = () => {
        const errors = {};
        
        if (!formData.name.trim()) {
            errors.name = 'Bucket name is required';
        }
        
        if (!formData.description.trim()) {
            errors.description = 'Description is required';
        }
        
        if (!formData.dataSourceId) {
            errors.dataSourceId = 'Data source is required';
        }
        
        if (!formData.tableName.trim()) {
            errors.tableName = 'Table name is required';
        }
        
        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSave = async () => {
        if (!validateForm()) {
            return;
        }

        try {
            setLoading(true);
            setError(null);

            const updateData = {
                name: formData.name,
                description: formData.description,
                bucketType: formData.bucketType,
                tableName: formData.tableName,
                dataSourceId: formData.dataSourceId
            };

            const result = await dataBucketService.updateBucket(bucket.id, updateData);
            setSuccess(`Bucket "${formData.name}" updated successfully`);
            
            // Notify parent component
            onBucketUpdated(result);
        } catch (err) {
            console.error('Failed to update bucket:', err);
            setError(`Failed to update bucket: ${err.response?.data?.message || err.message}`);
        } finally {
            setLoading(false);
        }
    };

    const getDataSourceName = (dataSourceId) => {
        const dataSource = dataSources.find(ds => ds.id === dataSourceId);
        return dataSource ? dataSource.name : 'Unknown';
    };

    return (
        <Box>
            <Breadcrumbs sx={{ mb: 3 }}>
                <Link color="inherit" href="#" onClick={onCancel} sx={{ textDecoration: 'none' }}>
                    Data Buckets
                </Link>
                <Typography color="text.primary">Edit {bucket?.name}</Typography>
            </Breadcrumbs>

            <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
                Edit Data Bucket
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Update bucket configuration and settings
            </Typography>

            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                    {success}
                </Alert>
            )}

            <Grid container spacing={4}>
                <Grid item xs={12} md={8}>
                    <Card>
                        <CardContent sx={{ p: 4 }}>
                            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                                Basic Information
                            </Typography>
                            
                            <Grid container spacing={3}>
                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Bucket Name"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange('name', e.target.value)}
                                        error={!!formErrors.name}
                                        helperText={formErrors.name}
                                        placeholder="e.g., Customer Transactions"
                                    />
                                </Grid>
                                
                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Description"
                                        value={formData.description}
                                        onChange={(e) => handleInputChange('description', e.target.value)}
                                        error={!!formErrors.description}
                                        helperText={formErrors.description}
                                        multiline
                                        rows={3}
                                        placeholder="Brief description of the bucket purpose"
                                    />
                                </Grid>
                                
                                <Grid item xs={12} md={6}>
                                    <FormControl fullWidth>
                                        <InputLabel>Bucket Type</InputLabel>
                                        <Select
                                            value={formData.bucketType}
                                            onChange={(e) => handleInputChange('bucketType', e.target.value)}
                                            label="Bucket Type"
                                        >
                                            {BUCKET_TYPES.map(type => (
                                                <MenuItem key={type.value} value={type.value}>
                                                    <Box>
                                                        <Typography variant="body2" fontWeight="medium">
                                                            {type.label}
                                                        </Typography>
                                                        <Typography variant="caption" color="text.secondary">
                                                            {type.description}
                                                        </Typography>
                                                    </Box>
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>
                                
                                <Grid item xs={12} md={6}>
                                    <FormControl fullWidth>
                                        <InputLabel>Data Source</InputLabel>
                                        <Select
                                            value={formData.dataSourceId}
                                            onChange={(e) => handleInputChange('dataSourceId', e.target.value)}
                                            error={!!formErrors.dataSourceId}
                                            label="Data Source"
                                        >
                                            {dataSources.map(source => (
                                                <MenuItem key={source.id} value={source.id}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <StorageIcon sx={{ mr: 1 }} />
                                                        <Box>
                                                            <Typography variant="body2">{source.name}</Typography>
                                                            <Typography variant="caption" color="text.secondary">
                                                                {source.type}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                </MenuItem>
                                            ))}
                                        </Select>
                                        {formErrors.dataSourceId && (
                                            <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                                                {formErrors.dataSourceId}
                                            </Typography>
                                        )}
                                    </FormControl>
                                </Grid>
                                
                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Table Name"
                                        value={formData.tableName}
                                        onChange={(e) => handleInputChange('tableName', e.target.value)}
                                        error={!!formErrors.tableName}
                                        helperText={formErrors.tableName}
                                        placeholder="e.g., customer_transactions"
                                    />
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Grid>
                
                <Grid item xs={12} md={4}>
                    <Card>
                        <CardContent sx={{ p: 3 }}>
                            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                                Bucket Information
                            </Typography>
                            
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="caption" color="text.secondary" display="block">
                                    Bucket ID
                                </Typography>
                                <Typography variant="body2" fontWeight="medium">
                                    {bucket?.id}
                                </Typography>
                            </Box>
                            
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="caption" color="text.secondary" display="block">
                                    Current Data Source
                                </Typography>
                                <Typography variant="body2" fontWeight="medium">
                                    {getDataSourceName(bucket?.dataSourceId)}
                                </Typography>
                            </Box>
                            
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="caption" color="text.secondary" display="block">
                                    Current Table
                                </Typography>
                                <Typography variant="body2" fontWeight="medium">
                                    {bucket?.tableName || 'Not specified'}
                                </Typography>
                            </Box>
                            
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="caption" color="text.secondary" display="block">
                                    Created Date
                                </Typography>
                                <Typography variant="body2" fontWeight="medium">
                                    {bucket?.createdAt ? new Date(bucket.createdAt).toLocaleDateString() : 'Unknown'}
                                </Typography>
                            </Box>
                            
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="caption" color="text.secondary" display="block">
                                    Last Updated
                                </Typography>
                                <Typography variant="body2" fontWeight="medium">
                                    {bucket?.updatedAt ? new Date(bucket.updatedAt).toLocaleDateString() : 'Unknown'}
                                </Typography>
                            </Box>
                            
                            <Divider sx={{ my: 2 }} />
                            
                            <Alert severity="info" sx={{ mt: 2 }}>
                                <Typography variant="body2">
                                    <strong>Note:</strong> Changing the data source or table may affect existing variables and rules.
                                </Typography>
                            </Alert>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                    variant="outlined"
                    startIcon={<ArrowBackIcon />}
                    onClick={onCancel}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button
                    variant="contained"
                    startIcon={<SaveIcon />}
                    onClick={handleSave}
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <CircularProgress size={20} sx={{ mr: 1 }} />
                            Saving...
                        </>
                    ) : (
                        'Save Changes'
                    )}
                </Button>
            </Box>
        </Box>
    );
};

export default ProperBucketEditor;
