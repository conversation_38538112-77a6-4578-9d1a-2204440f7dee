package com.progize.tms.repository.entity;

import com.progize.tms.repository.entity.base.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.envers.Audited;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "workflow_cases")
@Audited
public class WorkflowCase extends BaseEntity {

  @Column(name = "case_number", unique = true, nullable = false)
  private String caseNumber;

  @Column(name = "workflow_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private WorkflowType workflowType;

  @Column(name = "status", nullable = false)
  private String status;

  @Column(name = "process_instance_id")
  private String processInstanceId;

  @Column(name = "priority")
  private Integer priority;

  @Column(name = "due_date")
  private LocalDateTime dueDate;

  @Column(name = "entity_type", nullable = false)
  private String entityType;

  @Column(name = "entity_id", nullable = true)
  private Long entityId;

  @Column(name = "customer_id", nullable = true)
  private Long customerId;

  @Column(name = "rule_id", nullable = true)
  private Long ruleId;

  @Column(name = "final_outcome")
  private String finalOutcome;

  @Column(name = "summary", columnDefinition = "json")
  private String summary;

  @Column(name = "metadata", columnDefinition = "json")
  private String metadata;
}
