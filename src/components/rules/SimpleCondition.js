import React from 'react';
import {
  Box,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Tooltip,
  useTheme
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import InfoIcon from '@mui/icons-material/Info';
import ExpressionInput from '../common/ExpressionInput';

const SimpleCondition = ({
  condition,
  onChange,
  onRemove,
  fields,
  operators,
  isEditable = true,
  getFieldDisplayName
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // When editing, show only fields from the current category tab
  // When viewing, we need all fields to show the correct name
  const displayFields = fields;

  // Find the selected field object
  const selectedField = fields.find(field => field.id === condition.fieldId) ||
                        fields.find(field => field.name === condition.field) || {};

  // Use dataTypeCode if available, otherwise fall back to type
  const fieldType = selectedField.dataTypeCode || selectedField.type || 'string';

  // Filter operators based on field's supported operators if available
  const applicableOperators = operators.filter(op => {
    // If the field has explicit supportedOperators, use that
    if (selectedField.supportedOperators && selectedField.supportedOperators.length > 0) {
      return selectedField.supportedOperators.includes(op.id);
    }

    // Fallback to type-based filtering if no supportedOperators
    return op.applicableTypes && op.applicableTypes.includes(fieldType);
  });

  console.log(`Field: ${condition.fieldId || condition.field}, Type: ${fieldType}, Supported operators:`,
    selectedField.supportedOperators || 'Using type-based filtering');

  const handleFieldChange = (e) => {
    console.log('Field change event:', e.target.value);

    // Get the new field type
    const newFieldId = e.target.value;
    const newField = fields.find(field => field.id === newFieldId);
    const newFieldType = newField?.dataTypeCode || newField?.type || 'string';

    // Find a compatible operator for the new field type
    const compatibleOperators = operators.filter(op => {
      // If the field has explicit supportedOperators, use that
      if (newField.supportedOperators && newField.supportedOperators.length > 0) {
        return newField.supportedOperators.includes(op.id);
      }

      // Fallback to type-based filtering if no supportedOperators
      return op.applicableTypes && op.applicableTypes.includes(newFieldType);
    });

    console.log('Field type:', newFieldType);
    console.log('Compatible operators:', compatibleOperators);

    // Select the first compatible operator, if none found use empty string
    const newOperator = compatibleOperators.length > 0 ? compatibleOperators[0].id : '';

    // Create a completely new condition object to avoid reference issues
    // CRITICAL: Make sure id is preserved from the original condition
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      field: newField?.name, // Use the name field for rule creation
      fieldId: newFieldId, // Store the field ID for UI selection
      operator: newOperator,
      value: '' // Reset value when field changes
    };

    console.log('Updated condition with new field:', updatedCondition);
    onChange(updatedCondition);
  };

  const handleOperatorChange = (e) => {
    console.log('Operator change event:', e.target.value);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      operator: e.target.value
    };

    console.log('Updated condition with new operator:', updatedCondition);
    onChange(updatedCondition);
  };

  const handleValueChange = (e) => {
    console.log('Value change event:', e.target.value);

    // Create a completely new condition object to avoid reference issues
    const updatedCondition = {
      ...condition,
      id: condition.id, // Explicitly preserve the ID
      value: e.target.value,
      // Add a flag to identify if this is an expression value
      isExpression: e.target.value && e.target.value.includes('${')
    };

    console.log('Updated condition with new value:', updatedCondition);
    onChange(updatedCondition);
  };

  // Get a human-readable field name with category info if needed
  const displayFieldName = getFieldDisplayName ?
    getFieldDisplayName(condition.field) :
    (selectedField?.name || condition.field);

  // All fields are considered in the current tab since we've removed tabs
  const isFieldInCurrentTab = true;

  // If we have a field name but no fieldId, try to find the matching field and update the fieldId
  React.useEffect(() => {
    if (condition.field && !condition.fieldId && fields.length > 0) {
      const matchingField = fields.find(field => field.name === condition.field);
      if (matchingField) {
        // Update the condition with the fieldId
        onChange({
          ...condition,
          fieldId: matchingField.id
        });
      }
    }
  }, [condition, fields, onChange]);

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        position: 'relative',
        borderLeft: '4px solid #805AD5',
        backgroundColor: theme.palette.background.paper,
        opacity: isEditable ? 1 : 0.8
      }}
    >
      <Box sx={{ position: 'absolute', top: 8, right: 8, display: 'flex' }}>
        <IconButton
          size="small"
          color="error"
          onClick={onRemove}
        >
          <DeleteIcon />
        </IconButton>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          Simple Condition
        </Typography>
        <Tooltip title="Define a condition based on a single variable and value">
          <IconButton size="small" color="info">
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth size="small">
            <InputLabel>Field</InputLabel>
            <Select
              value={condition.fieldId || ''}
              label="Field"
              onChange={(e) => {
                console.log('Direct field change event:', e.target.value);
                handleFieldChange(e);
              }}
              disabled={!isEditable}
            >
              {displayFields.map(field => (
                <MenuItem
                  key={field.id}
                  value={field.id}
                  sx={{
                    // Add a subtle background color for enriched variables
                    backgroundColor: Boolean(field.isEnriched) ? 'rgba(0, 128, 0, 0.05)' : 'inherit',
                    // Add a left border based on variable type
                    borderLeft: Boolean(field.isEnriched) ? '3px solid #4CAF50' : '3px solid #3182CE'
                  }}
                  // Add onClick to log field details for debugging
                  onClick={(e) => {
                    // Don't interfere with normal selection behavior
                    if (e.ctrlKey || e.metaKey) {
                      e.stopPropagation();
                      console.log('Field details:', field);
                      console.log('isEnriched value:', field.isEnriched);
                      console.log('sourceBucketName:', field.sourceBucketName);
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Box sx={{ flexGrow: 1 }}>
                      {field.name}
                      {field.category && ` (${field.category})`}
                    </Box>
                    {Boolean(field.isEnriched) ? (
                      <Tooltip title={field.sourceBucketName ? `Enriched from ${field.sourceBucketName}` : 'Enriched variable'}>
                        <Box
                          sx={{
                            ml: 1,
                            fontSize: '0.75rem',
                            color: 'success.main',
                            border: '1px solid',
                            borderColor: 'success.main',
                            borderRadius: '4px',
                            px: 0.5,
                            py: 0.1,
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          Enriched
                        </Box>
                      </Tooltip>
                    ) : (
                      <Tooltip title="Variable from main bucket">
                        <Box
                          sx={{
                            ml: 1,
                            fontSize: '0.75rem',
                            color: 'primary.main',
                            border: '1px solid',
                            borderColor: 'primary.main',
                            borderRadius: '4px',
                            px: 0.5,
                            py: 0.1,
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          Main
                        </Box>
                      </Tooltip>
                    )}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth size="small">
            <InputLabel>Operator</InputLabel>
            <Select
              value={condition.operator || ''}
              label="Operator"
              onChange={(e) => {
                console.log('Direct operator change event:', e.target.value);
                handleOperatorChange(e);
              }}
              disabled={!isEditable}
            >
              {applicableOperators.map(op => (
                <MenuItem key={op.id} value={op.id}>
                  {op.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <ExpressionInput
            label="Value"
            value={condition.value || ''}
            onChange={(e) => {
              console.log('Direct value change event:', e.target.value);
              handleValueChange(e);
            }}
            // Pass the field type to filter compatible variables
            fieldType={fieldType}
            // Always use text type for expression input, regardless of field type
            type="text"
            disabled={!isEditable}
          />
        </Grid>
      </Grid>

      {/* Display description of the condition */}
      <Box sx={{
        mt: 2,
        p: 1,
        backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
        borderRadius: 1
      }}>
        <Typography variant="body2" color="textSecondary">
          {displayFieldName} {selectedField?.dataTypeCode === 'number' ? condition.operator : (applicableOperators.find(op => op.id === condition.operator)?.name || condition.operator)} {condition.value}
        </Typography>
      </Box>
    </Paper>
  );
};

export default SimpleCondition;
