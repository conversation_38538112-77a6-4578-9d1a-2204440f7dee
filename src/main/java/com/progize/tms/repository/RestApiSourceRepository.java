package com.progize.tms.repository;

import com.progize.tms.repository.entity.DataSourceEntity;
import com.progize.tms.repository.entity.RestApiSourceEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Repository for RestApiSourceEntity. */
@Repository
public interface RestApiSourceRepository extends JpaRepository<RestApiSourceEntity, Long> {
  /**
   * Find REST API source by data source entity.
   *
   * @param dataSource Data source entity
   * @return Optional REST API source entity
   */
  Optional<RestApiSourceEntity> findByDataSource(DataSourceEntity dataSource);

  /**
   * Find REST API source by data source ID.
   *
   * @param dataSourceId Data source ID
   * @return Optional REST API source entity
   */
  Optional<RestApiSourceEntity> findByDataSourceId(Long dataSourceId);

  /**
   * Delete REST API source by data source ID.
   *
   * @param dataSourceId Data source ID
   */
  void deleteByDataSourceId(Long dataSourceId);
}
