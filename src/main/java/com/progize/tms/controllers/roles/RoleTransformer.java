package com.progize.tms.controllers.roles;

import com.progize.tms.controllers.roles.req.CreateRoleRequest;
import com.progize.tms.controllers.roles.req.UpdateRoleRequest;
import com.progize.tms.controllers.roles.res.RoleResponse;
import com.progize.tms.repository.entity.Permission;
import com.progize.tms.repository.entity.Role;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class RoleTransformer {

  /** Transform a Role entity to a RoleResponse DTO */
  public RoleResponse toRoleResponse(Role role) {
    if (role == null) {
      return null;
    }

    // Create a new ArrayList to avoid ConcurrentModificationException
    List<Permission> permissions = new ArrayList<>(role.getPermissions());

    return RoleResponse.builder()
        .id(role.getId())
        .name(role.getName())
        .description(role.getDescription())
        .permissions(permissions.stream().map(this::toPermissionDto).collect(Collectors.toSet()))
        .build();
  }

  /** Transform a list of Role entities to a list of RoleResponse DTOs */
  public List<RoleResponse> toRoleResponseList(List<Role> roles) {
    return roles.stream().map(this::toRoleResponse).collect(Collectors.toList());
  }

  /** Transform a CreateRoleRequest DTO to a Role entity */
  public Role toEntity(CreateRoleRequest request) {
    if (request == null) {
      return null;
    }

    return Role.builder().name(request.getName()).description(request.getDescription()).build();
  }

  /** Update a Role entity from an UpdateRoleRequest DTO */
  public void updateEntity(Role role, UpdateRoleRequest request) {
    if (role == null || request == null) {
      return;
    }

    role.setName(request.getName());
    role.setDescription(request.getDescription());
  }

  private RoleResponse.PermissionDto toPermissionDto(Permission permission) {
    if (permission == null) {
      return null;
    }

    return RoleResponse.PermissionDto.builder()
        .id(permission.getId())
        .name(permission.getName())
        .description(permission.getDescription())
        // Don't include roles to avoid circular reference
        .build();
  }
}
