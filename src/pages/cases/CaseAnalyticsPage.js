import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Divider
} from '@mui/material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';
import CaseService from '../../services/CaseService';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

/**
 * Page component for displaying case analytics and metrics
 */
const CaseAnalyticsPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [caseData, setCaseData] = useState({
    openCases: 0,
    closedCases: 0,
    totalCases: 0,
    avgResolutionTime: 0,
    casesByStatus: {},
    casesByPriority: {},
    casesTrend: [],
    topRules: []
  });

  // Fetch analytics data when the component mounts
  useEffect(() => {
    // In a real application, this would call an API endpoint
    // For now, we'll simulate the data
    const fetchAnalytics = async () => {
      setLoading(true);
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        setCaseData({
          openCases: 42,
          closedCases: 128,
          totalCases: 170,
          avgResolutionTime: 2.3, // days
          casesByStatus: {
            'OPEN': 28,
            'IN_PROGRESS': 14,
            'UNDER_REVIEW': 8,
            'CLOSED': 120
          },
          casesByPriority: {
            'HIGH': 15,
            'MEDIUM': 47,
            'LOW': 108
          },
          casesTrend: [
            { date: 'Jan', open: 12, closed: 8 },
            { date: 'Feb', open: 18, closed: 14 },
            { date: 'Mar', open: 15, closed: 20 },
            { date: 'Apr', open: 25, closed: 18 },
            { date: 'May', open: 22, closed: 30 },
            { date: 'Jun', open: 30, closed: 38 }
          ],
          topRules: [
            { ruleName: 'High Value Transaction', count: 45 },
            { ruleName: 'Suspicious Location', count: 32 },
            { ruleName: 'Rapid Succession', count: 28 },
            { ruleName: 'New Account Large Transfer', count: 21 },
            { ruleName: 'Multiple Failed Attempts', count: 18 }
          ]
        });
        setError(null);
      } catch (err) {
        setError('Failed to fetch analytics data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  // Prepare chart data
  const statusChartData = {
    labels: Object.keys(caseData.casesByStatus),
    datasets: [
      {
        label: 'Cases by Status',
        data: Object.values(caseData.casesByStatus),
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(255, 159, 64, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(75, 192, 192, 0.6)'
        ],
        borderColor: [
          'rgb(255, 99, 132)',
          'rgb(255, 159, 64)',
          'rgb(54, 162, 235)',
          'rgb(75, 192, 192)'
        ],
        borderWidth: 1
      }
    ]
  };

  const priorityChartData = {
    labels: Object.keys(caseData.casesByPriority),
    datasets: [
      {
        label: 'Cases by Priority',
        data: Object.values(caseData.casesByPriority),
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(255, 159, 64, 0.6)',
          'rgba(75, 192, 192, 0.6)'
        ],
        borderColor: [
          'rgb(255, 99, 132)',
          'rgb(255, 159, 64)',
          'rgb(75, 192, 192)'
        ],
        borderWidth: 1
      }
    ]
  };

  const trendChartData = {
    labels: caseData.casesTrend.map(item => item.date),
    datasets: [
      {
        label: 'New Cases',
        data: caseData.casesTrend.map(item => item.open),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        tension: 0.1
      },
      {
        label: 'Closed Cases',
        data: caseData.casesTrend.map(item => item.closed),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        tension: 0.1
      }
    ]
  };

  const topRulesChartData = {
    labels: caseData.topRules.map(rule => rule.ruleName),
    datasets: [
      {
        label: 'Case Count',
        data: caseData.topRules.map(rule => rule.count),
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgb(54, 162, 235)',
        borderWidth: 1
      }
    ]
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: '',
      },
    },
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Case Analytics
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Total Cases
              </Typography>
              <Typography variant="h3">
                {caseData.totalCases}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Open Cases
              </Typography>
              <Typography variant="h3" color="error.main">
                {caseData.openCases}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Closed Cases
              </Typography>
              <Typography variant="h3" color="success.main">
                {caseData.closedCases}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Avg. Resolution Time
              </Typography>
              <Typography variant="h3">
                {caseData.avgResolutionTime} days
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Cases Trend Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Case Trend (Last 6 Months)
            </Typography>
            <Box sx={{ height: 'calc(100% - 30px)' }}>
              <Line options={chartOptions} data={trendChartData} />
            </Box>
          </Paper>
        </Grid>

        {/* Cases by Status Chart */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Cases by Status
            </Typography>
            <Box sx={{ height: 'calc(100% - 30px)', display: 'flex', justifyContent: 'center' }}>
              <Pie options={chartOptions} data={statusChartData} />
            </Box>
          </Paper>
        </Grid>

        {/* Top Rules Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Top 5 Rules Triggering Cases
            </Typography>
            <Box sx={{ height: 'calc(100% - 30px)' }}>
              <Bar options={chartOptions} data={topRulesChartData} />
            </Box>
          </Paper>
        </Grid>

        {/* Cases by Priority Chart */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Cases by Priority
            </Typography>
            <Box sx={{ height: 'calc(100% - 30px)', display: 'flex', justifyContent: 'center' }}>
              <Pie options={chartOptions} data={priorityChartData} />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CaseAnalyticsPage;
