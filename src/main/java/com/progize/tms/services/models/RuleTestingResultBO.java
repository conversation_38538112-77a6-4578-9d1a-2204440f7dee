package com.progize.tms.services.models;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * Business object containing rule testing results. Used in the service layer to maintain separation
 * from REST DTOs.
 */
@Data
public class RuleTestingResultBO {
  private boolean passed;
  private String generatedExpression;
  private long evaluationTime;
  private String explanation;
  private AnalysisResult analysis;
  private List<Issue> issues = new ArrayList<>();
  private DebugInfo debugInfo;

  @Data
  public static class AnalysisResult {
    private boolean syntaxValid;
    private List<TypeCompatibility> typeCompatibility = new ArrayList<>();
    private PerformanceAnalysis performance;
  }

  @Data
  public static class TypeCompatibility {
    private String field;
    private String declaredType;
    private String operatorId;
    private boolean compatible;
    private String reason;
  }

  @Data
  public static class PerformanceAnalysis {
    private String complexity;
    private List<String> recommendations = new ArrayList<>();
  }

  @Data
  public static class Issue {
    private String type; // INFO, WARNING, ERROR
    private String category;
    private String message;
    private String location;
    private String suggestion;
  }

  @Data
  public static class DebugInfo {
    private Map<String, Object> context;
    private List<EvaluationStep> evaluationTrace = new ArrayList<>();
  }

  @Data
  public static class EvaluationStep {
    private int step;
    private String expression;
    private Object value;
  }
}
