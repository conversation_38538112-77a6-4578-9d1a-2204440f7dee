import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Button,
  Typography,
  TextField,
  Select,
  MenuItem,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import apiClient from '../services/ApiService';

const WorkflowTestPage = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [processInstanceId, setProcessInstanceId] = useState(null);
  
  // Form states
  const [transactionId, setTransactionId] = useState(`TX-${Date.now()}`);
  const [ruleId, setRuleId] = useState(`RULE-${Math.floor(Math.random() * 1000)}`);
  const [decision, setDecision] = useState('');
  const [comments, setComments] = useState('');
  const [files, setFiles] = useState([]);

  // Generate dummy transaction details
  const generateTransactionDetails = () => ({
    amount: Math.floor(Math.random() * 1000000) / 100, // Random amount between 0 and 10000
    currency: 'USD',
    timestamp: new Date().toISOString(),
    customerName: 'John Doe',
    customerId: `CUST-${Math.floor(Math.random() * 10000)}`,
    accountNumber: `ACC-${Math.floor(Math.random() * 100000)}`,
    transactionType: 'WIRE_TRANSFER',
    sourceCountry: 'US',
    destinationCountry: 'GB',
    riskScore: Math.floor(Math.random() * 100)
  });

  // Start workflow
  const handleStartWorkflow = async () => {
    try {
      const transactionDetails = generateTransactionDetails();

      const url = `/api/workflow/compliance/start?transactionId=${encodeURIComponent(transactionId)}&ruleId=${encodeURIComponent(ruleId)}`;
      
      const response = await apiClient.post(url, transactionDetails);

      // Axios throws on non-200 responses, no need to check response.ok

      const processId = response.data;
      setProcessInstanceId(processId);
      loadTasks();
    } catch (error) {
      console.error('Error starting workflow:', error);
      // Show error to user
      setError(error.message);
    }
  };

  // Load tasks
  const loadTasks = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get('/api/workflow/compliance/tasks');
      // Axios throws on non-200 responses, no need to check response.ok
      const data = response.data;
      setTasks(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error('Error loading tasks:', err);
      setError(err.message);
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // Load tasks on mount
  useEffect(() => {
    loadTasks();
  }, []);

  // Complete officer review
  const handleOfficerReview = async () => {
    const formData = new FormData();
    formData.append('decision', decision);
    formData.append('comments', comments);
    files.forEach(file => formData.append('attachments', file));

    await apiClient.post(`/api/workflow/compliance/tasks/${selectedTask.id}/officer-review`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    loadTasks();
  };

  // Complete manager review
  const handleManagerReview = async () => {
    const formData = new FormData();
    formData.append('managerDecision', decision);
    formData.append('managerComments', comments);
    files.forEach(file => formData.append('attachments', file));

    await apiClient.post(`/api/workflow/compliance/tasks/${selectedTask.id}/manager-review`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    loadTasks();
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Workflow Test Interface
      </Typography>
      
      {/* Start Workflow Form */}
      <Paper elevation={3} sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Start New Workflow
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            label="Transaction ID"
            value={transactionId}
            onChange={(e) => setTransactionId(e.target.value)}
            helperText="Auto-generated for testing"
            disabled
          />
          <TextField
            label="Rule ID"
            value={ruleId}
            onChange={(e) => setRuleId(e.target.value)}
            helperText="Auto-generated for testing"
            disabled
          />
          <Button variant="contained" onClick={handleStartWorkflow}>
            Start Workflow
          </Button>
        </Box>
      </Paper>

      {/* Task List */}
      <Paper elevation={3} sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Active Tasks
        </Typography>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">{error}</Typography>
        ) : tasks.length === 0 ? (
          <Typography>No active tasks found</Typography>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Task Name</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Transaction</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {tasks.map(task => (
                  <TableRow 
                    key={task.id}
                    hover
                    selected={selectedTask?.id === task.id}
                  >
                    <TableCell>{task.name}</TableCell>
                    <TableCell>
                      {new Date(task.createTime).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      {task.customerInfo ? (
                        <>
                          {task.customerInfo.name}
                          <Chip 
                            size="small" 
                            label={task.customerInfo.riskLevel}
                            color={task.customerInfo.riskLevel === 'HIGH' ? 'error' : 'default'}
                            sx={{ ml: 1 }}
                          />
                        </>
                      ) : '-'}
                    </TableCell>
                    <TableCell>
                      {task.transactionInfo ? (
                        <>
                          ${task.transactionInfo.amount.toLocaleString()}
                          <Typography variant="caption" display="block" color="textSecondary">
                            {task.transactionInfo.type}
                          </Typography>
                        </>
                      ) : '-'}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={() => setSelectedTask(task)}
                      >
                        Review
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Task Details Dialog */}
      <Dialog 
        open={!!selectedTask} 
        onClose={() => setSelectedTask(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Task Details: {selectedTask?.name}
        </DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              {/* Customer Information */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Customer Information
                    </Typography>
                    {selectedTask.customerInfo ? (
                      <>
                        <Typography variant="subtitle1">
                          {selectedTask.customerInfo.name}
                        </Typography>
                        <Typography color="textSecondary">
                          ID: {selectedTask.customerInfo.customerId}
                        </Typography>
                        <Chip 
                          label={`Risk Level: ${selectedTask.customerInfo.riskLevel}`}
                          color={selectedTask.customerInfo.riskLevel === 'HIGH' ? 'error' : 'default'}
                          sx={{ mt: 1 }}
                        />
                      </>
                    ) : (
                      <Typography color="textSecondary">No customer information available</Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Transaction Information */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Transaction Information
                    </Typography>
                    {selectedTask.transactionInfo ? (
                      <>
                        <Typography variant="h5">
                          ${selectedTask.transactionInfo.amount.toLocaleString()}
                        </Typography>
                        <Typography color="textSecondary">
                          Type: {selectedTask.transactionInfo.type}
                        </Typography>
                        <Typography color="textSecondary">
                          Status: {selectedTask.transactionInfo.status}
                        </Typography>
                        <Typography color="textSecondary">
                          Date: {new Date(selectedTask.transactionInfo.timestamp).toLocaleString()}
                        </Typography>
                      </>
                    ) : (
                      <Typography color="textSecondary">No transaction information available</Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Task Action Form */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Task Action
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <Select
                        value={decision}
                        onChange={(e) => setDecision(e.target.value)}
                        label="Decision"
                      >
                        {selectedTask.name.includes('Officer') ? (
                          <>
                            <MenuItem value="false_positive">Mark as False Positive</MenuItem>
                            <MenuItem value="escalate">Escalate</MenuItem>
                          </>
                        ) : (
                          <>
                            <MenuItem value="close">Close Case</MenuItem>
                            <MenuItem value="return">Return to Officer</MenuItem>
                          </>
                        )}
                      </Select>
                      
                      <TextField
                        multiline
                        rows={4}
                        label="Comments"
                        value={comments}
                        onChange={(e) => setComments(e.target.value)}
                      />
                      
                      <input
                        type="file"
                        multiple
                        onChange={(e) => setFiles(Array.from(e.target.files))}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedTask(null)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={selectedTask?.name.includes('Officer') ? 
              handleOfficerReview : handleManagerReview}
          >
            Complete Task
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WorkflowTestPage;
