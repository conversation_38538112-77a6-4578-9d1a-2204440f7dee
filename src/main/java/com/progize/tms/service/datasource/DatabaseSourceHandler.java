package com.progize.tms.service.datasource;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progize.tms.exceptions.TmsBusinessException;
import com.progize.tms.exceptions.TmsError;
import com.progize.tms.repository.entity.DatabaseSourceEntity;
import com.progize.tms.service.model.SchemaEntity;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;

/** Handler for database data sources. */
@Slf4j
public class DatabaseSourceHandler implements DataSourceHandler {

  private final DatabaseSourceEntity databaseSource;
  private final ObjectMapper objectMapper;

  public DatabaseSourceHandler(DatabaseSourceEntity databaseSource) {
    this.databaseSource = databaseSource;
    this.objectMapper = new ObjectMapper();
  }

  @Override
  public boolean testConnection() {
    try (Connection connection = getConnection()) {
      return connection.isValid(5);
    } catch (Exception e) {
      log.error("Error testing connection: {}", e.getMessage(), e);
      return false;
    }
  }

  @Override
  public List<SchemaEntity> discoverSchema() {
    try (Connection connection = getConnection()) {
      List<SchemaEntity> schema = new ArrayList<>();

      DatabaseMetaData metaData = connection.getMetaData();

      // Get tables
      try (ResultSet tables =
          metaData.getTables(
              connection.getCatalog(),
              databaseSource.getSchemaName(),
              null,
              new String[] {"TABLE"})) {

        while (tables.next()) {
          String tableName = tables.getString("TABLE_NAME");
          SchemaEntity tableSchema = new SchemaEntity();
          tableSchema.setName(tableName);
          tableSchema.setEntityName(tableName);
          tableSchema.setEntityType(SchemaEntity.EntityType.TABLE);
          tableSchema.setType("table");

          List<SchemaEntity.SchemaField> fields = new ArrayList<>();

          // Get columns for the table
          try (ResultSet columns =
              metaData.getColumns(
                  connection.getCatalog(), databaseSource.getSchemaName(), tableName, null)) {

            while (columns.next()) {
              String columnName = columns.getString("COLUMN_NAME");
              String columnType = columns.getString("TYPE_NAME");
              boolean nullable = columns.getInt("NULLABLE") != 0;

              SchemaEntity.SchemaField field =
                  SchemaEntity.SchemaField.builder()
                      .name(columnName)
                      .type(columnType)
                      .nullable(nullable)
                      .build();

              fields.add(field);
            }
          }

          tableSchema.setFields(fields);
          schema.add(tableSchema);
        }
      }

      return schema;
    } catch (Exception e) {
      log.error("Error discovering schema: {}", e.getMessage(), e);
      throw new TmsBusinessException(TmsError.SCHEMA_DISCOVERY_ERROR);
    }
  }

  @Override
  public List<Map<String, Object>> executeQuery(String entityName, Map<String, Object> parameters) {
    try (Connection connection = getConnection()) {
      List<Map<String, Object>> results = new ArrayList<>();

      // Build SQL query
      StringBuilder sql = new StringBuilder();
      sql.append("SELECT * FROM ");

      // Add schema name if provided
      if (databaseSource.getSchemaName() != null && !databaseSource.getSchemaName().isEmpty()) {
        sql.append(databaseSource.getSchemaName()).append(".");
      }

      sql.append(entityName);

      // Add WHERE clause if parameters are provided
      if (parameters != null && !parameters.isEmpty()) {
        sql.append(" WHERE ");
        int i = 0;
        for (String key : parameters.keySet()) {
          if (i > 0) {
            sql.append(" AND ");
          }
          sql.append(key).append(" = ?");
          i++;
        }
      }

      // Prepare statement
      try (PreparedStatement statement = connection.prepareStatement(sql.toString())) {
        // Set parameters
        if (parameters != null && !parameters.isEmpty()) {
          int i = 1;
          for (Object value : parameters.values()) {
            statement.setObject(i++, value);
          }
        }

        // Execute query
        try (ResultSet resultSet = statement.executeQuery()) {
          ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
          int columnCount = resultSetMetaData.getColumnCount();

          // Process results
          while (resultSet.next()) {
            Map<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columnCount; i++) {
              String columnName = resultSetMetaData.getColumnName(i);
              Object value = resultSet.getObject(i);
              row.put(columnName, value);
            }
            results.add(row);
          }
        }
      }

      return results;
    } catch (Exception e) {
      log.error("Error executing query: {}", e.getMessage(), e);
      throw new TmsBusinessException(TmsError.CONNECTION_ERROR);
    }
  }

  /**
   * Get a connection to the database.
   *
   * @return Database connection
   * @throws SQLException if a database access error occurs
   */
  private Connection getConnection() throws SQLException {
    try {
      // Load driver
      Class.forName(databaseSource.getDriverClass());

      // Set properties
      Properties properties = new Properties();
      properties.setProperty("user", databaseSource.getUsername());
      properties.setProperty("password", "");
      if (databaseSource.getPassword() != null) {
        properties.setProperty("password", databaseSource.getPassword());
      }

      // Add additional properties if provided
      if (databaseSource.getAdditionalProperties() != null
          && !databaseSource.getAdditionalProperties().isEmpty()) {
        try {
          @SuppressWarnings("unchecked")
          Map<String, String> additionalProps =
              objectMapper.readValue(databaseSource.getAdditionalProperties(), Map.class);
          additionalProps.forEach(properties::setProperty);
        } catch (Exception e) {
          log.warn("Failed to parse additional properties: {}", e.getMessage());
        }
      }

      // Get connection
      return DriverManager.getConnection(databaseSource.getConnectionUrl(), properties);
    } catch (ClassNotFoundException e) {
      log.error("Database driver not found: {}", databaseSource.getDriverClass(), e);
      throw new TmsBusinessException(TmsError.CONNECTION_ERROR);
    } catch (SQLException e) {
      log.error("Error connecting to database: {}", e.getMessage(), e);
      throw e;
    }
  }
}
