package com.progize.tms.services.impl;

import com.progize.tms.services.AggregationProvider;
import com.progize.tms.services.ClickHouseQueryService;
import com.progize.tms.services.models.AggregationRequirement;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Implementation of AggregationProvider that uses ClickHouse to retrieve actual aggregation data.
 */
@Component
@RequiredArgsConstructor
public class ClickHouseAggregationProvider implements AggregationProvider {
  private final ClickHouseQueryService clickHouseQueryService;

  @Override
  public Map<String, Object> getAggregations(
      String customerId, Set<AggregationRequirement> requirements) {
    return clickHouseQueryService.executeAggregationsForCustomer(customerId, requirements);
  }

  @Override
  public Map<String, Object> getAggregations(
      String customerId, Set<AggregationRequirement> requirements, Long bucketId) {
    return clickHouseQueryService.executeAggregationsForCustomer(
        customerId, requirements, bucketId);
  }
}
