import React, { useState, useEffect } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  Alert,
  TextField,
  Typography,
  Stepper,
  Step,
  StepLabel,
  useTheme,
  alpha,
  Breadcrumbs,
  Link,
  IconButton,
  Avatar
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import StorageIcon from '@mui/icons-material/Storage';
import ApiIcon from '@mui/icons-material/Api';
import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import dataSourceService from '../../services/dataSourceService';
import TestConnectionButton from './TestConnectionButton';

/**
 * Dynamic form component for creating and editing data sources
 *
 * @param {Object} props - Component props
 * @param {string} props.mode - Form mode ('create' or 'edit')
 * @param {Object} props.initialData - Initial data for edit mode
 * @param {number} props.dataSourceId - Data source ID for edit mode
 */
const DataSourceForm = ({ mode = 'create', initialData = null, dataSourceId = null }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isEditMode = mode === 'edit';

  // Function to get icon based on data source type
  const getTypeIcon = (type) => {
    switch(type) {
      case 'DATABASE':
        return <StorageIcon />;
      case 'REST_API':
        return <ApiIcon />;
      default:
        return <StorageIcon />;
    }
  };

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: '',
    ...initialData
  });

  // UI state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [configurations, setConfigurations] = useState([]);
  const [selectedConfig, setSelectedConfig] = useState(null);
  const [errors, setErrors] = useState({});
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Load configurations on component mount
  useEffect(() => {
    const loadConfigurations = async () => {
      try {
        const configs = await dataSourceService.getDataSourceConfigurations();
        setConfigurations(configs);

        // If in edit mode and we have initialData, select the appropriate config
        if (isEditMode && initialData && initialData.type) {
          const config = configs.find(c => c.type === initialData.type);
          setSelectedConfig(config);
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading configurations:', err);
        setSnackbar({
          open: true,
          message: 'Failed to load form configurations. Please try again later.',
          severity: 'error'
        });
        setLoading(false);
      }
    };

    loadConfigurations();
  }, [isEditMode, initialData]);

  // Auto-fill driver class when initialData is loaded or when database type changes
  useEffect(() => {
    // Only apply for database sources
    if (formData.type === 'DATABASE' && formData.databaseType) {
      // If driver class is empty or not set, auto-fill it based on database type
      if (!formData.driverClass) {
        let driverClass = '';
        if (formData.databaseType === 'MYSQL') {
          driverClass = 'com.mysql.cj.jdbc.Driver';
        } else if (formData.databaseType === 'CLICKHOUSE') {
          driverClass = 'com.clickhouse.jdbc.ClickHouseDriver';
        }

        if (driverClass) {
          setFormData(prevData => ({
            ...prevData,
            driverClass: driverClass
          }));
        }
      }
    }
  }, [formData.type, formData.databaseType, formData.driverClass]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    // If changing the data source type, update the selected config
    if (name === 'type') {
      const config = configurations.find(c => c.type === value);
      setSelectedConfig(config);

      // Reset any type-specific fields
      const newFormData = {
        ...formData,
        [name]: value
      };

      // Only keep common fields when changing type
      if (formData.type && formData.type !== value) {
        const commonFields = ['name', 'description', 'type'];
        Object.keys(formData).forEach(key => {
          if (!commonFields.includes(key)) {
            delete newFormData[key];
          }
        });
      }

      setFormData(newFormData);
    } else if (name === 'databaseType') {
      // Auto-fill driver class based on database type
      let driverClass = '';
      if (value === 'MYSQL') {
        driverClass = 'com.mysql.cj.jdbc.Driver';
      } else if (value === 'CLICKHOUSE') {
        driverClass = 'com.clickhouse.jdbc.ClickHouseDriver';
      }

      setFormData({
        ...formData,
        [name]: value,
        driverClass: driverClass
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};

    // Validate common fields
    if (!formData.name) {
      newErrors.name = 'Name is required';
    }

    if (!formData.type) {
      newErrors.type = 'Type is required';
    }

    // Validate type-specific fields
    if (selectedConfig) {
      selectedConfig.fields.forEach(field => {
        if (field.required && !formData[field.name]) {
          newErrors[field.name] = `${field.label} is required`;
        }
      });

      // Validate conditional fields
      if (selectedConfig.conditionalFields) {
        Object.entries(selectedConfig.conditionalFields).forEach(([fieldName, conditions]) => {
          const fieldValue = formData[fieldName];
          if (fieldValue && conditions[fieldValue]) {
            conditions[fieldValue].forEach(field => {
              if (field.required && !formData[field.name]) {
                newErrors[field.name] = `${field.label} is required`;
              }
            });
          }
        });
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setSnackbar({
        open: true,
        message: 'Please fix the errors in the form',
        severity: 'error'
      });
      return;
    }

    try {
      setSaving(true);

      let response;
      if (formData.type === 'DATABASE') {
        if (isEditMode) {
          response = await dataSourceService.updateDatabaseSource(dataSourceId, formData);
        } else {
          response = await dataSourceService.createDatabaseSource(formData);
        }
      } else if (formData.type === 'REST_API') {
        if (isEditMode) {
          response = await dataSourceService.updateRestApiSource(dataSourceId, formData);
        } else {
          response = await dataSourceService.createRestApiSource(formData);
        }
      }

      setSnackbar({
        open: true,
        message: `Data source ${isEditMode ? 'updated' : 'created'} successfully!`,
        severity: 'success'
      });

      // Navigate back to the list after a short delay
      setTimeout(() => {
        navigate('/data-sources');
      }, 1500);
    } catch (err) {
      console.error('Error saving data source:', err);

      // Check for validation errors from the server
      if (err.response && err.response.data && err.response.data.errors) {
        const serverErrors = {};
        err.response.data.errors.forEach(error => {
          serverErrors[error.field] = error.message;
        });
        setErrors(serverErrors);
      }

      setSnackbar({
        open: true,
        message: `Failed to ${isEditMode ? 'update' : 'create'} data source: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle cancel button click
  const handleCancel = () => {
    navigate('/data-sources');
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Get fields to display based on the selected configuration
  const getFieldsToDisplay = () => {
    if (!selectedConfig) return [];

    const fields = [...selectedConfig.fields];

    // Add conditional fields based on form values
    if (selectedConfig.conditionalFields) {
      Object.entries(selectedConfig.conditionalFields).forEach(([fieldName, conditions]) => {
        const fieldValue = formData[fieldName];
        if (fieldValue && conditions[fieldValue]) {
          fields.push(...conditions[fieldValue]);
        }
      });
    }

    // Modify helper text for connection URL based on database type
    if (formData.type === 'DATABASE' && formData.databaseType) {
      const connectionUrlField = fields.find(field => field.name === 'connectionUrl');
      if (connectionUrlField) {
        // Clone the field to avoid modifying the original
        const index = fields.indexOf(connectionUrlField);
        const modifiedField = { ...connectionUrlField };

        // Set appropriate example URL based on database type
        if (formData.databaseType === 'MYSQL') {
          modifiedField.helperText = 'Example: ****************************************';
        } else if (formData.databaseType === 'CLICKHOUSE') {
          modifiedField.helperText = 'Example: ************************************';
        }

        // Replace the field in the array
        fields[index] = modifiedField;
      }
    }

    return fields;
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      noValidate
      sx={{
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Header with title and breadcrumbs */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        mb: 3,
        pb: 2,
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
      }}>
        <Box>
          <Breadcrumbs
            separator={<KeyboardArrowRightIcon fontSize="small" />}
            aria-label="breadcrumb"
            sx={{ mb: 1 }}
          >
            <Link
              underline="hover"
              color="inherit"
              component={RouterLink}
              to="/"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="small" />
              Home
            </Link>
            <Link
              underline="hover"
              color="inherit"
              component={RouterLink}
              to="/data-sources"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              Data Sources
            </Link>
            <Typography color="text.secondary" variant="body2">
              {isEditMode ? initialData?.name : 'Create New'}
            </Typography>
          </Breadcrumbs>

          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 600,
              background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              letterSpacing: '-0.5px',
              mb: 1
            }}
          >
            {isEditMode ? `Edit Data Source` : 'Create New Data Source'}
          </Typography>

          <Typography variant="body1" color="text.secondary">
            {isEditMode
              ? 'Update the configuration for this data source.'
              : 'Configure a new data source to connect to external data for your rules.'}
          </Typography>
        </Box>
      </Box>

      <Card
        sx={{
          mb: 3,
          borderRadius: '10px',
          overflow: 'hidden',
          transition: 'all 0.3s ease',
          boxShadow: '0 2px 8px -4px rgba(0,0,0,0.05), 0 4px 16px -8px rgba(0,0,0,0.04)',
          bgcolor: '#ffffff'
        }}
      >
        <CardHeader
          title={
            <Typography variant="h6" sx={{ fontWeight: 500, fontSize: '1rem' }}>
              Basic Information
            </Typography>
          }
          sx={{
            bgcolor: alpha(theme.palette.primary.main, 0.03),
            py: 1.5,
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.05)}`
          }}
        />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                error={!!errors.name}
                helperText={errors.name}
                required
                disabled={saving}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1.5
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl
                fullWidth
                error={!!errors.type}
                disabled={isEditMode || saving}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1.5
                  }
                }}
              >
                <InputLabel id="type-label">Type *</InputLabel>
                <Select
                  labelId="type-label"
                  name="type"
                  value={formData.type || ''}
                  onChange={handleChange}
                  label="Type *"
                  required
                  startAdornment={
                    formData.type && (
                      <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                        {getTypeIcon(formData.type)}
                      </Box>
                    )
                  }
                >
                  {configurations.map((config) => (
                    <MenuItem key={config.type} value={config.type}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          sx={{
                            width: 24,
                            height: 24,
                            mr: 1,
                            bgcolor: config.type === 'DATABASE'
                              ? alpha(theme.palette.primary.main, 0.1)
                              : alpha(theme.palette.secondary.main, 0.1),
                            color: config.type === 'DATABASE'
                              ? theme.palette.primary.main
                              : theme.palette.secondary.main
                          }}
                        >
                          {config.type === 'DATABASE' ? <StorageIcon fontSize="small" /> : <ApiIcon fontSize="small" />}
                        </Avatar>
                        {config.displayName}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
                {errors.type && <FormHelperText>{errors.type}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                multiline
                rows={2}
                disabled={saving}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1.5
                  }
                }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {selectedConfig && (
        <Card
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
            overflow: 'hidden'
          }}
        >
          <CardHeader
            title={
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {selectedConfig.displayName} Configuration
              </Typography>
            }
            sx={{
              bgcolor: selectedConfig.type === 'DATABASE'
                ? alpha(theme.palette.primary.main, 0.05)
                : alpha(theme.palette.secondary.main, 0.05),
              py: 1.5
            }}
          />
          <CardContent>
            <Grid container spacing={3}>
              {getFieldsToDisplay().map((field) => (
                <Grid item xs={12} md={6} key={field.name}>
                  {field.type === 'select' ? (
                    <FormControl
                      fullWidth
                      error={!!errors[field.name]}
                      disabled={saving}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1.5
                        }
                      }}
                    >
                      <InputLabel id={`${field.name}-label`}>
                        {field.label} {field.required ? '*' : ''}
                      </InputLabel>
                      <Select
                        labelId={`${field.name}-label`}
                        name={field.name}
                        value={formData[field.name] || ''}
                        onChange={handleChange}
                        label={`${field.label} ${field.required ? '*' : ''}`}
                        required={field.required}
                      >
                        {field.options.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors[field.name] && <FormHelperText>{errors[field.name]}</FormHelperText>}
                      {field.helperText && !errors[field.name] && (
                        <FormHelperText>{field.helperText}</FormHelperText>
                      )}
                    </FormControl>
                  ) : (
                    <TextField
                      fullWidth
                      label={field.label}
                      name={field.name}
                      type={field.type}
                      value={formData[field.name] || ''}
                      onChange={handleChange}
                      error={!!errors[field.name]}
                      helperText={errors[field.name] || field.helperText}
                      required={field.required}
                      disabled={saving}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1.5
                        }
                      }}
                    />
                  )}
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          mt: 4,
          pt: 3,
          borderTop: `1px solid ${alpha(theme.palette.divider, 0.05)}`
        }}
      >
        <Button
          variant="outlined"
          color="secondary"
          onClick={handleCancel}
          startIcon={<ArrowBackIcon />}
          disabled={saving}
          sx={{
            borderRadius: '28px',
            px: 3,
            borderColor: alpha(theme.palette.secondary.main, 0.5),
            '&:hover': {
              borderColor: theme.palette.secondary.main,
              backgroundColor: alpha(theme.palette.secondary.main, 0.05)
            }
          }}
        >
          Back
        </Button>
        <Box>
          {isEditMode && (
            <TestConnectionButton
              dataSourceId={dataSourceId}
              variant="outlined"
              sx={{
                mr: 2,
                borderRadius: '28px'
              }}
              disabled={saving}
            />
          )}
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
            disabled={saving}
            sx={{
              borderRadius: '28px',
              px: 3,
              py: 1,
              boxShadow: '0 2px 8px 0 rgba(0,118,255,0.2)',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: '0 4px 12px rgba(0,118,255,0.25)'
              }
            }}
          >
            {saving ? 'Saving...' : isEditMode ? 'Update' : 'Create'}
          </Button>
        </Box>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DataSourceForm;
