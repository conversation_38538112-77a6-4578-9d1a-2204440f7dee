package com.progize.tms.repository;

import com.progize.tms.repository.entity.WorkflowCase;
import com.progize.tms.repository.entity.WorkflowType;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface WorkflowCaseRepository extends JpaRepository<WorkflowCase, Long> {

  Optional<WorkflowCase> findByCaseNumber(String caseNumber);

  Optional<WorkflowCase> findByProcessInstanceId(String processInstanceId);

  List<WorkflowCase> findByWorkflowType(WorkflowType workflowType);

  List<WorkflowCase> findByEntityTypeAndEntityId(String entityType, Long entityId);

  @Query(
      "SELECT wc FROM WorkflowCase wc WHERE wc.status = :status AND wc.workflowType = :workflowType")
  List<WorkflowCase> findByStatusAndWorkflowType(String status, WorkflowType workflowType);

  @Query("SELECT DISTINCT wc.status FROM WorkflowCase wc WHERE wc.workflowType = :workflowType")
  List<String> findDistinctStatusByWorkflowType(WorkflowType workflowType);
}
