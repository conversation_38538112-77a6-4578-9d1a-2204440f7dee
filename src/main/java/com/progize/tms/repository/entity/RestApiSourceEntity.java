package com.progize.tms.repository.entity;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

import com.progize.tms.repository.entity.base.BaseEntity;
import com.progize.tms.repository.entity.enums.AuthenticationType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.Audited;

/** Entity representing REST API-specific configuration for a data source. */
@Entity
@Table(name = "data_source_rest_api")
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Audited(targetAuditMode = NOT_AUDITED)
public class RestApiSourceEntity extends BaseEntity {

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "data_source_id", nullable = false)
  private DataSourceEntity dataSource;

  @Column(name = "base_url", nullable = false)
  private String baseUrl;

  @Enumerated(EnumType.STRING)
  @Column(name = "authentication_type", nullable = false)
  private AuthenticationType authenticationType;

  @Column(name = "username")
  private String username;

  @Column(name = "password")
  private String password;

  @Column(name = "api_key_name")
  private String apiKeyName;

  @Column(name = "api_key_value")
  private String apiKeyValue;

  @Column(name = "api_key_location")
  private String apiKeyLocation;

  @Column(name = "oauth_token_url")
  private String oauthTokenUrl;

  @Column(name = "oauth_client_id")
  private String oauthClientId;

  @Column(name = "oauth_client_secret")
  private String oauthClientSecret;

  @Column(name = "oauth_scope")
  private String oauthScope;

  @Column(name = "headers", columnDefinition = "TEXT")
  private String headers;

  @Column(name = "request_timeout_ms")
  @Builder.Default
  private Integer requestTimeoutMs = 30000;
}
