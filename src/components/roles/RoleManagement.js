import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Chip,
  IconButton,
  Paper,
  Tooltip,
  useTheme,
  alpha,
  Alert,
  Snackbar,
  DialogContentText,
  styled
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SecurityIcon from '@mui/icons-material/Security';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import { roleService, permissionService } from '../../services/api';

// Styled components
const RoleCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  transition: 'all 0.3s cubic-bezier(.25,.8,.25,1)',
  '&:hover': {
    boxShadow: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
    transform: 'translateY(-2px)'
  }
}));

const PermissionChip = styled(Chip)(({ theme }) => ({
  height: 24,
  fontSize: '0.75rem',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.dark,
  borderColor: alpha(theme.palette.primary.main, 0.3),
  margin: theme.spacing(0.5),
  '& .MuiChip-label': {
    padding: '0 8px',
  }
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(5),
  padding: theme.spacing(1, 3),
  textTransform: 'none',
  fontWeight: 500,
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  transition: 'all 0.2s ease',
  '&:hover': {
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    transform: 'translateY(-1px)'
  }
}));

const RoleManagement = () => {
  const theme = useTheme();
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentRole, setCurrentRole] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissionIds: []
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch roles and permissions on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [rolesData, permissionsData] = await Promise.all([
        roleService.getAllRoles(),
        permissionService.getAllPermissions()
      ]);
      setRoles(Array.isArray(rolesData) ? rolesData : []);
      setPermissions(Array.isArray(permissionsData) ? permissionsData : []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load roles and permissions',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle permission checkbox changes
  const handlePermissionChange = (permissionId) => {
    setFormData((prevData) => {
      const currentPermissions = [...prevData.permissionIds];
      if (currentPermissions.includes(permissionId)) {
        return {
          ...prevData,
          permissionIds: currentPermissions.filter(id => id !== permissionId)
        };
      } else {
        return {
          ...prevData,
          permissionIds: [...currentPermissions, permissionId]
        };
      }
    });
  };

  // Reset form data
  const resetFormData = () => {
    setFormData({
      name: '',
      description: '',
      permissionIds: []
    });
  };

  // Open create modal
  const openCreateModal = () => {
    resetFormData();
    setShowCreateModal(true);
  };

  // Open edit modal
  const openEditModal = (role) => {
    setCurrentRole(role);
    setFormData({
      name: role.name,
      description: role.description || '',
      permissionIds: role.permissions.map(p => p.id)
    });
    setShowEditModal(true);
  };

  // Open delete modal
  const openDeleteModal = (role) => {
    setCurrentRole(role);
    setShowDeleteModal(true);
  };

  // Create role
  const createRole = async () => {
    try {
      setLoading(true);
      const response = await roleService.createRole(formData);
      setRoles([...roles, response]);
      setShowCreateModal(false);
      resetFormData();
      setSnackbar({
        open: true,
        message: 'Role created successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error creating role:', error);
      setSnackbar({
        open: true,
        message: 'Failed to create role',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Update role
  const updateRole = async () => {
    try {
      setLoading(true);
      const response = await roleService.updateRole(currentRole.id, formData);
      setRoles(roles.map(role => role.id === currentRole.id ? response : role));
      setShowEditModal(false);
      resetFormData();
      setSnackbar({
        open: true,
        message: 'Role updated successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error updating role:', error);
      setSnackbar({
        open: true,
        message: 'Failed to update role',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Update role permissions directly
  const updateRolePermissions = async (roleId, permissionIds) => {
    try {
      setLoading(true);
      const response = await roleService.updateRolePermissions(roleId, permissionIds);
      setRoles(roles.map(role => role.id === roleId ? response : role));
      setSnackbar({
        open: true,
        message: 'Role permissions updated successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error updating role permissions:', error);
      setSnackbar({
        open: true,
        message: 'Failed to update role permissions',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Delete role
  const deleteRole = async () => {
    try {
      setLoading(true);
      await roleService.deleteRole(currentRole.id);
      setRoles(roles.filter(role => role.id !== currentRole.id));
      setShowDeleteModal(false);
      setSnackbar({
        open: true,
        message: 'Role deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error deleting role:', error);
      setSnackbar({
        open: true,
        message: 'Failed to delete role',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  return (
    <Container fluid>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 3 
        }}>
          <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
            Role Management
          </Typography>
          <ActionButton
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={openCreateModal}
          >
            Create New Role
          </ActionButton>
        </Box>
        
        {loading && !roles.length ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {roles.length > 0 ? (
              <Box>
                {roles.map(role => (
                  <RoleCard key={role.id}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <SecurityIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {role.name}
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                            {role.description || 'No description provided'}
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', mt: 1 }}>
                            {role.permissions && role.permissions.map(permission => (
                              <PermissionChip key={permission.id} label={permission.name} size="small" />
                            ))}
                            {(!role.permissions || role.permissions.length === 0) && (
                              <Typography variant="caption" color="textSecondary">
                                No permissions assigned
                              </Typography>
                            )}
                          </Box>
                        </Box>
                        <Box>
                          <Tooltip title="Edit Role">
                            <IconButton 
                              onClick={() => openEditModal(role)}
                              sx={{ 
                                color: theme.palette.primary.main,
                                '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.1) }
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Role">
                            <IconButton 
                              onClick={() => openDeleteModal(role)}
                              sx={{ 
                                color: theme.palette.error.main,
                                '&:hover': { backgroundColor: alpha(theme.palette.error.main, 0.1) }
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </CardContent>
                  </RoleCard>
                ))}
              </Box>
            ) : (
              <Paper 
                sx={{ 
                  p: 4, 
                  borderRadius: 2,
                  textAlign: 'center',
                  bgcolor: alpha(theme.palette.background.paper, 0.6),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <Box sx={{ mb: 2 }}>
                  <SecurityIcon sx={{ fontSize: 48, color: theme.palette.text.disabled }} />
                </Box>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 500 }}>
                  No roles found
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                  Create a new role to get started
                </Typography>
                <ActionButton
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={openCreateModal}
                >
                  Create New Role
                </ActionButton>
              </Paper>
            )}
          </>
        )}
      </Box>

      {/* Create Role Modal */}
      <Dialog 
        open={showCreateModal} 
        onClose={() => setShowCreateModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
          py: 2
        }}>
          Create New Role
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" sx={{ '& .MuiTextField-root': { my: 1 } }}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter role name"
              required
              margin="normal"
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter role description"
              multiline
              rows={2}
              margin="normal"
            />
            <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
              Permissions
            </Typography>
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 2, 
                maxHeight: '200px', 
                overflowY: 'auto',
                borderColor: alpha(theme.palette.divider, 0.2)
              }}
            >
              {permissions.length > 0 ? (
                permissions.map(permission => (
                  <FormControlLabel
                    key={permission.id}
                    control={
                      <Checkbox
                        checked={formData.permissionIds.includes(permission.id)}
                        onChange={() => handlePermissionChange(permission.id)}
                        color="primary"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>{permission.name}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {permission.description || 'No description'}
                        </Typography>
                      </Box>
                    }
                    sx={{ display: 'block', mb: 1 }}
                  />
                ))
              ) : (
                <Typography variant="body2" color="textSecondary" align="center">
                  No permissions available
                </Typography>
              )}
            </Paper>
          </Box>
        </DialogContent>
        <DialogActions sx={{ 
          px: 3, 
          py: 2,
          borderTop: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
        }}>
          <Button 
            onClick={() => setShowCreateModal(false)} 
            color="inherit"
          >
            Cancel
          </Button>
          <Button 
            onClick={createRole}
            variant="contained" 
            color="primary"
            disabled={!formData.name.trim()}
            startIcon={<AddIcon />}
          >
            Create Role
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Role Modal */}
      <Dialog 
        open={showEditModal} 
        onClose={() => setShowEditModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
          py: 2
        }}>
          Edit Role
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" sx={{ '& .MuiTextField-root': { my: 1 } }}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter role name"
              required
              margin="normal"
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter role description"
              multiline
              rows={2}
              margin="normal"
            />
            <Typography variant="subtitle2" sx={{ mt: 2, mb: 1, display: 'flex', alignItems: 'center' }}>
              <VpnKeyIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
              Permissions
            </Typography>
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 2, 
                maxHeight: '200px', 
                overflowY: 'auto',
                borderColor: alpha(theme.palette.divider, 0.2)
              }}
            >
              {permissions.length > 0 ? (
                permissions.map(permission => (
                  <FormControlLabel
                    key={permission.id}
                    control={
                      <Checkbox
                        checked={formData.permissionIds.includes(permission.id)}
                        onChange={() => handlePermissionChange(permission.id)}
                        color="primary"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>{permission.name}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {permission.description || 'No description'}
                        </Typography>
                      </Box>
                    }
                    sx={{ display: 'block', mb: 1 }}
                  />
                ))
              ) : (
                <Typography variant="body2" color="textSecondary" align="center">
                  No permissions available
                </Typography>
              )}
            </Paper>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
              <Button
                size="small"
                color="primary"
                onClick={() => {
                  setFormData({
                    ...formData,
                    permissionIds: permissions.map(p => p.id)
                  });
                }}
              >
                Select All
              </Button>
              <Button
                size="small"
                color="inherit"
                onClick={() => {
                  setFormData({
                    ...formData,
                    permissionIds: []
                  });
                }}
              >
                Clear All
              </Button>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ 
          px: 3, 
          py: 2,
          borderTop: theme.palette.mode === 'dark' 
            ? '1px solid rgba(255, 255, 255, 0.05)' 
            : '1px solid rgba(0, 0, 0, 0.05)',
        }}>
          <Button 
            onClick={() => setShowEditModal(false)} 
            color="inherit"
          >
            Cancel
          </Button>
          <Button 
            onClick={updateRole}
            variant="contained" 
            color="primary"
            disabled={!formData.name.trim()}
            startIcon={<EditIcon />}
          >
            Update Role
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Role Modal */}
      <Dialog
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxWidth: '400px',
            backgroundImage: 'linear-gradient(to bottom, rgba(255,255,255,0.95), rgba(255,255,255,0.98))',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
          }
        }}
      >
        <DialogTitle sx={{
          fontWeight: 600,
          fontSize: '1.1rem',
          pb: 1,
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          color: theme.palette.error.main,
          display: 'flex',
          alignItems: 'center',
        }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Confirm Deletion
        </DialogTitle>
        <DialogContent sx={{ pt: 2, mt: 1 }}>
          <DialogContentText variant="body2" sx={{ color: theme.palette.text.primary }}>
            Are you sure you want to delete the role <Box component="span" sx={{ fontWeight: 600 }}>"{currentRole?.name}"</Box>? This action cannot be undone and may affect users with this role.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={() => setShowDeleteModal(false)}
            variant="outlined"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
            size="small"
          >
            Cancel
          </Button>
          <Button
            onClick={deleteRole}
            color="error"
            variant="contained"
            sx={{
              borderRadius: 6,
              py: 0.5,
              px: 2,
              transition: 'all 0.2s ease',
              backgroundColor: theme.palette.error.main,
              '&:hover': {
                backgroundColor: theme.palette.error.dark,
                boxShadow: '0 4px 12px rgba(211, 47, 47, 0.3)',
              }
            }}
            size="small"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar 
        open={snackbar.open} 
        autoHideDuration={6000} 
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity} 
          sx={{ width: '100%' }}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default RoleManagement;
